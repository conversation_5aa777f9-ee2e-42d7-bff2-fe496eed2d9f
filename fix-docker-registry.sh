#!/bin/bash

echo "🔧 Fixing Docker registry configuration..."
echo ""

# Check if Docker daemon.json exists
DOCKER_CONFIG_DIR="$HOME/.docker"
DAEMON_JSON="$DOCKER_CONFIG_DIR/daemon.json"

echo "📍 Docker config location: $DAEMON_JSON"

if [ -f "$DAEMON_JSON" ]; then
    echo "📄 Current Docker daemon.json content:"
    cat "$DAEMON_JSON"
    echo ""
    
    # Backup the current config
    cp "$DAEMON_JSON" "$DAEMON_JSON.backup"
    echo "💾 Backup created: $DAEMON_JSON.backup"
    
    # Remove registry-mirrors configuration
    echo "🗑️  Removing registry mirrors..."
    python3 -c "
import json
import sys

try:
    with open('$DAEMON_JSON', 'r') as f:
        config = json.load(f)
    
    # Remove registry-mirrors if it exists
    if 'registry-mirrors' in config:
        del config['registry-mirrors']
        print('✅ Removed registry-mirrors configuration')
    
    # Write back the cleaned config
    with open('$DAEMON_JSON', 'w') as f:
        json.dump(config, f, indent=2)
    
    print('✅ Docker configuration updated')
except Exception as e:
    print(f'❌ Error: {e}')
    sys.exit(1)
"
else
    echo "ℹ️  No Docker daemon.json found - this is normal"
fi

echo ""
echo "🔄 Please restart Docker Desktop for changes to take effect:"
echo "   1. Quit Docker Desktop completely"
echo "   2. Start Docker Desktop again"
echo "   3. Wait for it to fully start"
echo "   4. Try running the server again"
echo ""
echo "After restarting Docker, you can run:"
echo "   ./start-server.sh"
