#!/bin/bash

echo "🇨🇳 Setting up Docker for China..."
echo ""

# Create Docker daemon configuration for China
DOCKER_CONFIG_DIR="$HOME/.docker"
DAEMON_JSON="$DOCKER_CONFIG_DIR/daemon.json"

# Create .docker directory if it doesn't exist
mkdir -p "$DOCKER_CONFIG_DIR"

echo "📝 Creating Docker daemon configuration..."

# Create daemon.json with Chinese mirrors
cat > "$DAEMON_JSON" << 'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ],
  "insecure-registries": [],
  "debug": false,
  "experimental": false
}
EOF

echo "✅ Docker configuration created at: $DAEMON_JSON"
echo ""
echo "📄 Configuration content:"
cat "$DAEMON_JSON"
echo ""
echo "🔄 Please restart Docker Desktop:"
echo "   1. Quit Docker Desktop completely (Cmd+Q)"
echo "   2. Start Docker Desktop again"
echo "   3. Wait for it to show 'Docker Desktop is running'"
echo ""
echo "After restarting Docker Desktop, run:"
echo "   docker-compose up --build"
echo ""
echo "Or use the startup script:"
echo "   ./start-server.sh"
