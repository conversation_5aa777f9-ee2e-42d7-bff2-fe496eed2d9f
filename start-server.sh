#!/bin/bash

echo "🚀 Starting ODEX Server with Docker..."
echo "📋 This will start:"
echo "   - PostgreSQL database on port 5432"
echo "   - Odoo server on port 8069"
echo ""

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed. Please install Docker Compose."
    exit 1
fi

echo "🔧 Building and starting containers..."
docker-compose up --build

echo ""
echo "🎉 Server should be running at: http://localhost:8069"
echo "📊 Database admin interface: http://localhost:8069/web/database/manager"
echo "🔑 Default admin password: admin"
echo ""
echo "To stop the server, press Ctrl+C or run: docker-compose down"
