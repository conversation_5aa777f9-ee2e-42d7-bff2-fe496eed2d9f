# Running ODEX Server with Docker

This guide will help you run the ODEX (Odoo) server locally using Docker Desktop.

## Prerequisites

1. **Docker Desktop** - Make sure Docker Desktop is installed and running
2. **Docker Compose** - Usually comes with Docker Desktop

## Quick Start

### Option 1: Using the startup script (Recommended)
```bash
./start-server.sh
```

### Option 2: Manual Docker Compose
```bash
# Build and start the containers
docker-compose up --build

# Or run in background
docker-compose up --build -d
```

## What gets started

- **PostgreSQL Database**: Running on port 5432
- **ODEX Server**: Running on port 8069

## Accessing the Application

1. **Main Application**: http://localhost:8069
2. **Database Manager**: http://localhost:8069/web/database/manager

## Default Credentials

- **Admin Password**: `admin`
- **Database User**: `odoo`
- **Database Password**: `odoo`

## Creating Your First Database

1. Go to http://localhost:8069
2. You'll see the database creation page
3. Fill in:
   - **Database Name**: Choose any name (e.g., "mycompany")
   - **Email**: Your email address
   - **Password**: Choose a password for the admin user
   - **Language**: Select your preferred language
   - **Country**: Select your country
4. Click "Create database"

## Stopping the Server

```bash
# If running in foreground, press Ctrl+C
# Or if running in background:
docker-compose down
```

## Troubleshooting

### Port Already in Use
If you get port conflicts:
```bash
# Check what's using the ports
lsof -i :8069
lsof -i :5432

# Stop any conflicting services or change ports in docker-compose.yml
```

### Database Issues
```bash
# Reset the database
docker-compose down
docker volume rm odex-master_odoo-db-data
docker-compose up --build
```

### View Logs
```bash
# View all logs
docker-compose logs

# View specific service logs
docker-compose logs odoo
docker-compose logs db
```

## File Structure

- `docker-compose.yml` - Docker Compose configuration
- `Dockerfile` - Odoo server container definition
- `odoo-server.conf` - Odoo configuration file
- `start-server.sh` - Convenience startup script

## Development

The server code is mounted as a volume, so changes to the Python code will require a container restart:

```bash
docker-compose restart odoo
```
