<?xml version="1.0" encoding="UTF-8"?>

<templates xml:space="preserve">

<div t-name="reconciliation" class="o_reconciliation">
    <div class="o_form_view">
        <div class="o_form_sheet_bg">
            <div class="o_form_sheet"/>
        </div>
    </div>
</div>

<t t-name="reconciliation.statement">
    <div t-if="widget._initialState.valuemax">
        <div>
            <h1 class="statement_name"/>
            <h1 class="statement_name_edition" style="display: none;"><button class="btn btn-primary btn-sm pull-right">OK</button></h1>
            <div class="progress progress-striped">
                <div class="progress-text">
                    <span class="valuenow"><t t-esc="widget._initialState.valuenow"/></span> / <span class="valuemax"><t t-esc="widget._initialState.valuemax"/></span>
                </div>
                <div aria-valuemin="0" t-att-aria-valuenow="widget._initialState.valuenow" class="progress-bar" role="progressbar" style="width: 0%;" t-att-aria-valuemax="widget._initialState.valuemax"></div>
            </div>
            <button class="btn btn-default o_automatic_reconciliation pull-right" title="Let odoo try to reconcile entries for the user">Automatic reconciliation</button>
            <div class="notification_area"/>
        </div>
        <div class="o_reconciliation_lines"/>
        <div t-if="widget._initialState.valuemax &gt; widget._initialState.defaultDisplayQty">
            <button class="btn btn-default js_load_more">Load more</button>
        </div>
        <div class="pull-right text-muted">Tip: Hit CTRL-Enter to reconcile all the balanced items in the sheet.</div>
    </div>
    <div t-else="" class="o_view_nocontent">
        <p>Nothing to do!</p>
        <p>This page displays all the bank transactions that are to be reconciled and provides with a neat interface to do so.</p>
    </div>
</t>

<t t-name="reconciliation.manual.statement" t-extend="reconciliation.statement">
    <t t-jquery="div:first" t-operation="attributes">
        <attribute name="class">o_manual_statement</attribute>
    </t>
    <t t-jquery="h1:last, .progress, .o_automatic_reconciliation" t-operation="replace"></t>
    <t t-jquery=".o_view_nocontent p" t-operation="replace"></t>
    <t t-jquery=".o_view_nocontent" t-operation="append">
        <p><b>Good Job!</b> There is nothing to reconcile.</p>
        <p>All invoices and payments have been matched, your accounts' balances are clean.</p>
    </t>
</t>

<div t-name="reconciliation.done" class="done_message">
    <h2>Congrats, you're all done!</h2>
    <p>You reconciled <strong><t t-esc="number"/></strong> transactions in <strong><t t-esc="duration"/></strong>.<br/>That's on average <t t-esc="timePerTransaction"/> seconds per transaction.</p>
    <t t-if="context &amp;&amp; context.active_model">
        <p t-if="context['active_model'] === 'account.journal' || context['active_model'] === 'account.bank.statement' || context['active_model'] === 'account.bank.statement.import'" class="actions_buttons">
            <t t-if="context.journal_id">
                <button class="button_back_to_statement btn btn-default btn-sm" t-att-data_journal_id='context.journal_id'>Go to bank statement(s)</button>
            </t>
            <t t-if="context['active_model'] === 'account.bank.statement'">
                <button class="button_close_statement btn btn-primary btn-sm" style="display: inline-block;">Close statement</button>
            </t>
        </p>
    </t>
</div>

<t t-name="reconciliation.line">
    <t t-set="state" t-value="widget._initialState"/>
    <div class="o_reconciliation_line" t-att-data-mode="state.mode">
        <table class="accounting_view">
            <caption>
                <div class="pull-right o_buttons">
                    <button t-attf-class="o_validate btn btn-default btn-sm #{!state.balance.type ? '' : 'hidden'}">Validate</button>
                    <button t-attf-class="o_reconcile btn btn-primary btn-sm #{state.balance.type &gt; 0 ? '' : 'hidden'}">Reconcile</button>
                    <span t-attf-class="o_no_valid text-danger #{state.balance.type &lt; 0 ? '' : 'hidden'}">Select a partner or choose a counterpart</span>
                </div>
            </caption>
            <thead>
                <tr>
                    <td class="cell_action"><span class="toggle_match fa fa-cog"></span></td>
                    <td class="cell_account_code"><t t-esc="state.st_line.account_code"/></td>
                    <td class="cell_due_date"><t t-esc="state.st_line.date"/></td>
                    <td class="cell_label"><t t-if="state.st_line.name" t-esc="state.st_line.name"/> <t t-if="state.st_line.amount_currency_str"> (<t t-esc="state.st_line.amount_currency_str"/>)</t></td>
                    <td class="cell_left"><t t-if="state.st_line.amount &gt; 0"><t t-raw="state.st_line.amount_str"/></t></td>
                    <td class="cell_right"><t t-if="state.st_line.amount &lt; 0"><t t-raw="state.st_line.amount_str"/></t></td>
                    <td class="cell_info_popover"></td>
                </tr>
            </thead>
            <tbody>
                <t t-foreach="state.reconciliation_proposition" t-as="line"><t t-call="reconciliation.line.mv_line"/></t>
            </tbody>
            <tfoot>
                <t t-call="reconciliation.line.balance"/>
            </tfoot>
        </table>
        <div class="match">
            <t t-call="reconciliation.line.match"/>
        </div>
        <div class="create"></div>
    </div>
</t>

<t t-name="reconciliation.manual.line" t-extend="reconciliation.line">
    <t t-jquery=".o_buttons" t-operation="replace">
        <div class="pull-right o_buttons">
            <button t-attf-class="o_validate btn btn-default btn-sm #{!state.balance.type ? '' : 'hidden'}">Reconcile</button>
            <button t-attf-class="o_reconcile btn btn-primary btn-sm #{state.balance.type &gt; 0 ? '' : 'hidden'}">Reconcile</button>
            <button t-attf-class="o_no_valid btn btn-default btn-sm #{state.balance.type &lt; 0 ? '' : 'hidden'}">Skip</button>
        </div>
    </t>
    <t t-jquery=".accounting_view tbody" t-operation="append">
        <t t-if='!_.filter(state.reconciliation_proposition, {"display": true}).length'>
            <t t-set="line" t-value='{}'/>
            <t t-call="reconciliation.line.mv_line"/>
        </t>
    </t>
    <t t-jquery=".accounting_view thead tr" t-operation="replace">
        <tr>
            <td class="cell_action"><span class="toggle_match fa fa-cog"></span></td>
            <td colspan="3"><span/><span t-if="state.last_time_entries_checked">Last Reconciliation: <t t-esc="state.last_time_entries_checked"/></span></td>
            <td colspan="2"><t t-esc="state.st_line.account_code"/></td>
            <td class="cell_info_popover"></td>
        </tr>
    </t>
</t>

<t t-name="reconciliation.line.balance">
    <tr t-if="state.balance.amount_currency &amp;&amp; !(state.reconciliation_proposition[0] || {}).partial_reconcile">
        <td class="cell_action"><span class="toggle_create fa fa-play"></span></td>
        <td class="cell_account_code"><t t-esc="state.balance.account_code"/></td>
        <td class="cell_due_date"></td>
        <td class="cell_label"><t t-if="state.st_line.partner_id">Open balance</t><t t-else="">Choose counterpart or Create Write-off</t></td>
        <td class="cell_left"><t t-if="state.balance.amount_currency &lt; 0"><span t-if="state.balance.amount_currency_str" t-attf-class="o_multi_currency o_multi_currency_color_#{state.balance.currency_id%8} line_info_button fa fa-money" t-att-data-content="state.balance.amount_currency_str"/><t t-raw="state.balance.amount_str"/></t></td>
        <td class="cell_right"><t t-if="state.balance.amount_currency &gt; 0"><span t-if="state.balance.amount_currency_str" t-attf-class="o_multi_currency o_multi_currency_color_#{state.balance.currency_id%8} line_info_button fa fa-money" t-att-data-content="state.balance.amount_currency_str"/><t t-raw="state.balance.amount_str"/></t></td>
        <td class="cell_info_popover"></td>
    </tr>
</t>


<div t-name="reconciliation.line.match">
    <div class="match_controls">
        <input class="filter" placeholder="Filter..." type="text" value=""/>
        <span class="pull-right fa fa-chevron-right disabled"></span>
        <span class="pull-right fa fa-chevron-left disabled"></span>
    </div>
    <table>
        <tbody>
        </tbody>
    </table>
</div>


<div t-name="reconciliation.line.create">
    <div class="quick_add">
        <div class="btn-group btn-group-sm o_reconcile_models" t-if="state.reconcileModels">
            <t t-foreach="state.reconcileModels" t-as="reconcileModel">
                <button class="btn btn-primary" t-att-data-reconcile-model-id="reconcileModel.id"><t t-esc="reconcileModel.name"/></button>
            </t>
            <p t-if="!state.reconcileModels.length" style="color: #bbb;">You did not configure any reconcile model yet, you can do it <a style="cursor: pointer;" class="reconcile_model_create">there</a>.</p>
        </div>
        <div class="dropdown pull-right">
            <a data-toggle="dropdown" href="#"><span class="fa fa-cog"/></a>
            <ul class="dropdown-menu dropdown-menu-right" role="menu" aria-labelledby="Presets config">
                <li><a class="reconcile_model_create" href="#">Create model</a></li>
                <li><a class="reconcile_model_edit" href="#">Modify models</a></li>
            </ul>
        </div>
    </div>
    <table class="pull-left">
        <tr class="create_account_id">
            <td class="o_td_label">Account</td>
            <td class="o_td_field"></td>
        </tr>
        <tr class="create_tax_id">
            <td class="o_td_label">Tax</td>
            <td class="o_td_field"></td>
        </tr>
        <tr class="create_analytic_account_id">
            <td class="o_td_label">Analytic Acc.</td>
            <td class="o_td_field"></td>
        </tr>
    </table>
    <table class="pull-right">
        <tbody>
            <tr class="create_journal_id" style="display: none;">
                <td class="o_td_label">Journal</td>
                <td class="o_td_field"></td>
            </tr>
            <tr class="create_label">
                <td class="o_td_label">Label</td>
                <td class="o_td_field"></td>
            </tr>
            <tr class="create_amount">
                <td class="o_td_label">Amount</td>
                <td class="o_td_field"></td>
            </tr>
        </tbody>
    </table>
    <div class="add_line_container">
        <a class="add_line" t-att-style="!state.balance.amout ? 'display: none;' : null"><i class="fa fa-plus-circle"/> Save and New</a>
    </div>
</div>


<t t-name="reconciliation.line.mv_line">
    <tr t-if="line.display !== false" t-attf-class="mv_line #{line.already_paid ? ' already_reconciled' : ''} #{line.__invalid ? 'invalid' : ''} #{line.is_tax ? 'is_tax' : ''}" t-att-data-line-id="line.id" t-att-data-selected="selected">
        <td class="cell_action"><span class="fa fa-add-remove"/></td>
        <td class="cell_account_code"><t t-esc="line.account_code"/></td>
        <td class="cell_due_date"><t t-esc="line.date_maturity === false ? line.date : line.date_maturity"/></td>
        <td class="cell_label">
            <t t-if="line.partner_id &amp;&amp; line.partner_id !== state.st_line.partner_id">
                <t t-if="line.partner_name.length">
                    <t t-esc="line.partner_name"/>: 
                </t>   
            </t>
            <t t-esc="line.label || line.name"/>
            <t t-if="line.ref &amp;&amp; line.ref.length"> : </t>
            <t t-esc="line.ref"/>
        </td>
        <td class="cell_left"><t t-if="line.amount &lt; 0"><span t-if="line.amount_currency_str" t-attf-class="o_multi_currency o_multi_currency_color_#{line.currency_id%8} line_info_button fa fa-money" t-att-data-content="line.amount_currency_str"/><t t-raw="line.amount_str"/></t></td>
        <td class="cell_right"><t t-if="line.amount &gt; 0"><span t-if="line.amount_currency_str" t-attf-class="o_multi_currency o_multi_currency_color_#{line.currency_id%8} line_info_button fa fa-money" t-att-data-content="line.amount_currency_str"/><t t-raw="line.amount_str"/></t></td>
        <td class="cell_info_popover"></td>
    </tr>
</t>


<t t-name="reconciliation.line.mv_line.details">
    <table class='details'>
        <tr t-if="line.account_code"><td>Account</td><td><t t-esc="line.account_code"/> <t t-esc="line.account_name"/></td></tr>
        <tr><td>Journal</td><td><t t-esc="line.journal_id[1]"/></td></tr>
        <tr><td>Label</td><td><t t-esc="line.label"/></td></tr>
        <tr t-if="line.ref"><td>Ref</td><td><t t-esc="line.ref"/></td></tr>
        <tr t-if="line.partner_id"><td>Partner</td><td><t t-esc="line.partner_name"/></td></tr>
        <tr><td>Date</td><td><t t-esc="line.date"/></td></tr>
        <tr><td>Due Date</td><td><t t-esc="line.date_maturity === false ? line.date : line.date_maturity"/></td></tr>
        <tr><td>Amount</td><td><t t-raw="line.total_amount_str"/><t t-if="line.total_amount_currency_str"> (<t t-esc="line.total_amount_currency_str"/>)</t></td></tr>
        <tr t-if="line.is_partially_reconciled"><td>Residual</td><td>
            <t t-raw="line.amount_str"/><t t-if="line.amount_currency_str"> (<t t-esc="line.amount_currency_str"/>)</t>
        </td></tr>
        <tr class="one_line_info" t-if='line.already_paid'>
            <td colspan="2">This payment is registered but not reconciled.</td>
        </tr>
    </table>
</t>


<t t-name="reconciliation.line.statement_line.details">
    <table class='details'>
        <tr><td>Date</td><td><t t-esc="state.st_line.date"/></td></tr>
        <tr t-if="state.st_line.partner_name"><td>Partner</td><td><t t-esc="state.st_line.partner_name"/></td></tr>
        <tr t-if="state.st_line.ref"><td>Transaction</td><td><t t-esc="state.st_line.ref"/></td></tr>
        <tr><td>Description</td><td><t t-esc="state.st_line.name"/></td></tr>
        <tr><td>Amount</td><td><t t-raw="state.st_line.amount_str"/><t t-if="state.st_line.amount_currency_str"> (<t t-esc="state.st_line.amount_currency_str"/>)</t></td></tr>
        <tr><td>Account</td><td><t t-esc="state.st_line.account_code"/> <t t-esc="state.st_line.account_name"/></td></tr>
        <tr t-if="state.st_line.note"><td>Note</td><td style="white-space: pre;"><t t-esc="state.st_line.note"/></td></tr>
    </table>
</t>

<t t-name="reconciliation.notification">
    <div t-att-class="'notification alert-dismissible alert alert-' + type" role="alert">
        <button type="button" class="close" data-dismiss="alert"><span aria-hidden="true" class="fa fa-times"></span><span class="sr-only">Close</span></button>
        <t t-esc="message" />
        <t t-if="details !== undefined">
            <a class="fa fa-external-link" rel="do_action" href="#"
                t-att-data-action_name="details.name"
                t-att-data-model="details.model"
                t-att-data-ids="details.ids">
            </a>
        </t>
    </div>
</t>

</templates>
