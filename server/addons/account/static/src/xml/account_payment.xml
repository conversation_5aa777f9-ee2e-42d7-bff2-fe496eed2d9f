<?xml version="1.0" encoding="UTF-8"?>

<templates xml:space="preserve">

    <t t-name="ShowPaymentInfo">
        <div>
            <t t-if="outstanding">
                <div>
                    <strong class="pull-left" id="outstanding"><t t-esc="title"></t></strong>
                </div>
            </t>
            <table style="width:100%;">
                <t t-foreach="lines" t-as="line">
                    <tr>
                    <t t-if="outstanding">
                        <td>
                            <a title="assign to invoice" role="button" class="oe_form_field outstanding_credit_assign" t-att-data-id="line.id" style="margin-right: 10px;">Add</a>
                        </td>
                        <td style="max-width: 10em;">
                            <div class="oe_form_field" style="margin-right: 30px; text-overflow: ellipsis; overflow: hidden; white-space: nowrap;" t-att-title="line.title"><t t-esc="line.journal_name"></t></div>
                        </td>
                    </t>
                    <t t-if="!outstanding">
                        <td>
                            <a role="button" tabindex="0" class="js_payment_info fa fa-info-circle" t-att-index="line.index" style="margin-right:5px;"></a>
                        </td>
                        <td>
                            <i class="o_field_widget text-right o_payment_label">Paid on <t t-esc="line.date"></t></i>
                        </td>
                    </t>
                        <td style="text-align:right;">
                            <span class="oe_form_field oe_form_field_float oe_form_field_monetary" style="margin-left: -10px;">
                                <t t-if="line.position === 'before'">
                                    <t t-esc="line.currency"/>
                                </t>
                                <t t-esc="line.amount"></t> 
                                <t t-if="line.position === 'after'">
                                    <t t-esc="line.currency"/>
                                </t>
                            </span>
                        </td>
                    </tr>
                </t>
            </table>
        </div>
    </t>

    <t t-name="PaymentPopOver">
        <div>
            <table>
                <tr>
                    <td><strong>Name: </strong></td>
                    <td style="text-align:right;"><t t-esc="name"/></td>
                </tr>
                <tr>
                    <td><strong>Date: </strong></td>
                    <td style="text-align:right;"><t t-esc="date"/></td>
                </tr>
                <tr>
                    <td><strong>Payment Method: </strong></td>
                    <td style="text-align:right;"><t t-esc="journal_name"/></td>
                </tr>
                <tr>
                    <td><strong>Memo: </strong></td>
                    <td style="text-align:right;"><t t-esc="ref"/></td>
                </tr>
                <tr>
                    <td><strong>Amount: </strong></td>
                    <td style="text-align:right;">
                        <t t-if="position === 'before'">
                            <t t-esc="currency"/>
                        </t>
                        <t t-esc="amount"></t> 
                        <t t-if="position === 'after'">
                            <t t-esc="currency"/>
                        </t>
                    </td>
                </tr>
            </table>
        </div>
        <button class="btn btn-xs btn-primary js_unreconcile_payment pull-left" t-att-payment-id="payment_id" style="margin-top:5px; margin-bottom:5px;">Unreconcile</button>
        <button class="btn btn-xs btn-default js_open_payment pull-right" t-att-payment-id="account_payment_id" t-att-invoice-id="invoice_id" t-att-move-id="move_id" style="margin-top:5px; margin-bottom:5px;">Open</button>
    </t>

</templates>
