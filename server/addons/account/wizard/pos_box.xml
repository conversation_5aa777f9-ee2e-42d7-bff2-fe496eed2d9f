<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <record model="ir.ui.view" id="cash_box_in_form">
            <field name="name">cash_box_in</field>
            <field name="model">cash.box.in</field>
            <field name="arch" type="xml">
                <form string="Put Money In">
                    <separator string="Fill in this form if you put money in the cash register:" colspan="4" />
                    <group>
                        <field name="name" class="oe_inline"/>
                        <field name="amount" class="oe_inline"/>
                    </group>
                    <footer>
                        <button name="run" string="Put Money In" type="object" class="btn-primary"/>
                        <button special="cancel" string="Cancel" class="btn-default"/>
                    </footer>
                </form>
            </field>
        </record>

        <act_window 
            name="Put Money In"
            res_model="cash.box.in"
            src_model="account.bank.statement"
            view_mode="form"
            target="new"
            key2="client_action_multi"
            id="action_cash_box_in" />

        <record model="ir.ui.view" id="cash_box_out_form">
            <field name="name">cash_box_out</field>
            <field name="model">cash.box.out</field>
            <field name="arch" type="xml">
                <form string="Take Money Out">
                    <separator string="Describe why you take money from the cash register:"/>
                    <group>
                        <field name="name" class="oe_inline"/>
                        <field name="amount" class="oe_inline"/>
                    </group>

                    <footer>
                        <button name="run" string="Take Money Out" type="object" class="btn-primary"/>
                        <button class="btn-default" special="cancel" string="Cancel" />
                    </footer>
                </form>
            </field>
        </record>

        <act_window 
            name="Take Money Out"
            res_model="cash.box.out"
            src_model="account.bank.statement"
            view_mode="form"
            target="new"
            key2="client_action_multi"
            id="action_cash_box_out" />
    </data>
</odoo>
