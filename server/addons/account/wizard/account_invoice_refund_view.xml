<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_account_invoice_refund" model="ir.ui.view">
            <field name="name">account.invoice.refund.form</field>
            <field name="model">account.invoice.refund</field>
            <field name="arch" type="xml">
                <form string="Credit Note">
                    <group>
                         <group>
                             <field name="refund_only" invisible="1"/>
                             <field name="filter_refund" attrs="{'invisible': [('refund_only','=',True)]}" widget="radio"/>
                         </group>
                         <group>
                             <div attrs="{'invisible':['|',('refund_only','=',True),('filter_refund','!=','refund')]}" class="oe_grey" colspan="4">
                                You will be able to edit and validate this
                                credit note directly or keep it draft,
                                waiting for the document to be issued by
                                your supplier/customer.
                             </div>
                             <div attrs="{'invisible':['|',('refund_only','=',True),('filter_refund','!=','cancel')]}" class="oe_grey" colspan="4">
                                Use this option if you want to cancel an invoice you should not
                                have issued. The credit note will be created, validated and reconciled
                                with the invoice. You will not be able to modify the credit note.
                             </div>
                             <div attrs="{'invisible':['|',('refund_only','=',True),('filter_refund','!=','modify')]}" class="oe_grey" colspan="4">
                                Use this option if you want to cancel an invoice and create a new
                                one. The credit note will be created, validated and reconciled
                                with the current invoice. A new draft invoice will be created
                                so that you can edit it.
                             </div>
                         </group>
                         <group>
                             <field name="description"/>
                         </group>
                         <group>
                             <field name="date_invoice"/>
                             <field name="date" groups="base.group_no_one"/>
                         </group>
                    </group>
                    <footer>
                        <button string='Add Credit Note' name="invoice_refund" type="object" class="btn-primary"/>
                        <button string="Cancel" class="btn-default" special="cancel"/>
                    </footer>
               </form>
            </field>
        </record>

        <record id="action_account_invoice_refund" model="ir.actions.act_window">
            <field name="name">Credit Note</field>
            <field name="res_model">account.invoice.refund</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form</field>
           <field name="view_id" ref="view_account_invoice_refund"/>
           <field name="target">new</field>
        </record>


    </data>
</odoo>
