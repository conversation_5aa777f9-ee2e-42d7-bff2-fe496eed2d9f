<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="tax_adjustments_wizard" model="ir.ui.view">
        <field name="name">tax.adjustments.wizard.form</field>
        <field name="model">tax.adjustments.wizard</field>
        <field name="arch" type="xml">
        <form>
            <h1>
                <field name="reason" class="oe_inline" placeholder="Reason..."/>
            </h1>
            <group>
                <group>
                    <field name="amount"/>
                    <field name="adjustment_type"/>
                </group>
                <group>
                    <field name="tax_id" widget="selection"/>
                </group>
                <group string="Accounts">
                    <field name="debit_account_id"/>
                    <field name="credit_account_id"/>
                </group>
                <group string="Options">
                    <field name="journal_id"/>
                    <field name="date"/>
                </group>
            </group>
            <footer>
              <div attrs="{'invisible': [('adjustment_type', '=', 'credit')]}">
                <button name="create_move_debit" string="Create and post move" type="object" default_focus="1" class="oe_highlight"/>
                <button string="Cancel" class="btn btn-default" special="cancel" />
              </div>
              <div attrs="{'invisible': [('adjustment_type', '!=', 'credit')]}">
                <button name="create_move_credit" string="Create and post move" type="object" default_focus="1" class="oe_highlight"/>
                <button string="Cancel" class="btn btn-default" special="cancel" />
              </div>
            </footer>
        </form>
        </field>
    </record>
</odoo>
