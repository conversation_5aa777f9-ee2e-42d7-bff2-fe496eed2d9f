<odoo>
    <data>

        <record id="account_invoice_confirm_view" model="ir.ui.view">
            <field name="name">account.invoice.confirm.form</field>
            <field name="model">account.invoice.confirm</field>
            <field name="arch" type="xml">
                <form string="Confirm Draft Invoices">
                    <p class="oe_grey">
                        Once draft invoices are confirmed, you will not be able
                        to modify them. The invoices will receive a unique
                        number and journal items will be created in your chart
                        of accounts.
                    </p>
                    <footer>
                        <button string="Confirm Invoices" name="invoice_confirm" type="object" default_focus="1" class="btn-primary"/>
                        <button string="Cancel" class="btn-default" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <act_window id="action_account_invoice_confirm"
            multi="True"
            key2="client_action_multi" name="Confirm Draft Invoices"
            res_model="account.invoice.confirm" src_model="account.invoice"
            view_mode="form" target="new" view_type="form" />

    </data>
</odoo>
