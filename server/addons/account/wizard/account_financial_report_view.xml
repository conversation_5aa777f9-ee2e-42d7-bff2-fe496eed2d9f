<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="accounting_report_view" model="ir.ui.view">
        <field name="name">Accounting Report</field>
        <field name="model">accounting.report</field>
        <field name="inherit_id" ref="account.account_common_report_view"/>
        <field name="arch" type="xml">
            <field name="target_move" position="before">
                <field name="account_report_id" domain="[('parent_id','=',False)]"/>
            </field>
            <field name="target_move" position="after">
                <field name="enable_filter"/>
                <field name="debit_credit" attrs="{'invisible': [('enable_filter','=',True)]}"/>
            </field>
            <field name="journal_ids" position="after">
                <notebook tabpos="up" colspan="4">
                    <page string="Comparison" name="comparison" attrs="{'invisible': [('enable_filter','=',False)]}">
                        <group>
                            <field name="label_filter" attrs="{'required': [('enable_filter', '=', True)]}"/>
                            <field name="filter_cmp"/>
                        </group>
                        <group string="Dates" attrs="{'invisible':[('filter_cmp', '!=', 'filter_date')]}">
                            <field name="date_from_cmp" attrs="{'required':[('filter_cmp', '=', 'filter_date')]}"/>
                            <field name="date_to_cmp" attrs="{'required':[('filter_cmp', '=', 'filter_date')]}"/>
                        </group>
                    </page>
                </notebook>
            </field>
            <field name="journal_ids" position="replace"/>
        </field>
    </record>

    <record id="action_account_report_bs" model="ir.actions.act_window">
        <field name="name">Balance Sheet</field>
        <field name="res_model">accounting.report</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_type">form</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="accounting_report_view"/>
        <field name="target">new</field>
        <field name="context" eval="{'default_account_report_id':ref('account.account_financial_report_balancesheet0')}"/>
    </record>

    <menuitem id="menu_account_report_bs" name="Balance Sheet" action="action_account_report_bs" parent="menu_finance_legal_statement" groups="account.group_account_user"/>

    <record id="action_account_report_pl" model="ir.actions.act_window">
        <field name="name">Profit and Loss</field>
        <field name="res_model">accounting.report</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_type">form</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="accounting_report_view"/>
        <field name="target">new</field>
        <field name="context" eval="{'default_account_report_id':ref('account.account_financial_report_profitandloss0')}"/>
    </record>

    <menuitem id="menu_account_report_pl" name="Profit and Loss" action="action_account_report_pl" parent="menu_finance_legal_statement" groups="account.group_account_user"/>

    <record id="action_account_report" model="ir.actions.act_window">
        <field name="name">Financial Reports</field>
        <field name="res_model">accounting.report</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_type">form</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="accounting_report_view"/>
        <field name="target">new</field>
    </record>

    <menuitem id="menu_account_report" name="Financial Report" action="action_account_report" parent="menu_finance_legal_statement" sequence="100" groups="account.group_account_user"/>

</odoo>
