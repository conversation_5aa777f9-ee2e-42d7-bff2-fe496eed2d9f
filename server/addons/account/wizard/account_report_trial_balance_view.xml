<?xml version="1.0" encoding="utf-8"?>

<odoo>

    <record id="account_report_balance_view" model="ir.ui.view">
        <field name="name">Trial Balance</field>
        <field name="model">account.balance.report</field>
        <field name="inherit_id" ref="account_common_report_view"/>
        <field name="arch" type="xml">
            <data>
            <field name="journal_ids" position="replace"/>
                <xpath expr="//field[@name='target_move']" position="after">
                    <field name="display_account" widget="radio"/>
                    <newline/>
                </xpath>
            </data>
        </field>
    </record>

    <record id="action_account_balance_menu" model="ir.actions.act_window">
        <field name="name">Trial Balance</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">account.balance.report</field>
        <field name="view_type">form</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="account_report_balance_view"/>
        <field name="target">new</field>
        <field name="binding_model_id" ref="account.model_account_account" />
        <field name="binding_type">report</field>
    </record>

    <menuitem id="menu_general_Balance_report"
        name="Trial Balance"
        parent="account.menu_finance_legal_statement"
        action="action_account_balance_menu"
        groups="account.group_account_user"/>

</odoo>
