<?xml version="1.0" encoding="UTF-8"?>

<odoo>
    <data>

        <record id="setup_view_company_form" model="ir.ui.view">
            <field name="name">account.setup.company.form</field>
            <field name="model">res.company</field>
            <field name="inherit_id" ref="base.view_company_form"/>
            <field name="mode">primary</field>
            <field name="priority">42000</field>
            <field name="arch" type="xml">
                <xpath expr="//form/sheet" position="after">
                    <footer>
                        <field name="account_setup_company_data_done" invisible="True"/>
                        <button string="Mark as done" type="object" name="mark_company_setup_as_done_action" class="oe_highlight" attrs="{'invisible': [('account_setup_company_data_done','=',True)]}"/>
                        <button string="Save" type="action" name="account.setup_wizard_close" attrs="{'invisible': [('account_setup_company_data_done','=',True)]}"/>
                        <button string="Save" type="action" name="account.setup_wizard_close" class="oe_highlight" attrs="{'invisible': [('account_setup_company_data_done','=',False)]}"/>
                        <button string="Unmark as done" type="object" name="unmark_company_setup_as_done_action" attrs="{'invisible': [('account_setup_company_data_done','=',False)]}"/>
                    </footer>
                </xpath>
            </field>
        </record>

        <record id="setup_financial_year_opening_form" model="ir.ui.view">
            <field name="name">account.financial.year.op.setup.wizard.form</field>
            <field name="model">account.financial.year.op</field>
            <field name="arch" type="xml">
                <form>
                    <div class="row">
                        <div class="col-xs-4">
                            <label for="opening_date"/>
                        </div>
                        <div class="col-xs-8">
                            <field name="opening_move_posted" invisible="1"/>
                            <field name="opening_date" attrs="{'readonly': [('opening_move_posted', '=', True)]}"/>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-xs-4">
                            <strong>Fiscal Year End</strong>
                        </div>
                        <div class="col-xs-4">
                            <field name="fiscalyear_last_day"/>
                        </div>
                        <div class="col-xs-4">
                            <field name="fiscalyear_last_month"/>
                        </div>
                    </div>
                    <footer>
                        <field name="account_setup_fy_data_done" invisible="True"/>
                        <button string="Mark as done" type="object" name="mark_as_done" class="oe_highlight" attrs="{'invisible': [('account_setup_fy_data_done','=',True)]}"/>
                        <button string="Save" type="action" name="account.setup_wizard_close" attrs="{'invisible': [('account_setup_fy_data_done','=',True)]}"/>
                        <button string="Save" type="action" name="account.setup_wizard_close" class="oe_highlight" attrs="{'invisible': [('account_setup_fy_data_done','=',False)]}"/>
                        <button string="Unmark as done" type="object" name="unmark_as_done" attrs="{'invisible': [('account_setup_fy_data_done','=',False)]}"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="setup_bank_journal_form" model="ir.ui.view">
            <field name="name">account.setup.bank.journal.form</field>
            <field name="model">account.journal</field>
            <field name="inherit_id" ref="account.view_account_bank_journal_form"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="//form/sheet" position="inside">
                    <button type="object" name="setup_save_journal_and_create_more">You can define additional accounts here</button>
                </xpath>
                <xpath expr="//div[@name='advanced_info']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//form/sheet" position="after">
                    <footer>
                        <field name="account_setup_bank_data_done" invisible="True"/>
                        <button string="Mark as done" type="object" name="mark_bank_setup_as_done_action" class="oe_highlight" attrs="{'invisible': [('account_setup_bank_data_done','=',True)]}"/>
                        <button string="Save" type="action" name="account.setup_wizard_close" attrs="{'invisible': [('account_setup_bank_data_done','=',True)]}"/>
                        <button string="Save" type="action" name="account.setup_wizard_close" class="oe_highlight" attrs="{'invisible': [('account_setup_bank_data_done','=',False)]}"/>
                        <button string="Unmark as done" type="object" name="unmark_bank_setup_as_done_action" attrs="{'invisible': [('account_setup_bank_data_done','=',False)]}"/>
                    </footer>
                </xpath>
            </field>
        </record>

        <record id="setup_opening_move_wizard_form" model="ir.ui.view">
            <field name="name">account.setup.opening.move.wizard.form</field>
            <field name="model">account.opening</field>
            <field name="arch" type="xml">
                <form>
                    <group>
                        <group>
                            <field name="journal_id"/>
                        </group>
                        <group>
                            <field name="date"/>
                        </group>
                    </group>

                    <field name="company_id" invisible="1"/>
                    <field name="opening_move_line_ids" context="{'tree_view_ref': 'account.setup_opening_move_lines_tree'}"/>

                    <footer>
                        <button string="Save" type="action" name="account.setup_wizard_close"/>
                        <button string="Discard" special="cancel"/>
                        <button string="Post" name="validate" type="object" class="oe_highlight"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="setup_posted_move_form" model="ir.ui.view">
            <field name="name">account.setup.posted.move.form</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="//form/sheet" position="after">
                    <footer>
                        <button string="Save" type="action" class="oe_highlight" name="account.setup_wizard_close"/>
                    </footer>
                </xpath>
            </field>
        </record>

        <record id="setup_opening_move_lines_tree" model="ir.ui.view">
            <field name="name">account.setup.opening.move.lines.tree</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <tree editable="bottom" string="Journal Items" decoration-info="is_unaffected_earnings_line == True">
                    <field name="account_id" domain="[('company_id', '=', parent.company_id), ('deprecated', '=', False)]"/>
                    <field name="partner_id"/>
                    <field name="name"/>
                    <field name="amount_currency" groups="base.group_multi_currency"/>
                    <field name="currency_id" options="{'no_create': True}" groups="base.group_multi_currency"/>
                    <field name="debit" sum="Total Debit"/>
                    <field name="credit" sum="Total Credit"/>
                    <field name="company_id" invisible="True"/>
                    <field name="is_unaffected_earnings_line" invisible="True"/>
                </tree>
            </field>
        </record>

        <record id="init_accounts_tree" model="ir.ui.view">
            <field name="name">account.setup.opening.move.line.tree</field>
            <field name="model">account.account</field>
            <field name="arch" type="xml">
                <tree editable="top" create="1" delete="1">
                    <field name="code"/>
                    <field name="name"/>
                    <field name="user_type_id"/>
                    <field name="opening_debit"/>
                    <field name="opening_credit"/>
                </tree>
            </field>
        </record>

        <record id="setup_wizard_close" model="ir.actions.act_window_close">
            <field name="name">Close setup wizards</field>
        </record>

    </data>
</odoo>
