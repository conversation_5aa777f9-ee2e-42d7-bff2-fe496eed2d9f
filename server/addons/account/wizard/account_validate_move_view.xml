<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!--Account Move lines-->
        <record id="validate_account_move_view" model="ir.ui.view">
            <field name="name">Post Journal Entries</field>
            <field name="model">validate.account.move</field>
            <field name="arch" type="xml">
                <form string="Post Journal Entries">
                    <label string="All selected journal entries will be validated and posted. You won't be able to modify them afterwards."/>
                    <footer>
                        <button string="Post Journal Entries" name="validate_move" type="object" default_focus="1" class="btn-primary"/>
                        <button string="Cancel" class="btn-default" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_validate_account_move" model="ir.actions.act_window">
            <field name="name">Post Journal Entries</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">validate.account.move</field>
            <field name="view_type">form</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="validate_account_move_view"/>
            <field name="context">{}</field>
            <field name="target">new</field>
            <field name="help">This wizard will validate all journal entries selected. Once journal entries are validated, you can not update them anymore.</field>
            <field name="binding_model_id" ref="account.model_account_move" />
        </record>

    </data>
</odoo>
