<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!--Email template -->
        <record id="mail_template_data_payment_receipt" model="mail.template">
            <field name="name">Payment Receipt - Send by Email</field>
            <field name="subject">${object.company_id.name} Payment Receipt (Ref ${object.name or 'n/a' })</field>
            <field name="partner_to">${object.partner_id.id}</field>
            <field name="model_id" ref="account.model_account_payment"/>
            <field name="auto_delete" eval="True"/>
            <field name="report_template" ref="account.action_report_payment_receipt"/>
            <field name="report_name">${(object.name or '').replace('/','-')}</field>
            <field name="lang">${object.partner_id.lang}</field>
            <field name="body_html" type="xml">
<p>Dear ${object.partner_id.name},</p>
<p>Thank you for your payment.<br />Here is your payment receipt <strong>${(object.name or '').replace('/','-')}</strong> amounting to <strong>${format_amount(object.amount, object.currency_id)}</strong> from ${object.company_id.name}.</p>
<p>If you have any questions, please do not hesitate to contact us.</p>
<p>Best regards,
% if user and user.signature:
${user.signature | safe}
% endif
</p>
</field>
        </record>

        <act_window name="Send Receipt By Email"
            res_model="mail.compose.message"
            src_model="account.payment"
            view_mode="form"
            target="new"
            key2="client_action_relate"
            id="account_send_payment_receipt_by_email_action"
            context="{
                'default_model': 'account.payment',
                'mail_post_autofollow': True,
                'default_composition_mode': 'comment',
                'default_use_template': True,
                'default_res_id': active_id,
                'default_template_id': ref('account.mail_template_data_payment_receipt'),
                'force_email': True,
            }"
        />
    </data>
</odoo>
