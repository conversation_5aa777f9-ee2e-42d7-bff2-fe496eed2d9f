<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
    <record id="view_account_invoice_report_pivot" model="ir.ui.view">
         <field name="name">account.invoice.report.pivot</field>
         <field name="model">account.invoice.report</field>
         <field name="arch" type="xml">
             <pivot string="Invoices Analysis" disable_linking="True">
                 <field name="categ_id" type="row"/>
                 <field name="date" type="col"/>
                 <field name="price_total" type="measure"/>
             </pivot>
         </field>
    </record>

    <record id="view_account_invoice_report_graph" model="ir.ui.view">
         <field name="name">account.invoice.report.graph</field>
         <field name="model">account.invoice.report</field>
         <field name="arch" type="xml">
             <graph string="Invoices Analysis">
                 <field name="categ_id"/>
                 <field name="price_total" type="measure"/>
             </graph>
         </field>
    </record>

    <!-- Custom reports (aka filters) -->
    <record id="filter_invoice_salespersons" model="ir.filters">
        <field name="name">By Salespersons</field>
        <field name="model_id">account.invoice.report</field>
        <field name="domain">[]</field>
        <field name="user_id" eval="False"/>
        <field name="context">{'group_by': ['date:month', 'user_id']}</field>
    </record>
    <record id="filter_invoice_product" model="ir.filters">
        <field name="name">By Product</field>
        <field name="model_id">account.invoice.report</field>
        <field name="domain">[]</field>
        <field name="user_id" eval="False"/>
        <field name="context">{'group_by': ['date:month', 'product_id'], 'set_visible':True, 'residual_invisible':True}</field>
    </record>
    <record id="filter_invoice_product_category" model="ir.filters">
        <field name="name">By Product Category</field>
        <field name="model_id">account.invoice.report</field>
        <field name="domain">[]</field>
        <field name="user_id" eval="False"/>
        <field name="context">{'group_by': ['date:month', 'categ_id'], 'residual_invisible':True}</field>
    </record>
    <record id="filter_invoice_refund" model="ir.filters">
        <field name="name">By Credit Note</field>
        <field name="model_id">account.invoice.report</field>
        <field name="domain">[('type', '=', 'out_refund')]</field>
        <field name="user_id" eval="False"/>
        <field name="context">{'group_by': ['date:month', 'user_id']}</field>
    </record>
    <record id="filter_invoice_country" model="ir.filters">
        <field name="name">By Country</field>
        <field name="model_id">account.invoice.report</field>
        <field name="domain">[]</field>
        <field name="user_id" eval="False"/>
        <field name="context">{'group_by': ['date:month', 'country_id']}</field>
    </record>

    <record id="view_account_invoice_report_search" model="ir.ui.view">
        <field name="name">account.invoice.report.search</field>
        <field name="model">account.invoice.report</field>
        <field name="arch" type="xml">
            <search string="Invoices Analysis">
                <filter string="My Invoices" name="my_invoice" domain="[('user_id', '=', uid)]"/>
                <separator/>
                <field name="date"/>
                <filter string="This Year" name="thisyear" domain="['|', ('date', '=', False), '&amp;',('date','&lt;=', time.strftime('%%Y-12-31')),('date','&gt;=',time.strftime('%%Y-01-01'))]" help="Journal invoices with period in current year"/>
                <separator/>
                <filter string="To Invoice" domain="[('state','=','draft')]" help = "Draft Invoices"/>
                <filter string="Invoiced" name="current" domain="[('state','not in', ('draft','cancel'))]"/>
                <separator/>
                <filter string="This Month" name="this_month" domain="[('date', '&gt;=', context_today().strftime('%%Y-%%m-01'))]"/>
                <filter string="Last Month" name="last_month" domain="['&amp;', ('date', '&gt;=', (context_today() - relativedelta(months=1)).strftime('%%Y-%%m-01')), ('date', '&lt;', context_today().strftime('%%Y-%%m-01'))]"/>
                <separator/>
                <filter string="Customer" name="customer" domain="['|', ('type','=','out_invoice'),('type','=','out_refund')]"/>
                <filter string="Vendor" name="supplier" domain="['|', ('type','=','in_invoice'),('type','=','in_refund')]"/>
                <separator/>
                <filter string="Invoice" domain="['|', ('type','=','out_invoice'),('type','=','in_invoice')]"/>
                <filter string="Credit Note" domain="['|', ('type','=','out_refund'),('type','=','in_refund')]"/>
                <field name="partner_id" operator="child_of"/>
                <field name="user_id" />
                <field name="categ_id" filter_domain="[('categ_id', 'child_of', self)]"/>
                <field name="account_analytic_id"/>
                <group expand="1" string="Group By">
                    <filter string="Partner" name="partner_id" context="{'group_by':'partner_id','residual_visible':True}"/>
                    <filter string="Salesperson" name='user' context="{'group_by':'user_id'}"/>
                    <filter string="Product Category" name="category_product" context="{'group_by':'categ_id','residual_invisible':True}"/>
                    <filter string="Status" context="{'group_by':'state'}"/>
                    <filter string="Company" context="{'group_by':'company_id'}" groups="base.group_multi_company"/>
                    <separator orientation="vertical" />
                    <filter string="Date" context="{'group_by':'date'}"/>
                    <filter string="Due Month" context="{'group_by':'date_due:month'}"/>
                    <filter string="Analytic Account" name="account_analytic_id" context="{'group_by':'account_analytic_id'}" groups="analytic.group_analytic_accounting" />
                </group>
            </search>
        </field>
    </record>

    <record id="action_account_invoice_report_all_supp" model="ir.actions.act_window">
        <field name="name">Invoices Analysis</field>
        <field name="res_model">account.invoice.report</field>
        <field name="view_type">form</field>
        <field name="view_mode">graph,pivot</field>
        <field name="context">{'search_default_current':1, 'search_default_supplier':1, 'group_by':[], 'group_by_no_leaf':1, 'search_default_year': 1}</field>
        <field name="search_view_id" ref="view_account_invoice_report_search"/>
        <field name="help">From this report, you can have an overview of the amount invoiced from your vendors. The search tool can also be used to personalise your Invoices reports and so, match this analysis to your needs.</field>
    </record>
    <record id="action_account_invoice_report_all" model="ir.actions.act_window">
        <field name="name">Invoices Analysis</field>
        <field name="res_model">account.invoice.report</field>
        <field name="view_type">form</field>
        <field name="view_mode">graph,pivot</field>
        <field name="context">{'search_default_current':1, 'search_default_customer':1, 'group_by':[], 'group_by_no_leaf':1, 'search_default_year': 1}</field>
        <field name="search_view_id" ref="view_account_invoice_report_search"/>
        <field name="help">From this report, you can have an overview of the amount invoiced to your customers. The search tool can also be used to personalise your Invoices reports and so, match this analysis to your needs.</field>
    </record>

    <menuitem name="Invoices" action="action_account_invoice_report_all" id="menu_action_account_invoice_report_all" parent="account.account_reports_management_menu" sequence="18"/>

    <act_window
        id="act_account_invoice_partner_relation"
        name="Monthly Turnover"
        groups="account.group_account_manager"
        context="{'search_default_partner_id':[active_id], 'search_default_month':1,'search_default_user':1,'group_by_no_leaf':1,'group_by':[]}"
        res_model="account.invoice.report"
        src_model="res.partner"
        view_mode="graph"/>

</data>
</odoo>
