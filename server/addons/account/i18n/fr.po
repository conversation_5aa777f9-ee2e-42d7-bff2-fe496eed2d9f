# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <AUTHOR> <EMAIL>, 2017
# <PERSON>TOUR <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON>, 2017
# <AUTHOR> <EMAIL>, 2017
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <AUTHOR> <EMAIL>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# Fréd<PERSON><PERSON>IETART <<EMAIL>>, 2017
# Saad <PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> (ACSONE), 2017
# <AUTHOR> <EMAIL>, 2017
# luca<PERSON>deliege <<EMAIL>>, 2017
# <PERSON>ttre <<EMAIL>>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# Hamid Darabi, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# Cédric Le Brouster, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# f5f0a0dac17dde787a1d812a6989680e, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# 6534c450c77b2549e41c52e2051f5839, 2017
# e34d4dcb8e697b071167e94624cfbccb, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2018
# Florian Hatat, 2018
# <AUTHOR> <EMAIL>, 2018
# 31c77b784710e4a287a92d851a40e039, 2018
# bb76cd9ac0cb7e20167a14728edb858b, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2019
# Martin Trigaux, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-03-29 09:07+0000\n"
"PO-Revision-Date: 2017-09-20 10:13+0000\n"
"Last-Translator: Aurélien Pillevesse <<EMAIL>>, 2019\n"
"Language-Team: French (https://www.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_state
msgid ""
" * The 'Draft' status is used when a user is encoding a new and unconfirmed Invoice.\n"
" * The 'Open' status is used when user creates invoice, an invoice number is generated. It stays in the open status till the user pays the invoice.\n"
" * The 'Paid' status is set automatically when the invoice is paid. Its related journal entries may or may not be reconciled.\n"
" * The 'Cancelled' status is used when user cancel invoice."
msgstr ""
" * L'état \"Brouillon\" est utilisé lorsqu'un utilisateur est en train de saisir ou de modifier une nouvelle facture non confirmée.\n"
" * L'état 'Ouvert' est utilisé lorsque l'utilisateur crée une facture, celle-ci a alors un numéro de facture La facture reste dans ce statut \"Ouvert\" jusqu'à ce qu'elle soit payée.\n"
" * L'état 'Payé' est affecté automatiquement lorsque la facture est payée. Ses écritures dans les journaux connexes peuvent ou non être réconciliées.\n"
" * L'état \"Annulé\" est utilisé lorsque l'utilisateur annule la facture."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_code_digits
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_code_digits
msgid "# of Digits"
msgstr "Nb. de chiffres"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_code_digits
msgid "# of Digits *"
msgstr "Nb. de chiffres *"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_nbr
msgid "# of Lines"
msgstr "Nb. de lignes"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_trans_nbr
msgid "# of Transaction"
msgstr "Nb. de transactions"

#. module: account
#: model:mail.template,report_name:account.mail_template_data_payment_receipt
msgid "${(object.name or '').replace('/','-')}"
msgstr "${(object.name or '').replace('/','-')}"

#. module: account
#: model:mail.template,subject:account.email_template_edi_invoice
msgid "${object.company_id.name} Invoice (Ref ${object.number or 'n/a'})"
msgstr "${object.company_id.name} Facture (Ref ${object.number or 'n/a'})"

#. module: account
#: model:mail.template,subject:account.mail_template_data_payment_receipt
msgid ""
"${object.company_id.name} Payment Receipt (Ref ${object.name or 'n/a' })"
msgstr ""
"${object.company_id.name} Reçu de paiement (Ref ${object.name or 'n/a' })"

#. module: account
#: model:mail.template,subject:account.mail_template_data_notification_email_account_invoice
msgid "${object.subject}"
msgstr "${object.subject}"

#. module: account
#: code:addons/account/models/account_bank_statement.py:462
#, python-format
msgid "%d transactions were automatically reconciled."
msgstr "%d transactions ont été lettrées automatiquement."

#. module: account
#: code:addons/account/models/account.py:809
#, python-format
msgid "%s (Copy)"
msgstr "%s (Copie)"

#. module: account
#: code:addons/account/models/account.py:211
#: code:addons/account/models/account.py:484
#: code:addons/account/models/account.py:485
#, python-format
msgid "%s (copy)"
msgstr "%s (copie)"

#. module: account
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "&amp;nbsp;<span>on</span>"
msgstr "&amp;nbsp;<span>sur</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ", if accounting or purchase is installed"
msgstr ", si la comptabilité ou les achats sont installés"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "- First Number:"
msgstr "- Premier numéro :"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "-> Reconcile"
msgstr "réconcilier"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "-> View partially reconciled entries"
msgstr "-> Voir les écritures partiellement lettrées"

#. module: account
#: code:addons/account/models/account_bank_statement.py:463
#, python-format
msgid "1 transaction was automatically reconciled."
msgstr "1 transaction a été lettré automatiquement."

#. module: account
#: model:account.payment.term,name:account.account_payment_term_15days
msgid "15 Days"
msgstr "15 jours"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_net
msgid "30 Net Days"
msgstr "30 jours nets"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_advance
msgid "30% Advance End of Following Month"
msgstr "30% d'avance, fin du mois suivant"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "5) For setup, you will need the following information:"
msgstr "Les informations suivantes sont nécessaires à l'installation :"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid ": General ledger"
msgstr ": Grand livre"

#. module: account
#: code:addons/account/models/account.py:554
#, python-format
msgid ": Refund"
msgstr ": Avoir"

#. module: account
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid ": Trial Balance"
msgstr ": Balance générale"

#. module: account
#: model:mail.template,body_html:account.mail_template_data_payment_receipt
msgid ""
"<?xml version=\"1.0\"?>\n"
"<data><p>Dear ${object.partner_id.name},</p>\n"
"<p>Thank you for your payment.<br/>Here is your payment receipt <strong>${(object.name or '').replace('/','-')}</strong> amounting to <strong>${format_amount(object.amount, object.currency_id)}</strong> from ${object.company_id.name}.</p>\n"
"<p>If you have any questions, please do not hesitate to contact us.</p>\n"
"<p>Best regards,\n"
"% if user and user.signature:\n"
"${user.signature | safe}\n"
"% endif\n"
"</p>\n"
"</data>"
msgstr ""
"<?xml version=\"1.0\"?>\n"
"<data><p>Chèr(e) ${object.partner_id.name},</p>\n"
"<p>Merci pour votre règlement. <br/> Voici le reçu de votre paiement <strong>${(object.name or '').replace('/','-')}</strong> d'un montant de <strong>${format_amount(object.amount, object.currency_id)}</strong> de ${object.company_id.name}.</p>\n"
"<p>Si vous avez des questions, pensez à nous contacter.</p>\n"
"<p>Sincères salutations,\n"
"% if user and user.signature:\n"
"${user.signature | safe}\n"
"% endif\n"
"</p>\n"
"</data>"

#. module: account
#: model:mail.template,body_html:account.mail_template_data_notification_email_account_invoice
msgid ""
"<div>\n"
"% set record = ctx.get('record')\n"
"% set company = record and record.company_id or ctx.get('company')\n"
"<table border=\"0\" width=\"100%\" cellpadding=\"0\" bgcolor=\"#ededed\" style=\"padding: 20px; background-color: #ededed; border-collapse:separate;\" summary=\"o_mail_notification\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\">\n"
"                        <span style=\"font-size:20px; color:white; font-weight: bold;\">\n"
"                            Invoice ${object.record_name}\n"
"                        </span>\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\">\n"
"                        <img src=\"/logo.png?company=${company.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${company.name}\"/>\n"
"                    </td>\n"
"                </tr>\n"
"          </table>\n"
"    </td>\n"
"  </tr>\n"
"  <!-- CONTENT -->\n"
"  <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"min-width: 590px; background-color: rgb(255, 255, 255); padding: 20px; border-collapse:separate;\">\n"
"                <tbody>\n"
"                    <td valign=\"top\" style=\"font-family:Arial,Helvetica,sans-serif; color: #555; font-size: 14px;\">\n"
"                        ${object.body | safe}\n"
"                    </td>\n"
"                </tbody>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\" align=\"left\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                        ${company.name}<br/>\n"
"                        ${company.phone or ''}\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                        % if company.email:\n"
"                        <a href=\"mailto:${company.email}\" style=\"text-decoration:none; color: white;\">${company.email}</a><br/>\n"
"                        % endif\n"
"                        % if company.website:\n"
"                        <a href=\"${company.website}\" style=\"text-decoration:none; color: white;\">\n"
"                        ${company.website}\n"
"                        </a>\n"
"                        % endif\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <tr>\n"
"        <td align=\"center\">\n"
"            Powered by <a target=\"_blank\" href=\"https://www.odoo.com\">Odoo</a>.\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</div>"
msgstr ""
"<div>\n"
"% set record = ctx.get('record')\n"
"% set company = record and record.company_id or ctx.get('company')\n"
"<table border=\"0\" width=\"100%\" cellpadding=\"0\" bgcolor=\"#ededed\" style=\"padding: 20px; background-color: #ededed; border-collapse:separate;\" summary=\"o_mail_notification\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\">\n"
"                        <span style=\"font-size:20px; color:white; font-weight: bold;\">\n"
"                            Facture ${object.record_name}\n"
"                        </span>\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\">\n"
"                        <img src=\"/logo.png?company=${company.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${company.name}\"/>\n"
"                    </td>\n"
"                </tr>\n"
"          </table>\n"
"    </td>\n"
"  </tr>\n"
"  <!-- CONTENT -->\n"
"  <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"min-width: 590px; background-color: rgb(255, 255, 255); padding: 20px; border-collapse:separate;\">\n"
"                <tbody>\n"
"                    <td valign=\"top\" style=\"font-family:Arial,Helvetica,sans-serif; color: #555; font-size: 14px;\">\n"
"                        ${object.body | safe}\n"
"                    </td>\n"
"                </tbody>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\" align=\"left\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                        ${company.name}<br/>\n"
"                        ${company.phone or ''}\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                        % if company.email:\n"
"                        <a href=\"mailto:${company.email}\" style=\"text-decoration:none; color: white;\">${company.email}</a><br/>\n"
"                        % endif\n"
"                        % if company.website:\n"
"                        <a href=\"${company.website}\" style=\"text-decoration:none; color: white;\">\n"
"                        ${company.website}\n"
"                        </a>\n"
"                        % endif\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <tr>\n"
"        <td align=\"center\">\n"
"            Fourni par <a target=\"_blank\" href=\"https://www.odoo.com\">Odoo</a>.\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</div>"

#. module: account
#: model:mail.template,body_html:account.email_template_edi_invoice
msgid ""
"<div>\n"
"<p>Dear ${object.partner_id.name}\n"
"% set access_action = object.with_context(force_website=True).get_access_action()\n"
"% set is_online = access_action and access_action['type'] == 'ir.actions.act_url'\n"
"% set access_url = object.get_mail_url()\n"
"\n"
"% if object.partner_id.parent_id:\n"
"    (<i>${object.partner_id.parent_id.name}</i>)\n"
"% endif\n"
",</p>\n"
"<p>Here is, in attachment, your \n"
"% if object.number:\n"
"invoice <strong>${object.number}</strong>\n"
"% else:\n"
"invoice\n"
"% endif\n"
"% if object.origin:\n"
"(with reference: ${object.origin})\n"
"% endif\n"
"amounting in <strong>${format_amount(object.amount_total, object.currency_id)}</strong>\n"
"from ${object.company_id.name}.\n"
"</p>\n"
"\n"
"% if is_online:\n"
"    <br/><br/>\n"
"    <center>\n"
"      <a href=\"${access_url}\" style=\"background-color: #1abc9c; padding: 20px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 16px;\" class=\"o_default_snippet_text\">View Invoice</a>\n"
"    </center>\n"
"% endif\n"
"    <br/><br/>\n"
"\n"
"% if object.state=='paid':\n"
"    <p>This invoice is already paid.</p>\n"
"% else:\n"
"    <p>Please remit payment at your earliest convenience.</p>\n"
"% endif\n"
"\n"
"<p>Thank you,</p>\n"
"<p style=\"color:#888888\">\n"
"% if object.user_id and object.user_id.signature:\n"
"    ${object.user_id.signature | safe}\n"
"% endif\n"
"</p>\n"
"</div>"
msgstr ""
"<div>\n"
"<p>Cher ${object.partner_id.name}\n"
"% set access_action = object.with_context(force_website=True).get_access_action()\n"
"% set is_online = access_action and access_action['type'] == 'ir.actions.act_url'\n"
"% set access_url = object.get_mail_url()\n"
"\n"
"% if object.partner_id.parent_id:\n"
"    (<i>${object.partner_id.parent_id.name}</i>)\n"
"% endif\n"
",</p>\n"
"<p>Voici, en pièce jointe, votre \n"
"% if object.number:\n"
"facture <strong>${object.number}</strong>\n"
"% else:\n"
"facture\n"
"% endif\n"
"% if object.origin:\n"
"(avec la référence ${object.origin})\n"
"% endif\n"
", son montant s'élève à <strong>${format_amount(object.amount_total, object.currency_id)}</strong>\n"
"émis par ${object.company_id.name}.\n"
"</p>\n"
"\n"
"% if is_online:\n"
"    <br/><br/>\n"
"    <center>\n"
"      <a href=\"${access_url}\" style=\"background-color: #1abc9c; padding: 20px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 16px;\" class=\"o_default_snippet_text\">Voir la facture</a>\n"
"    </center>\n"
"% endif\n"
"    <br/><br/>\n"
"\n"
"% if object.state=='paid':\n"
"    <p>Cette facture est déjà payée.</p>\n"
"% else:\n"
"    <p>Merci de procéder au payement dès que possible.</p>\n"
"% endif\n"
"\n"
"<p>Merci,</p>\n"
"<p style=\"color:#888888\">\n"
"% if object.user_id and object.user_id.signature:\n"
"    ${object.user_id.signature | safe}\n"
"% endif\n"
"</p>\n"
"</div>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<span class=\"fa fa-arrow-circle-o-down\"/> Install Now"
msgstr "<span class=\"fa fa-arrow-circle-o-down\"/> Installer maintenant"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Les valeurs définies ici sont"
" propres à l'entreprise.\" groups=\"base.group_multi_company\"/>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<span class=\"fa\"/> Invite Your Users"
msgstr "<span class=\"fa\"/> Inviter vos utilisateurs"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-check\"/><span "
"class=\"hidden-xs\"> Paid</span></span>"
msgstr ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-check\"/><span "
"class=\"hidden-xs\"> Payé</span></span>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-remove\"/><span "
"class=\"hidden-xs\"> Cancelled</span></span>"
msgstr ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-remove\"/><span "
"class=\"hidden-xs\"> Annulé</span></span>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"label label-info\"><i class=\"fa fa-fw fa-clock-o\"/><span "
"class=\"hidden-xs\"> Waiting for Payment</span></span>"
msgstr "123En attente de paiement31"

#. module: account
#: model:ir.ui.view,arch_db:account.partner_view_buttons
msgid "<span class=\"o_stat_text\">Invoiced</span>"
msgstr "<span class=\"o_stat_text\">Facturé</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>Import a file</strong><br/>\n"
"                                        <span class=\"small\">Recommended if &gt;100 products</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>Importer un fichier</strong><br/>\n"
"                                        <span class=\"small\">Recommendé si &gt;100 articles</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>Import</strong><br/>\n"
"                                        <span class=\"small\">&gt; 200 contacts</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>Importer</strong><br/>\n"
"                                        <span class=\"small\">&gt; 200 contacts</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> Create manually</strong><br/>\n"
"                                        <span class=\"small\">&lt; 200 contacts</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> Créer manuellement</strong><br/>\n"
"                                        <span class=\"small\">&lt; 200 contacts</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> Create manually</strong><br/>\n"
"                                        <span class=\"small\">Recommended if &lt;100 products</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> Créer manuellement</strong><br/>\n"
"                                        <span class=\"small\">Recommandé si &lt;100 articles</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Accountant</strong> (Advanced access)\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Comptable</strong> (accès avancé)\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Advisor</strong> (Full access)\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Conseiller</strong> (accès complet)\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Billing</strong> (Limited access)\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Facturation</strong> (accès limité)\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Customer follow-up</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Relance client</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Invoice payment by Check</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Paiement de facture par chèque</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Invoice payment by Wire Transfer</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Paiement de facture par virement électronique</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Pay your bills</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Payer vos factures</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Reconcile Bank Statements</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Lettrer les relevés bancaires</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Record your Bills</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Enregistrer vos factures</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span title=\"Balance in Odoo\">Balance in GL</span>"
msgstr "<span title=\"Balance in Odoo\">Solde en GL</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span title=\"Latest Statement\">Latest Statement</span>"
msgstr "<span title=\"Latest Statement\">Dernier relevé</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "<span> From </span>"
msgstr "<span> De </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "<span> To </span>"
msgstr "<span> à </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Journal Entries</span>"
msgstr "<span>Pièces comptables</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Bill</span>"
msgstr "<span>Nouvelle facture</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Invoice</span>"
msgstr "<span>Nouvelle facture</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New</span>"
msgstr "<span>Nouveau</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<span>Not due</span>"
msgstr "<span>Non échu</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Operations</span>"
msgstr "<span>Opérations</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Reconciliation</span>"
msgstr "<span>Lettrage</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Reporting</span>"
msgstr "<span>Suivi d'activité</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>View</span>"
msgstr "<span>Vue</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>-The Odoo Team</strong>"
msgstr "<strong>-L'équipe Odoo</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>1. Register Outstanding Invoices</strong>"
msgstr "<strong>1. Enregistrer les factures en circulation</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>2. Register Unmatched Payments</strong>"
msgstr "<strong>2. Enregistrer les paiements sans correspondance</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_invoice_document_with_payments
msgid "<strong>Amount Due</strong>"
msgstr "<strong>Montant dû</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Assets Management</strong>"
msgstr "<strong>Gestion des actifs</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Automated documents sending:</strong> automatically send your "
"invoices by email or snail mail."
msgstr ""
"<strong>envoi automatisé de documents :</strong> envoyez automatiquement vos"
" factures par courriel ou courrier postal ;"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "<strong>Balance :</strong>"
msgstr "<strong>Solde :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Banking interface:</strong> with live bank feed synchronization and "
"bank statement import."
msgstr ""
"<strong>interface bancaire :</strong> synchronisation de données bancaires "
"en direct et import de relevés bancaires."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Cash transactions</strong><br/> (for which there is no invoice or "
"bill), should be entered directly into your Cash Registers bank account."
msgstr ""
"Les <strong>transactions en espèces</strong><br/> (pour lesquelles il n'y a "
"pas de facture), devraient être saisies directement dans le compte bancaire "
"du registre de caisse."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Check the Taxes configuration:</strong>"
msgstr "<strong>Vérifier la configuration des taxes :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Check unpaid invoices</strong><br/>\n"
"                                         Run an <i>Aged Receivable report</i> and check which customers still owe you money."
msgstr ""
"<strong>Vérifiez les factures impayées</strong><br/>\n"
"Exécutez la <i>Balance Âgée Clients</i> et vérifiez quels clients vous doivent encore de l'argent."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Clean customer invoices:</strong> easy to create, beautiful and full"
" featured invoices."
msgstr ""
"<strong>factures clients limpides :</strong> des factures faciles à créer, "
"jolies et complètes ;"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
msgid "<strong>Company:</strong>"
msgstr "<strong>Société :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Contracts &amp; Subscriptions</strong>"
msgstr "<strong>Contrats &amp; Abonnements</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Create a Customer Invoice</strong>"
msgstr "<strong>Créer une facture client</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Create a Deposit Ticket</strong><br/>\n"
"                                        This allows you to record the different payments that constitute your bank deposit. (You may need to"
msgstr ""
"<strong>Créer un bordereau de dépôt</strong><br/>\n"
"Ceci vous permet d'enregistrer les différents paiements qui constituent votre dépôt. (Il se peut que vous"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Create the bill in Odoo</strong><br/> with a proper due date, and "
"create the vendor if it doesnt' exist yet."
msgstr ""
"<strong>Créer la facture dans Odoo</strong><br/> avec la bonne date "
"d'échéance, et créer le fournisseur s'il n'existe pas encore."

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
msgid "<strong>Customer Address</strong>"
msgstr "<strong>Adresse du client</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Customer Code:</strong>"
msgstr "<strong>Numéro de client :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Customer: </strong>"
msgstr "<strong>Client: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Date from :</strong>"
msgstr "<strong>Date du :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Date to :</strong>"
msgstr "<strong>Date au :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Deposit Tickets</strong>"
msgstr "<strong>Bordereau de dépôt</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Description:</strong>"
msgstr "<strong>Description :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Display Account:</strong>"
msgstr "<strong>Afficher Compte:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "<strong>Display Account</strong>"
msgstr "<strong>Afficher Compte</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Due Date:</strong>"
msgstr "<strong>Date d'échéance :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Entries Sorted By:</strong>"
msgstr "<strong>Entrées triées par :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Expenses</strong>"
msgstr "<strong>Dépenses</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
msgid "<strong>Fiscal Year End</strong>"
msgstr "<strong>Fin d'exercice fiscal</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "<strong>From</strong>"
msgstr "<strong>De</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Invoice Date:</strong>"
msgstr "<strong>Date de la facture :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Journal:</strong>"
msgstr "<strong>Journal :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "<strong>Journals:</strong>"
msgstr "<strong>Journaux :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Mark the bills to pay</strong><br/>\n"
"                                        Group or filter your bills to see those due in the next week, then open each bill individually, click on <strong>'Pay'</strong> and select the payment method you prefer."
msgstr ""
"<strong>Indiquez les factures à payer</strong><br/>\n"
"Regroupez et filtrez vos factures pour voir celles qui arrivent à échéance la semaine prochaine, ensuite ouvrez chaque facture individuellement, cliquez sur <strong>'Payer'</strong> et sélectionnez votre moyen de paiement préféré."

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Memo: </strong>"
msgstr "<strong>Mémo : </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Next action:</strong><br/>"
msgstr "<strong>Prochaine action :</strong><br/>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>On-the-fly payment reconciliation:</strong> automatic suggestions of"
" outstanding payments when creating invoices."
msgstr ""
"<strong>lettrage du paiement à la volée :</strong> suggestions automatiques "
"des paiements en suspens lors de la création d'une facture ;"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Or generate payment orders</strong><br/>\n"
"                                        Create a Payment Order and select the bills you'd like to pay as Entry lines (only the validated bills will appear)."
msgstr ""
"<strong>Ou générez des bons pour paiement</strong><br/>\n"
"Créez un bon pour paiement et sélectionnez les factures que vous voulez payer comme lignes d'écriture (seules les factures validées apparaitront)."

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<strong>Partner's:</strong>"
msgstr "<strong>Partenaire :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Payment Amount: </strong>"
msgstr "<strong>Montant du paiement : </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Payment Date: </strong>"
msgstr "<strong>Date de paiement: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Payment Method: </strong>"
msgstr "<strong>Méthode de paiement: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<strong>Period Length (days)</strong>"
msgstr "<strong>Durée de la période (jours)</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Print checks</strong><br/>\n"
"                                        From the Vendor Payments list, select those you want to pay and click on Print Check (you may first need to activate the check feature in the"
msgstr ""
"<strong>Imprimer les chèques</strong><br/>\n"
"                                        Depuis la liste des paiements fournisseurs, sélectionnez ceux que vous souhaitez payer et cliquez sur Imprimer les chèques (vous devrez peut-être activer la fonctionnalité de chèque avant dans"

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "<strong>Purchase</strong>"
msgstr "<strong>Achat</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Purchases</strong>"
msgstr "<strong>Achats</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Reconcile Bank Statement</strong>"
msgstr "<strong>Réconcilier le relevé bancaire</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Reconcile with existing transaction</strong><br/>\n"
"                                        In this case, Odoo should automatically match the bank statement with the previously recorded check transaction."
msgstr ""
"<strong>Lettrez avec des transactions existantes</strong><br/>\n"
"                                        Dans ce cas, Odoo devrait automatiquement faire la correspondance entre le relevé bancaire et la transaction du chèque enregistrée précédemment."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Reconcile your Bank Statements</strong>"
msgstr "<strong>Lettrez vos relevés bancaires</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Record Bank Statement (or import file)</strong><br/>\n"
"                                        Depending on the volume of your transactions, you should be recording your bank statement every week to several times a day."
msgstr ""
"<strong>Enregistrer un relevé bancaire (ou importer un fichier)</strong><br/>\n"
"Selon le volume de vos transactions, vous aurez à enregistrer votre relevé bancaire toutes les semaines, à plusieurs fois par jour."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Record Bank Statement</strong>"
msgstr "<strong>Enregistrez un relevé bancaire</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Record a payment by check on the Invoice</strong><br/>\n"
"                                        Simply click on the 'Pay' button."
msgstr ""
"<strong>Enregistrez un paiement par chèque sur une facture</strong><br/>\n"
"                                        Cliquez simplement sur le bouton 'Payer'."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Reduced data entry:</strong> no need to manually create invoices, "
"register bank statements, and send payment follow-ups."
msgstr ""
"<strong>saisie de donnée réduite :</strong> pas besoin de créer manuellement"
" des factures, d'enregistrer des relevés bancaires ni d'envoyer des relances"
" de paiement."

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Reference:</strong>"
msgstr "<strong>Référence :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Revenue Recognition</strong>"
msgstr "<strong>Reconnaissance de revenu</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Send follow-up letters</strong>"
msgstr "<strong>Envoyez les lettres de relance</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "<strong>Sorted By:</strong>"
msgstr "<strong>Trié par :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Source:</strong>"
msgstr "<strong>Origine :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<strong>Start Date:</strong>"
msgstr "<strong>Date de début :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "<strong>Subtotal :</strong>"
msgstr "<strong>Sous-total :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Subtotal</strong>"
msgstr "<strong>Sous-total</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Target Moves:</strong>"
msgstr "<strong>Pièces cibles :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Test the following three scenarios in order to assist you in "
"familiarizing yourself with Odoo:</strong>"
msgstr ""
"<strong>Tester les trois scénarios suivants pour vous familiariser avec Odoo"
" :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>There are three different levels of access rights in Odoo:</strong>"
msgstr ""
"<strong>Il y a 3 niveaux différents de droits d'accès dans Odoo :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "<strong>There is nothing due with this customer.</strong>"
msgstr "<strong>Aucun montant dû pour ce client.</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_account_kanban
msgid "<strong>Type: </strong>"
msgstr "<strong>Type :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Validate the bill</strong><br/> after encoding the products and "
"taxes."
msgstr ""
"<strong>Valider la facture</strong><br/> après avoir saisi les articles et "
"les taxes."

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Vendor: </strong>"
msgstr "<strong>Fournisseur :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "<strong>to </strong>"
msgstr "<strong>à </strong>"

#. module: account
#: model:ir.actions.act_window,help:account.action_view_bank_statement_tree
msgid ""
"A Cash Register allows you to manage cash entries in your cash\n"
"                journals. This feature provides an easy way to follow up cash\n"
"                payments on a daily basis. You can enter the coins that are in\n"
"                your cash box, and then post entries when money comes in or\n"
"                goes out of the cash box."
msgstr ""
"Une caisse de liquides vous permet de gérer les écritures dans vos journaux "
"de caisse. Cette fonctionnalité offre une façon simple de suivre les "
"mouvements de caisse au jour le jour. Vous pouvez encoder les pièces de "
"monnaie qui sont dans la caisse et ensuite poster des écritures lorsque "
"l'argent entre ou sort de la caisse."

#. module: account
#: code:addons/account/models/account_bank_statement.py:383
#, python-format
msgid "A Cash transaction can't have a 0 amount."
msgstr "Une transaction en liquide ne peut avoir un montant égal à 0."

#. module: account
#: code:addons/account/models/account_invoice.py:1699
#, python-format
msgid "A Payment Terms should have its last line of type Balance."
msgstr ""
"Une condition de paiement devrait avoir sa dernière ligne de type Balance."

#. module: account
#: code:addons/account/models/account_invoice.py:1702
#, python-format
msgid "A Payment Terms should have only one line of type Balance."
msgstr ""
"Une condition de paiement ne devrait avoir qu'une seule ligne de type "
"Balance."

#. module: account
#: code:addons/account/models/account.py:722
#, python-format
msgid "A bank account can only belong to one journal."
msgstr "Un compte bancaire doit être attaché à un seul journal."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_tree
msgid ""
"A bank statement is a summary of all financial transactions\n"
"                occurring over a given period of time on a bank account. You\n"
"                should receive this periodicaly from your bank."
msgstr ""
"Un relevé bancaire est un sommaire de toutes les transactions financières "
"réalisées durant une période de temps donnée sur un compte bancaire. Vous "
"devriez le recevoir périodiquement de votre banque."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_line
msgid "A bank statement line is a financial transaction on a bank account."
msgstr ""
"Une ligne de relevé bancaire est une transaction financière sur un compte "
"bancaire."

#. module: account
#: model:ir.actions.act_window,help:account.action_move_journal_line
msgid ""
"A journal entry consists of several journal items, each of\n"
"                which is either a debit or a credit transaction."
msgstr ""
"Une pièce de journal consiste en plusieurs écritures de journaux, dont "
"chacune est soit une transaction au débit ou au crédit."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_journal_form
msgid ""
"A journal is used to record transactions of all accounting data\n"
"                related to the day-to-day business."
msgstr ""
"Un journal est utilisé pour enregistrer les transactions de vos données "
"comptables liées à vos activités quotidiennes."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "A list of common taxes and their rates."
msgstr "une liste des taxes courantes avec leur taux ;"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "A list of your customer and supplier payment terms."
msgstr "une liste de vos conditions de paiement client et fournisseur ;"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"A product in Odoo is something you sell or buy \n"
"                        whether or not it is goods, consumables, or services.\n"
"                        Choose how you want to create your products:"
msgstr ""
"Un produit dans Odoo est quelque chose que vous pouvez \n"
"vendre ou acheter qu'il s'agisse de biens, de consommables \n"
"ou de services.\n"
"Choisissez comment vous souhaitez créer vos produits :"

#. module: account
#: code:addons/account/models/account_move.py:891
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr "Un lettrage doit impliquer au moins 2 écritures comptables."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"A rounding per line is advised if your prices are tax-included. That way, "
"the sum of line subtotals equals the total with taxes."
msgstr ""
"Un arrondi par ligne est conseillé si vos prix comprennent les taxes. De "
"cette façon, la somme des sous-totaux de ligne est égale au total taxes "
"incluses."

#. module: account
#: code:addons/account/models/account_bank_statement.py:881
#: code:addons/account/models/account_bank_statement.py:884
#, python-format
msgid "A selected move line was already reconciled."
msgstr "Une des écritures sélectionnées est déja lettrée."

#. module: account
#: code:addons/account/models/account_bank_statement.py:892
#, python-format
msgid "A selected statement line was already reconciled with an account move."
msgstr ""
"Une des lignes de relevé sélectionnée a déjà été rapprochée d'une écriture."

#. module: account
#: sql_constraint:account.fiscal.position.tax:0
msgid "A tax fiscal position could be defined only once time on same taxes."
msgstr ""
"Une position fiscale budgétaire ne peut être défini qu'une seule fois sur "
"les mêmes taxes."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_journal_form
msgid ""
"A typical company may use one journal per payment method (cash,\n"
"                bank accounts, checks), one purchase journal, one sales journal\n"
"                and one for miscellaneous information."
msgstr ""
"Il se peut qu'une société typique utilise un journal par méthode de paiement (espèce,\n"
"                virement bancaire, chèque), un journal des achats, un journal des ventes\n"
"                et un pour les opérations diverses."

#. module: account
#: model:res.groups,name:account.group_warning_account
msgid "A warning can be set on a partner (Account)"
msgstr "Une alerte peut être mise sur un contact (compte)"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:521
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:523
#: code:addons/account/static/src/xml/account_reconciliation.xml:170
#: code:addons/account/static/src/xml/account_reconciliation.xml:228
#: code:addons/account/static/src/xml/account_reconciliation.xml:253
#: model:ir.model,name:account.model_account_account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_account_id
#: model:ir.model.fields,field_description:account.field_account_move_dummy_account_id
#: model:ir.model.fields,field_description:account.field_account_move_line_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_account_id
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
#: model:ir.ui.view,arch_db:account.view_account_form
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_account_search
#, python-format
msgid "Account"
msgstr "Compte"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_accountant
msgid "Account Accountant"
msgstr "Comptable"

#. module: account
#: model:ir.model,name:account.model_account_aged_trial_balance
msgid "Account Aged Trial balance Report"
msgstr "Balance âgée"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Account Balances"
msgstr "Balance des comptes"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_cashbox
msgid "Account Bank Statement Cashbox Details"
msgstr "Détails"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_closebalance
msgid "Account Bank Statement closing balance"
msgstr "Solde de fermeture du relevé bancaire"

#. module: account
#: model:ir.model,name:account.model_account_common_account_report
msgid "Account Common Account Report"
msgstr "Rapports de comptabilité courants"

#. module: account
#: model:ir.model,name:account.model_account_common_journal_report
msgid "Account Common Journal Report"
msgstr "Impression des Journaux"

#. module: account
#: model:ir.model,name:account.model_account_common_partner_report
msgid "Account Common Partner Report"
msgstr "Rapport du compte \"Partenaires ordinaires\""

#. module: account
#: model:ir.model,name:account.model_account_common_report
msgid "Account Common Report"
msgstr "Rapport de comptabilité"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_currency_id
#: model:ir.model.fields,field_description:account.field_account_account_template_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_currency_id
msgid "Account Currency"
msgstr "Devise du compte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_account_dest_id
msgid "Account Destination"
msgstr "Compte de destination"

#. module: account
#: model:ir.model,name:account.model_account_move
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Account Entry"
msgstr "Écriture comptable"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_group_form
#: model:ir.ui.view,arch_db:account.view_account_group_tree
msgid "Account Group"
msgstr "Groupe de comptes"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_group_tree
msgid "Account Groups"
msgstr "Groupes de comptes"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_bank_journal_id
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_tree
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#: model:ir.ui.view,arch_db:account.view_account_journal_tree
msgid "Account Journal"
msgstr "Journal des comptes"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_account_line_id
msgid "Account Line"
msgstr "Ligne du compte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_account_ids
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Account Mapping"
msgstr "Affectation des comptes"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_reversal
msgid "Account Move Reversal"
msgstr "Extourne de la pièce comptable"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_acc_name
msgid "Account Name."
msgstr "Nom du compte."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_bank_acc_number
msgid "Account Number"
msgstr "Numéro de compte"

#. module: account
#: model:ir.model,name:account.model_account_report_partner_ledger
msgid "Account Partner Ledger"
msgstr "Livre des tiers"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_account_payable_id
#: model:ir.model.fields,field_description:account.field_res_users_property_account_payable_id
msgid "Account Payable"
msgstr "Compte fournisseur"

#. module: account
#: model:ir.model,name:account.model_account_print_journal
msgid "Account Print Journal"
msgstr "Impression de journal comptable"

#. module: account
#: model:ir.ui.view,arch_db:account.view_category_property_form
msgid "Account Properties"
msgstr "Propriétés du compte"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_account_receivable_id
#: model:ir.model.fields,field_description:account.field_res_users_property_account_receivable_id
msgid "Account Receivable"
msgstr "Compte client"

#. module: account
#: model:ir.model,name:account.model_account_financial_report
#: model:ir.model.fields,field_description:account.field_account_financial_report_children_ids
#: model:ir.ui.view,arch_db:account.view_account_financial_report_form
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
#: model:ir.ui.view,arch_db:account.view_account_financial_report_tree
msgid "Account Report"
msgstr "Rapport de compte"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_account_report_id
#: model:ir.ui.menu,name:account.menu_account_financial_reports_tree
msgid "Account Reports"
msgstr "Rapports de comptes"

#. module: account
#: model:ir.model,name:account.model_account_cash_rounding
msgid "Account Rounding"
msgstr "Arrondi de compte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_account_src_id
msgid "Account Source"
msgstr "Source comptable"

#. module: account
#: model:ir.ui.view,arch_db:account.account_bank_statement_graph
#: model:ir.ui.view,arch_db:account.account_bank_statement_pivot
#: model:ir.ui.view,arch_db:account.account_move_line_graph_date
#: model:ir.ui.view,arch_db:account.account_move_line_graph_date_cash_basis
msgid "Account Statistics"
msgstr "Statistiques du compte"

#. module: account
#: model:ir.model,name:account.model_account_account_tag
msgid "Account Tag"
msgstr "Étiquette du compte"

#. module: account
#: model:ir.actions.act_window,name:account.account_tag_action
msgid "Account Tags"
msgstr "Étiquettes de comptes"

#. module: account
#: model:ir.ui.view,arch_db:account.view_tax_form
#: model:ir.ui.view,arch_db:account.view_tax_tree
msgid "Account Tax"
msgstr "Taxe"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_account_tax_template_tree
msgid "Account Tax Template"
msgstr "Modèle de compte de taxe"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_taxcloud
msgid "Account TaxCloud"
msgstr "Compte TaxCloud"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
#: model:ir.ui.view,arch_db:account.view_account_template_form
#: model:ir.ui.view,arch_db:account.view_account_template_search
#: model:ir.ui.view,arch_db:account.view_account_template_tree
msgid "Account Template"
msgstr "Modèle de compte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_stock_valuation_account_id
#: model:ir.model.fields,field_description:account.field_res_company_property_stock_valuation_account_id
msgid "Account Template for Stock Valuation"
msgstr "Modèle de compte pour la valorisation d'inventaire"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_template_form
msgid "Account Templates"
msgstr "Modèles de comptes"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Account Total"
msgstr "Total du compte"

#. module: account
#: selection:account.financial.report,type:0
#: model:ir.model,name:account.model_account_account_type
#: model:ir.model.fields,field_description:account.field_account_account_type_name
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_account_type
#: model:ir.ui.view,arch_db:account.view_account_search
#: model:ir.ui.view,arch_db:account.view_account_template_search
#: model:ir.ui.view,arch_db:account.view_account_type_form
#: model:ir.ui.view,arch_db:account.view_account_type_search
#: model:ir.ui.view,arch_db:account.view_account_type_tree
msgid "Account Type"
msgstr "Type de compte"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_user_type_id
#: model:ir.model.fields,help:account.field_account_move_line_user_type_id
msgid ""
"Account Type is used for information purpose, to generate country-specific "
"legal reports, and set the rules to close a fiscal year and generate opening"
" entries."
msgstr ""
"Le type de compte est utilisé comme indication pour l'utilisateur, ainsi que"
" pour créer des rapports comptables spécifiques à certains pays, et enfin "
"pour gérer les clôtures d'exercices (et établir les écritures "
"correspondantes)"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_type_form
#: model:ir.model.fields,field_description:account.field_account_financial_report_account_type_ids
msgid "Account Types"
msgstr "Types de compte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_type_control_ids
msgid "Account Types Allowed"
msgstr "Types de compte autorisés"

#. module: account
#: model:ir.model,name:account.model_account_unreconcile
msgid "Account Unreconcile"
msgstr "Annuler le lettrage"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_group_search
msgid "Account group"
msgstr "Groupe de comptes"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_group_search
msgid "Account groups"
msgstr "Groupes de comptes"

#. module: account
#: model:ir.model,name:account.model_account_move_line_reconcile
msgid "Account move line reconcile"
msgstr "Lettrage de lignes d'écritures"

#. module: account
#: model:ir.model,name:account.model_account_move_line_reconcile_writeoff
msgid "Account move line reconcile (writeoff)"
msgstr "Ligne d'écriture lettrée (écriture d'écart)"

#. module: account
#: model:ir.model,name:account.model_account_move_reversal
msgid "Account move reversal"
msgstr "Extourne de la pièce comptable"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_account_src_id
msgid "Account on Product"
msgstr "Compte de l'article"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_tag_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template_tag_ids
msgid "Account tag"
msgstr "Étiquette du compte"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_refund_account_id
msgid ""
"Account that will be set on invoice tax lines for credit notes. Leave empty "
"to use the expense account."
msgstr ""
"Compte utilisé sur les lignes de taxes des avoirs. Laissez vide pour "
"utiliser le compte de dépenses."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_account_id
#: model:ir.model.fields,help:account.field_account_tax_template_account_id
msgid ""
"Account that will be set on invoice tax lines for invoices. Leave empty to "
"use the expense account."
msgstr ""
"Compte utilisé sur les lignes de taxes des factures. Laissez vide pour "
"utiliser le compte de dépense."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_template_refund_account_id
msgid ""
"Account that will be set on invoice tax lines for refunds. Leave empty to "
"use the expense account."
msgstr ""
"Compte utilisé sur les lignes de taxes des avoirs. Laissez vide pour "
"utiliser le compte de dépenses."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_account_dest_id
msgid "Account to Use Instead"
msgstr "Compte à utiliser à la place"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_cash_basis_account
#: model:ir.model.fields,help:account.field_account_tax_template_cash_basis_account
msgid ""
"Account used as counterpart for the journal entry, for taxes eligible based "
"on payments."
msgstr ""
"Compte utilisé comme contrepartie pour la pièce comptable, pour les taxes "
"exigibles en fonction des paiements."

#. module: account
#: model:ir.ui.menu,name:account.account_account_menu
msgid "Accounting"
msgstr "Comptabilité"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Accounting App Options"
msgstr "Options de l'application Comptabilité"

#. module: account
#: model:ir.ui.view,arch_db:account.view_wizard_multi_chart
msgid "Accounting Application Configuration"
msgstr "Paramétrage de l'application de comptabilité"

#. module: account
#: model:web.planner,tooltip_planner:account.planner_account
msgid "Accounting Configuration: a step-by-step guide."
msgstr "Configuration de la comptabilité : un guide pas-à-pas."

#. module: account
#: model:ir.actions.act_window,name:account.open_account_journal_dashboard_kanban
msgid "Accounting Dashboard"
msgstr "Tableau de bord de la comptabilité"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_date
msgid "Accounting Date"
msgstr "Date comptable"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Accounting Documents"
msgstr "Documents comptables"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_accounting_entries
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Accounting Entries"
msgstr "Écritures comptables"

#. module: account
#: model:ir.model,name:account.model_accounting_report
msgid "Accounting Report"
msgstr "Rapport comptable"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Accounting Settings"
msgstr "Paramètres de la comptabilité"

#. module: account
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Accounting-related settings are managed on"
msgstr "Les paramètres liés à la comptabilité sont gérés"

#. module: account
#: selection:account.account.tag,applicability:0
#: selection:account.financial.report,type:0
#: model:ir.model.fields,field_description:account.field_account_financial_report_account_ids
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
#: model:ir.ui.view,arch_db:account.view_account_search
msgid "Accounts"
msgstr "Comptes"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_account_control_ids
msgid "Accounts Allowed"
msgstr "Comptes autorisés"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_account
msgid "Accounts Fiscal Position"
msgstr "Comptes de régime de taxes"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_template_form
msgid "Accounts Mapping"
msgstr "Affectation des comptes"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_actions
msgid "Actions"
msgstr "Actions"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Activate Other Currencies"
msgstr "Activer d'autres devises"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Activate the option in the"
msgstr "Activer l'option dans"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_active
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_active
#: model:ir.model.fields,field_description:account.field_account_journal_active
#: model:ir.model.fields,field_description:account.field_account_payment_term_active
#: model:ir.model.fields,field_description:account.field_account_tax_active
#: model:ir.model.fields,field_description:account.field_account_tax_template_active
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Active"
msgstr "Actif/ve"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:17
#, python-format
msgid "Add"
msgstr "Ajouter"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid "Add Credit Note"
msgstr "Ajouter un avoir"

#. module: account
#: selection:account.cash.rounding,strategy:0
msgid "Add a rounding line"
msgstr "Ajouter une ligne d'arrondi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_has_second_line
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_has_second_line
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Add a second line"
msgstr "Ajouter une seconde ligne"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Add an internal note..."
msgstr "Ajouter une note interne…"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_comment
msgid "Additional Information"
msgstr "Information complémentaire"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Additional notes..."
msgstr "Notes supplémentaires..."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Address"
msgstr "Adresse"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_tax_id
msgid "Adjustment Tax"
msgstr "Taxe d'ajustement"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_adjustment_type
msgid "Adjustment Type"
msgstr "Type d'ajustement"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_tax_form
msgid "Advanced Options"
msgstr "Options avancées"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Advanced Settings"
msgstr "Paramètres avancés"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries
msgid "Adviser"
msgstr "Conseiller"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_include_base_amount
msgid "Affect Base of Subsequent Taxes"
msgstr "Impacte la base des taxes ultérieures"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_include_base_amount
msgid "Affect Subsequent Taxes"
msgstr "Impacte les taxes ultérieures"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_aged_balance_view
#: model:ir.actions.report,name:account.action_report_aged_partner_balance
#: model:ir.ui.menu,name:account.menu_aged_trial_balance
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Aged Partner Balance"
msgstr "Balance âgée des tiers"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
msgid ""
"Aged Partner Balance is a more detailed report of your receivables by "
"intervals. Odoo calculates a table of credit balance by start Date. So if "
"you request an interval of 30 days Odoo generates an analysis of creditors "
"for the past month, past two months, and so on."
msgstr ""
"La Balance âgée du Partenaire est un rapport plus détaillé de vos créances "
"par périodes. Odoo calcule un tableau de solde créditeur par date de début. "
"Donc, si vous demandez une période de 30 jours alors Odoo génère une analyse"
" des créanciers pour le mois passé, les deux derniers mois, et ainsi de "
"suite."

#. module: account
#: selection:account.balance.report,display_account:0
#: selection:account.common.account.report,display_account:0
#: selection:account.report.general.ledger,display_account:0
msgid "All"
msgstr "Tous"

#. module: account
#: selection:account.aged.trial.balance,target_move:0
#: selection:account.balance.report,target_move:0
#: selection:account.common.account.report,target_move:0
#: selection:account.common.journal.report,target_move:0
#: selection:account.common.partner.report,target_move:0
#: selection:account.common.report,target_move:0
#: selection:account.print.journal,target_move:0
#: selection:account.report.general.ledger,target_move:0
#: selection:account.report.partner.ledger,target_move:0
#: selection:account.tax.report,target_move:0
#: selection:accounting.report,target_move:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "All Entries"
msgstr "Toutes les écritures"

#. module: account
#: model:ir.actions.act_window,name:account.action_all_partner_invoices
msgid "All Invoices"
msgstr "Toutes les factures"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_all_lines_reconciled
msgid "All Lines Reconciled"
msgstr "Toutes les lignes léttrées"

#. module: account
#: selection:account.aged.trial.balance,target_move:0
#: selection:account.balance.report,target_move:0
#: selection:account.common.account.report,target_move:0
#: selection:account.common.journal.report,target_move:0
#: selection:account.common.partner.report,target_move:0
#: selection:account.common.report,target_move:0
#: selection:account.print.journal,target_move:0
#: selection:account.report.general.ledger,target_move:0
#: selection:account.report.partner.ledger,target_move:0
#: selection:account.tax.report,target_move:0
#: selection:accounting.report,target_move:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "All Posted Entries"
msgstr "Toutes les écritures comptabilisées"

#. module: account
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "All accounts"
msgstr "Tous les comptes"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "All accounts'"
msgstr "Tous les comptes"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:47
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr ""
"Toutes les factures et paiements ont été lettrés, la balance des comptes est"
" juste."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_state
msgid ""
"All manually created new journal entries are usually in the status "
"'Unposted', but you can set the option to skip that status on the related "
"journal. In that case, they will behave as journal entries automatically "
"created by the system on document validation (invoices, bank statements...) "
"and will be created in 'Posted' status."
msgstr ""
"Toutes les nouvelles écritures créées manuellement sont généralement à "
"l'état \"Non validé\". Vous pouvez toutefois définir l'option qui permet de "
"passer ce statut dans le journal lié. Dans ce cas, elles se comporteront "
"comme des écritures créées automatiquement par le système pour la validation"
" de document (factures, relevés bancaires, etc.) et seront créées avec le "
"statut \"Validé\"."

#. module: account
#: model:ir.ui.view,arch_db:account.validate_account_move_view
msgid ""
"All selected journal entries will be validated and posted. You won't be able"
" to modify them afterwards."
msgstr ""
"Toutes les écritures sélectionnées seront directement comptabilisées. Vous "
"ne serez pas en mesure de les modifier par la suite."

#. module: account
#: code:addons/account/models/account_bank_statement.py:240
#, python-format
msgid ""
"All the account entries lines must be processed in order to close the "
"statement."
msgstr ""
"Toutes les transactions bancaires doivent être traitées pour clôturer le "
"relevé bancaire."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_update_posted
msgid "Allow Cancelling Entries"
msgstr "Autoriser l'annulation d'écriture"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_reconcile
msgid "Allow Invoices & payments Matching"
msgstr "Permettre le lettrage des factures et des paiements"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_product_margin
msgid "Allow Product Margin"
msgstr "Autoriser la marge du produit"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_reconcile
msgid "Allow Reconciliation"
msgstr "Autoriser le lettrage"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_l10n_us_check_printing
msgid "Allow check printing and deposits"
msgstr "Activer l'impression et le dépôt de chèques ."

#. module: account
#: model:res.groups,name:account.group_cash_rounding
msgid "Allow the cash rounding management"
msgstr "Autoriser la gestion d'arrondis d'espèces"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allow to configure taxes using cash basis"
msgstr "Autoriser la configuration des taxes via la comptabilité de caisse"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allows you to use the analytic accounting."
msgstr "Vous permet d'utiliser la comptabilité analytique."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:193
#: code:addons/account/static/src/xml/account_reconciliation.xml:235
#: code:addons/account/static/src/xml/account_reconciliation.xml:252
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_amount
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_subtotal
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_total
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_amount
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_amount_total
#: model:ir.model.fields,field_description:account.field_account_move_amount
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_amount
#: model:ir.model.fields,field_description:account.field_account_tax_amount
#: model:ir.model.fields,field_description:account.field_account_tax_template_amount
#: model:ir.model.fields,field_description:account.field_cash_box_in_amount
#: model:ir.model.fields,field_description:account.field_cash_box_out_amount
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_amount
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_payment_tree
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_account_supplier_payment_tree
#: model:ir.ui.view,arch_db:account.view_move_line_form
#, python-format
msgid "Amount"
msgstr "Montant"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_amount_currency
#: model:ir.model.fields,field_description:account.field_account_analytic_line_analytic_amount_currency
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_amount_currency
#: model:ir.model.fields,field_description:account.field_account_move_line_amount_currency
msgid "Amount Currency"
msgstr "Montant en Devise"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_residual
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid "Amount Due"
msgstr "Montant dû"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_residual_company_signed
msgid "Amount Due in Company Currency"
msgstr "Montant dû en devise société"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_residual_signed
msgid "Amount Due in Invoice Currency"
msgstr "Montant dû en devise de la facture"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Amount Paid"
msgstr "Montant payé"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_amount_rounding
msgid "Amount Rounding"
msgstr "Arrondi du montant"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_subtotal_signed
msgid "Amount Signed"
msgstr "Montant signé"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_amount_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_amount_type
msgid "Amount Type"
msgstr "Type de montant"

#. module: account
#: model:ir.model.fields,help:account.field_account_partial_reconcile_amount
msgid "Amount concerned by this matching. Assumed to be always positive"
msgstr "Montant du rapprochement. Doit être positif"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_amount_currency
msgid "Amount in Currency"
msgstr "Montant en devise"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Amount type"
msgstr "Type de montant"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:68
#, python-format
msgid "Amount:"
msgstr "Montant :"

#. module: account
#: sql_constraint:account.fiscal.position.account:0
msgid ""
"An account fiscal position could be defined only once time on same accounts."
msgstr ""
"La position fiscale d'un compte peut être définie seulement une seule fois "
"pour ce compte."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_form
msgid ""
"An account is part of a ledger allowing your company\n"
"                to register all kinds of debit and credit transactions.\n"
"                Companies present their annual accounts in two main parts: the\n"
"                balance sheet and the income statement (profit and loss\n"
"                account). The annual accounts of a company are required by law\n"
"                to disclose a certain amount of information."
msgstr ""
"Un compte fait partie d'un livre permettant à votre société d'enregistrer "
"toute sorte de transaction au débit ou au crédit. Les sociétés présentent "
"leurs comptes annuels en 2 parties principales : le bilan et le compte de "
"résultat (compte de perte et profit). Les comptes annuels d'une société sont"
" requis par la loi afin de publier un certain nombre d'information."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_type_form
msgid ""
"An account type is used to determine how an account is used in\n"
"                each journal. The deferral method of an account type determines\n"
"                the process for the annual closing. Reports such as the Balance\n"
"                Sheet and the Profit and Loss report use the category\n"
"                (profit/loss or balance sheet)."
msgstr ""
"Un type de compte est utilisé afin de déterminer comment un compte est utilisé dans\n"
" chaque journal. La méthode de report d'un type de compte détermine\n"
" le processus à la clôture de l'exercice. Les rapports tels que le bilan\n"
" ou le compte de résultat utilisent la\n"
" catégorie (résultat ou bilan)."

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Analytic"
msgstr "Analytique"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:178
#, python-format
msgid "Analytic Acc."
msgstr "Compte analytique."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_account_analytic_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_account_analytic_id
#: model:ir.model.fields,field_description:account.field_account_move_line_analytic_account_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_analytic_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_analytic_account_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Analytic Account"
msgstr "Compte analytique"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_group_analytic_accounting
#: model:ir.ui.menu,name:account.menu_analytic_accounting
msgid "Analytic Accounting"
msgstr "Comptabilité analytique"

#. module: account
#: model:ir.actions.act_window,name:account.action_open_partner_analytic_accounts
#: model:ir.ui.menu,name:account.account_analytic_def_account
#: model:ir.ui.view,arch_db:account.partner_view_button_contracts_count
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytic Accounts"
msgstr "Comptes analytiques"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_analytic
msgid "Analytic Cost"
msgstr "Coût analytique"

#. module: account
#: model:ir.actions.act_window,name:account.analytic_line_reporting_pivot
#: model:ir.ui.menu,name:account.menu_action_analytic_lines_reporting
#: model:ir.ui.menu,name:account.menu_action_analytic_lines_tree
msgid "Analytic Entries"
msgstr "Écritures analytiques"

#. module: account
#: model:ir.model,name:account.model_account_analytic_line
msgid "Analytic Line"
msgstr "Ligne analytique"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Analytic Lines"
msgstr "Lignes analytiques"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_analytic_tag_ids
#: model:ir.ui.menu,name:account.account_analytic_tag_menu
msgid "Analytic Tags"
msgstr "Étiquettes analytiques"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_account_analytic_id
msgid "Analytic account"
msgstr "Compte Analytique"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_analytic_line_ids
msgid "Analytic lines"
msgstr "Lignes analytiques"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_analytic_tag_ids
msgid "Analytic tags"
msgstr "Tags analytiques"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytics"
msgstr "Comptabilité analytique"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_exigible
msgid "Appears in VAT report"
msgstr "Présent dans le rapport de TVA"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_applicability
msgid "Applicability"
msgstr "Applicabilité"

#. module: account
#: selection:tax.adjustments.wizard,adjustment_type:0
msgid "Applied on credit journal item"
msgstr "Appliquer aux éléments du journal des crédits"

#. module: account
#: selection:tax.adjustments.wizard,adjustment_type:0
msgid "Applied on debit journal item"
msgstr "Appliquer aux éléments du journal des débits"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_auto_apply
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_auto_apply
msgid "Apply automatically this fiscal position."
msgstr "Appliquer automatiquement cette position fiscale."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_country_group_id
msgid "Apply only if delivery or invocing country match the group."
msgstr ""
"Appliquer seulement si le pays de livraison ou de facturation correspond au "
"groupe."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_country_group_id
msgid "Apply only if delivery or invoicing country match the group."
msgstr ""
"Appliquer seulement si le pays de livraison ou de facturation correspond au "
"groupe."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_country_id
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_country_id
msgid "Apply only if delivery or invoicing country match."
msgstr ""
"Appliquer seulement si le pays de livraison ou de facturation correspond."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_vat_required
#: model:ir.model.fields,help:account.field_account_fiscal_position_vat_required
msgid "Apply only if partner has a VAT number."
msgstr ""
"S'applique seulement si le partenaire a un numéro de TVA intracommunautaire."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Apply right VAT rates for digital products sold in EU"
msgstr ""
"Appliquer des taux de TVA appropriés pour les articles numériques vendus "
"dans l'UE"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "April"
msgstr "Avril"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Archived"
msgstr "Archivé/s"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Ask for a Credit Note"
msgstr "Demander un avoir"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_assets0
msgid "Assets"
msgstr "Actifs"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_asset
msgid "Assets Management"
msgstr "Gestion des immobilisations"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_account_ids
msgid "Associated Account Templates"
msgstr "Modèles de comptes associés"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_at_least_one_inbound
msgid "At Least One Inbound"
msgstr "Au moins un entrant"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_at_least_one_outbound
msgid "At Least One Outbound"
msgstr "Au moins un sortant"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "August"
msgstr "Août"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Auto-detect"
msgstr "Détection automatique"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automate deferred revenues entries for multi-year contracts"
msgstr ""
"Automatisez la génération des entrées comptables des revenus différés pour "
"les contrats pluriannuel"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automated Entries"
msgstr "Génération automatique d'entrées comptables"

#. module: account
#: code:addons/account/models/company.py:411
#: code:addons/account/wizard/setup_wizards.py:79
#, python-format
msgid "Automatic Balancing Line"
msgstr "Ligne de solde automatique"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_currency_rate_live
msgid "Automatic Currency Rates"
msgstr "Taux de change automatiques"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automatic Import"
msgstr "Import automatique"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Automatic formatting"
msgstr "Mise en forme automatique"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:24
#, python-format
msgid "Automatic reconciliation"
msgstr "Reconciliation automatique"

#. module: account
#: code:addons/account/models/account_bank_statement.py:468
#, python-format
msgid "Automatically reconciled items"
msgstr "Ecritures lettrées automatiquement"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_price_average
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_currency_price_average
msgid "Average Price"
msgstr "Prix moyen"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Awaiting payments"
msgstr "Paiements en attente"

#. module: account
#: code:addons/account/models/chart_template.py:194
#, python-format
msgid "BILL"
msgstr "FACTURE"

#. module: account
#: selection:res.partner,trust:0
msgid "Bad Debtor"
msgstr "Mauvais Débiteur"

#. module: account
#: selection:account.payment.term.line,value:0
#: model:ir.model.fields,field_description:account.field_account_move_line_balance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_payment_receipt
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Balance"
msgstr "Solde"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_balance_cash_basis
msgid "Balance Cash Basis"
msgstr "Balance sur base des paiements"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_balancesheet0
#: model:ir.actions.act_window,name:account.action_account_report_bs
#: model:ir.ui.menu,name:account.menu_account_report_bs
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Balance Sheet"
msgstr "Bilan"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_balance_end
msgid "Balance as calculated based on Opening Balance and transaction lines"
msgstr "Solde correspondant au solde initial et aux lignes du relevé"

#. module: account
#: selection:account.bank.accounts.wizard,account_type:0
#: selection:account.journal,type:0
#: code:addons/account/models/chart_template.py:758
#: model:ir.model.fields,field_description:account.field_account_journal_bank_id
#: model:ir.model.fields,field_description:account.field_res_partner_bank_account_count
#: model:ir.model.fields,field_description:account.field_res_users_bank_account_count
#, python-format
msgid "Bank"
msgstr "Banque"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Bank &amp; Cash"
msgstr "Banque &amp; Cash"

#. module: account
#: code:addons/account/models/company.py:226
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_bank_account_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_bank_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_partner_bank_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_partner_bank_id
#: model:ir.model.fields,field_description:account.field_account_journal_bank_account_id
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#, python-format
msgid "Bank Account"
msgstr "Compte bancaire"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Bank Account Name"
msgstr "Nom du compte bancaire"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_partner_bank_id
msgid ""
"Bank Account Number to which the invoice will be paid. A Company bank "
"account if this is a Customer Invoice or Vendor Credit Note, otherwise a "
"Partner bank account number."
msgstr ""
"Numéro du compte bancaire sur lequel la facture sera payée. Un compte "
"bancaire de la Société s'il s'agit d'une facture client ou d'un avoir du "
"fournisseur, sinon un numéro de compte bancaire du tiers."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:21
#: model:ir.actions.act_window,name:account.action_account_bank_journal_form
#: model:ir.model,name:account.model_res_partner_bank
#: model:ir.ui.menu,name:account.menu_action_account_bank_journal_form
#, python-format
msgid "Bank Accounts"
msgstr "Comptes bancaires"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_bank_account_code_prefix
msgid "Bank Accounts Prefix"
msgstr "Préfixe des comptes bancaires"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_bank_statements_source
msgid "Bank Feeds"
msgstr "Flux bancaires"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_yodlee
msgid "Bank Interface - Sync your bank feeds automatically"
msgstr ""
"Interface bancaire - Synchronisez vos relevés bancaires automatiquement"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_bank_journal_ids
msgid "Bank Journals"
msgstr "Journaux de banque"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Bank Operations"
msgstr "Opérations bancaires"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_tree
msgid "Bank Reconciliation Move Presets"
msgstr "Préselection de transactions bancaires à réconcilier"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_search
msgid "Bank Reconciliation Move preset"
msgstr "Préselection de transactions bancaires à réconcilier"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_bank_data_done
msgid "Bank Setup Marked As Done"
msgstr "Configuration de la banque marquée comme terminée"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Bank Statement"
msgstr "Relevé bancaire"

#. module: account
#: code:addons/account/models/account_bank_statement.py:937
#, python-format
msgid "Bank Statement %s"
msgstr "Relevé bancaire %s"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Ligne de relevé bancaire"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_line
msgid "Bank Statement Lines"
msgstr "Lignes de relevé bancaire"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_tree
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Bank Statements"
msgstr "Relevés bancaires"

#. module: account
#: model:ir.ui.view,arch_db:account.partner_view_buttons
msgid "Bank account(s)"
msgstr "Comptes de banques"

#. module: account
#: model:account.account.type,name:account.data_account_type_liquidity
msgid "Bank and Cash"
msgstr "Banque et liquidités"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_action.js:15
#, python-format
msgid "Bank reconciliation"
msgstr "Rapprochement bancaire"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_account_setup_bank_data_done
msgid "Bank setup marked as done"
msgstr "Configuration de la banque marquée comme terminée"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_statement_line_id
msgid "Bank statement line reconciled with this entry"
msgstr "Transaction bancaire lettrée avec cette écriture"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_bank_statement
msgid "Bank statements"
msgstr "Relevés de compte"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:39
#, python-format
msgid "Bank: Balance"
msgstr "Banque : Solde"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_base
#: model:ir.ui.view,arch_db:account.portal_invoice_report
msgid "Base"
msgstr "Base"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_base_amount
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Base Amount"
msgstr "Montant de base"

#. module: account
#: selection:account.tax,tax_exigibility:0
#: selection:account.tax.template,tax_exigibility:0
msgid "Based on Invoice"
msgstr "Basé sur la facture"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_tax_exigibility
#: model:ir.model.fields,help:account.field_account_tax_template_tax_exigibility
msgid ""
"Based on Invoice: the tax is due as soon as the invoice is validated.\n"
"Based on Payment: the tax is due as soon as the payment of the invoice is received."
msgstr ""
"Basé sur la facture : la taxe est due dès que la facture est validée.\n"
"Basé sur le paiement : la taxe est due dès que le paiement de la facture est reçu."

#. module: account
#: selection:account.tax,tax_exigibility:0
#: selection:account.tax.template,tax_exigibility:0
msgid "Based on Payment"
msgstr "Basé sur le paiement"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Batch Deposits"
msgstr "Bordereaux de dépôt"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Before continuing, you must install the Chart of Account related to your "
"country (or the generic one if your country is not listed)."
msgstr ""
"Avant de continuer, vous devrez installer le plan comptable de votre pays "
"(ou le plan générique si votre pays n'est pas dans la liste)."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_belongs_to_company
msgid "Belong to the user's current company"
msgstr "Appartient à la société de l'utilisateur"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Bill"
msgstr "Facture"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "Bill Date"
msgstr "Date de facturation"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Bill lines"
msgstr "Lignes de facture"

#. module: account
#: model:res.groups,name:account.group_account_invoice
msgid "Billing"
msgstr "Facturation"

#. module: account
#: model:res.groups,name:account.group_account_manager
msgid "Billing Manager"
msgstr "Gestionnaire de facturation"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills"
msgstr "Factures"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills Analysis"
msgstr "Analyse des factures"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:186
#, python-format
msgid "Bills to pay"
msgstr "Factures à payer"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "Blocking Message"
msgstr "Message Bloquant"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_type_include_initial_balance
msgid "Bring Accounts Balance Forward"
msgstr "Reporter la balance des comptes"

#. module: account
#: model:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid "Browse available countries."
msgstr "Parcourir les pays disponibles."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_budget
msgid "Budget Management"
msgstr "Gestion du budget"

#. module: account
#: model:ir.ui.menu,name:account.account_reports_business_intelligence_menu
msgid "Business Intelligence"
msgstr "Analyse statistique"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_name
msgid "Button Label"
msgstr "Nom du bouton"

#. module: account
#: model:ir.filters,name:account.filter_invoice_country
msgid "By Country"
msgstr "Par pays"

#. module: account
#: model:ir.filters,name:account.filter_invoice_refund
msgid "By Credit Note"
msgstr "Par avoir"

#. module: account
#: model:ir.filters,name:account.filter_invoice_product
msgid "By Product"
msgstr "Par article"

#. module: account
#: model:ir.filters,name:account.filter_invoice_product_category
msgid "By Product Category"
msgstr "Par catégorie d'article"

#. module: account
#: model:ir.filters,name:account.filter_invoice_salespersons
msgid "By Salespersons"
msgstr "Par vendeur"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_active
msgid ""
"By unchecking the active field, you may hide a fiscal position without "
"deleting it."
msgstr ""
"En décochant le champ 'actif', vous masquez le régime des taxes sans "
"l'effacer."

#. module: account
#: code:addons/account/models/chart_template.py:173
#: code:addons/account/models/chart_template.py:197
#, python-format
msgid "CABA"
msgstr "CABA"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "CAMT Import"
msgstr "Import CAMT"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "CSV Import"
msgstr "Import CSV"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_visible
msgid "Can be Visible?"
msgstr "Peut être visible ?"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.account_common_report_view
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
#: model:ir.ui.view,arch_db:account.cash_box_in_form
#: model:ir.ui.view,arch_db:account.cash_box_out_form
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
#: model:ir.ui.view,arch_db:account.validate_account_move_view
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
#: model:ir.ui.view,arch_db:account.view_account_move_reversal
#: model:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Cancel"
msgstr "Annuler"

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Cancel: create credit note and reconcile"
msgstr "Annuler : créer un avoir et lettrer"

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: selection:account.payment,state:0
msgid "Cancelled"
msgstr "Annulé"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Cancelled Invoice"
msgstr "Facture annulée"

#. module: account
#: code:addons/account/wizard/account_invoice_refund.py:56
#, python-format
msgid ""
"Cannot create a credit note for the invoice which is already reconciled, "
"invoice should be unreconciled first, then only you can add credit note for "
"this invoice."
msgstr ""
"Impossible de créer un avoir pour la facture déjà lettrée. Le lettrage doit "
"être annulé avant de pouvoir ajouter un avoir pour cette facture."

#. module: account
#: code:addons/account/wizard/account_invoice_refund.py:54
#, python-format
msgid "Cannot create credit note for the draft/cancelled invoice."
msgstr ""
"Impossible de créer un avoir pour l'ébauche de facture/la facture annulée."

#. module: account
#: code:addons/account/models/account_move.py:197
#, python-format
msgid "Cannot create moves for different companies."
msgstr ""
"Impossible de créer une écriture comptable relative à plusieurs sociétés."

#. module: account
#: code:addons/account/models/account_move.py:229
#, python-format
msgid "Cannot create unbalanced journal entry."
msgstr "Impossible de créer une pièce non balancée."

#. module: account
#: code:addons/account/models/account_invoice.py:641
#, python-format
msgid ""
"Cannot find a chart of accounts for this company, You should configure it. \n"
"Please go to Account Configuration."
msgstr ""
"Impossible de trouver un plan comptable pour cette société. Il vous faut le configurer.\n"
"Veuillez aller dans les configurations comptables"

#. module: account
#: code:addons/account/models/account.py:594
#, python-format
msgid "Cannot generate an unused account code."
msgstr "Impossible de créer un code comptable non utilisé."

#. module: account
#: code:addons/account/models/account.py:624
#, python-format
msgid ""
"Cannot generate an unused journal code. Please fill the 'Shortcode' field."
msgstr ""
"Impossible de générer un code journal. Merci de compléter le champ 'Code "
"court'."

#. module: account
#: selection:account.bank.accounts.wizard,account_type:0
#: selection:account.journal,type:0
#: code:addons/account/models/chart_template.py:758
#, python-format
msgid "Cash"
msgstr "Espèces"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_cash_account_code_prefix
msgid "Cash Accounts Prefix"
msgstr "Préfixe des comptes de trésorerie"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_tax_exigibility
msgid "Cash Basis"
msgstr "Comptabilité de caisse"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_tax_cash_basis_journal_id
msgid "Cash Basis Journal"
msgstr "Journal de comptabilité de trésorerie"

#. module: account
#: code:addons/account/models/chart_template.py:197
#, python-format
msgid "Cash Basis Tax Journal"
msgstr "Journal de taxes de comptabilité de trésorerie"

#. module: account
#: code:addons/account/models/account_bank_statement.py:210
#: model:ir.actions.act_window,name:account.action_view_account_bnk_stmt_cashbox
#, python-format
msgid "Cash Control"
msgstr "Contrôle de caisse"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Cash Operations"
msgstr "Opérations en liquide"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_bank_statement_tree
msgid "Cash Registers"
msgstr "Caisses"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_group_cash_rounding
msgid "Cash Rounding"
msgstr "Arrondi d'espèces"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_cash_rounding_id
msgid "Cash Rounding Method"
msgstr "Méthode d'arrondi d'espèces"

#. module: account
#: model:ir.actions.act_window,name:account.rounding_list_action
#: model:ir.ui.menu,name:account.menu_action_rounding_form_view
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Cash Roundings"
msgstr "Arrondis d'espèces"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Cash Statements"
msgstr "Relevés de caisse"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_bank_account_ids
msgid "Cash and Banks"
msgstr "Caisse et comptes bancaires"

#. module: account
#: code:addons/account/models/account_bank_statement.py:185
#, python-format
msgid "Cash difference observed during the counting (%s)"
msgstr "Différence de trésorerie observée au comptage (%s)"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:37
#, python-format
msgid "Cash: Balance"
msgstr "Espèces : Solde"

#. module: account
#: model:ir.model,name:account.model_account_cashbox_line
msgid "CashBox Line"
msgstr "Mouvement de caisse"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_cashbox_id
msgid "Cashbox"
msgstr "Caisse"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_cashbox_lines_ids
msgid "Cashbox Lines"
msgstr "Mouvements de caisse"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_expense_categ_id
msgid "Category of Expense Account"
msgstr "Catégorie de compte de dépense"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_income_categ_id
msgid "Category of Income Account"
msgstr "Catégorie de compte de revenu"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Change"
msgstr "Modifier"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_writeoff_label
msgid "Change label of the counterpart that will hold the payment difference"
msgstr ""
"Changer le libellé de la contrepartie qui retiendra la différence de "
"paiement"

#. module: account
#: code:addons/account/controllers/portal.py:146
#, python-format
msgid ""
"Changing VAT number is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"Vous ne pouvez pas modifier votre identifiant de TVA si des factures ont "
"déjà été émises pour votre compte. Veuillez nous contacter directement pour "
"effectuer cette modification."

#. module: account
#: code:addons/account/controllers/portal.py:149
#, python-format
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"Vous ne pouvez pas modifier votre nom si des factures ont déjà été émises "
"pour votre compte. Veuillez nous contacter directement pour effectuer cette "
"modification."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_res_company_chart_template_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_chart_template_id
msgid "Chart Template"
msgstr "Modèle de plan comptable"

#. module: account
#: model:ir.actions.act_window,name:account.open_account_charts_modules
msgid "Chart Templates"
msgstr "Modèle de plan comptable"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_coa_done
msgid "Chart of Account Checked"
msgstr "Plan comptable vérifié"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:278
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:13
#: model:ir.actions.act_window,name:account.action_account_form
#: model:ir.ui.menu,name:account.menu_action_account_form
#: model:ir.ui.view,arch_db:account.account_planner
#, python-format
msgid "Chart of Accounts"
msgstr "Plan comptable"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_form
#: model:ir.ui.view,arch_db:account.view_account_chart_template_tree
msgid "Chart of Accounts Template"
msgstr "Modèle de plan comptable"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_chart_template_form
msgid "Chart of Accounts Templates"
msgstr "Modèles de plans de comptes"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Chart of Accounts has been\n"
"                            installed. You should review it and create any additional accounts."
msgstr ""
"Le plan de comptes a été installé.\n"
"Vous pouvez le parcourir et y créer des comptes additionnels."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_list
msgid "Chart of accounts"
msgstr "Plan comptable"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_account_bnk_stmt_check
msgid "Check Closing Balance"
msgstr "Vérifier le solde de clôture"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_is_difference_zero
msgid "Check if difference is zero."
msgstr "A vérifier si non égal à zéro."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_reconcile
msgid ""
"Check this box if this account allows invoices & payments matching of "
"journal items."
msgstr ""
"Cochez cette case si ce compte permet de faire du rapprochement entre "
"factures et paiements."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_refund_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" credit notes made from this journal"
msgstr ""
"Cochez cette case si vous ne souhaitez pas partager la même séquence pour "
"les factures et les avoirs créés à partir de ce journal."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_update_posted
msgid ""
"Check this box if you want to allow the cancellation the entries related to "
"this journal or of the invoice related to this journal"
msgstr ""
"Cochez cette case si vous voulez autoriser l'annulation des écritures ou des"
" factures associées à ce journal."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_price_include
#: model:ir.model.fields,help:account.field_account_tax_template_price_include
msgid ""
"Check this if the price you use on the product and invoices includes this "
"tax."
msgstr ""
"Cochez cette case si le prix que vous utilisez pour l'article et les "
"factures inclut cette taxe."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_reconcile
msgid ""
"Check this option if you want the user to reconcile entries in this account."
msgstr ""
"Cocher cette option si vous souhaitez que l'utilisateur lettre les écritures"
" dans ce compte."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Checks"
msgstr "Chèques"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_children_tax_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template_children_tax_ids
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_tax_form
msgid "Children Taxes"
msgstr "Sous taxes"

#. module: account
#: code:addons/account/models/chart_template.py:155
#: model:ir.actions.act_window,name:account.action_wizard_multi_chart
#, python-format
msgid "Choose Accounting Template"
msgstr "Choisissez un modèle comptable"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:131
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr "Choisissez la contrepartie ou créez une écriture"

#. module: account
#: model:ir.actions.act_window,help:account.action_account_bank_journal_form
msgid "Click to add a bank account."
msgstr "Cliquez pour ajouter un compte bancaire."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_journal_form
msgid "Click to add a journal."
msgstr "Cliquez pour ajouter un journal."

#. module: account
#: model:ir.actions.act_window,help:account.account_tag_action
msgid "Click to add a new tag."
msgstr "Cliquez sur pour ajouter une nouvelle étiquette."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_form
msgid "Click to add an account."
msgstr "Cliquer pour ajouter un compte."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_out_refund
msgid "Click to create a credit note."
msgstr "Cliquer pour ajouter un avoir."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree1
msgid "Click to create a customer invoice."
msgstr "Cliquez pour créer une facture client."

#. module: account
#: model:ir.actions.act_window,help:account.action_move_journal_line
msgid "Click to create a journal entry."
msgstr "Cliquez pour créer une écriture comptable."

#. module: account
#: model:ir.actions.act_window,help:account.action_view_bank_statement_tree
msgid "Click to create a new cash log."
msgstr "Cliquez pour saisir une entrée de trésorerie."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_reconcile_model
msgid "Click to create a reconciliation model."
msgstr "Cliquez pour créer un modèle de lettrage."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_type_form
msgid "Click to define a new account type."
msgstr "Cliquez pour définir un nouveau type de compte."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree2
msgid "Click to record a new vendor bill."
msgstr "Cliquez pour saisir une nouvelle facture fournisseur."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_in_refund
msgid "Click to record a new vendor credit note."
msgstr "Cliquez pour enregistrer un nouvel avoir fournisseur."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_tree
msgid "Click to register a bank statement."
msgstr "Cliquez pour saisir un nouveau relevé bancaire."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_payments
#: model:ir.actions.act_window,help:account.action_account_payments_payable
msgid "Click to register a payment"
msgstr "Cliquer pour enregistrer un règlement"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:260
#, python-format
msgid "Close"
msgstr "Fermer"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:60
#, python-format
msgid "Close statement"
msgstr "Fermer le relevé"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_date_done
msgid "Closed On"
msgstr "Fermé le"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_method_code
#: model:ir.model.fields,field_description:account.field_account_account_code
#: model:ir.model.fields,field_description:account.field_account_account_template_code
#: model:ir.model.fields,field_description:account.field_account_analytic_line_code
#: model:ir.model.fields,field_description:account.field_account_payment_method_code
#: model:ir.model.fields,field_description:account.field_account_payment_payment_method_code
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_method_code
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Code"
msgstr "Code"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group_code_prefix
msgid "Code Prefix"
msgstr "Préfixe du code"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_coin_value
msgid "Coin/Bill Value"
msgstr "Valeur de la pièce/billet"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Collect customer payments in one-click using Euro SEPA Service."
msgstr ""
"Obtenez le paiement de vos clients en un clic à l'aide du service Euro SEPA."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_color
#: model:ir.model.fields,field_description:account.field_account_journal_color
msgid "Color Index"
msgstr "Index de la couleur"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_label_filter
msgid "Column Label"
msgstr "Titre de la colonne"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_comment
msgid "Comment"
msgstr "Commentaires"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_commercial_partner_id
#: model:ir.model.fields,help:account.field_account_invoice_report_commercial_partner_id
msgid "Commercial Entity"
msgstr "Entité commerciale"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_common_menu
msgid "Common Report"
msgstr "Rapport"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Communication"
msgstr "Communication"

#. module: account
#: model:ir.model,name:account.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_ref_company_ids
#: model:ir.model.fields,field_description:account.field_res_users_ref_company_ids
msgid "Companies that refers to partner"
msgstr "Sociétés qui font référence au partenaire"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_company_id
#: model:ir.model.fields,field_description:account.field_account_account_company_id
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_company_id
#: model:ir.model.fields,field_description:account.field_account_balance_report_company_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_company_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_company_id
#: model:ir.model.fields,field_description:account.field_account_chart_template_company_id
#: model:ir.model.fields,field_description:account.field_account_common_account_report_company_id
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_company_id
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_company_id
#: model:ir.model.fields,field_description:account.field_account_common_report_company_id
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_company_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_company_id
#: model:ir.model.fields,field_description:account.field_account_journal_company_id
#: model:ir.model.fields,field_description:account.field_account_move_company_id
#: model:ir.model.fields,field_description:account.field_account_move_line_company_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_company_id
#: model:ir.model.fields,field_description:account.field_account_opening_company_id
#: model:ir.model.fields,field_description:account.field_account_payment_company_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_company_id
#: model:ir.model.fields,field_description:account.field_account_print_journal_company_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_company_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_company_id
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_company_id
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_company_id
#: model:ir.model.fields,field_description:account.field_account_tax_company_id
#: model:ir.model.fields,field_description:account.field_account_tax_report_company_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_company_id
#: model:ir.model.fields,field_description:account.field_accounting_report_company_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_company_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Company"
msgstr "Société"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line_company_currency_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_company_currency_id
msgid "Company Currency"
msgstr "Devise de la société"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:210
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:25
#, python-format
msgid "Company Data"
msgstr "Données sur la société"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_company_data_done
msgid "Company Setup Marked As Done"
msgstr "Configuration de la société marquée comme terminée"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_has_chart_of_accounts
msgid "Company has a chart of accounts"
msgstr "La société a un plan comptable"

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_company_id
#: model:ir.model.fields,help:account.field_account_bank_statement_company_id
#: model:ir.model.fields,help:account.field_account_bank_statement_line_company_id
#: model:ir.model.fields,help:account.field_account_journal_company_id
#: model:ir.model.fields,help:account.field_account_move_company_id
#: model:ir.model.fields,help:account.field_account_payment_company_id
#: model:ir.model.fields,help:account.field_account_register_payments_company_id
msgid "Company related to this journal"
msgstr "Société associée à ce journal"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Compare actual revenues &amp; costs with budgets"
msgstr "Comparez les revenus &amp; les coûts à l'aide des budgets"

#. module: account
#: model:ir.ui.view,arch_db:account.accounting_report_view
msgid "Comparison"
msgstr "Comparaison"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_complete_tax_set
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_complete_tax_set
msgid "Complete Set of Taxes"
msgstr "Complétez le jeu de taxes."

#. module: account
#: code:addons/account/models/account_invoice.py:575
#, python-format
msgid "Compose Email"
msgstr "Rédiger un email"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Compute tax rates based on U.S. ZIP codes"
msgstr "Calcul les taux de taxes sur base des codes postaux (pour les USA). "

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_counterpart
msgid ""
"Compute the counter part accounts of this journal item for this journal "
"entry. This can be needed in reports."
msgstr ""
"Calcule les comptes de contre-partie de cette pièce pour cette ligne "
"d'écriture. Des rapports peuvent en avoir besoin."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_balance_end
msgid "Computed Balance"
msgstr "Solde calculé"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_configuration
msgid "Configuration"
msgstr "Configuration"

#. module: account
#: code:addons/account/models/account_payment.py:643
#: code:addons/account/models/account_payment.py:645
#, python-format
msgid "Configuration Error !"
msgstr "Erreur de configuration !"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:29
#, python-format
msgid "Configuration Steps:"
msgstr "Étapes de configuration :"

#. module: account
#: code:addons/account/models/account_invoice.py:462
#, python-format
msgid ""
"Configuration error!\n"
"Could not find any account to create the invoice, are you sure you have a chart of account installed?"
msgstr ""
"Erreur de configuration !\n"
"Aucun compte trouvé pour la création de la facture. Veuillez vérifier qu'un plan de compte a bien été installé."

#. module: account
#: code:addons/account/models/account.py:443
#, python-format
msgid ""
"Configuration error!\n"
"The currency of the journal should be the same than the default credit account."
msgstr ""
"Erreur de configuration !\n"
"La devise de ce journal doit être la même que celle du compte de crédit par défaut."

#. module: account
#: code:addons/account/models/account.py:445
#, python-format
msgid ""
"Configuration error!\n"
"The currency of the journal should be the same than the default debit account."
msgstr ""
"Erreur de configuration!\n"
"La devise du journal devrait être la même que celle du compte de débit par défaut."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Configuration menu"
msgstr "Menu de configuration"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Configure"
msgstr "Configurer"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
#: model:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Confirm"
msgstr "Confirmer"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_confirm
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid "Confirm Draft Invoices"
msgstr "Confirmer des factures brouillon"

#. module: account
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid "Confirm Invoices"
msgstr "Confirmer les factures"

#. module: account
#: model:ir.actions.server,name:account.action_account_confirm_payments
msgid "Confirm Payments"
msgstr "Confirmer les paiements"

#. module: account
#: model:ir.model,name:account.model_account_invoice_confirm
msgid "Confirm the selected invoices"
msgstr "Confirmer les factures sélectionnées"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Confirmed"
msgstr "Confirmé"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
msgid ""
"Confirming this will create automatically a journal entry with the "
"difference in the profit/loss account set on the cash journal."
msgstr ""
"Confirmer ceci va automatiquement créer une ligne d'écriture avec la "
"différence dans le compte de perte et profit paramétré sur le journal de la "
"caisse"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:52
#, python-format
msgid "Congrats, you're all done!"
msgstr "Félicitations ! Vous avez terminé."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Congratulations, you're done!"
msgstr "Félicitations, vous avez terminé!"

#. module: account
#: model:ir.model,name:account.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: account
#: model:ir.model,name:account.model_account_abstract_payment
msgid ""
"Contains the logic shared between models which allows to register payments"
msgstr ""
"Contient la logique partagée entre les modèles qui permet d'enregistrer les "
"paiements"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_contract_ids
#: model:ir.model.fields,field_description:account.field_res_partner_contracts_count
#: model:ir.model.fields,field_description:account.field_res_users_contract_ids
#: model:ir.model.fields,field_description:account.field_res_users_contracts_count
msgid "Contracts"
msgstr "Contrats"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Control-Access"
msgstr "Contrôle des accès"

#. module: account
#: model:account.account.type,name:account.data_account_type_direct_costs
msgid "Cost of Revenue"
msgstr "Coût des ventes"

#. module: account
#: code:addons/account/models/chart_template.py:873
#, python-format
msgid ""
"Could not install new chart of account as there are already accounting "
"entries existing"
msgstr ""
"Impossible d'installer le nouveau plan comptable en raison d'écritures "
"comptables déjà existantes."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_counterpart
msgid "Counterpart"
msgstr "Contrepartie"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_account_id
msgid "Counterpart Account"
msgstr "Compte de contrepartie"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_country_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_country_id
msgid "Country"
msgstr "Pays"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_country_group_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_country_group_id
msgid "Country Group"
msgstr "Groupe de pays"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_country_id
msgid "Country of the Partner Company"
msgstr "Pays du partenaire"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Create Customers"
msgstr "Créer des clients"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Create Vendors"
msgstr "Créer des Fournisseurs"

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Create a draft credit note"
msgstr "Créer une ébauche d'avoir"

#. module: account
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Create and post move"
msgstr "Créer et publier un déplacement"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:288
#, python-format
msgid "Create cash statement"
msgstr "Créer un relevé de caisse"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:274
#, python-format
msgid "Create invoice/bill"
msgstr "Créer une facture"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:163
#, python-format
msgid "Create model"
msgstr "Créer le modèle"

#. module: account
#: model:ir.actions.act_window,help:account.rounding_list_action
msgid "Create the first cash rounding"
msgstr "Créer le premier arrondi d'espèces"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Create your products"
msgstr "Créez vos produits"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_create_uid
#: model:ir.model.fields,field_description:account.field_account_account_tag_create_uid
#: model:ir.model.fields,field_description:account.field_account_account_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_account_type_create_uid
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_create_uid
#: model:ir.model.fields,field_description:account.field_account_balance_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_create_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_account_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_financial_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_create_uid
#: model:ir.model.fields,field_description:account.field_account_group_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_create_uid
#: model:ir.model.fields,field_description:account.field_account_journal_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal_create_uid
#: model:ir.model.fields,field_description:account.field_account_opening_create_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal_create_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_create_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_register_payments_create_uid
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_create_uid
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile_create_uid
#: model:ir.model.fields,field_description:account.field_accounting_report_create_uid
#: model:ir.model.fields,field_description:account.field_cash_box_in_create_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out_create_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_create_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move_create_uid
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_create_uid
msgid "Created by"
msgstr "Créé par"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_create_date
#: model:ir.model.fields,field_description:account.field_account_account_tag_create_date
#: model:ir.model.fields,field_description:account.field_account_account_template_create_date
#: model:ir.model.fields,field_description:account.field_account_account_type_create_date
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_create_date
#: model:ir.model.fields,field_description:account.field_account_balance_report_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_create_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_create_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_create_date
#: model:ir.model.fields,field_description:account.field_account_chart_template_create_date
#: model:ir.model.fields,field_description:account.field_account_common_account_report_create_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_create_date
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_create_date
#: model:ir.model.fields,field_description:account.field_account_common_report_create_date
#: model:ir.model.fields,field_description:account.field_account_financial_report_create_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_create_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_create_date
#: model:ir.model.fields,field_description:account.field_account_group_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_line_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_create_date
#: model:ir.model.fields,field_description:account.field_account_journal_create_date
#: model:ir.model.fields,field_description:account.field_account_move_create_date
#: model:ir.model.fields,field_description:account.field_account_move_line_create_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_create_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_create_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal_create_date
#: model:ir.model.fields,field_description:account.field_account_opening_create_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_method_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_create_date
#: model:ir.model.fields,field_description:account.field_account_print_journal_create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_create_date
#: model:ir.model.fields,field_description:account.field_account_register_payments_create_date
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_create_date
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_group_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_report_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_template_create_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile_create_date
#: model:ir.model.fields,field_description:account.field_accounting_report_create_date
#: model:ir.model.fields,field_description:account.field_cash_box_in_create_date
#: model:ir.model.fields,field_description:account.field_cash_box_out_create_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_create_date
#: model:ir.model.fields,field_description:account.field_validate_account_move_create_date
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_create_date
msgid "Created on"
msgstr "Créé le"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_credit
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Credit"
msgstr "Crédit"

#. module: account
#: model:account.account.type,name:account.data_account_type_credit_card
msgid "Credit Card"
msgstr "Carte de crédit "

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_credit_cash_basis
msgid "Credit Cash Basis"
msgstr "Créditer la comptabilité de caisse"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_credit_move_id
msgid "Credit Move"
msgstr "Mouvement de crédit"

#. module: account
#: code:addons/account/models/account_invoice.py:441
#: code:addons/account/models/account_invoice.py:1216
#: code:addons/account/wizard/account_invoice_refund.py:111
#: model:ir.actions.act_window,name:account.action_account_invoice_refund
#: model:ir.model,name:account.model_account_invoice_refund
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#, python-format
msgid "Credit Note"
msgstr "Avoir"

#. module: account
#: code:addons/account/models/account_invoice.py:442
#, python-format
msgid "Credit Note - %s"
msgstr "Avoir - %s"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Credit Note Bill"
msgstr "Facture de l'avoir"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_date_invoice
msgid "Credit Note Date"
msgstr "Date de l'avoir"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_refund_sequence_id
msgid "Credit Note Entry Sequence"
msgstr "Séquence d'écriture de l'avoir"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Credit Notes"
msgstr "Avoirs"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_refund_sequence_number_next
msgid "Credit Notes: Next Number"
msgstr "Avoirs : numéro suivant"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_credit_account_id
msgid "Credit account"
msgstr "Compte créditeur"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_credit
msgid "Credit amount"
msgstr "Montant du crédit"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_matched_credit_ids
msgid "Credit journal items that are matched with this journal item."
msgstr "Pièces au crédit qui correspondent à cette pièce."

#. module: account
#: model:ir.ui.menu,name:account.menu_action_currency_form
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Currencies"
msgstr "Devises"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_currency_id
#: model:ir.model.fields,field_description:account.field_account_analytic_line_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_journal_currency_id
#: model:ir.model.fields,field_description:account.field_account_chart_template_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_currency_id
#: model:ir.model.fields,field_description:account.field_account_journal_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line_currency_id
#: model:ir.model.fields,field_description:account.field_account_opening_currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_company_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_currency_id
#: model:ir.model.fields,field_description:account.field_account_payment_currency_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_currency_id
#: model:ir.model.fields,field_description:account.field_res_config_settings_currency_id
#: model:ir.model.fields,field_description:account.field_res_partner_currency_id
#: model:ir.model.fields,field_description:account.field_res_users_currency_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_currency_id
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Currency"
msgstr "Devise"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_currency_rate
msgid "Currency Rate"
msgstr "Taux de la devise"

#. module: account
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_currency_id
msgid "Currency as per company's country."
msgstr "Devise selon le pays de la société."

#. module: account
#: code:addons/account/models/account_move.py:1663
#: code:addons/account/models/account_move.py:1675
#, python-format
msgid "Currency exchange rate difference"
msgstr "Différence de taux de change"

#. module: account
#: code:addons/account/models/account.py:451
#, python-format
msgid ""
"Currency field should only be set if the journal's currency is different "
"from the company's. Leave the field blank to use company currency."
msgstr ""
"Le chanp Devise ne doit être rempli que si le journal des devises est "
"différent de celui de la société. Laissez ce champ vide pour utiliser la "
"devise de la société."

#. module: account
#: model:account.account.type,name:account.data_account_type_current_assets
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Current Assets"
msgstr "Actifs circulants"

#. module: account
#: model:account.account.type,name:account.data_account_type_current_liabilities
msgid "Current Liabilities"
msgstr "Passif circulant"

#. module: account
#: model:account.account.type,name:account.data_unaffected_earnings
msgid "Current Year Earnings"
msgstr "Bénéfices de l'année en cours"

#. module: account
#: selection:account.abstract.payment,partner_type:0
#: selection:account.payment,partner_type:0
#: selection:account.register.payments,partner_type:0
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_payment_tree
msgid "Customer"
msgstr "Client"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_payment.py:677
#, python-format
msgid "Customer Credit Note"
msgstr "Avoir du client"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_out_refund
#: model:ir.ui.menu,name:account.menu_action_invoice_out_refund
msgid "Customer Credit Notes"
msgstr "Avoirs des clients"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Customer Flow"
msgstr "Flux client"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
msgid "Customer Invoice"
msgstr "Facture client"

#. module: account
#: code:addons/account/models/chart_template.py:193
#: model:ir.actions.act_window,name:account.action_invoice_tree1
#: model:ir.ui.menu,name:account.menu_action_invoice_tree1
#: model:ir.ui.view,arch_db:account.account_planner
#, python-format
msgid "Customer Invoices"
msgstr "Factures clients"

#. module: account
#: code:addons/account/models/account_payment.py:675
#, python-format
msgid "Customer Payment"
msgstr "Paiement client"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_payment_term_id
#: model:ir.model.fields,field_description:account.field_res_users_property_payment_term_id
msgid "Customer Payment Terms"
msgstr "Conditions de Paiement Client"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Customer Payments"
msgstr "Paiements client"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_product_taxes_id
#: model:ir.model.fields,field_description:account.field_product_template_taxes_id
msgid "Customer Taxes"
msgstr "Taxes à la vente"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Customer ref:"
msgstr "Réf. du client :"

#. module: account
#: model:ir.ui.menu,name:account.menu_account_customer
msgid "Customers"
msgstr "Clients"

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "DOWN"
msgstr "BAS"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/account_dashboard_setup_bar.js:214
#: model:ir.ui.menu,name:account.menu_board_journal_1
#, python-format
msgid "Dashboard"
msgstr "Tableau de bord"

#. module: account
#. openerp-web
#: selection:account.print.journal,sort_selection:0
#: selection:account.report.general.ledger,sortby:0
#: selection:accounting.report,filter_cmp:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:233
#: code:addons/account/static/src/xml/account_reconciliation.xml:248
#: model:ir.model.fields,field_description:account.field_account_bank_statement_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_date
#: model:ir.model.fields,field_description:account.field_account_invoice_report_date
#: model:ir.model.fields,field_description:account.field_account_move_date
#: model:ir.model.fields,field_description:account.field_account_move_line_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_date_p
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_date
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_overdue_document
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Date"
msgstr "Date "

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_opening_date
msgid ""
"Date at which the opening entry of this company's accounting has been "
"posted."
msgstr ""
"Date à laquelle l'écriture d'ouverture concernant la comptabilité de cette "
"société a été enregistrée."

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op_opening_date
msgid ""
"Date from which the accounting is managed in Odoo. It is the date of the "
"opening entry."
msgstr ""
"Date à compter de laquelle la comptabilité est gérée dans Odoo. Il s'agit de"
" la date de l'écriture d'ouverture."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:56
#, python-format
msgid "Date:"
msgstr "Date :"

#. module: account
#: model:ir.ui.view,arch_db:account.accounting_report_view
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Dates"
msgstr "Dates"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Day(s) after the end of the invoice month (Net EOM)"
msgstr "Jour(s) après expiration du mois de facturation (Net EOM)"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Day(s) after the invoice date"
msgstr "Jour(s) après la date de facturation"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Deactivate setup bar on the dashboard"
msgstr "Désactiver la barre de configuration du tableau de bord"

#. module: account
#: code:addons/account/models/company.py:45
#, python-format
msgid ""
"Dear Sir/Madam,\n"
"\n"
"Our records indicate that some payments on your account are still due. Please find details below.\n"
"If the amount has already been paid, please disregard this notice. Otherwise, please forward us the total amount stated below.\n"
"If you have any queries regarding your account, Please contact us.\n"
"\n"
"Thank you in advance for your cooperation.\n"
"Best Regards,"
msgstr ""
"Madame, Monsieur,\n"
"\n"
"D'après nos relevés, il semble que nous sommes encore en attente à ce jour de paiements de votre part, dont les détails sont indiqués ci-dessous.\n"
"Si ces sommes ont déjà été réglées, vous pouvez ignorer ce rappel. Dans le cas contraire, nous vous remercions de bien vouloir nous faire parvenir votre règlement.\n"
"Si vous avez d'autres questions concernant votre compte, vous pouvez nous contacter directement.\n"
"\n"
"En vous remerciant par avance.\n"
"\n"
"Cordialement,"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_debit
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Debit"
msgstr "Débit"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_debit_cash_basis
msgid "Debit Cash Basis"
msgstr "Trésorerie de débit"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_inbound_payment_method_ids
msgid "Debit Methods"
msgstr "Méthodes de débit"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_debit_move_id
msgid "Debit Move"
msgstr "Mouvement de débit"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_debit_account_id
msgid "Debit account"
msgstr "Compte débiteur"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_debit
msgid "Debit amount"
msgstr "Montant Débit"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_matched_debit_ids
msgid "Debit journal items that are matched with this journal item."
msgstr ""
"Éléments du journal de débit qui correspondent à cet élément du journal."

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "December"
msgstr "décembre"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_refund_sequence
msgid "Dedicated Credit Note Sequence"
msgstr "Séquence dédiée aux avoirs"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_default_credit_account_id
msgid "Default Credit Account"
msgstr "Compte de crédit par défaut"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_default_debit_account_id
msgid "Default Debit Account"
msgstr "Compte de débit par défaut"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_default_purchase_tax_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_purchase_tax_id
msgid "Default Purchase Tax"
msgstr "Taxe d'achat par défaut"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_default_sale_tax_id
msgid "Default Sale Tax"
msgstr "Taxe de vente par défaut"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_sale_tax_id
msgid "Default Sales Tax"
msgstr "Taxes à la vente par défaut"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tax_ids
#: model:ir.model.fields,field_description:account.field_account_account_template_tax_ids
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_account_chart_template_form
#: model:ir.ui.view,arch_db:account.view_account_template_form
msgid "Default Taxes"
msgstr "Taxes par défaut"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Default taxes applied to local transactions"
msgstr "Taxes appliquées par défaut aux transactions nationales"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Deferred Revenues Management"
msgstr "Gestion des revenus différés"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash."
msgstr ""
"Définissez la plus petite monnaie de la devise utilisée pour payer en "
"espèces."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_cash_rounding_id
msgid ""
"Defines the smallest coinage of the currency that can be used to pay by "
"cash."
msgstr ""
"Définit la plus petite monnaie de la devise pouvant être utilisée pour payer"
" en espèces."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_tax_form
msgid "Definition"
msgstr "Définition"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_trust
#: model:ir.model.fields,field_description:account.field_res_users_trust
msgid "Degree of trust you have in this debtor"
msgstr "Niveau de confiance que vous avez avec ce débiteur"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_deprecated
msgid "Deprecated"
msgstr "Obsolète"

#. module: account
#: model:account.account.type,name:account.data_account_type_depreciation
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Depreciation"
msgstr "Amortissement"

#. module: account
#: model:ir.ui.view,arch_db:account.cash_box_out_form
msgid "Describe why you take money from the cash register:"
msgstr ""
"Expliquez la raison pour laquelle vous retirez de l'argent de la caisse :"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:251
#: model:ir.model.fields,field_description:account.field_account_account_type_note
#: model:ir.model.fields,field_description:account.field_account_invoice_line_name
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_account_type_form
#, python-format
msgid "Description"
msgstr "Description"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_note
msgid "Description on the Invoice"
msgstr "Description sur la facture"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_destination_account_id
msgid "Destination Account"
msgstr "Compte de destination"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_auto_apply
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_auto_apply
msgid "Detect Automatically"
msgstr "Détecter automatiquement"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_template_type_tax_use
#: model:ir.model.fields,help:account.field_account_tax_type_tax_use
msgid ""
"Determines where the tax is selectable. Note : 'None' means a tax can't be "
"used by itself, however it can still be used in a group."
msgstr ""
"Détermine où la taxe est sélectionnable. Remarque : 'None' signifie que la "
"taxe ne peut pas être utilisée toute seule, mais elle peut être utilisée "
"dans un groupe."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_difference
msgid "Difference"
msgstr "Différence"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_writeoff_account_id
msgid "Difference Account"
msgstr "Compte pour la différence"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_difference
msgid ""
"Difference between the computed ending balance and the specified ending "
"balance."
msgstr "Différence entre le solde final calculé et le solde final spécifié."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Direct connection to your bank"
msgstr "connexion directe à votre banque ;"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Disc.(%)"
msgstr "Rem.(%)"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_wizard_form
msgid "Discard"
msgstr "Annuler"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_discount
msgid "Discount (%)"
msgstr "Remise (%)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_balance_report_display_account
#: model:ir.model.fields,field_description:account.field_account_common_account_report_display_account
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_display_account
msgid "Display Accounts"
msgstr "Afficher les comptes"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_debit_credit
msgid "Display Debit/Credit Columns"
msgstr "Afficher les colonnes de débit/crédit"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_display_name
#: model:ir.model.fields,field_description:account.field_account_account_display_name
#: model:ir.model.fields,field_description:account.field_account_account_tag_display_name
#: model:ir.model.fields,field_description:account.field_account_account_template_display_name
#: model:ir.model.fields,field_description:account.field_account_account_type_display_name
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_display_name
#: model:ir.model.fields,field_description:account.field_account_balance_report_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_display_name
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_display_name
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_display_name
#: model:ir.model.fields,field_description:account.field_account_chart_template_display_name
#: model:ir.model.fields,field_description:account.field_account_common_account_report_display_name
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_display_name
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_display_name
#: model:ir.model.fields,field_description:account.field_account_common_report_display_name
#: model:ir.model.fields,field_description:account.field_account_financial_report_display_name
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_display_name
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_display_name
#: model:ir.model.fields,field_description:account.field_account_group_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_line_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_report_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_display_name
#: model:ir.model.fields,field_description:account.field_account_journal_display_name
#: model:ir.model.fields,field_description:account.field_account_move_display_name
#: model:ir.model.fields,field_description:account.field_account_move_line_display_name
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_display_name
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_display_name
#: model:ir.model.fields,field_description:account.field_account_move_reversal_display_name
#: model:ir.model.fields,field_description:account.field_account_opening_display_name
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_method_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_term_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_display_name
#: model:ir.model.fields,field_description:account.field_account_print_journal_display_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_display_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_display_name
#: model:ir.model.fields,field_description:account.field_account_register_payments_display_name
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_display_name
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_group_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_report_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_template_display_name
#: model:ir.model.fields,field_description:account.field_account_unreconcile_display_name
#: model:ir.model.fields,field_description:account.field_accounting_report_display_name
#: model:ir.model.fields,field_description:account.field_cash_box_in_display_name
#: model:ir.model.fields,field_description:account.field_cash_box_out_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_financial_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_generalledger_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_journal_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_overdue_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_partnerledger_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_tax_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_trialbalance_display_name
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_display_name
#: model:ir.model.fields,field_description:account.field_validate_account_move_display_name
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_display_name
msgid "Display Name"
msgstr "Nom à afficher"

#. module: account
#: selection:account.financial.report,display_detail:0
msgid "Display children flat"
msgstr "Afficher les enfants"

#. module: account
#: selection:account.financial.report,display_detail:0
msgid "Display children with hierarchy"
msgstr "Affiche les enregistrements enfants dans une vue hiérarchique"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_display_detail
msgid "Display details"
msgstr "Affiche les détails"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_description
msgid "Display on Invoices"
msgstr "Affichage sur les factures"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_print_docsaway
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Docsaway"
msgstr "Docsaway"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid ""
"Document: Customer account statement<br/>\n"
"                    Date:"
msgstr ""
"Objet : Relevé de compte client<br/>\n"
"Date :"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_payables_documents
#: model:ir.ui.menu,name:account.menu_finance_receivables_documents
msgid "Documents"
msgstr "Documents"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Don't hesitate to"
msgstr "Ne pas hésiter à"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_page
msgid "Download"
msgstr "Télécharger"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Download the"
msgstr "Télécharger le"

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: selection:account.payment,state:0
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Draft"
msgstr "Brouillon"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Draft Bill"
msgstr "Ébauche de facture"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Draft Credit Note"
msgstr "Ébauche d'avoir"

#. module: account
#: code:addons/account/models/account_invoice.py:439
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Draft Invoice"
msgstr "Facture en brouillon"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Draft Invoices"
msgstr "Factures en brouillon"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Draft bills"
msgstr "Factures en brouillon"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_draft_tree
msgid "Draft statements"
msgstr "Relevés brouillon"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Due"
msgstr "Dûe"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_residual
msgid "Due Amount"
msgstr "Montant Dû"

#. module: account
#. openerp-web
#: code:addons/account/controllers/portal.py:71
#: code:addons/account/static/src/xml/account_reconciliation.xml:234
#: model:ir.model.fields,field_description:account.field_account_invoice_date_due
#: model:ir.model.fields,field_description:account.field_account_invoice_report_date_due
#: model:ir.ui.view,arch_db:account.portal_my_invoices
#: model:ir.ui.view,arch_db:account.report_overdue_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Due Date"
msgstr "Date d'échéance"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Due Date Computation"
msgstr "Calcul de la date d'échéance"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Due Month"
msgstr "Mois d'échéance"

#. module: account
#: model:ir.actions.report,name:account.action_report_print_overdue
msgid "Due Payments"
msgstr "Paiements en retard"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_tree
msgid "Due Type"
msgstr "Type d'échéance"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_date_maturity
msgid "Due date"
msgstr "Date d'échéance"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Duplicate"
msgstr "Dupliquer"

#. module: account
#: code:addons/account/models/account_invoice.py:1194
#, python-format
msgid ""
"Duplicated vendor reference detected. You probably encoded twice the same "
"vendor bill/credit note."
msgstr ""
"Doublon de référence fournisseur détecté. Vous avez probablement saisi deux "
"fois la même facture/le même avoir fournisseur."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_reports
msgid "Dynamic Reports"
msgstr "Rapports dynamiques"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_l10n_eu_service
msgid "EU Digital Goods VAT"
msgstr "TVA sur les produits numériques dans l'UE"

#. module: account
#: code:addons/account/models/chart_template.py:171
#: code:addons/account/models/chart_template.py:186
#: code:addons/account/models/chart_template.py:196
#, python-format
msgid "EXCH"
msgstr "EXCH"

#. module: account
#: code:addons/account/models/account_move.py:1052
#, python-format
msgid "Either pass both debit and credit or none."
msgstr "Soit passer le crédit et le débit soit aucun."

#. module: account
#: model:ir.model,name:account.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Assistant de composition de email"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_enable_filter
msgid "Enable Comparison"
msgstr "Activer le comparatif"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "End"
msgstr "Fin"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_date_to
#: model:ir.model.fields,field_description:account.field_account_balance_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_account_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_report_date_to
#: model:ir.model.fields,field_description:account.field_account_print_journal_date_to
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_date_to
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_date_to
#: model:ir.model.fields,field_description:account.field_account_tax_report_date_to
#: model:ir.model.fields,field_description:account.field_accounting_report_date_to
#: model:ir.model.fields,field_description:account.field_accounting_report_date_to_cmp
msgid "End Date"
msgstr "Date de fin"

#. module: account
#: model:account.payment.term,name:account.account_payment_term
msgid "End of Following Month"
msgstr "Fin du mois suivant"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_balance_end_real
msgid "Ending Balance"
msgstr "Solde final"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_end_id
msgid "Ending Cashbox"
msgstr "Fermeture de la Caisse"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Enjoy your Odoo experience,"
msgstr "Profitez de votre expérience Odoo,"

#. module: account
#: model:ir.actions.act_window,name:account.action_move_line_form
msgid "Entries"
msgstr "Ecritures"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_print_journal_sort_selection
msgid "Entries Sorted by"
msgstr "Écritures triées par"

#. module: account
#: code:addons/account/models/account_move.py:1019
#, python-format
msgid "Entries are not of the same account!"
msgstr "Les lignes n'ont pas le même compte!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Entries to Review"
msgstr "Écritures à vérifier"

#. module: account
#: code:addons/account/models/account_analytic_line.py:58
#, python-format
msgid "Entries: "
msgstr "Écritures : "

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "Entry Label"
msgstr "Libellé de la pièce comptable"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_sequence_id
msgid "Entry Sequence"
msgstr "Séquence d'écriture"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_move_line_ids
msgid "Entry lines"
msgstr "Lignes d'écritures"

#. module: account
#: model:account.account.type,name:account.data_account_type_equity
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Equity"
msgstr "Capitaux propres"

#. module: account
#: code:addons/account/models/res_config_settings.py:132
#, python-format
msgid "Error!"
msgstr "Erreur !"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Excel template"
msgstr "Modèle Excel"

#. module: account
#: code:addons/account/models/chart_template.py:196
#, python-format
msgid "Exchange Difference"
msgstr "Différence de change"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_currency_exchange_journal_id
#: model:ir.model.fields,field_description:account.field_res_config_settings_currency_exchange_journal_id
msgid "Exchange Gain or Loss Journal"
msgstr "Journal pour le gain ou la perte de change"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_exchange_move_id
msgid "Exchange Move"
msgstr "Mouvement de change"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Exchange rates can be automatically updated once a day from <strong>Yahoo "
"Finance</strong> or the <strong>European Central Bank</strong>. You can "
"activate this feature in the bottom of the"
msgstr ""
"Le taux de change peut-être automatiquement mis à jour une fois par jour "
"depuis <strong>Yahoo Finance</strong>ou la <strong>Banque centrale "
"européenne</strong>. Vous pouvez activer cette option en bas du"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_expects_chart_of_accounts
msgid "Expects a Chart of Accounts"
msgstr "Plan comptable attendu"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_expense0
msgid "Expense"
msgstr "Dépense"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_category_property_account_expense_categ_id
#: model:ir.model.fields,field_description:account.field_product_product_property_account_expense_id
#: model:ir.model.fields,field_description:account.field_product_template_property_account_expense_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Expense Account"
msgstr "Compte de dépenses"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_expense_id
msgid "Expense Account on Product Template"
msgstr "Compte de dépense sur les modèles d'article"

#. module: account
#: model:account.account.type,name:account.data_account_type_expenses
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Expenses"
msgstr "Notes de frais"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_reference
msgid "External Reference"
msgstr "Référence externe"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Extra Features"
msgstr "Fonctionnalités additionnelles"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Favorites"
msgstr "Favoris"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "February"
msgstr "Février"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_state_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_state_ids
msgid "Federal States"
msgstr "États fédéraux"

#. module: account
#: selection:account.journal,bank_statements_source:0
msgid "File Import"
msgstr "Importation de fichier"

#. module: account
#: model:ir.ui.view,arch_db:account.cash_box_in_form
msgid "Fill in this form if you put money in the cash register:"
msgstr "Remplissez ce formulaire si vous ajoutez de l'argent en caisse :"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_filter_cmp
msgid "Filter by"
msgstr "Filtrer par"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:141
#, python-format
msgid "Filter..."
msgstr "Filtrer..."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_general_account_id
#: model:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
msgid "Financial Account"
msgstr "Compte financier"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_style_overwrite
msgid "Financial Report Style"
msgstr "Style des rapports financiers"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_financial_report_tree
#: model:ir.actions.act_window,name:account.action_account_report
#: model:ir.ui.menu,name:account.menu_account_reports
msgid "Financial Reports"
msgstr "Rapports financiers"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_fy_data_done
msgid "Financial Year Setup Marked As Done"
msgstr "Configuration de l'exercice fiscal marquée comme terminée"

#. module: account
#: model:ir.actions.report,name:account.action_report_financial
msgid "Financial report"
msgstr "Rapport financier"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_account_setup_fy_data_done
msgid "Financial year setup marked as done"
msgstr "Configuration de l'exercice fiscal marquée comme terminée"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "First, register any outstanding customer invoices and vendor bills:"
msgstr ""
"D'abord, enregistrer toute facture client ou fournisseur en circulation :"

#. module: account
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Fiscal Information"
msgstr "Information fiscale"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Fiscal Localization"
msgstr "Localisation Comptable"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_position_id
msgid "Fiscal Mapping"
msgstr "Correspondance fiscale"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Fiscal Periods"
msgstr "Périodes fiscales"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_fiscal_position_template_form
#: model:ir.model,name:account.model_account_fiscal_position
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_position_id
#: model:ir.model.fields,field_description:account.field_account_invoice_fiscal_position_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_fiscal_position_id
#: model:ir.model.fields,field_description:account.field_res_partner_property_account_position_id
#: model:ir.model.fields,field_description:account.field_res_users_property_account_position_id
#: model:ir.ui.view,arch_db:account.view_account_position_form
#: model:ir.ui.view,arch_db:account.view_account_position_template_search
#: model:ir.ui.view,arch_db:account.view_account_position_template_tree
#: model:ir.ui.view,arch_db:account.view_account_position_tree
msgid "Fiscal Position"
msgstr "Position Fiscale"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_name
#: model:ir.ui.view,arch_db:account.view_account_position_template_form
#: model:ir.ui.view,arch_db:account.view_account_position_template_search
msgid "Fiscal Position Template"
msgstr "Modèles des positions fiscales"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_fiscal_position_form
#: model:ir.ui.menu,name:account.menu_action_account_fiscal_position_form
msgid "Fiscal Positions"
msgstr "Positions fiscales"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:251
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:17
#, python-format
msgid "Fiscal Year"
msgstr "Exercice fiscal"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_fiscalyear_last_day
#: model:ir.model.fields,field_description:account.field_res_company_fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "Dernier jour de l'exercice fiscal"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_fiscalyear_last_month
#: model:ir.model.fields,field_description:account.field_res_company_fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "Dernier mois de l'exercice fiscal"

#. module: account
#: selection:account.reconcile.model,amount_type:0
#: selection:account.reconcile.model,second_amount_type:0
#: selection:account.reconcile.model.template,amount_type:0
#: selection:account.reconcile.model.template,second_amount_type:0
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Fixed"
msgstr "Fixé"

#. module: account
#: selection:account.payment.term.line,value:0
msgid "Fixed Amount"
msgstr "Montant fixe"

#. module: account
#: model:account.account.type,name:account.data_account_type_fixed_assets
msgid "Fixed Assets"
msgstr "Immobilisations"

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_second_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template_second_amount
msgid ""
"Fixed amount will count as a debit if it is negative, as a credit if it is "
"positive."
msgstr ""
"Le montant fixe comptera comme un débit si c'est négatif, comme un crédit si"
" c'est positif."

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_receivables_follow_up
msgid "Follow-up"
msgstr "Suivi"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_reports_followup
msgid "Follow-up Levels"
msgstr "Niveaux de relances"

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_report_sign
msgid ""
"For accounts that are typically more debited than credited and that you "
"would like to print as negative amounts in your reports, you should reverse "
"the sign of the balance; e.g.: Expense account. The same applies for "
"accounts that are typically more credited than debited and that you would "
"like to print as positive amounts in your reports; e.g.: Income account."
msgstr ""
"Pour les comptes qui sont généralement plus débité à crédit et que vous "
"souhaitez imprimer que les montants négatifs dans vos rapports, vous devez "
"inverser le signe de l'équilibre, par exemple: compte de charges. La même "
"chose s'applique pour les comptes qui sont généralement plus crédité que "
"débité et que vous souhaitez imprimer comme des montants positifs dans vos "
"rapports, par exemple: Compte de produits."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line_value_amount
msgid "For percent enter a ratio between 0-100."
msgstr "Pour le pourcentage, saisissez une valeur entre 0 et 100."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"For the Odoo Team,<br/>\n"
"                            Fabien Pinckaers, Founder"
msgstr ""
"Au nom de l'équipe Odoo,<br/>\n"
"                            Fabien Pinckaers, fondateur"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_currency_id
msgid "Forces all moves for this account to have this account currency."
msgstr ""
"Oblige toutes les écritures de ce compte à avoir cette devise secondaire."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_currency_id
#: model:ir.model.fields,help:account.field_account_bank_accounts_wizard_currency_id
msgid "Forces all moves for this account to have this secondary currency."
msgstr ""
"Oblige toutes les écritures de ce compte à avoir cette devise secondaire."

#. module: account
#: code:addons/account/report/account_aged_partner_balance.py:205
#: code:addons/account/report/account_balance.py:64
#: code:addons/account/report/account_general_ledger.py:114
#: code:addons/account/report/account_journal.py:100
#: code:addons/account/report/account_partner_ledger.py:74
#: code:addons/account/report/account_report_financial.py:149
#: code:addons/account/report/account_tax.py:13
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr ""
"Le contenu du formulaire est manquant, le rapport ne peut pas être imprimé. "

#. module: account
#: code:addons/account/models/account_invoice.py:96
#, python-format
msgid "Free Reference"
msgstr "Référence libre"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "From Payable accounts"
msgstr "Depuis les comptes fournisseurs"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "From Receivable accounts"
msgstr "Depuis les comptes débiteurs"

#. module: account
#: model:ir.actions.act_window,help:account.action_account_invoice_report_all_supp
msgid ""
"From this report, you can have an overview of the amount invoiced from your "
"vendors. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"Dans ce rapport, vous avez un aperçu du montant facturé par vos "
"fournisseurs. L'outil de recherche peut aussi être utilisé pour "
"personnaliser  les rapports de facturation afin qu'ils soient adaptés à vos "
"besoins."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_invoice_report_all
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customers. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"À partir de ce rapport, vous avez un aperçu du montant facturé à votre "
"client. L'outil de recherche peut aussi être utilisé pour personnaliser "
"l'analyse des factures, et ainsi mieux correspondre à vos besoins."

#. module: account
#: model:ir.model,name:account.model_account_full_reconcile
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_full_reconcile_id
msgid "Full Reconcile"
msgstr "Lettrage complet"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:113
#, python-format
msgid "Future"
msgstr "Future"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Future Activities"
msgstr "Activités futures"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "GROSS PROFIT"
msgstr "BÉNÉFICE BRUT"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_income_currency_exchange_account_id
#: model:ir.model.fields,field_description:account.field_res_company_income_currency_exchange_account_id
msgid "Gain Exchange Rate Account"
msgstr "Compte de gain de change"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_general_ledger_menu
#: model:ir.actions.report,name:account.action_report_general_ledger
#: model:ir.ui.menu,name:account.menu_general_ledger
msgid "General Ledger"
msgstr "Grand livre"

#. module: account
#: model:ir.model,name:account.model_account_report_general_ledger
msgid "General Ledger Report"
msgstr "Grand livre"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_generate_entries
msgid "Generate Entries"
msgstr "Générer les lignes d'écritures"

#. module: account
#: model:ir.ui.menu,name:account.account_reports_legal_statements_menu
msgid "Generic Statements"
msgstr "Relevés génériques"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Get started"
msgstr "Démarrer"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Get warnings when invoicing specific customers"
msgstr "Recevez des avertissements en facturant des clients spécifiques"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Get your bank statements automatically imported every 4 hours, or in one-"
"click, using Yodlee and Plaid services. Once installed, set “Bank Feeds” to "
"“Bank Synchronization” in bank account settings. Then, click “Configure” on "
"the online account to enter your bank credentials."
msgstr ""
"Importez vos relevés bancaires automatiquement toutes les 4 heures, ou en un"
" clique, en utilisant les services Yodlee et Plaid. Une fois installé, "
"configurez les \"Relevés Bancaires\" en \"Synchronisation bancaire\" dans "
"les paramètres du compte bancaire. Ensuite, cliquez sur \"Configurer\" sur "
"le compte en ligne pour entrer vos identifiants."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_sequence
msgid "Gives the sequence of this line when displaying the invoice."
msgstr "Donne la séquence de cette ligne lors de l'affichage de la facture"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_sequence
msgid ""
"Gives the sequence order when displaying a list of bank statement lines."
msgstr "Donne l'ordre d'affichage d'une liste de lignes de relevés bancaires."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_tax_sequence
msgid "Gives the sequence order when displaying a list of invoice tax."
msgstr ""
"Détermine l'ordre de séquence dans lequel sont affichés les lignes de taxes "
"des factures"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line_sequence
msgid ""
"Gives the sequence order when displaying a list of payment terms lines."
msgstr ""
"Définit l'ordre utilisé pour afficher une liste de conditions de paiement."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:57
#, python-format
msgid "Go to bank statement(s)"
msgstr "Voir les relevés bancaires"

#. module: account
#: code:addons/account/models/account_invoice.py:642
#, python-format
msgid "Go to the configuration panel"
msgstr "Aller sur le panneau de configuration"

#. module: account
#: selection:res.partner,trust:0
msgid "Good Debtor"
msgstr "Bon débiteur"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:46
#, python-format
msgid "Good Job!"
msgstr "Bravo !"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_group_id
#: model:ir.model.fields,field_description:account.field_account_account_template_group_id
msgid "Group"
msgstr "Groupe"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_search
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#: model:ir.ui.view,arch_db:account.view_account_template_search
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Group By"
msgstr "Regrouper par"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_group_invoice_lines
msgid "Group Invoice Lines"
msgstr "Grouper les lignes de facture"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Group of Taxes"
msgstr "Groupe de taxes"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Group received checks before depositing them to the bank"
msgstr "Le groupe a reçu des chèques et les a déposés à la banque"

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "HALF-UP"
msgstr "ARRONDIR À L'ENTIER SUPÉRIEUR"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_has_accounting_entries
msgid "Has Accounting Entries"
msgstr "Présente des écritures comptables"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_has_invoices
msgid "Has Invoices"
msgstr "Présente des factures"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_has_outstanding
msgid "Has Outstanding"
msgstr "Présente des encours"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_has_unreconciled_entries
#: model:ir.model.fields,field_description:account.field_res_users_has_unreconciled_entries
msgid "Has Unreconciled Entries"
msgstr "Présente des écritures non rapprochées"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_hide_payment_method
#: model:ir.model.fields,field_description:account.field_account_payment_hide_payment_method
#: model:ir.model.fields,field_description:account.field_account_register_payments_hide_payment_method
msgid "Hide Payment Method"
msgstr "Masquer la méthode de paiement"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_account_hide_setup_bar
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Hide Setup Bar"
msgstr "Masquer la barre de configuration"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_hide_tax_exigibility
msgid "Hide Use Cash Basis Option"
msgstr "Masquer l'option d'utilisation de la trésorerie"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "How do account types affect your reports?"
msgstr "En quoi les types de compte affectent-ils vos rapports ?"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "How total tax amount is computed in orders and invoices"
msgstr ""
"Définissez comment le montant total des taxes est calculé sur les commandes "
"et factures"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_id
#: model:ir.model.fields,field_description:account.field_account_account_id
#: model:ir.model.fields,field_description:account.field_account_account_tag_id
#: model:ir.model.fields,field_description:account.field_account_account_template_id
#: model:ir.model.fields,field_description:account.field_account_account_type_id
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_id
#: model:ir.model.fields,field_description:account.field_account_balance_report_id
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_id
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_id
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_id
#: model:ir.model.fields,field_description:account.field_account_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_common_account_report_id
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_id
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_id
#: model:ir.model.fields,field_description:account.field_account_common_report_id
#: model:ir.model.fields,field_description:account.field_account_financial_report_id
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_id
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_id
#: model:ir.model.fields,field_description:account.field_account_group_id
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_id
#: model:ir.model.fields,field_description:account.field_account_invoice_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_id
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_id
#: model:ir.model.fields,field_description:account.field_account_journal_id
#: model:ir.model.fields,field_description:account.field_account_move_id
#: model:ir.model.fields,field_description:account.field_account_move_line_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_id
#: model:ir.model.fields,field_description:account.field_account_move_reversal_id
#: model:ir.model.fields,field_description:account.field_account_opening_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_id
#: model:ir.model.fields,field_description:account.field_account_payment_id
#: model:ir.model.fields,field_description:account.field_account_payment_method_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_id
#: model:ir.model.fields,field_description:account.field_account_print_journal_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_id
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_id
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_id
#: model:ir.model.fields,field_description:account.field_account_tax_group_id
#: model:ir.model.fields,field_description:account.field_account_tax_id
#: model:ir.model.fields,field_description:account.field_account_tax_report_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_id
#: model:ir.model.fields,field_description:account.field_account_unreconcile_id
#: model:ir.model.fields,field_description:account.field_accounting_report_id
#: model:ir.model.fields,field_description:account.field_cash_box_in_id
#: model:ir.model.fields,field_description:account.field_cash_box_out_id
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance_id
#: model:ir.model.fields,field_description:account.field_report_account_report_financial_id
#: model:ir.model.fields,field_description:account.field_report_account_report_generalledger_id
#: model:ir.model.fields,field_description:account.field_report_account_report_journal_id
#: model:ir.model.fields,field_description:account.field_report_account_report_overdue_id
#: model:ir.model.fields,field_description:account.field_report_account_report_partnerledger_id
#: model:ir.model.fields,field_description:account.field_report_account_report_tax_id
#: model:ir.model.fields,field_description:account.field_report_account_report_trialbalance_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_id
#: model:ir.model.fields,field_description:account.field_validate_account_move_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_id
msgid "ID"
msgstr "ID"

#. module: account
#: code:addons/account/models/chart_template.py:193
#, python-format
msgid "INV"
msgstr "FAC"

#. module: account
#: code:addons/account/models/account_bank_statement.py:389
#, python-format
msgid "If \"Amount Currency\" is specified, then \"Amount\" must be as well."
msgstr "Si \"Montant en devise\" est spécifié alors 'Montant' doit l'être aussi."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_nocreate
msgid ""
"If checked, the new chart of accounts will not contain this by default."
msgstr ""
"Si cette case est cochée, le nouveau plan comptable ne contiendra pas ce "
"compte par défaut."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_reversal_journal_id
msgid "If empty, uses the journal of the journal entry to be reversed."
msgstr "Si vide, utilise le journal de la pièce à extourner."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_include_base_amount
#: model:ir.model.fields,help:account.field_account_tax_template_include_base_amount
msgid ""
"If set, taxes which are computed after this one will be computed based on "
"the price tax included."
msgstr ""
"Si paramétré, les taxes qui sont calculées après celle-ci seront calculées "
"selon le prix taxe incluse."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_analytic
#: model:ir.model.fields,help:account.field_account_tax_template_analytic
msgid ""
"If set, the amount computed by this tax will be assigned to the same "
"analytic account as the invoice line (if any)"
msgstr ""
"Si vous paramétrez cette option, le montant calculé par cette taxe sera "
"attribué au même compte analytique que la ligne de facture (le cas échéant)"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_active
msgid ""
"If the active field is set to False, it will allow you to hide the payment "
"terms without removing it."
msgstr ""
"Si le champ actif est défini sur Faux, vous pourrez masquer les conditions "
"de paiement sans les supprimer."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_group_invoice_lines
msgid ""
"If this box is checked, the system will try to group the accounting lines "
"when generating them from invoices."
msgstr ""
"Si cette case est cochée, le système essaiera de grouper les lignes "
"comptables lorsqu'il les créera à partir des factures."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you check this box, you will be able to collect payments using SEPA "
"Direct Debit mandates."
msgstr ""
"Si vous cochez cette case, vous pourrez collecter les paiements grâce à des "
"mandats SEPA de prélèvement automatique."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you check this box, you will be able to register your payment using SEPA."
msgstr ""
"Si vous cochez cette case, vous pourrez enregistrer votre paiement grâce au "
"système SEPA."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"If you have less than 200 contacts, we recommend you\n"
"                                        create them manually."
msgstr ""
"Si vous avez moins de 200 contacts, nous vous conseillons\n"
"                                        de les créer manuellement."

#. module: account
#: model:ir.model.fields,help:account.field_account_report_general_ledger_initial_balance
msgid ""
"If you selected date, this field allow you to add a row to display the "
"amount of debit/credit/balance that precedes the filter you've set."
msgstr ""
"Si vous avez choisi une date, ce champ vous permet d'ajouter une ligne pour "
"afficher le montant du crédit/débit/balance qui précède le filtre que vous "
"avez paramétré."

#. module: account
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
msgid ""
"If you unreconcile transactions, you must also verify all the actions that "
"are linked to those transactions because they will not be disabled"
msgstr ""
"Si vous déléttrez des écritures, gardez à l'esprit que ces écritures ne "
"seront pas effacées pour autant. Les  ajustements ou ecarts de change "
"resteront enregsitrés."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_payment_term_id
msgid ""
"If you use payment terms, the due date will be computed automatically at the"
" generation of accounting entries. If you keep the payment terms and the due"
" date empty, it means direct payment. The payment terms may compute several "
"due dates, for example 50% now, 50% in one month."
msgstr ""
"Si vous utilisez des conditions de paiement, la date d'échéance sera "
"calculée automatiquement lors de la création des écritures comptables. Si "
"vous laissez les conditions de paiement et la date d'échéance vides, cela "
"implique un paiement automatique. Il se peut que les conditions de paiement "
"calculent plusieurs dates d'échéance, par exemple 50 % maintenant et 50 % "
"dans un mois."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_date_due
msgid ""
"If you use payment terms, the due date will be computed automatically at the"
" generation of accounting entries. The Payment terms may compute several due"
" dates, for example 50% now and 50% in one month, but if you want to force a"
" due date, make sure that the payment term is not set on the invoice. If you"
" keep the Payment terms and the due date empty, it means direct payment."
msgstr ""
"Si vous utilisez des conditions de paiement, la date d'échéance sera "
"calculée automatiquement lors de la création des écritures comptables. Il se"
" peut que les conditions de paiement calculent plusieurs dates d'échéance "
"(par exemple, 50 % maintenant et 50 % dans un mois), mais si vous voulez "
"définir une date d'échéance précise, assurez-vous qu'aucune condition de "
"paiement n'est indiquée sur la facture. Si vous laissez les conditions de "
"paiement et la date d'échéance vides, cela implique un paiement automatique."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"If you want to be able to send customer statements from Odoo, you must:"
msgstr ""
"Si vous voulez être en mesure d'envoyer des déclarations de clients depuis "
"Odoo, vous devez:"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"If you want to be able to send your customers their statements \n"
"                        from Odoo, you first need to record all outstanding transactions \n"
"                        in the payable and receivable accounts. These would be invoices \n"
"                        that have not been paid or payments that have not been reconciled."
msgstr ""
"Si vous voulez être capable d'envoyer à vos clients leurs rappels de paiemet\n"
"                        depuis Odoo, vous devez enregistrer toutes les transactions en circulation\n"
"                        dans les comptes payables et recevables. Celles-ci seraient des factures\n"
"                        qui n'ont pas été payées ou des paiements qui n'ont pas été lettrés."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "If you want to do it yourself:"
msgstr "Pour le faire soi-même :"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you're selling digital goods to customers in the EU, you must charge VAT "
"based on your customers' locations. This rule applies regardless of you are "
"located. Digital goods are defined in the legislation as broadcasting, "
"telecommunications, and services that are electronically supplied instead of"
" shipped. Gift cards sent online are not included in the definition."
msgstr ""
"Si vous vendez des produits numériques à des clients au sein de l'UE, vous "
"devez leur facturer la TVA en fonction de l'endroit où ils se trouvent. "
"Cette règle s'applique quel que soit votre emplacement. Les produits "
"numériques sont définis comme suit par la loi : radiodiffusion, "
"télécommunications et services qui sont fournis sous forme électronique et "
"non pas expédiés. Les cartes cadeaux envoyées en ligne n'entrent pas dans le"
" cadre de cette définition. "

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_product_image
msgid ""
"Image of the product variant (Big-sized image of product template if false)."
" It is automatically resized as a 1024x1024px image, with aspect ratio "
"preserved."
msgstr ""
"Image de la variante d'article (si  non renseigné, image de grandes "
"dimensions du modèle d'article). Automatiquement redimensionné en image de "
"1024x1024px, en conservant les proportions."

#. module: account
#: model:account.payment.term,name:account.account_payment_term_immediate
msgid "Immediate Payment"
msgstr "Paiement immédiat"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_qif
msgid "Import .qif files"
msgstr "Importer des fichiers .qif"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_csv
msgid "Import in .csv format"
msgstr "Importer au format .csv"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_ofx
msgid "Import in .ofx format"
msgstr "Importer au format .ofx"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_camt
msgid "Import in CAMT.053 format"
msgstr "Importer au format CAMT.053"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Import using the \"Import\" button on the top left corner of"
msgstr ""
"Importer en utilisant le bouton \"Importer\" dans le coin en haut à gauche "
"de"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements automatically"
msgstr "Importez vos relevés bancaires automatiquement"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in CAMT.053"
msgstr "Importez vos relevés bancaires en CAMT.053"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in CSV"
msgstr "Importez vos relevés bancaires en CSV"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in OFX"
msgstr "Importez vos relevés bancaires en OFX"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in QIF"
msgstr "Importez vos relevés bancaires en QIF"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Importing your statements in via a supported file format (QIF, OFX, CODA or "
"CSV format)"
msgstr ""
"importation de vos relevés dans un format de fichier supporté (formats QIF, "
"OFX, CODA ou CSV) ;"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "In Odoo,"
msgstr "Dans Odoo,"

#. module: account
#: code:addons/account/models/account_bank_statement.py:409
#, python-format
msgid ""
"In order to delete a bank statement line, you must first cancel it to delete"
" related journal items."
msgstr ""
"Pour supprimer une écriture comptable générée par relevé bancaire, vous "
"devez d'abord annuler la ligne du relevé correspondants"

#. module: account
#: code:addons/account/models/account_bank_statement.py:199
#, python-format
msgid ""
"In order to delete a bank statement, you must first cancel it to delete "
"related journal items."
msgstr ""
"Pour pouvoir supprimer un relevé bancaire, vous devez d'abord l'annuler pour"
" supprimer les éléments associés du journal."

#. module: account
#: code:addons/account/models/account_payment.py:144
#, python-format
msgid ""
"In order to pay multiple invoices at once, they must use the same currency."
msgstr ""
"Afin de payer plusieurs factures à la fois, elles doivent utiliser la même "
"devise."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "In your old accounting software, print a trial balance"
msgstr ""
"Dans votre ancien logiciel de comptabilité, imprimez la balance générale des"
" comptes"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Inactive"
msgstr "Inactif"

#. module: account
#: selection:account.payment.method,payment_type:0
msgid "Inbound"
msgstr "Entrant"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_initial_balance
msgid "Include Initial Balances"
msgstr "Inclure les soldes initiaux"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_analytic
msgid "Include in Analytic Cost"
msgstr "Inclure dans le coût analytique"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_price_include
#: model:ir.model.fields,field_description:account.field_account_tax_template_price_include
msgid "Included in Price"
msgstr "Inclus dans le prix"

#. module: account
#: model:account.account.type,name:account.data_account_type_revenue
#: model:account.financial.report,name:account.account_financial_report_income0
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Income"
msgstr "Revenus"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_category_property_account_income_categ_id
#: model:ir.model.fields,field_description:account.field_product_product_property_account_income_id
#: model:ir.model.fields,field_description:account.field_product_template_property_account_income_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Income Account"
msgstr "Compte de revenus"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_income_id
msgid "Income Account on Product Template"
msgstr "Compte de revenu sur les modèles d'articles"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:700
#, python-format
msgid "Incorrect Operation"
msgstr "Opération incorrecte"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Information"
msgstr "Information"

#. module: account
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
msgid "Information addendum"
msgstr "Information supplémentaire"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:296
#: code:addons/account/models/company.py:311
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:9
#, python-format
msgid "Initial Balances"
msgstr "Soldes initiaux"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_stock_account_input_categ_id
#: model:ir.model.fields,field_description:account.field_res_company_property_stock_account_input_categ_id
msgid "Input Account for Stock Valuation"
msgstr "Compte d'entrée pour la valorisation d'inventaire"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Install Chart of Account"
msgstr "Installer le plan comptable"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Install More Packages"
msgstr "Installer plus de localisations comptables"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_transfer_account_id
msgid "Inter-Banks Transfer Account"
msgstr "Compte de transfert inter-bancaire"

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_transfer_account_id
#: model:ir.model.fields,help:account.field_res_company_transfer_account_id
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_transfer_account_id
msgid ""
"Intermediary account used when moving money from a liquidity account to "
"another"
msgstr ""
"Compte intermédiaire utilisé pour déplacer de l'argent d'un compte de "
"trésorerie vers un autre"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_narration
msgid "Internal Note"
msgstr "Note interne"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_note
msgid "Internal Notes"
msgstr "Notes internes"

#. module: account
#: selection:account.payment,payment_type:0
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Internal Transfer"
msgstr "Transfert interne"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_internal_type
msgid "Internal Type"
msgstr "Type interne"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_template_form
msgid "Internal notes..."
msgstr "Notes internes..."

#. module: account
#: code:addons/account/models/partner.py:45
#, python-format
msgid "Invalid \"Zip Range\", please configure it properly."
msgstr ""
"Intervalle de codes postaux incorrecte, veuillez le configurer correctement"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Invite Users"
msgstr "Invitez des utilisateurs"

#. module: account
#: code:addons/account/models/account_invoice.py:1214
#: model:ir.model,name:account.model_account_invoice
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_invoice_id
#: model:ir.model.fields,field_description:account.field_account_move_line_invoice_id
#: model:ir.model.fields,field_description:account.field_res_partner_invoice_warn
#: model:ir.model.fields,field_description:account.field_res_users_invoice_warn
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:res.request.link,name:account.req_link_invoice
#, python-format
msgid "Invoice"
msgstr "Facture"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid "Invoice #"
msgstr "Facture n°"

#. module: account
#: code:addons/account/models/account_invoice.py:440
#, python-format
msgid "Invoice - %s"
msgstr "Facture - %s"

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_created
#: model:mail.message.subtype,name:account.mt_invoice_created
msgid "Invoice Created"
msgstr "Facture créée"

#. module: account
#: code:addons/account/controllers/portal.py:70
#: model:ir.model.fields,field_description:account.field_account_invoice_date_invoice
#: model:ir.ui.view,arch_db:account.portal_my_invoices
#: model:ir.ui.view,arch_db:account.report_payment_receipt
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Invoice Date"
msgstr "Date de facture"

#. module: account
#: model:ir.model,name:account.model_account_invoice_line
#: model:ir.ui.view,arch_db:account.view_invoice_line_form
#: model:ir.ui.view,arch_db:account.view_invoice_line_tree
msgid "Invoice Line"
msgstr "Ligne de facture"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_invoice_line_ids
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Invoice Lines"
msgstr "Lignes de facture"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Invoice Number"
msgstr "Numéro de facture"

#. module: account
#: sql_constraint:account.invoice:0
msgid "Invoice Number must be unique per Company!"
msgstr "Le numéro de facture doit être unique par société !"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Invoice Number:"
msgstr "Numéro de facture :"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_invoice_id
msgid "Invoice Reference"
msgstr "Référence de la facture"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_state
msgid "Invoice Status"
msgstr "État de la facture"

#. module: account
#: model:ir.model,name:account.model_account_invoice_tax
msgid "Invoice Tax"
msgstr "Taxe"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_invoice_id
msgid "Invoice for which this invoice is the credit note"
msgstr "Facture à l'origine de l'avoir"

#. module: account
#: code:addons/account/models/account_invoice.py:753
#, python-format
msgid "Invoice must be cancelled in order to reset it to draft."
msgstr ""
"La facture définitive doit être annulée pour la remettre au format "
"brouillon."

#. module: account
#: code:addons/account/models/account_invoice.py:801
#, python-format
msgid "Invoice must be in draft or open state in order to be cancelled."
msgstr ""
"Pour pouvoir être annulée, la facture doit être ouverte ou à l'état "
"d'ébauche."

#. module: account
#: code:addons/account/models/account_invoice.py:775
#, python-format
msgid "Invoice must be in draft state in order to validate it."
msgstr "La facture doit être à l'état d'ébauche pour pouvoir être validée."

#. module: account
#: code:addons/account/models/account_invoice.py:795
#, python-format
msgid "Invoice must be paid in order to set it to register payment."
msgstr "La facture doit être réglée pour pouvoir enregistrer le paiement."

#. module: account
#: code:addons/account/models/account_invoice.py:787
#, python-format
msgid "Invoice must be validated in order to set it to register payment."
msgstr "La facture doit être validée pour pouvoir y enregistrer un paiement."

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_paid
msgid "Invoice paid"
msgstr "Facture payée"

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_validated
msgid "Invoice validated"
msgstr "Facture validée"

#. module: account
#: model:mail.template,report_name:account.email_template_edi_invoice
msgid ""
"Invoice_${(object.number or '').replace('/','_')}_${object.state == 'draft' "
"and 'draft' or ''}"
msgstr ""
"Facture_${(object.number or '').replace('/','_')}_${object.state == 'draft' "
"and 'brouillon' or ''}"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Invoiced"
msgstr "Facturée"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_refund_out_tree
#: model:ir.actions.act_window,name:account.action_invoice_tree
#: model:ir.actions.report,name:account.account_invoices
#: model:ir.model.fields,field_description:account.field_account_payment_invoice_ids
#: model:ir.model.fields,field_description:account.field_account_register_payments_invoice_ids
#: model:ir.model.fields,field_description:account.field_res_partner_invoice_ids
#: model:ir.model.fields,field_description:account.field_res_users_invoice_ids
#: model:ir.ui.menu,name:account.menu_action_account_invoice_report_all
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.portal_my_home_invoice
#: model:ir.ui.view,arch_db:account.portal_my_home_menu_invoice
#: model:ir.ui.view,arch_db:account.portal_my_invoices
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_account_payment_form
#: model:ir.ui.view,arch_db:account.view_account_payment_graph
#: model:ir.ui.view,arch_db:account.view_invoice_graph
#: model:ir.ui.view,arch_db:account.view_invoice_line_calendar
#: model:ir.ui.view,arch_db:account.view_invoice_pivot
msgid "Invoices"
msgstr "Factures"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_report_all
#: model:ir.actions.act_window,name:account.action_account_invoice_report_all_supp
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_graph
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_pivot
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Invoices Analysis"
msgstr "Analyse des factures"

#. module: account
#: model:ir.model,name:account.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Statistiques de facturation"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:186
#, python-format
msgid "Invoices owed to you"
msgstr "Factures qui vous sont dues"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Invoices to validate"
msgstr "Factures à valider"

#. module: account
#: model:ir.actions.report,name:account.account_invoices_without_payment
msgid "Invoices without Payment"
msgstr "Factures sans paiement"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance
#: model:ir.ui.view,arch_db:account.product_template_form_view
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Invoicing"
msgstr "Facturation"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_is_unaffected_earnings_line
msgid "Is Unaffected Earnings Line"
msgstr "Est une ligne de revenus non concernée"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_is_rounding_line
msgid "Is a rounding line in case of cash rounding."
msgstr "Est une ligne d'arrondi en cas de somme en espèces arrondie"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_is_difference_zero
msgid "Is zero"
msgstr "Est zéro"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_default_credit_account_id
#: model:ir.model.fields,help:account.field_res_company_income_currency_exchange_account_id
msgid "It acts as a default account for credit amount"
msgstr "Sert de compte par défaut pour le crédit"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_default_debit_account_id
#: model:ir.model.fields,help:account.field_res_company_expense_currency_exchange_account_id
msgid "It acts as a default account for debit amount"
msgstr "Ça sert de compte par défaut pour les montants en débit"

#. module: account
#: model:ir.model.fields,help:account.field_account_report_partner_ledger_amount_currency
msgid ""
"It adds the currency column on report if the currency differs from the "
"company currency."
msgstr ""
"Il ajoute la colonne devise sur le rapport si la devise est différente de la"
" devise de la société."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_reconciled
msgid ""
"It indicates that the invoice has been paid and the journal entry of the "
"invoice has been reconciled with one or several journal entries of payment."
msgstr ""
"Cela indique que la facture a été réglée et que l'écriture du journal "
"correspondante a été rapprochée d'une ou de plusieurs écritures de paiement."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_sent
msgid "It indicates that the invoice has been sent."
msgstr "Cela indique que la facture a été envoyée."

#. module: account
#: code:addons/account/models/account_move.py:1050
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr ""
"Il est obligatoire de spécifier un compte et un journal pour créer une "
"radiation."

#. module: account
#: code:addons/account/models/account_payment.py:470
#, python-format
msgid ""
"It is not allowed to delete a payment that already created a journal entry "
"since it would create a gap in the numbering. You should create the journal "
"entry again and cancel it thanks to a regular revert."
msgstr ""
"La suppression d'un règlement qui a déjà créé une entrée de journal n'est "
"pas autorisée car cela créerait une discontinuité dans la numérotation. Vous"
" devriez recréer l'entrée de journal et l'annuler via une annulation "
"classique."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"It's common practice to change your accounting software \n"
"                            at the end of a fiscal year. This allows you to have less \n"
"                            data to import and balances to set. If you plan to do so, \n"
"                            we recommend you start using Odoo for invoicing and payments \n"
"                            now, and then move all other accounting transactions at a later time."
msgstr ""
"C'est une pratique commune de changer votre logiciel comptable à la fin de "
"l'exercice. Ceci vous permet d'avoir moins de données à importer et de "
"balances à paramétrer. Si vous planifiez de le faire ainsi, nous vous "
"recommandons d'utiliser Odoo pour votre facturation et vos paiements "
"maintenant, et de déplacer toutes vos transactions comptables plus tard."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"It's recommended that you do not delete any accounts, even if they are not "
"relevant. Simply make them inactive."
msgstr ""
"Il est recommandé que vous ne supprimiez aucun compte, même s'ils ne sont "
"pas pertinents. Il suffit de les rendre inactifs."

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Italic Text (smaller)"
msgstr "Texte en italique (plus petit)"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Items"
msgstr "Éléments"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
msgid "JRNL"
msgstr "JRNL"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "January"
msgstr "Janvier"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:185
#: code:addons/account/static/src/xml/account_reconciliation.xml:229
#: model:ir.model,name:account.model_account_journal
#: model:ir.model.fields,field_description:account.field_account_bank_statement_journal_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_journal_id
#: model:ir.model.fields,field_description:account.field_account_invoice_journal_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_journal_id
#: model:ir.model.fields,field_description:account.field_account_move_journal_id
#: model:ir.model.fields,field_description:account.field_account_move_line_journal_id
#: model:ir.model.fields,field_description:account.field_account_opening_journal_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_journal_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_journal_id
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_journal_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Journal"
msgstr "Journal"

#. module: account
#: selection:account.report.general.ledger,sortby:0
msgid "Journal & Partner"
msgstr "Journal et partenaire"

#. module: account
#: code:addons/account/models/account_bank_statement.py:254
#: model:ir.actions.act_window,name:account.action_move_journal_line
#: model:ir.actions.act_window,name:account.action_move_select
#: model:ir.ui.menu,name:account.menu_action_move_journal_line_form
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#: model:ir.ui.view,arch_db:account.view_move_tree
#, python-format
msgid "Journal Entries"
msgstr "Pièces comptables"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Journal Entries by Month"
msgstr "Ecritures par mois"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_move_id
#: model:ir.model.fields,field_description:account.field_account_move_line_move_id
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Journal Entry"
msgstr "Écriture du journal"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_move_name
#: model:ir.model.fields,field_description:account.field_account_invoice_move_name
#: model:ir.model.fields,field_description:account.field_account_payment_move_name
msgid "Journal Entry Name"
msgstr "Nom de l'écriture du journal"

#. module: account
#: selection:account.print.journal,sort_selection:0
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Journal Entry Number"
msgstr "N° écriture dans le journal"

#. module: account
#: model:ir.model,name:account.model_account_move_line
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Journal Item"
msgstr "Écriture comptable"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_writeoff_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_label
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Journal Item Label"
msgstr "Étiquette de pièce"

#. module: account
#: code:addons/account/models/account_payment.py:414
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_move_line
#: model:ir.actions.act_window,name:account.act_account_move_to_account_move_line_open
#: model:ir.actions.act_window,name:account.action_account_moves_all_a
#: model:ir.actions.act_window,name:account.action_account_moves_all_tree
#: model:ir.actions.act_window,name:account.action_move_line_graph
#: model:ir.actions.act_window,name:account.action_move_line_graph_cash_basis
#: model:ir.actions.act_window,name:account.action_move_line_graph_cash_basis_posted
#: model:ir.actions.act_window,name:account.action_move_line_graph_posted
#: model:ir.actions.act_window,name:account.action_move_line_select
#: model:ir.actions.act_window,name:account.action_move_line_select_by_partner
#: model:ir.actions.act_window,name:account.action_move_line_select_by_type
#: model:ir.actions.act_window,name:account.action_move_line_select_tax_audit
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_journal_entry_ids
#: model:ir.model.fields,field_description:account.field_account_move_line_ids
#: model:ir.model.fields,field_description:account.field_res_partner_journal_item_count
#: model:ir.model.fields,field_description:account.field_res_users_journal_item_count
#: model:ir.ui.menu,name:account.menu_action_account_moves_all
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.setup_opening_move_lines_tree
#: model:ir.ui.view,arch_db:account.view_account_payment_form
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_pivot
#: model:ir.ui.view,arch_db:account.view_move_line_tree
#, python-format
msgid "Journal Items"
msgstr "Écritures comptables"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_action.js:323
#: model:ir.actions.client,name:account.action_manual_reconcile
#, python-format
msgid "Journal Items to Reconcile"
msgstr "Écritures comptables à lettrer"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_name
msgid "Journal Name"
msgstr "Nom du journal"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "Journal and Partner"
msgstr "Journal et partenaire"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Journal invoices with period in current year"
msgstr "Ecritures de facturation avec une période sur l'exercice en cours"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Journal items where matching number isn't set"
msgstr "Entrées de journal non lettrées"

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_opening_journal_id
msgid ""
"Journal where the opening entry of this company's accounting has been "
"posted."
msgstr ""
"Journal dans lequel a été publiée l'écriture d'ouverture concernant la "
"comptabilité de cette entreprise."

#. module: account
#: model:ir.actions.act_window,name:account.action_account_journal_form
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_journal_ids
#: model:ir.model.fields,field_description:account.field_account_balance_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_account_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_print_journal_journal_ids
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_journal_ids
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_journal_ids
#: model:ir.model.fields,field_description:account.field_account_tax_report_journal_ids
#: model:ir.model.fields,field_description:account.field_accounting_report_journal_ids
#: model:ir.ui.menu,name:account.menu_action_account_journal_form
msgid "Journals"
msgstr "Journaux"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_print_journal_menu
#: model:ir.actions.report,name:account.action_report_journal
#: model:ir.ui.menu,name:account.menu_print_journal
msgid "Journals Audit"
msgstr "Audit de journaux"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "July"
msgstr "Juillet"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "June"
msgstr "Juin"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_reason
msgid "Justification"
msgstr "Justificatif"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_kanban_dashboard
msgid "Kanban Dashboard"
msgstr "Tableau de bord Kanban"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "Tableau de bord graphique Kanban"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Keep empty for no control"
msgstr "Laissez vide pour aucun contrôle"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_date_invoice
msgid "Keep empty to use the current date"
msgstr "Laissez vide pour utiliser la date courante"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_date
msgid "Keep empty to use the invoice date."
msgstr "Laissez vide pour utiliser la date de la facture."

#. module: account
#: selection:account.payment,payment_difference_handling:0
msgid "Keep open"
msgstr "Laissez ouvert"

#. module: account
#: model:ir.model.fields,help:account.field_product_product_property_account_income_id
#: model:ir.model.fields,help:account.field_product_template_property_account_income_id
msgid ""
"Keep this field empty to use the default value from the product category."
msgstr ""
"Laissez ce champ vide pour utiliser la valeur par défaut de la catégorie "
"d'article."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:522
#: code:addons/account/static/src/xml/account_reconciliation.xml:189
#: code:addons/account/static/src/xml/account_reconciliation.xml:230
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_name
#: model:ir.model.fields,field_description:account.field_account_move_line_name
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#, python-format
msgid "Label"
msgstr "Libellé"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_description
msgid "Label on Invoices"
msgstr "Étiquettes sur les factures"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment___last_update
#: model:ir.model.fields,field_description:account.field_account_account___last_update
#: model:ir.model.fields,field_description:account.field_account_account_tag___last_update
#: model:ir.model.fields,field_description:account.field_account_account_template___last_update
#: model:ir.model.fields,field_description:account.field_account_account_type___last_update
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance___last_update
#: model:ir.model.fields,field_description:account.field_account_balance_report___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line___last_update
#: model:ir.model.fields,field_description:account.field_account_cash_rounding___last_update
#: model:ir.model.fields,field_description:account.field_account_cashbox_line___last_update
#: model:ir.model.fields,field_description:account.field_account_chart_template___last_update
#: model:ir.model.fields,field_description:account.field_account_common_account_report___last_update
#: model:ir.model.fields,field_description:account.field_account_common_journal_report___last_update
#: model:ir.model.fields,field_description:account.field_account_common_partner_report___last_update
#: model:ir.model.fields,field_description:account.field_account_common_report___last_update
#: model:ir.model.fields,field_description:account.field_account_financial_report___last_update
#: model:ir.model.fields,field_description:account.field_account_financial_year_op___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template___last_update
#: model:ir.model.fields,field_description:account.field_account_full_reconcile___last_update
#: model:ir.model.fields,field_description:account.field_account_group___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_line___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_refund___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_report___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_tax___last_update
#: model:ir.model.fields,field_description:account.field_account_journal___last_update
#: model:ir.model.fields,field_description:account.field_account_move___last_update
#: model:ir.model.fields,field_description:account.field_account_move_line___last_update
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile___last_update
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff___last_update
#: model:ir.model.fields,field_description:account.field_account_move_reversal___last_update
#: model:ir.model.fields,field_description:account.field_account_opening___last_update
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile___last_update
#: model:ir.model.fields,field_description:account.field_account_payment___last_update
#: model:ir.model.fields,field_description:account.field_account_payment_method___last_update
#: model:ir.model.fields,field_description:account.field_account_payment_term___last_update
#: model:ir.model.fields,field_description:account.field_account_payment_term_line___last_update
#: model:ir.model.fields,field_description:account.field_account_print_journal___last_update
#: model:ir.model.fields,field_description:account.field_account_reconcile_model___last_update
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template___last_update
#: model:ir.model.fields,field_description:account.field_account_register_payments___last_update
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger___last_update
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger___last_update
#: model:ir.model.fields,field_description:account.field_account_tax___last_update
#: model:ir.model.fields,field_description:account.field_account_tax_group___last_update
#: model:ir.model.fields,field_description:account.field_account_tax_report___last_update
#: model:ir.model.fields,field_description:account.field_account_tax_template___last_update
#: model:ir.model.fields,field_description:account.field_account_unreconcile___last_update
#: model:ir.model.fields,field_description:account.field_accounting_report___last_update
#: model:ir.model.fields,field_description:account.field_cash_box_in___last_update
#: model:ir.model.fields,field_description:account.field_cash_box_out___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_financial___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_generalledger___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_journal___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_overdue___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_partnerledger___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_tax___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_trialbalance___last_update
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard___last_update
#: model:ir.model.fields,field_description:account.field_validate_account_move___last_update
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts___last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Last Month"
msgstr "Le mois dernier"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:119
#, python-format
msgid "Last Reconciliation:"
msgstr "Dernier rapprochement :"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_write_uid
#: model:ir.model.fields,field_description:account.field_account_account_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_account_type_write_uid
#: model:ir.model.fields,field_description:account.field_account_account_write_uid
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_write_uid
#: model:ir.model.fields,field_description:account.field_account_balance_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_write_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_write_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_account_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_financial_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_write_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_write_uid
#: model:ir.model.fields,field_description:account.field_account_group_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_write_uid
#: model:ir.model.fields,field_description:account.field_account_journal_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_write_uid
#: model:ir.model.fields,field_description:account.field_account_opening_write_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_write_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal_write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_write_uid
#: model:ir.model.fields,field_description:account.field_account_register_payments_write_uid
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_write_uid
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_write_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile_write_uid
#: model:ir.model.fields,field_description:account.field_accounting_report_write_uid
#: model:ir.model.fields,field_description:account.field_cash_box_in_write_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out_write_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_write_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move_write_uid
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_write_date
#: model:ir.model.fields,field_description:account.field_account_account_template_write_date
#: model:ir.model.fields,field_description:account.field_account_account_type_write_date
#: model:ir.model.fields,field_description:account.field_account_account_write_date
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_write_date
#: model:ir.model.fields,field_description:account.field_account_balance_report_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_write_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_write_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_write_date
#: model:ir.model.fields,field_description:account.field_account_chart_template_write_date
#: model:ir.model.fields,field_description:account.field_account_common_account_report_write_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_write_date
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_write_date
#: model:ir.model.fields,field_description:account.field_account_common_report_write_date
#: model:ir.model.fields,field_description:account.field_account_financial_report_write_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_write_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_write_date
#: model:ir.model.fields,field_description:account.field_account_group_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_line_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_write_date
#: model:ir.model.fields,field_description:account.field_account_journal_write_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_write_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_write_date
#: model:ir.model.fields,field_description:account.field_account_move_line_write_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal_write_date
#: model:ir.model.fields,field_description:account.field_account_move_write_date
#: model:ir.model.fields,field_description:account.field_account_opening_write_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_method_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_write_date
#: model:ir.model.fields,field_description:account.field_account_print_journal_write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_write_date
#: model:ir.model.fields,field_description:account.field_account_register_payments_write_date
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_write_date
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_group_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_report_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_template_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_write_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile_write_date
#: model:ir.model.fields,field_description:account.field_accounting_report_write_date
#: model:ir.model.fields,field_description:account.field_cash_box_in_write_date
#: model:ir.model.fields,field_description:account.field_cash_box_out_write_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_write_date
#: model:ir.model.fields,field_description:account.field_validate_account_move_write_date
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Last day of current month"
msgstr "Dernier jour du mois en cours"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Last day of following month"
msgstr "Dernier jour du mois suivant"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_last_time_entries_checked
#: model:ir.model.fields,help:account.field_res_users_last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed for this partner. "
"It is set either if there's not at least an unreconciled debit and an "
"unreconciled credit or if you click the \"Done\" button."
msgstr ""
"Dernière fois où les factures et les paiements ont été mis en correspondance"
" pour ce partenaire. Cela se produit lorsqu'il n'y a pas au moins un débit "
"non rapproché et un crédit non rapproché, ou lorsque vous cliquez sur « "
"Terminé »."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed on this account. It"
" is set either if there's not at least an unreconciled debit and an "
"unreconciled credit Or if you click the \"Done\" button."
msgstr ""
"La dernière fois que la correspondance entre les factures et les paiements a"
" été faites pour ce compte. C'est renseigné s'il n'y a pas au moins un débit"
" et un crédit non-lettré ou si vous cliquez sur le bouton \"Terminé\"."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Late Activities"
msgstr "Activités en retard"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_last_time_entries_checked
#: model:ir.model.fields,field_description:account.field_res_partner_last_time_entries_checked
#: model:ir.model.fields,field_description:account.field_res_users_last_time_entries_checked
msgid "Latest Invoices & Payments Matching Date"
msgstr "Date de dernière correspondance des factures et paiements"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group_parent_left
msgid "Left Parent"
msgstr "Parent gauche"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Legal Name"
msgstr "Dénomination sociale"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Legal Notes..."
msgstr "Notes officielles..."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_note
msgid "Legal mentions that have to be printed on the invoices."
msgstr "Mentions légales à imprimer sur les factures."

#. module: account
#: code:addons/account/models/account_invoice.py:216
#, python-format
msgid "Less Payment"
msgstr "Moins paiement"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:24
#, python-format
msgid "Let odoo try to reconcile entries for the user"
msgstr "Laisser odoo essayer de réconcilier les entrées pour l'utilisateur"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Let your customers pay their invoices online"
msgstr "Permettez à vos clients de payer leurs factures en ligne"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_level
msgid "Level"
msgstr "Niveau"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_liability0
#: model:account.financial.report,name:account.account_financial_report_liabilitysum0
msgid "Liability"
msgstr "Passif"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_move_id
msgid "Link to the automatically generated Journal Items."
msgstr "Lier automatiquement à la ligne d'écriture générée."

#. module: account
#: selection:account.account.type,type:0
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Liquidity"
msgstr "Liquidités"

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_tax_template_ids
msgid "List of all the taxes that have to be installed by the wizard"
msgstr "Liste des taxes installées via l'assistant"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Litigation"
msgstr "Litige"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:29
#, python-format
msgid "Load more"
msgstr "Charger plus"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_fiscalyear_lock_date
msgid "Lock Date"
msgstr "Date de verrouillage"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "Date de verrouillage pour les non-conseillers"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Logo"
msgstr "Logo"

#. module: account
#: code:addons/account/models/account_bank_statement.py:173
#, python-format
msgid "Loss"
msgstr "Pertes"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_loss_account_id
msgid "Loss Account"
msgstr "Compte de perte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_expense_currency_exchange_account_id
#: model:ir.model.fields,field_description:account.field_res_company_expense_currency_exchange_account_id
msgid "Loss Exchange Rate Account"
msgstr "Compte de perte de change"

#. module: account
#: code:addons/account/models/chart_template.py:195
#, python-format
msgid "MISC"
msgstr "OD"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Mail your invoices in one-click using"
msgstr "Envoyez par courrier vos factures en un clique avec"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Main Currency"
msgstr "Devise principale"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Main Title 1 (bold, underlined)"
msgstr "Titre principal 1 (gras,souligné)"

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_currency_id
msgid "Main currency of the company."
msgstr "Devise principale de la société."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Main currency of your company"
msgstr "Devise principale de votre société"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Manage time &amp; material contracts or fixed-price recurring subscriptions."
msgstr ""
"Gérer les contrats au temps passé ou les abonnements récurrents au forfait."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Manage your employee expenses, from encoding, to payments and reporting."
msgstr ""
"Gérer les notes de frais de vos employés, de la saisie au paiement et au "
"rapport."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Manage your various fixed assets, such as buildings, machinery, materials, "
"cars, etc..., and calculate their associated depreciation over time."
msgstr ""
"Gérer vos immobilisations, comme l'immobilier, la machinerie, les matières, "
"voitures, etc... et calculer leur dépréciation au fur et à mesure."

#. module: account
#: model:ir.ui.menu,name:account.account_management_menu
#: model:ir.ui.menu,name:account.account_reports_management_menu
#: model:ir.ui.menu,name:account.menu_finance_entries_management
msgid "Management"
msgstr "Gestion"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Managing bank fees"
msgstr "Gestion des frais bancaires"

#. module: account
#: model:account.payment.method,name:account.account_payment_method_manual_in
#: model:account.payment.method,name:account.account_payment_method_manual_out
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_manual
msgid "Manual"
msgstr "Manuel"

#. module: account
#: model:ir.ui.view,arch_db:account.view_invoice_tax_form
#: model:ir.ui.view,arch_db:account.view_invoice_tax_tree
msgid "Manual Invoice Taxes"
msgstr "Taxes de facture manuelle"

#. module: account
#: model:ir.actions.client,name:account.action_manual_reconciliation
#: model:ir.ui.menu,name:account.menu_action_manual_reconciliation
msgid "Manual Reconciliation"
msgstr "Lettrage manuel"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_inbound_payment_method_ids
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo,you are suggested to reconcile the transaction with the batch deposit. Enable this option from the settings."
msgstr ""
"Paiement manuel : soyez payé en espèces, par chèque ou par tout autre moyen externe à Odoo.\n"
"Paiement électronique : soyez payé automatiquement par un acquéreur en demandant une transaction sur une carte enregistrée par le client lors de l'achat ou de l'inscription en ligne (jeton de paiement).\n"
"Dépôt groupé : encaissez plusieurs chèques clients à la fois en générant un dépôt groupé à envoyer à votre banque. Lors de l'encodage du relevé bancaire dans Odoo, vous pouvez rapprocher la transaction et le dépôt groupé. Activez cette option dans les paramètres."

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_payment_method_id
#: model:ir.model.fields,help:account.field_account_payment_payment_method_id
#: model:ir.model.fields,help:account.field_account_register_payments_payment_method_id
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Check: Pay bill by check and print it from Odoo.\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo, you are suggested to reconcile the transaction with the batch deposit.To enable batch deposit,module account_batch_deposit must be installed.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. To enable sepa credit transfer, module account_sepa must be installed "
msgstr ""
"Paiement manuel : soyez payé en espèces, par chèque ou par tout autre moyen externe à Odoo.\n"
"Paiement électronique : soyez payé automatiquement par un acquéreur en demandant une transaction sur une carte enregistrée par le client lors de l'achat ou de l'inscription en ligne (jeton de paiement).\n"
"Chèque : payez par chèque en l'imprimant depuis Odoo.\n"
"Dépôt groupé : encaissez plusieurs chèques clients à la fois en générant un dépôt groupé à envoyer à votre banque. Lors de l'encodage du relevé bancaire dans Odoo, vous pouvez rapprocher la transaction et le dépôt groupé. Pour activer le dépôt groupé, vous devez installer le module account_batch_deposit (compte-dépôt-groupé).\n"
"Virement SEPA : payez en envoyant un fichier de virement SEPA à votre banque. Pour activer les virements SEPA, vous devez installer le module account_sepa (compte-sepa) "

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_outbound_payment_method_ids
msgid ""
"Manual:Pay bill by cash or any other method outside of Odoo.\n"
"Check:Pay bill by check and print it from Odoo.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. Enable this option from the settings."
msgstr ""
"Paiement manuel : payez en espèces ou par tout autre moyen externe à Odoo.\n"
"Chèque : payez par chèque en l'imprimant depuis Odoo.\n"
"Virement SEPA : payez en envoyant un fichier de virement SEPA à votre banque. Activez cette option dans les paramètres."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Manually enter your transactions using our"
msgstr "Saisie manuelle de vos transactions en utilisant notre"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "March"
msgstr "Mars"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Margin Analysis"
msgstr "Analyse des marges"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model:ir.ui.view,arch_db:account.setup_view_company_form
msgid "Mark as done"
msgstr "Marquer comme fait"

#. module: account
#: selection:account.payment,payment_difference_handling:0
msgid "Mark invoice as fully paid"
msgstr "Marquer la facture comme payée complètement"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_payables_master_data
#: model:ir.ui.menu,name:account.menu_finance_receivables_master_data
msgid "Master Data"
msgstr "Données de base"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_matched_credit_ids
msgid "Matched Credit"
msgstr "Crédit correspondant"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_matched_debit_ids
msgid "Matched Debit"
msgstr "Débit correspondant"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_reconciled_line_ids
#: model:ir.ui.view,arch_db:account.view_full_reconcile_form
msgid "Matched Journal Items"
msgstr "Écritures lettrées"

#. module: account
#: model:ir.ui.view,arch_db:account.view_full_reconcile_form
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Matching"
msgstr "Equivalent "

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_full_reconcile_id
msgid "Matching Number"
msgstr "Référence de lettrage"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_max_date
msgid "Max Date of Matched Lines"
msgstr "Date max. des lignes correspondantes"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "May"
msgstr "Mai"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_communication
#: model:ir.model.fields,field_description:account.field_account_payment_communication
#: model:ir.model.fields,field_description:account.field_account_register_payments_communication
msgid "Memo"
msgstr "Mémo"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:64
#, python-format
msgid "Memo:"
msgstr "Mémo :"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_invoice_warn_msg
#: model:ir.model.fields,field_description:account.field_res_users_invoice_warn_msg
msgid "Message for Invoice"
msgstr "Message pour Facture"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Cost of Revenue"
msgstr "Moins le cout des ventes"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Credit Card Accounts"
msgstr "Moins les comptes de cartes de crédit"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Current Liabilities"
msgstr "Moins passif actuel"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Expenses"
msgstr "Moins les frais"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Non-Current Liabilities"
msgstr "Moins passif immobilisé"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Payable Accounts"
msgstr "Moins comptes fournisseurs"

#. module: account
#: selection:account.journal,type:0
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Miscellaneous"
msgstr "Divers"

#. module: account
#: code:addons/account/models/chart_template.py:195
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#, python-format
msgid "Miscellaneous Operations"
msgstr "Opérations diverses"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:164
#, python-format
msgid "Modify models"
msgstr "Modifier les modèles"

#. module: account
#: selection:account.cash.rounding,strategy:0
msgid "Modify tax amount"
msgstr "Modifier le montant des taxes"

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Modify: create credit note, reconcile and create a new draft invoice"
msgstr ""
"Modifier : créer un avoir, le lettrer et créer une nouvelle facture en "
"brouillon"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Monitor your product margins from invoices"
msgstr "Contrôlez vos marges par articles à partir des factures"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_invoice_partner_relation
msgid "Monthly Turnover"
msgstr "Chiffre d'affaire mensuel"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Most currencies are already created by default. If you plan\n"
"                        to use some of them, you should check their <strong>Active</strong>\n"
"                        field."
msgstr ""
"La plupart des devises sont déjà créées par défaut. Si vous planifiez  d'en "
"utiliser quelque-unes, vous devriez cocher leur champ "
"<strong>Active</strong>."

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Move"
msgstr "Mouvement"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_move_id
#: model:ir.model.fields,field_description:account.field_account_payment_move_line_ids
msgid "Move Line"
msgstr "Ligne d'écriture"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_move_line_count
msgid "Move Line Count"
msgstr "Nombre de lignes d'écriture"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_move_reconciled
msgid "Move Reconciled"
msgstr "Écriture lettrée"

#. module: account
#: code:addons/account/models/account_move.py:1362
#, python-format
msgid "Move name (id): %s (%s)"
msgstr "Nom de l'écriture (id) : %s (%s)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_register_payments_multi
msgid "Multi"
msgstr "Multi"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Multi Currency"
msgstr "Multi devise"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Multi-Currencies"
msgstr "Multi-devises"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "My Activities"
msgstr "Mes activités"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "My Invoices"
msgstr "Mes factures"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "NET ASSETS"
msgstr "Actifs nets"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "NET PROFIT"
msgstr "Profit net"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_name
#: model:ir.model.fields,field_description:account.field_account_account_tag_name
#: model:ir.model.fields,field_description:account.field_account_account_template_name
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_name
#: model:ir.model.fields,field_description:account.field_account_chart_template_name
#: model:ir.model.fields,field_description:account.field_account_group_name
#: model:ir.model.fields,field_description:account.field_account_payment_method_name
#: model:ir.model.fields,field_description:account.field_account_payment_name
#: model:ir.model.fields,field_description:account.field_account_tax_group_name
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Name"
msgstr "Nom"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:52
#, python-format
msgid "Name:"
msgstr "Nom :"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_narration
msgid "Narration"
msgstr "Narration"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Navigate easily through reports and see what is behind the numbers"
msgstr ""
"Naviguez facilement à travers les rapports et voyez ce qui se cache derrière"
" les chiffres"

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "Net"
msgstr "Net"

#. module: account
#: selection:account.bank.statement,state:0
msgid "New"
msgstr "Nouveau"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "New Statement"
msgstr "Nouveau Relevé"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "New Transactions"
msgstr "Nouvelles transactions"

#. module: account
#: code:addons/account/models/account_move.py:1339
#, python-format
msgid "New expected payment date: "
msgstr "Nouvelle date de paiement prévue :"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_sequence_number_next
#: model:ir.model.fields,field_description:account.field_account_invoice_sequence_number_next_prefix
#: model:ir.model.fields,field_description:account.field_account_journal_sequence_number_next
msgid "Next Number"
msgstr "Nombre Suivant"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Next, register any unmatched payments:<br/>"
msgstr "Ensuite, enregistrez tout paiement sans correspondance : "

#. module: account
#: selection:accounting.report,filter_cmp:0
msgid "No Filters"
msgstr "Aucun filtre"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_blocked
msgid "No Follow-up"
msgstr "Aucun suivi"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "No Message"
msgstr "Aucun message"

#. module: account
#: selection:account.financial.report,display_detail:0
msgid "No detail"
msgstr "Aucun détail"

#. module: account
#: code:addons/account/models/account.py:116
#, python-format
msgid "No opening move defined !"
msgstr "Aucune écriture d'ouverture n'a été définie !"

#. module: account
#: model:ir.model.fields,help:account.field_res_company_fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""
"Aucun utilisateur, y compris les conseillers, ne peuvent modifier les "
"comptes comptables avant cette date inclusivement. Utilisez cela pour "
"verrouiller l'exercice fiscal par exemple."

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_code_digits
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_code_digits
msgid "No. of Digits to use for account code"
msgstr "Nombre de chiffres à utiliser pour le code de compte"

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_code_digits
msgid "No. of digits to use for account code"
msgstr "Nombre de chiffres à utiliser pour le code de compte"

#. module: account
#: model:account.account.type,name:account.data_account_type_non_current_assets
msgid "Non-current Assets"
msgstr "Actifs immobilisés"

#. module: account
#: model:account.account.type,name:account.data_account_type_non_current_liabilities
msgid "Non-current Liabilities"
msgstr "Passif immobilisé"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
msgid "None"
msgstr "Aucun"

#. module: account
#: selection:res.partner,trust:0
msgid "Normal Debtor"
msgstr "Débiteur normal"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Normal Text"
msgstr "Texte standard"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:254
#: model:ir.model.fields,field_description:account.field_account_account_template_note
#, python-format
msgid "Note"
msgstr "Note"

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_out_refund
msgid ""
"Note that the easiest way to create a credit note is to do it directly form\n"
"                the customer invoice, to refund it totally or partially."
msgstr ""
"Notez que la façon la plus simple de créer un avoir est de le faire directement à partir de\n"
"                la facture client,  pour la rembourser totalement ou partiellement.."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_note
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_note
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_note
#: model:ir.ui.view,arch_db:account.view_account_template_form
msgid "Notes"
msgstr "Notes"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Nothing to Reconcile"
msgstr "Rien à lettrer"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:34
#, python-format
msgid "Nothing to do!"
msgstr "Rien à faire !"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "November"
msgstr "Novembre"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_name
#: model:ir.model.fields,field_description:account.field_account_invoice_number
#: model:ir.model.fields,field_description:account.field_account_move_name
msgid "Number"
msgstr "Numéro"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Number (Move)"
msgstr "Numéro (pièce)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_number
msgid "Number of Coins/Bills"
msgstr "Nombre de pièces/billets"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_days
msgid "Number of Days"
msgstr "Nombre de jours"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_accounts_code_digits
msgid "Number of digits in an account code"
msgstr "Nombre de chiffres dans un code de compte"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "OFX Import"
msgstr "Importazione OFX"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:17
#, python-format
msgid "OK"
msgstr "OK"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "October"
msgstr "Octobre"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Odoo Accounting has many extra-features:"
msgstr "Odoo comptabilité a beaucoup de fonctionnalités additionnelles :"

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_line
msgid ""
"Odoo allows you to reconcile a statement line directly with\n"
"                the related sale or puchase invoice(s)."
msgstr ""
"Odoo vous permet de lettre une ligne de relevé directement avec la(es) "
"facture(s) de ventes ou d'achats associée(s)."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_tree
msgid ""
"Odoo allows you to reconcile a statement line directly with\n"
"                the related sale or puchase invoices."
msgstr ""
"Odoo vous permet de lettrer une ligne de relevé directement avec\n"
"  la facture de ventes ou d'achats associée."

#. module: account
#: model:ir.actions.act_window,help:account.action_move_journal_line
msgid ""
"Odoo automatically creates one journal entry per accounting\n"
"                document: invoice, refund, vendor payment, bank statements,\n"
"                etc. So, you should record journal entries manually only/mainly\n"
"                for miscellaneous operations."
msgstr ""
"Odoo crée automatiquement une écriture par document comptable : facture, "
"avoir, paiement fournisseur, relevés bancaires, etc. Donc, vous devriez "
"enregistrer des écritures manuelles seulement/principalement pour des "
"opérations diverses."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Odoo can manage multiple companies, but we suggest to setup everything for "
"your first company before configuring the other ones."
msgstr ""
"Odoo peut gérer plusieurs sociétés, mais nous suggérons de tout installer "
"pour votre première société avant de configurer les autres."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Odoo has already preconfigured a few taxes according to your "
"country.<br/>Simply review them and check if you need more."
msgstr ""
"Odoo a déjà préconfiguré quelques taxes selon votre pays. <br/>Simplement "
"les révisez et vérifiez si vous en avez besoin de plus."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Odoo should do most of the reconciliation work automatically, so you'll only"
" need to review a few of them when a <strong>'Reconcile Items'</strong> "
"button appears on your Vendor Bills dash."
msgstr ""
"Odoo devrait faire la plupart du travail de lettrage, donc vous devriez n'en"
" réviser que quelques-uns quand le bouton <strong>\"Lettrer les "
"écritures\"</strong> apparait sur votre tableau de bord des factures "
"fournisseurs."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree1
msgid ""
"Odoo's electronic invoicing allows to ease and fasten the\n"
"                collection of customer payments. Your customer receives the\n"
"                invoice by email and he can pay online and/or import it\n"
"                in his own system."
msgstr ""
"La facturation électronique d'Odoo permet de faciliter et d'accélérer la "
"collection des paiements clients. Votre client reçoit la facture par email "
"et il peut payer en ligne et/ou l'importer dans son propre système."

#. module: account
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid ""
"Once draft invoices are confirmed, you will not be able\n"
"                        to modify them. The invoices will receive a unique\n"
"                        number and journal items will be created in your chart\n"
"                        of accounts."
msgstr ""
"Une fois que les factures en brouillon sont confirmées, vous ne serez plus "
"capable des les modifier. Les factures recevront un numéro unique et une "
"écriture sera créée dans votre charte de comptes."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Once installed, set 'Bank Feeds' to 'File Import' in bank account "
"settings.This adds a button to import from the Accounting dashboard."
msgstr ""
"Une fois installé, configurez les \"relevés bancaires\" sur \"Import de "
"fichier\" dans les paramètres du compte bancaire. Cela ajoute un bouton pour"
" importer depuis votre tableau de bord de la comptabilité."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Once you have created your chart of accounts, you will need to update your "
"account balances."
msgstr ""
"Une fois que vous avez créé votre plan comptable, vous aurez besoin de "
"mettre à jour les soldes des comptes."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Once your bank accounts are registered, you will be able \n"
"                        to access your statements from the Accounting Dashboard. \n"
"                        The available methods for synchronization are as follows."
msgstr ""
"Lorsque vos comptes bancaires seront enregistrés, vous pourrez accéder à vos"
" relevés depuis le tableau de bord de la comptabilité. Les méthodes "
"disponibles pour la synchronisation sont les suivantes :"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Once your company information is correct, you should"
msgstr ""
"Une fois que les informations sur votre société sont correctes, vous devriez"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "One bank statement for each bank account you hold."
msgstr "Un relevé bancaire pour chaque compte bancaire que vous détenez."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_payment
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Online Payment"
msgstr "Paiement en ligne"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_only_one_chart_template
msgid "Only One Chart Template Available"
msgstr "Seulement un modèle de charte disponible"

#. module: account
#: code:addons/account/models/account_payment.py:484
#, python-format
msgid "Only a draft payment can be posted."
msgstr "Seul un projet de paiement peut être publié."

#. module: account
#: code:addons/account/models/chart_template.py:866
#, python-format
msgid "Only administrators can change the settings"
msgstr "Seuls les administrateurs peuvent modifier les paramètres"

#. module: account
#: model:ir.model.fields,help:account.field_res_company_period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""
"Seuls les utilisateurs avec le rôle 'Conseiller' peuvent modifier les "
"comptes comptables avant cette date inclusivement. Utilisez cela pour "
"verrouiller des périodes pendant l'exercice fiscal par exemple."

#. module: account
#. openerp-web
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: code:addons/account/static/src/xml/account_payment.xml:82
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Open"
msgstr "Ouvert"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:664
#: code:addons/account/static/src/xml/account_reconciliation.xml:131
#, python-format
msgid "Open balance"
msgstr "Balance ouverte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_opening_date
#: model:ir.model.fields,field_description:account.field_account_opening_date
#: model:ir.model.fields,field_description:account.field_res_company_account_opening_date
msgid "Opening Date"
msgstr "Date d'ouverture"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_opening_journal_id
msgid "Opening Journal"
msgstr "Ouverture du journal"

#. module: account
#: code:addons/account/models/company.py:339
#: model:ir.model.fields,field_description:account.field_account_opening_opening_move_id
#: model:ir.model.fields,field_description:account.field_res_company_account_opening_move_id
#, python-format
msgid "Opening Journal Entry"
msgstr "Ouverture de la pièce comptable"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_opening_opening_move_line_ids
msgid "Opening Journal Items"
msgstr "Ouverture des écritures comptables"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_opening_move_posted
msgid "Opening Move Posted"
msgstr "Ouverture d'une écriture comptabilisée"

#. module: account
#: model:ir.model.fields,help:account.field_account_cashbox_line_number
msgid "Opening Unit Numbers"
msgstr "Nombres d'unités d'ouverture"

#. module: account
#: code:addons/account/models/account.py:138
#, python-format
msgid "Opening balance"
msgstr "Solde initial"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_opening_credit
msgid "Opening credit"
msgstr "Crédit d’ouverture"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_opening_credit
msgid "Opening credit value for this account."
msgstr "Valeur du crédit d’ouverture pour ce compte."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_opening_debit
msgid "Opening debit"
msgstr "Débit d'ouverture"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_opening_debit
msgid "Opening debit value for this account."
msgstr "Valeur du débit d’ouverture pour ce compte."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Operation Templates"
msgstr "Modèles d'opération"

#. module: account
#: code:addons/account/models/account_bank_statement.py:1014
#, python-format
msgid ""
"Operation not allowed. Since your statement line already received a number, "
"you cannot reconcile it entirely with existing journal entries otherwise it "
"would make a gap in the numbering. You should book an entry and make a "
"regular revert of it in case you want to cancel it."
msgstr ""
"Opération interdite. Votre ligne de relevé ayant déjà reçu une référence, "
"vous ne pouvez pas la rapprocher totalement d'entrées de journal existante, "
"cela créerait une discontinuité dans la numérotation.  Vous devriez créer "
"une entrée et annuler celle-ci pour annuler la ligne."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_nocreate
msgid "Optional Create"
msgstr "Créer optionnel"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag_ids
#: model:ir.model.fields,help:account.field_account_account_template_tag_ids
#: model:ir.model.fields,help:account.field_account_tax_tag_ids
#: model:ir.model.fields,help:account.field_account_tax_template_tag_ids
msgid "Optional tags you may want to assign for custom reporting"
msgstr ""
"Étiquettes optionnelles que vous pourriez assigner pour des rapports "
"personnalisés"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_option
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Options"
msgstr "Options"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Original Amount"
msgstr "Montant original"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_payment_id
msgid "Originator Payment"
msgstr "Paiement effectué par"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_line_id
msgid "Originator tax"
msgstr "Taxe"

#. module: account
#: model:account.account.type,name:account.data_account_type_other_income
msgid "Other Income"
msgstr "Autres revenus"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Other Info"
msgstr "Autres informations"

#. module: account
#: selection:account.payment.method,payment_type:0
msgid "Outbound"
msgstr "Sortant"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_stock_account_output_categ_id
#: model:ir.model.fields,field_description:account.field_res_company_property_stock_account_output_categ_id
msgid "Output Account for Stock Valuation"
msgstr "Compte de sortie pour la valorisation d'inventaire"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_outstanding_credits_debits_widget
msgid "Outstanding Credits Debits Widget"
msgstr "Widget de crédits/débits en circulation"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Outstanding Transactions"
msgstr "Transactions en cours"

#. module: account
#: code:addons/account/models/account_invoice.py:131
#, python-format
msgid "Outstanding credits"
msgstr "Crédits en circulation"

#. module: account
#: code:addons/account/models/account_invoice.py:134
#, python-format
msgid "Outstanding debits"
msgstr "Débits en circulation"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Overdue"
msgstr "Retard"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_overdue_msg
msgid "Overdue Payments Message"
msgstr "Message pour les règlements en retard"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Overdue invoices, maturity date passed"
msgstr "Factures en retard, date d'échéance dépassé"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_legal_statement
msgid "PDF Reports"
msgstr "Rapports PDF"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Package"
msgstr "Localisation comptable"

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: model:ir.ui.view,arch_db:account.report_overdue_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:mail.message.subtype,name:account.mt_invoice_paid
msgid "Paid"
msgstr "Payée"

#. module: account
#: code:addons/account/models/account_payment.py:430
#, python-format
msgid "Paid Invoices"
msgstr "Factures payées"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:28
#: model:ir.ui.view,arch_db:account.report_invoice_document_with_payments
#, python-format
msgid "Paid on"
msgstr "Payé le"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_reconciled
msgid "Paid/Reconciled"
msgstr "Payé/lettré"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_parent_id
#: model:ir.model.fields,field_description:account.field_account_group_parent_id
msgid "Parent"
msgstr "Parent"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_parent_id
msgid "Parent Chart Template"
msgstr "Modèle de plan de compte parent"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
msgid "Parent Report"
msgstr "Rapport parent"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_parent_state
msgid "Parent State"
msgstr "État parent"

#. module: account
#: model:ir.model,name:account.model_account_partial_reconcile
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Partial Reconcile"
msgstr "Lettrage partiel"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:232
#: code:addons/account/static/src/xml/account_reconciliation.xml:249
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_partner_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_partner_id
#: model:ir.model.fields,field_description:account.field_account_move_line_partner_id
#: model:ir.model.fields,field_description:account.field_account_move_partner_id
#: model:ir.model.fields,field_description:account.field_account_payment_partner_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_partner_id
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#, python-format
msgid "Partner"
msgstr "Partenaire"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_commercial_partner_id
msgid "Partner Company"
msgstr "Société partenaire"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_partner_ledger_menu
#: model:ir.actions.report,name:account.action_report_partnerledger
#: model:ir.ui.menu,name:account.menu_partner_ledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
msgid "Partner Ledger"
msgstr "Livre des tiers"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_partner_name
msgid "Partner Name"
msgstr "Nom du partenaire"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_partner_type
#: model:ir.model.fields,field_description:account.field_account_payment_partner_type
#: model:ir.model.fields,field_description:account.field_account_register_payments_partner_type
msgid "Partner Type"
msgstr "Type de partenaire"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_result_selection
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_result_selection
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_result_selection
msgid "Partner's"
msgstr "Du partenaire"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Partners"
msgstr "Partenaires"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:106
#, python-format
msgid "Past"
msgstr "Passé"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Pay your bills in one-click using Euro SEPA service"
msgstr "Payez vos factures en un clic à l'aide du service Euro SEPA"

#. module: account
#: model:account.account.type,name:account.data_account_type_payable
#: selection:account.account.type,type:0
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Payable"
msgstr "Payable"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_payable_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Payable Account"
msgstr "Compte fournisseur"

#. module: account
#: selection:account.aged.trial.balance,result_selection:0
#: selection:account.common.partner.report,result_selection:0
#: selection:account.report.partner.ledger,result_selection:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.view_account_search
msgid "Payable Accounts"
msgstr "Comptes fournisseurs"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_debit_limit
#: model:ir.model.fields,field_description:account.field_res_users_debit_limit
msgid "Payable Limit"
msgstr "Limite de débit"

#. module: account
#: model:ir.ui.view,arch_db:account.product_template_form_view
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Payables"
msgstr "Dettes"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_amount
#: model:ir.model.fields,field_description:account.field_account_payment_amount
#: model:ir.model.fields,field_description:account.field_account_register_payments_amount
msgid "Payment Amount"
msgstr "Montant du paiement"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_date
#: model:ir.model.fields,field_description:account.field_account_payment_payment_date
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_date
msgid "Payment Date"
msgstr "Date de règlement"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_payment_difference
#: model:ir.model.fields,field_description:account.field_account_payment_payment_difference_handling
msgid "Payment Difference"
msgstr "Différence de paiement"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_journal_id
#: model:ir.model.fields,field_description:account.field_account_payment_journal_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_journal_id
msgid "Payment Journal"
msgstr "Journal des Paiements"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Payment Matching"
msgstr "Correspondance des paiements"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Payment Method"
msgstr "Méthode de paiement"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_method_id
#: model:ir.model.fields,field_description:account.field_account_payment_payment_method_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_method_id
msgid "Payment Method Type"
msgstr "Type des méthodes de paiement"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:60
#, python-format
msgid "Payment Method:"
msgstr "Méthode de paiement :"

#. module: account
#: model:ir.model,name:account.model_account_payment_method
#: model:ir.model.fields,field_description:account.field_account_journal_outbound_payment_method_ids
msgid "Payment Methods"
msgstr "Moyens de paiement"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_payment_move_line_ids
msgid "Payment Move Lines"
msgstr "Lignes de transfert de paiement"

#. module: account
#: model:ir.actions.report,name:account.action_report_payment_receipt
msgid "Payment Receipt"
msgstr "Reçu de paiement"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Payment Receipt:"
msgstr "Reçu de paiement :"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_reference_type
#: model:ir.model.fields,field_description:account.field_account_payment_payment_reference
msgid "Payment Reference"
msgstr "Référence du paiement"

#. module: account
#: model:ir.actions.act_window,name:account.action_payment_term_form
#: model:ir.model,name:account.model_account_payment_term
#: model:ir.model.fields,field_description:account.field_account_invoice_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_payment_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_name
#: model:ir.ui.menu,name:account.menu_action_payment_term_form
#: model:ir.ui.view,arch_db:account.account_planner
#: model:ir.ui.view,arch_db:account.view_payment_term_form
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
#: model:ir.ui.view,arch_db:account.view_payment_term_line_tree
#: model:ir.ui.view,arch_db:account.view_payment_term_search
#: model:ir.ui.view,arch_db:account.view_payment_term_tree
msgid "Payment Terms"
msgstr "Conditions de règlement"

#. module: account
#: model:ir.model,name:account.model_account_payment_term_line
msgid "Payment Terms Line"
msgstr "Détail des conditions de règlement"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_type
#: model:ir.model.fields,field_description:account.field_account_payment_method_payment_type
#: model:ir.model.fields,field_description:account.field_account_payment_payment_type
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_type
msgid "Payment Type"
msgstr "Type de paiement"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_form
msgid "Payment terms explanation for the customer..."
msgstr "Explication des conditions de règlement pour le client..."

#. module: account
#: model:account.payment.term,note:account.account_payment_term_15days
msgid "Payment terms: 15 Days"
msgstr "Conditions de paiement : 15 jours"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_net
msgid "Payment terms: 30 Net Days"
msgstr "Condition de paiement : 30 jours net"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_advance
msgid "Payment terms: 30% Advance End of Following Month"
msgstr "Conditions de paiement : 30 % d'avance, fin du mois suivant"

#. module: account
#: model:account.payment.term,note:account.account_payment_term
msgid "Payment terms: End of Following Month"
msgstr "Conditions de paiement: fin du mois suivant"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_immediate
msgid "Payment terms: Immediate Payment"
msgstr "Condition de paiement : Paiement immédiat"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_payment_id
msgid "Payment that created this entry"
msgstr "Paiement qui a créé cette écriture"

#. module: account
#: code:addons/account/models/account_payment.py:229
#: model:ir.actions.act_window,name:account.action_account_payments
#: model:ir.actions.act_window,name:account.action_account_payments_payable
#: model:ir.model,name:account.model_account_payment
#: model:ir.model.fields,field_description:account.field_account_invoice_payment_ids
#: model:ir.ui.menu,name:account.menu_action_account_payments_payable
#: model:ir.ui.menu,name:account.menu_action_account_payments_receivable
#: model:ir.ui.view,arch_db:account.partner_view_buttons
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#, python-format
msgid "Payments"
msgstr "Paiements"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "Correspondance des paiements"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_payments_widget
msgid "Payments Widget"
msgstr "Widget des paiements"

#. module: account
#: model:ir.actions.act_window,help:account.action_account_payments
#: model:ir.actions.act_window,help:account.action_account_payments_payable
msgid ""
"Payments are used to register liquidity movements (send, collect or transfer money).\n"
"                  You can then process those payments by your own means or by using installed facilities."
msgstr ""
"Les paiements sont utilisés pour enregistrer des mouvements de trésorerie "
"(envoi, collecte ou transfert d'argent). Vous pouvez ensuite traiter ces "
"paiements par vos propres moyens ou en utilisant les mécanismes installés."

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Payments to do"
msgstr "Paiements à faire"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_tree_pending_invoice
msgid "Pending Invoice"
msgstr "Facture en attente"

#. module: account
#: selection:account.payment.term.line,value:0
msgid "Percent"
msgstr "Pourcent"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_matched_percentage
msgid "Percentage Matched"
msgstr "Pourcentage de correspondance"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Percentage of Price"
msgstr "Pourcentage du prix"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Percentage of Price Tax Included"
msgstr "Pourcentage du prix taxes incluses"

#. module: account
#: selection:account.reconcile.model,second_amount_type:0
#: selection:account.reconcile.model.template,second_amount_type:0
msgid "Percentage of amount"
msgstr "Pourcentage du montant"

#. module: account
#: selection:account.reconcile.model,amount_type:0
#: selection:account.reconcile.model.template,amount_type:0
msgid "Percentage of balance"
msgstr "Pourcentage de la balance"

#. module: account
#: code:addons/account/models/account_invoice.py:1776
#, python-format
msgid "Percentages for Payment Terms Line must be between 0 and 100."
msgstr ""
"Les pourcentages de la limite de paiement doivent être compris entre 0 et "
"100."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Period"
msgstr "Période"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_period_length
msgid "Period Length (days)"
msgstr "Durée de la période (jours)"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_plaid
msgid "Plaid Connector"
msgstr "Connecteur Plaid"

#. module: account
#: model:ir.model,name:account.model_web_planner
msgid "Planner"
msgstr "Planificateur"

#. module: account
#: code:addons/account/wizard/pos_box.py:27
#, python-format
msgid "Please check that the field 'Journal' is set on the Bank Statement"
msgstr "Vérifiez que le champ 'Journal' est rempli sur le relevé de banque"

#. module: account
#: code:addons/account/wizard/pos_box.py:29
#, python-format
msgid "Please check that the field 'Transfer Account' is set on the company."
msgstr ""
"SVP, veuillez vérifier que le champ \"Compte de transfert\" est paramétré "
"sur la société."

#. module: account
#: code:addons/account/models/account_invoice.py:1070
#, python-format
msgid "Please create some invoice lines."
msgstr "Merci de créer des lignes de facture."

#. module: account
#: code:addons/account/models/account_move.py:157
#, python-format
msgid "Please define a sequence for the credit notes"
msgstr "Veuillez définir une séquence pour les avoirs"

#. module: account
#: code:addons/account/models/account_move.py:162
#, python-format
msgid "Please define a sequence on the journal."
msgstr "Veuillez définir une séquence sur le journal."

#. module: account
#: code:addons/account/models/account_invoice.py:1068
#, python-format
msgid "Please define sequence on the journal related to this invoice."
msgstr ""
"Veuillez définir une séquence dans le journal relatif à cette facture."

#. module: account
#: code:addons/account/models/company.py:336
#, python-format
msgid ""
"Please install a chart of accounts or create a miscellaneous journal before "
"proceeding."
msgstr ""
"Mettez en place un plan comptable ou créez un journal des opérations "
"diverses avant de continuer."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Bank"
msgstr "Plus banque"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Fixed Assets"
msgstr "Plus immobilisations"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Net Profit"
msgstr "Plus Profit net"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Non-Current Assets"
msgstr "Plus actif immobilisé"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Other Income"
msgstr "Plus autres revenus"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_wizard_form
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Post"
msgstr "Comptabiliser"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Post All Entries"
msgstr "Comptabiliser toutes les écritures"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Post Difference In"
msgstr "Comptabiliser la différence dans"

#. module: account
#: model:ir.actions.act_window,name:account.action_validate_account_move
#: model:ir.ui.view,arch_db:account.validate_account_move_view
msgid "Post Journal Entries"
msgstr "Comptabiliser les écritures"

#. module: account
#: selection:account.move,state:0 selection:account.payment,state:0
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Posted"
msgstr "Comptabilisé"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Posted Journal Entries"
msgstr "Écritures comptabilisées"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Posted Journal Items"
msgstr "Écritures comptabilisées"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_bank_account_code_prefix
#: model:ir.model.fields,field_description:account.field_res_company_bank_account_code_prefix
msgid "Prefix of the bank accounts"
msgstr "Préfixe pour les comptes bancaires"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_cash_account_code_prefix
msgid "Prefix of the cash accounts"
msgstr "Préfixe pour les comptes de trésorerie"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_cash_account_code_prefix
msgid "Prefix of the main cash accounts"
msgstr "Préfixe pour les comptes de trésorerie principaux"

#. module: account
#: model:account.account.type,name:account.data_account_type_prepayments
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Prepayments"
msgstr "Prépaiements"

#. module: account
#: selection:account.financial.report,sign:0
msgid "Preserve balance sign"
msgstr "Conserver le signe de la balance"

#. module: account
#: model:ir.model,name:account.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""
"Préconfigurer pour créer des pièces comptables pendant la correspondance "
"entre des factures et des paiements"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.account_common_report_view
msgid "Print"
msgstr "Imprimer"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Print Invoice"
msgstr "Imprimer la facture"

#. module: account
#: model:ir.model.fields,help:account.field_account_common_journal_report_amount_currency
#: model:ir.model.fields,help:account.field_account_print_journal_amount_currency
msgid ""
"Print Report with the currency column if the currency differs from the "
"company currency."
msgstr ""
"Imprimer le rapport avec la colonne monnaie si la devise diffère de la "
"devise de la société."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Print checks to pay your vendors"
msgstr "Imprimer les chèques pour payer vos fournisseurs"

#. module: account
#: model:ir.model,name:account.model_product_product
#: model:ir.model.fields,field_description:account.field_account_analytic_line_product_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_product_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_product_id
#: model:ir.model.fields,field_description:account.field_account_move_line_product_id
#: model:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
msgid "Product"
msgstr "Article"

#. module: account
#: model:ir.model,name:account.model_product_category
#: model:ir.model.fields,field_description:account.field_account_invoice_report_categ_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Product Category"
msgstr "Catégorie d'article"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_product_image
msgid "Product Image"
msgstr "Image de l'article"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_product_qty
msgid "Product Quantity"
msgstr "Quantité d'articles"

#. module: account
#: model:ir.model,name:account.model_product_template
msgid "Product Template"
msgstr "Modèle d'article"

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action
#: model:ir.ui.view,arch_db:account.product_product_view_tree
msgid "Products"
msgstr "Articles"

#. module: account
#: code:addons/account/models/account_bank_statement.py:177
#, python-format
msgid "Profit"
msgstr "Profit"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Profit &amp; Loss"
msgstr "Pertes et profits"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_profitloss_toreport0
msgid "Profit (Loss) to report"
msgstr "Bénéfice (perte) à reporter"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_profit_account_id
msgid "Profit Account"
msgstr "Compte de profit"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_profitandloss0
#: model:ir.actions.act_window,name:account.action_account_report_pl
#: model:ir.ui.menu,name:account.menu_account_report_pl
msgid "Profit and Loss"
msgstr "Compte de résultat"

#. module: account
#: code:addons/account/models/account_payment.py:135
#, python-format
msgid ""
"Programming error: wizard action executed without active_ids in context."
msgstr ""
"Erreur de programmation : action de l'assistant exécutée sans active_ids "
"dans le contexte."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_form
msgid "Properties"
msgstr "Propriétés"

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action_purchasable
#: model:ir.ui.menu,name:account.product_product_menu_purchasable
msgid "Purchasable Products"
msgstr "Produits pouvant être achetés"

#. module: account
#: selection:account.journal,type:0
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Purchase"
msgstr "Achats"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_wizard_multi_chart
msgid "Purchase Tax"
msgstr "Taxe d'achat"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_purchase_tax_rate
msgid "Purchase Tax(%)"
msgstr "Taxe d'achat (%)"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:35
#, python-format
msgid "Purchase: Untaxed Total"
msgstr "Achat : total HT"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
#: model:ir.ui.menu,name:account.menu_finance_payables
#: model:ir.ui.view,arch_db:account.view_account_journal_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Purchases"
msgstr "Achats"

#. module: account
#: model:ir.actions.act_window,name:account.action_cash_box_in
#: model:ir.ui.view,arch_db:account.cash_box_in_form
msgid "Put Money In"
msgstr "Mettre de l'argent dedans"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Python Code"
msgstr "Code Python"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "QIF Import"
msgstr "Import QIF"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_quantity
#: model:ir.model.fields,field_description:account.field_account_move_line_quantity
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Quantity"
msgstr "Quantité"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_description
#: model:ir.model.fields,field_description:account.field_cash_box_in_name
#: model:ir.model.fields,field_description:account.field_cash_box_out_name
msgid "Reason"
msgstr "Motif"

#. module: account
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Reason..."
msgstr "Motif ..."

#. module: account
#: model:account.account.type,name:account.data_account_type_receivable
#: selection:account.account.type,type:0
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Receivable"
msgstr "Recevable"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_receivable_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Receivable Account"
msgstr "Compte client"

#. module: account
#: selection:account.aged.trial.balance,result_selection:0
#: selection:account.common.partner.report,result_selection:0
#: selection:account.report.partner.ledger,result_selection:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.view_account_form
#: model:ir.ui.view,arch_db:account.view_account_search
msgid "Receivable Accounts"
msgstr "Comptes clients"

#. module: account
#: selection:account.aged.trial.balance,result_selection:0
#: selection:account.common.partner.report,result_selection:0
#: selection:account.report.partner.ledger,result_selection:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Receivable and Payable Accounts"
msgstr "Comptes de tiers"

#. module: account
#: model:ir.ui.view,arch_db:account.product_template_form_view
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Receivables"
msgstr "Créances"

#. module: account
#: selection:account.abstract.payment,payment_type:0
#: selection:account.payment,payment_type:0
#: selection:account.register.payments,payment_type:0
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Receive Money"
msgstr "Règlement"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:73
#: code:addons/account/static/src/xml/account_reconciliation.xml:105
#: code:addons/account/static/src/xml/account_reconciliation.xml:106
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#, python-format
msgid "Reconcile"
msgstr "Lettrer"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_account_move_line_reconcile
msgid "Reconcile Entries"
msgstr "Lettrer les écritures"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Reconcile With Write-Off"
msgstr "Lettrage avec écart"

#. module: account
#: code:addons/account/wizard/account_reconcile.py:86
#, python-format
msgid "Reconcile Writeoff"
msgstr "Lettrer la radiation"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/tour_bank_statement_reconciliation.js:11
#, python-format
msgid "Reconcile the demo bank statement"
msgstr "Lettrer le relevé bancaire de démo"

#. module: account
#: selection:account.payment,state:0
#: model:ir.model.fields,field_description:account.field_account_move_line_reconciled
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Reconciled"
msgstr "Lettré"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_reconciled
msgid "Reconciled Entries"
msgstr "Écritures lettrées"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Reconciled entries"
msgstr "Écritures lettrées"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Reconciliation"
msgstr "Lettrage"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_reconcile_model
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Reconciliation Models"
msgstr "Modèles de lettrage"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_partial_reconcile_ids
msgid "Reconciliation Parts"
msgstr "Espaces de réconciliation"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Reconciliation Transactions"
msgstr "Transactions de lettrage"

#. module: account
#: model:ir.actions.client,name:account.action_bank_reconcile
#: model:ir.actions.client,name:account.action_bank_reconcile_bank_statements
msgid "Reconciliation on Bank Statements"
msgstr "Lettrage de relevés bancaires"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Reconciling journal entries"
msgstr "Lettrage d'écritures en cours"

#. module: account
#: selection:account.journal,bank_statements_source:0
msgid "Record Manually"
msgstr "Enregistrez manuellement"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Record transactions in foreign currencies"
msgstr "Enregistrer des transactions dans des devises étrangères"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Recording invoices"
msgstr "Enregistrement de factures en cours"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:231
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#, python-format
msgid "Ref"
msgstr "Réf"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_ref
msgid "Ref."
msgstr "Réf."

#. module: account
#: code:addons/account/controllers/portal.py:72
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_ref
#: model:ir.model.fields,field_description:account.field_account_bank_statement_name
#: model:ir.model.fields,field_description:account.field_account_move_line_ref
#: model:ir.model.fields,field_description:account.field_account_move_ref
#: model:ir.model.fields,field_description:account.field_cash_box_in_ref
#, python-format
msgid "Reference"
msgstr "Référence"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_uom_name
msgid "Reference Unit of Measure"
msgstr "Unité de mesure de référence"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Reference number"
msgstr "Numéro de référence"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_origin
#: model:ir.model.fields,help:account.field_account_invoice_origin
msgid "Reference of the document that produced this invoice."
msgstr "Référence du document qui a produit cette facture."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_payment_reference
msgid ""
"Reference of the document used to issue this payment. Eg. check number, file"
" name, etc."
msgstr ""
"Référence du document utilisée pour émettre ce paiement, c'est-à-dire numéro"
" du chèque, nom du fichier, etc."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_name
msgid "Reference/Description"
msgstr "Référence/description"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_invoice_ids
msgid "Refund Invoices"
msgstr "Avoirs"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_filter_refund
msgid "Refund Method"
msgstr "Méthode de remboursement"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_refund_filter_refund
msgid ""
"Refund base on this type. You can not Modify and Cancel if the invoice is "
"already reconciled"
msgstr ""
"Base d'avoir sur ce type. Vous ne pouvez pas modifier et annuler si la "
"facture est déjà lettrée"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_payment
#: model:ir.actions.act_window,name:account.action_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.view_account_payment_form
#: model:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Register Payment"
msgstr "Enregistrer un paiement"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Register Payments"
msgstr "Enregistrer des paiements"

#. module: account
#: model:ir.model,name:account.model_account_register_payments
msgid "Register payments on multiple invoices"
msgstr "Enregistrez des paiements sur plusieurs factures"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Registering outstanding invoices and payments can be a huge undertaking, \n"
"                        but you can start using Odoo without it by:"
msgstr ""
"Enregistrer les factures et paiements en circulation peut être une énorme "
"entreprise, mais vous pouvez commencer à utiliser Odoo sans en :"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Registering payments related to outstanding invoices separately in a "
"different account (e.g. Account Receivables 2014)"
msgstr ""
"Enregistrement des paiements associés aux factures en circulation dans un "
"journal séparé (ex : Comptes recevables 2014)"

#. module: account
#: selection:account.account.type,type:0
msgid "Regular"
msgstr "Régulier"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_residual_company_signed
msgid "Remaining amount due in the currency of the company."
msgstr "Montant restant dû dans la devise de la société."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_residual_signed
msgid "Remaining amount due in the currency of the invoice."
msgstr "Montant restant dû dans la devise de la facture."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_residual
msgid "Remaining amount due."
msgstr "Montant restant dû."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_tax_dest_id
msgid "Replacement Tax"
msgstr "Taxe de remplacement"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_financial_report_form
msgid "Report"
msgstr "Rapport"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_name
msgid "Report Name"
msgstr "Nom du rapport"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.account_common_report_view
msgid "Report Options"
msgstr "Options du rapport"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
msgid "Report Type"
msgstr "Type de Rapport"

#. module: account
#: selection:account.financial.report,type:0
#: model:ir.model.fields,field_description:account.field_account_financial_report_account_report_id
msgid "Report Value"
msgstr "Valeur du rapport"

#. module: account
#: model:ir.ui.menu,name:account.account_report_folder
#: model:ir.ui.menu,name:account.menu_finance_reports
msgid "Reporting"
msgstr "Rapport"

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding_rounding
msgid "Represent the non-zero value smallest coinage (for example, 0.05)."
msgstr ""
"Représentez la pièce ayant la plus petite valeur unitaire différente de zéro"
" (par exemple, 0,05)."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Reset to Draft"
msgstr "Remettre en brouillon"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:236
#, python-format
msgid "Residual"
msgstr "Résiduel"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_amount_residual
msgid "Residual Amount"
msgstr "Montant résiduel"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_amount_residual_currency
msgid "Residual Amount in Currency"
msgstr "Montant résiduel en devise"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_user_id
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Responsible"
msgstr "Responsable"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_deferred_revenue
msgid "Revenue Recognition"
msgstr "Reconnaissance des revenus"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal_date
msgid "Reversal date"
msgstr "Date de l'extourne"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Reverse Entry"
msgstr "Écriture d'extourne"

#. module: account
#: code:addons/account/wizard/account_move_reversal.py:20
#: model:ir.actions.act_window,name:account.action_view_account_move_reversal
#: model:ir.ui.view,arch_db:account.view_account_move_reversal
#, python-format
msgid "Reverse Moves"
msgstr "Extourner les pièces"

#. module: account
#: selection:account.financial.report,sign:0
msgid "Reverse balance sign"
msgstr "Renverser le signe de la balance"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review Fiscal Positions"
msgstr "Réviser les positions fiscales"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review Terms"
msgstr "Modifier les termes"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review existing Taxes"
msgstr "Revoir les taxes existantes"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review the Chart of Accounts"
msgstr "Revoir le plan de comptes"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review the list of available currencies (from the"
msgstr "Revoir la liste des devises disponibles (depuis le"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group_parent_right
msgid "Right Parent"
msgstr "Parent à droite"

#. module: account
#: selection:res.company,tax_calculation_rounding_method:0
msgid "Round Globally"
msgstr "Arrondir globalement"

#. module: account
#: selection:res.company,tax_calculation_rounding_method:0
msgid "Round per Line"
msgstr "Arrondir à la ligne"

#. module: account
#: model:ir.ui.view,arch_db:account.rounding_form_view
msgid "Rounding Form"
msgstr "Forme d'arrondi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_is_rounding_line
msgid "Rounding Line"
msgstr "Ligne d'arrondi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_rounding_method
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Rounding Method"
msgstr "Méthode d'arrondi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_rounding
msgid "Rounding Precision"
msgstr "Précision d'arrondi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_strategy
msgid "Rounding Strategy"
msgstr "Stratégie d'arrondi"

#. module: account
#: model:ir.ui.view,arch_db:account.rounding_tree_view
msgid "Rounding Tree"
msgstr "Arbre d'arrondi"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_sepa
msgid "SEPA Credit Transfer (SCT)"
msgstr "Transfert de crédit SEPA  (SCT)"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "SEPA Direct Debit (SDD)"
msgstr " Débit direct SEPA (SDD)"

#. module: account
#: selection:account.journal,type:0
#: model:ir.ui.view,arch_db:account.report_tax
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Sale"
msgstr "Vente"

#. module: account
#: model:ir.ui.view,arch_db:account.view_wizard_multi_chart
msgid "Sale Tax"
msgstr "Taxe à la vente"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
#: model:ir.ui.menu,name:account.menu_finance_receivables
#: model:ir.ui.view,arch_db:account.view_account_journal_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Sales"
msgstr "Vente"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Sales Tax"
msgstr "Taxe de vente"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_sale_tax_rate
msgid "Sales Tax(%)"
msgstr "Taxe de vente (%)"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:33
#, python-format
msgid "Sales: Untaxed Total"
msgstr "Ventes : total HT"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_id
#: model:ir.model.fields,field_description:account.field_account_invoice_user_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Salesperson"
msgstr "Vendeur"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model:ir.ui.view,arch_db:account.setup_opening_move_wizard_form
#: model:ir.ui.view,arch_db:account.setup_posted_move_form
#: model:ir.ui.view,arch_db:account.setup_view_company_form
msgid "Save"
msgstr "Sauvegarder"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:199
#, python-format
msgid "Save and New"
msgstr "Sauvegarder et Créer nouveau"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr ""
"Sauvegardez cette page et revenez ici pour configurer la fonctionnalité."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Search Account Journal"
msgstr "Chercher un journal"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_template_search
msgid "Search Account Templates"
msgstr "Chercher un modèle de compte"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Search Bank Statements"
msgstr "Chercher un relevé bancaire"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_line_search
msgid "Search Bank Statements Line"
msgstr "Chercher une ligne de relevés bancaires"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Search Chart of Account Templates"
msgstr "Cherche un modèle de plan de comptes"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Search Invoice"
msgstr "Chercher une facture"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Search Journal Items"
msgstr "Chercher des pièces"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Search Move"
msgstr "Chercher une pièce"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Search Operations"
msgstr "Chercher des opérations"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Search Tax Templates"
msgstr "Rechercher les modèles de taxe"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Search Taxes"
msgstr "Chercher des taxes"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_account_id
msgid "Second Account"
msgstr "Deuxième Compte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_amount
msgid "Second Amount"
msgstr "Deuxième Montant"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_amount_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_amount_type
msgid "Second Amount type"
msgstr "Deuxième type de Montant"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_analytic_account_id
msgid "Second Analytic Account"
msgstr "Deuxième Compte analytique"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_journal_id
msgid "Second Journal"
msgstr "Deuxième Journal"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_label
msgid "Second Journal Item Label"
msgstr "Deuxième étiquette de ligne de journal"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_tax_id
msgid "Second Tax"
msgstr "Deuxième Taxe"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_access_token
msgid "Security Token"
msgstr "Jeton de sécurité"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_type
msgid ""
"Select 'Sale' for customer invoices journals.\n"
"Select 'Purchase' for vendor bills journals.\n"
"Select 'Cash' or 'Bank' for journals that are used in customer or vendor payments.\n"
"Select 'General' for miscellaneous operations journals."
msgstr ""
"Sélectionnez 'Vente' pour les journaux des factures des clients.\n"
"Sélectionnez 'Achat' pour les journaux des factures des fournisseurs.\n"
"Sélectionnez 'Espèces' ou 'Banque' pour les journaux utilisés pour les paiements des clients ou des fournisseurs.\n"
"Sélectionnez 'Général' pour les journaux des opérations diverses."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:74
#, python-format
msgid "Select a partner or choose a counterpart"
msgstr "Sélectionner un tiers ou une contre-partie"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line_value
msgid "Select here the kind of valuation related to this payment terms line."
msgstr ""
"Choisissez le type de valorisation lié à cette ligne de condition de "
"paiements."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Select this if the taxes should use cash basis, which will create an entry "
"for such taxes on a given account during reconciliation."
msgstr ""
"Sélectionnez ceci si les taxes doivent faire l'objet d'une comptabilité de "
"caisse, ceci créera une écriture pour ces taxes sur un compte donné pendant "
"le lettrage"

#. module: account
#: code:addons/account/wizard/account_invoice_state.py:21
#, python-format
msgid ""
"Selected invoice(s) cannot be confirmed as they are not in 'Draft' state."
msgstr ""
"Les factures sélectionnées ne peuvent pas être confirmées car elles sont en "
"'Brouillon'."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_invoice_warn
#: model:ir.model.fields,help:account.field_res_users_invoice_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Sélectionner l'option 'Avertissement' notifiera l'utilisateur avec le "
"Message. Sélectionner 'Message Bloquant' lancera une exception avec le "
"message et bloquera le flux. Le Message doit être encodé dans le champ "
"suivant."

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action_sellable
#: model:ir.ui.menu,name:account.product_product_menu_sellable
msgid "Sellable Products"
msgstr "Produits en vente"

#. module: account
#: selection:account.abstract.payment,payment_type:0
#: selection:account.payment,payment_type:0
#: selection:account.register.payments,payment_type:0
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Send Money"
msgstr "Envoyer de l'argent"

#. module: account
#: model:ir.actions.act_window,name:account.account_send_payment_receipt_by_email_action
msgid "Send Receipt By Email"
msgstr "Envoyer le reçu par email"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Send by Email"
msgstr "Envoyer par email"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Sending customer statements (outstanding invoices) manually during the "
"transition period"
msgstr ""
"Envoi des rappels clients (factures en circulation) manuellement durant la "
"période de transition"

#. module: account
#: selection:account.payment,state:0
#: model:ir.model.fields,field_description:account.field_account_invoice_sent
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Sent"
msgstr "Envoyé"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "September"
msgstr "Septembre"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_sequence
#: model:ir.model.fields,field_description:account.field_account_financial_report_sequence
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_sequence
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_sequence
#: model:ir.model.fields,field_description:account.field_account_invoice_line_sequence
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_sequence
#: model:ir.model.fields,field_description:account.field_account_journal_sequence
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_sequence
#: model:ir.model.fields,field_description:account.field_account_payment_term_sequence
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_sequence
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_sequence
#: model:ir.model.fields,field_description:account.field_account_tax_group_sequence
#: model:ir.model.fields,field_description:account.field_account_tax_sequence
#: model:ir.model.fields,field_description:account.field_account_tax_template_sequence
msgid "Sequence"
msgstr "Séquence"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Set To Draft"
msgstr "Marquer comme brouillon"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag_active
msgid "Set active to false to hide the Account Tag without removing it."
msgstr ""
"Mettre le champs actif à faux pour masquer l'étiquette du compte sans la "
"supprimer."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_active
msgid "Set active to false to hide the Journal without removing it."
msgstr ""
"Mettre le champs actif à faux pour masquer le journal sans le supprimer."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_active
#: model:ir.model.fields,help:account.field_account_tax_template_active
msgid "Set active to false to hide the tax without removing it."
msgstr "Mettre active à faux pour cache la taxe sans la supprimer."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Set the default Sales and Purchase taxes"
msgstr "Saisir les taxes de ventes et achats par défaut"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_tax_adjustment
msgid ""
"Set this field to true if this tax can be used in the tax adjustment wizard,"
" used to manually fill some data in the tax declaration"
msgstr ""
"Définissez ce champ sur VRAI si cette taxe peut être utilisée dans "
"l'assistant d'ajustement fiscal, utilisé pour remplir manuellement certaines"
" données de la déclaration fiscale"

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_visible
msgid ""
"Set this to False if you don't want this template to be used actively in the"
" wizard that generate Chart of Accounts from templates, this is useful when "
"you want to generate accounts of this template only when loading its child "
"template."
msgstr ""
"Mettre ceci à faux si vous ne voulez pas que ce modèle soit utilisé dans "
"l'assistant qui crée votre charte de compte depuis les modèles, c'est utile "
"si vous voulez générer les comptes de ce modèle seulement en chargeant le "
"sous-modèle."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Set to Draft"
msgstr "Marquer comme brouillon"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_config
#: model:ir.ui.menu,name:account.menu_account_config
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Settings"
msgstr "Configuration"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Setup"
msgstr "Configuration"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_bar_closed
msgid "Setup Bar Closed"
msgstr "Barre de configuration fermée"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_code
msgid "Short Code"
msgstr "Code"

#. module: account
#: model:res.groups,name:account.group_account_user
msgid "Show Full Accounting Features"
msgstr "Montrer les fonctions de comptabilité complète"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Show active taxes"
msgstr "Montrer les taxes actives"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Montrez toutes les enregistrements pour lesquelles la date des prochaines "
"actions est pour aujourd'hui ou avant."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Show inactive taxes"
msgstr "Montrer les taxes inactives"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_show_on_dashboard
msgid "Show journal on dashboard"
msgstr "Montrer le journal dans le tableau de bord"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_sign
msgid "Sign on Reports"
msgstr "Signe sur les rapports"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Simplify your cash, checks, or credit cards deposits with an integrated "
"batch payment function."
msgstr ""
"Simplifie vos dépôts de liquide, chèque ou cartes de crédit avec une "
"fonction de paiement en lot intégrée."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:107
#, python-format
msgid "Skip"
msgstr "Ignorer"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Smallest Text"
msgstr "Plus petit texte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_sortby
msgid "Sort by"
msgstr "Trier par"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_origin
#: model:ir.model.fields,field_description:account.field_account_invoice_origin
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Source Document"
msgstr "Document d'origine"

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding_strategy
msgid ""
"Specify which way will be used to round the invoice amount to the rounding "
"precision"
msgstr ""
"Indiquez le moyen qui sera utilisé pour arrondir le montant de la facture"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_date_from
#: model:ir.model.fields,field_description:account.field_account_balance_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_account_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_report_date_from
#: model:ir.model.fields,field_description:account.field_account_print_journal_date_from
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_date_from
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_date_from
#: model:ir.model.fields,field_description:account.field_account_tax_report_date_from
#: model:ir.model.fields,field_description:account.field_accounting_report_date_from
#: model:ir.model.fields,field_description:account.field_accounting_report_date_from_cmp
msgid "Start Date"
msgstr "Date de début"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_balance_start
msgid "Starting Balance"
msgstr "Solde initial"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_start_id
msgid "Starting Cashbox"
msgstr "Ouverture de la Caisse"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "State"
msgstr "Statut"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_parent_state
msgid "State of the parent account.move"
msgstr "État de account.move parent"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_statement_id
#: model:ir.model.fields,field_description:account.field_account_move_line_statement_id
msgid "Statement"
msgstr "Relevé"

#. module: account
#: code:addons/account/models/account_bank_statement.py:245
#, python-format
msgid "Statement %s confirmed, journal items were created."
msgstr "Relevé %s confirmé, les écritures ont été créées."

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_line_form
msgid "Statement Line"
msgstr "Ligne de relevé"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_ids
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_line_tree
msgid "Statement lines"
msgstr "Lignes de relevé"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_tree
msgid "Statements"
msgstr "Relevés"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "States"
msgstr "États"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_states_count
msgid "States Count"
msgstr "Nombre d'états"

#. module: account
#: code:addons/account/controllers/portal.py:73
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_state
#: model:ir.model.fields,field_description:account.field_account_bank_statement_state
#: model:ir.model.fields,field_description:account.field_account_invoice_state
#: model:ir.model.fields,field_description:account.field_account_move_state
#: model:ir.model.fields,field_description:account.field_account_payment_state
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Status"
msgstr "État"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_subtotal
msgid "Subtotal"
msgstr "Sous-total"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Supplier Payments"
msgstr "Paiements fournisseurs"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "TOTAL ASSETS"
msgstr "TOTAL DES ACTIFS"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "TOTAL EQUITY"
msgstr "Total des capitaux propres"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_ids
#: model:ir.model.fields,field_description:account.field_account_tax_tag_ids
#: model:ir.ui.view,arch_db:account.account_tag_view_form
msgid "Tags"
msgstr "Étiquettes"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Tags for Multidimensional Analytics"
msgstr "Étiquettes pour la comptabilité analytique multi-dimensionnelle"

#. module: account
#: model:ir.actions.act_window,name:account.action_cash_box_out
#: model:ir.ui.view,arch_db:account.cash_box_out_form
msgid "Take Money Out"
msgstr "Retirer de l'argent"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_target_move
#: model:ir.model.fields,field_description:account.field_account_balance_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_account_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_report_target_move
#: model:ir.model.fields,field_description:account.field_account_print_journal_target_move
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_target_move
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_target_move
#: model:ir.model.fields,field_description:account.field_account_tax_report_target_move
#: model:ir.model.fields,field_description:account.field_accounting_report_target_move
msgid "Target Moves"
msgstr "Mouvements cibles"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:174
#: model:ir.model,name:account.model_account_tax
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_tax
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_tax_id
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_tax
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#, python-format
msgid "Tax"
msgstr "Taxe"

#. module: account
#: code:addons/account/models/chart_template.py:842
#: code:addons/account/models/chart_template.py:845
#, python-format
msgid "Tax %.2f%%"
msgstr "Taxe %.2f%%"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_account_id
msgid "Tax Account"
msgstr "Compte de taxe"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_refund_account_id
msgid "Tax Account on Credit Notes"
msgstr "Compte de taxe pour les avoirs"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_refund_account_id
msgid "Tax Account on Refunds"
msgstr "Compte de taxe pour les avoirs"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_tax_adjustment
#: model:ir.model.fields,field_description:account.field_account_tax_template_tax_adjustment
msgid "Tax Adjustment"
msgstr "Ajustement fiscal"

#. module: account
#: model:ir.actions.act_window,name:account.tax_adjustments_form
#: model:ir.ui.menu,name:account.menu_action_tax_adjustment
msgid "Tax Adjustments"
msgstr "Ajustements fiscaux"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Tax Amount"
msgstr "Montant de la taxe"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Tax Application"
msgstr "Application de la taxe"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_tax_calculation_rounding_method
msgid "Tax Calculation Rounding Method"
msgstr "Méthode d'arrondi pour le calcul de la taxe"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_tax_cash_basis_rec_id
msgid "Tax Cash Basis Entry of"
msgstr "Ecriture pour la taxe sur base des paiements"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_tax_cash_basis_journal_id
msgid "Tax Cash Basis Journal"
msgstr "Journal pour taxes sur base des paiements"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_amount_type
#: model:ir.model.fields,field_description:account.field_account_tax_template_amount_type
msgid "Tax Computation"
msgstr "Calcul de la taxe"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Tax Declaration"
msgstr "Déclaration de taxe"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_name
msgid "Tax Description"
msgstr "Description de la taxe"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_tax_exigibility
#: model:ir.model.fields,field_description:account.field_account_tax_template_tax_exigibility
msgid "Tax Due"
msgstr "Taxe due"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_tax_group_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_tax_group_id
msgid "Tax Group"
msgstr "Groupe de taxe"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Tax ID"
msgstr "Numéro de TVA"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_line_ids
msgid "Tax Lines"
msgstr "Lignes de taxe"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_tax_ids
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Tax Mapping"
msgstr "Correspondance des taxes"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_name
#: model:ir.model.fields,field_description:account.field_account_tax_template_name
msgid "Tax Name"
msgstr "Nom de la taxe"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_cash_basis_account
#: model:ir.model.fields,field_description:account.field_account_tax_template_cash_basis_account
msgid "Tax Received Account"
msgstr "Compte de taxe reçue"

#. module: account
#: model:ir.actions.report,name:account.action_report_account_tax
#: model:ir.model,name:account.model_account_tax_report
#: model:ir.ui.menu,name:account.menu_account_report
#: model:ir.ui.view,arch_db:account.report_tax
msgid "Tax Report"
msgstr "Rapport de taxes"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_tax_report
msgid "Tax Reports"
msgstr "Rapports de taxes"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_type_tax_use
#: model:ir.model.fields,field_description:account.field_account_tax_type_tax_use
msgid "Tax Scope"
msgstr "Portée de la taxe"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_tax_src_id
msgid "Tax Source"
msgstr "Base de la taxe"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Tax Template"
msgstr "Modèle de taxe"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_tax_template_ids
msgid "Tax Template List"
msgstr "Liste des modèles de taxe"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_tax_template_form
msgid "Tax Templates"
msgstr "Modèles de taxe"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr "Méthode d'arrondi pour le calcul des taxes"

#. module: account
#: sql_constraint:account.tax:0 sql_constraint:account.tax.template:0
msgid "Tax names must be unique !"
msgstr "Le nom de la taxe doit être unique!"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_tax_src_id
msgid "Tax on Product"
msgstr "Taxe sur l'article"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_tax_dest_id
msgid "Tax to Apply"
msgstr "Taxe à appliquer"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "TaxCloud"
msgstr "TaxCloud"

#. module: account
#: selection:account.account.tag,applicability:0
#: model:account.tax.group,name:account.tax_group_taxes
#: model:ir.actions.act_window,name:account.action_tax_form
#: model:ir.model.fields,field_description:account.field_account_invoice_line_invoice_line_tax_ids
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_ids
#: model:ir.ui.menu,name:account.menu_action_tax_form
#: model:ir.ui.view,arch_db:account.account_planner
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Taxes"
msgstr "Taxes"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_tax
msgid "Taxes Fiscal Position"
msgstr "Régime fiscal des taxes"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_template_form
msgid "Taxes Mapping"
msgstr "Correspondance des taxes"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Taxes used in Purchases"
msgstr "Taxes utilisées sur les achats"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Taxes used in Sales"
msgstr "Taxes sur les ventes"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Taxes, fiscal positions, chart of accounts &amp; legal statements for your "
"country"
msgstr ""
"Taxes, positions fiscales, plan comptable &amp; rapports légaux pour votre "
"pays"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_balance
msgid ""
"Technical field holding the debit - credit in order to open meaningful graph"
" views from reports"
msgstr ""
"Champ technique égal à 'débit - crédit', utilisé pour les vues graphes dans "
"les rapports"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_balance_cash_basis
msgid ""
"Technical field holding the debit_cash_basis - credit_cash_basis in order to"
" open meaningful graph views from reports"
msgstr ""
"Champ technique comprenant le debit_cash_basis - credit_cash_basis afin "
"d'ouvrir des graphiques pertients des rapports"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_move_name
msgid ""
"Technical field holding the number given to the invoice, automatically set "
"when the invoice is validated then stored to set the same number again if "
"the invoice is cancelled, set to draft and re-validated."
msgstr ""
"Champ technique gardant le numéro donné à la facture, configuré "
"automatiquement quand la facture est validée et ensuite classée pour "
"configurer le même numéro à nouveau si la facture est annulée, configurée en"
" brouillon et re-validée."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_move_name
#: model:ir.model.fields,help:account.field_account_payment_move_name
msgid ""
"Technical field holding the number given to the journal entry, automatically"
" set when the statement line is reconciled then stored to set the same "
"number again if the line is cancelled, set to draft and re-processed again."
msgstr ""
"Champ technique contenant le numéro attribué à l'écriture de journal, "
"renseigné automatiquement lorsque la ligne de relevé est réconciliée puis il"
" est stocké afin d'utiliser le même numéro si la ligne est annulée, modifiée"
" à l'état \"Brouillon\" et retraitée à nouveau."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_bank_data_done
msgid "Technical field holding the status of the bank setup step."
msgstr ""
"Champ technique comprenant le statut de l'étape de configuration de la "
"banque."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_coa_done
msgid "Technical field holding the status of the chart of account setup step."
msgstr ""
"Champ technique comprenant le statut de l'étape de configuration du plan "
"comptable."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_company_data_done
msgid "Technical field holding the status of the company setup step."
msgstr ""
"Champ technique comprenant le statut de l'étape de configuration de la "
"société."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_fy_data_done
msgid "Technical field holding the status of the financial year setup step."
msgstr ""
"Champ technique comprenant le statut de l'étape de configuration de l'année "
"fiscale."

#. module: account
#: model:ir.model.fields,help:account.field_account_register_payments_multi
msgid ""
"Technical field indicating if the user selected invoices from multiple "
"partners or from different types."
msgstr ""
"Champ technique indiquant si l'utilisateur a sélectionné des factures de "
"plusieurs partenaires ou de différents types."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_bar_closed
msgid ""
"Technical field set to True when setup bar has been closed by the user."
msgstr ""
"Champ technique défini sur Vrai lorsque la barre de configuration a été "
"fermée par l'utilisateur."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_refund_only
msgid ""
"Technical field to hide filter_refund in case invoice is partially paid"
msgstr ""
"Champ technique pour cacher filter_refund dans le cas où la facture est "
"partiellement payée"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_journal_type
#: model:ir.model.fields,help:account.field_account_payment_has_invoices
msgid "Technical field used for usability purposes"
msgstr "Champ technique utilisé à des fins ergonomiques"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_matched_percentage
msgid "Technical field used in cash basis method"
msgstr "Champ technique utilisé pour les méthodes sur paiements"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_account_setup_bank_data_done
msgid "Technical field used in the special view for the setup bar step."
msgstr ""
"Champ technique utilisé dans la vue spéciale pour l'étape de la barre de "
"configuration."

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_payment_method_code
#: model:ir.model.fields,help:account.field_account_payment_payment_method_code
#: model:ir.model.fields,help:account.field_account_register_payments_payment_method_code
msgid ""
"Technical field used to adapt the interface to the payment type selected."
msgstr ""
"Champ technique utilisé pour adapter l'interface au type de paiement "
"sélectionné."

#. module: account
#: model:ir.model.fields,help:account.field_account_partial_reconcile_max_date
msgid ""
"Technical field used to determine at which date this reconciliation needs to"
" be shown on the aged receivable/payable reports."
msgstr ""
"Champ technique utilisé pour déterminer la date à laquelle ce lettrage doit "
"être affiché sur les balances âgées des clients/fournisseurs."

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_hide_payment_method
#: model:ir.model.fields,help:account.field_account_payment_hide_payment_method
#: model:ir.model.fields,help:account.field_account_register_payments_hide_payment_method
msgid ""
"Technical field used to hide the payment method if the selected journal has "
"only one available which is 'manual'"
msgstr ""
"Champ technique utilisé pour cacher la méthode de paiement si le journal "
"sélectionné a seulement une méthode possible qui est \"manuelle\""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_tax_cash_basis_rec_id
msgid ""
"Technical field used to keep track of the tax cash basis reconciliation. "
"This is needed when cancelling the source: it will post the inverse journal "
"entry to cancel that part too."
msgstr ""
"Champ technique utilisé pour garder une trace des lettrages des taxes "
"d'encaissement. Ceci est nécessaire lors de l'annulation de la source : cela"
" comptabilisera aussi l'inverse dans l'écriture comptable pour annuler cette"
" partie."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_tax_exigible
msgid ""
"Technical field used to mark a tax line as exigible in the vat report or not"
" (only exigible journal items are displayed). By default all new journal "
"items are directly exigible, but with the feature cash_basis on taxes, some "
"will become exigible only when the payment is recorded."
msgstr ""
"Champ technique utilisé pour marquer une ligne de Taxe comme exigible ou non"
" dans le rapport de la TVA (seules les lignes de journal exigibles sont "
"affichées). Par défaut, toutes les nouvelles lignes de journal sont à l'état"
" « exigible », mais avec la fonction cash_basis sur les taxes, certaines ne "
"deviendront exigibles que lorsque le paiement est enregistré."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_is_unaffected_earnings_line
msgid ""
"Tells whether or not this line belongs to an unaffected earnings account"
msgstr ""
"Indique si cette ligne appartient ou non à un compte de bénéfices non "
"concerné"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_chart_template_id
msgid "Template"
msgstr "Modèle"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_account_template
msgid "Template Account Fiscal Mapping"
msgstr "Modèle de correspondance fiscale de compte"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_tax_template
msgid "Template Tax Fiscal Position"
msgstr "Modèle de position fiscale"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "Modèle de position fiscale"

#. module: account
#: model:ir.model,name:account.model_account_chart_template
msgid "Templates for Account Chart"
msgstr "Modèles pour le plan comptable"

#. module: account
#: model:ir.model,name:account.model_account_account_template
msgid "Templates for Accounts"
msgstr "Modèles pour les comptes"

#. module: account
#: model:ir.model,name:account.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Modèles pour les taxes"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Term Type"
msgstr "Types de délais"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_ids
#: model:ir.ui.view,arch_db:account.view_payment_term_form
msgid "Terms"
msgstr "Délais"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Terms &amp; Conditions"
msgstr "Conditions générales"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Terms and conditions..."
msgstr "Conditions générales..."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "That's on average"
msgstr "C'est-à-dire en moyenne"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_internal_type
#: model:ir.model.fields,help:account.field_account_account_type_type
msgid ""
"The 'Internal Type' is used for features available on different types of "
"accounts: liquidity type is for cash or bank accounts, payable/receivable is"
" for vendor/customer accounts."
msgstr ""
"Le \"Type interne\" est utilisé pour des caractéristiques disponibles sur "
"différents types de comptes : le type liquidité est pour l'argent cash ou "
"les comptes banquaires, payabls/recevables par les comptes des "
"vendeurs/clients."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The Advisors have full access to the Accounting application, \n"
"                                        plus access to miscellaneous operations such as salary and asset management."
msgstr ""
"Les consultants ont un accès complet aux applications comptables,\n"
"ainsi qu'à diverses opérations telles que les salaires ou la gestion d'actifs."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The Deposit Ticket module can also be used to settle credit card batch made "
"of multiple transactions."
msgstr ""
"Le module Deposit Ticket peut aussi être utilisé pour fixer des lots de "
"cartes de crédit fait de plusieurs transactions."

#. module: account
#: code:addons/account/models/account_move.py:1209
#, python-format
msgid "The account %s (%s) is deprecated !"
msgstr "Le compte %s (%s) est obsolète !"

#. module: account
#: code:addons/account/models/account_move.py:1021
#, python-format
msgid "The account %s (%s) is not marked as reconciliable !"
msgstr "Le compte %s (%s)  n'est pas indiqué comme réconciliable !"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_bank_journal_id
msgid "The accounting journal corresponding to this bank account."
msgstr "Le journal comptable correspondant a ce compte en banque."

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_currency_exchange_journal_id
msgid ""
"The accounting journal where automatic exchange differences will be "
"registered"
msgstr ""
"Le journal des comptes dans lequel les différences de change automatique "
"seront consignées"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_amount_currency
#: model:ir.model.fields,help:account.field_account_move_line_amount_currency
msgid ""
"The amount expressed in an optional other currency if it is a multi-currency"
" entry."
msgstr ""
"Le montant exprimé dans une autre devise optionnelle si c'est une écriture "
"multi devise."

#. module: account
#: model:ir.model.fields,help:account.field_account_analytic_line_amount_currency
#: model:ir.model.fields,help:account.field_account_analytic_line_analytic_amount_currency
msgid ""
"The amount expressed in the related account currency if not equal to the "
"company one."
msgstr ""
"Le montant exprimé dans la devise propre au compte si celle-ci est "
"différente de celle de la société."

#. module: account
#: code:addons/account/models/account_move.py:508
#, python-format
msgid ""
"The amount expressed in the secondary currency must be positive when account"
" is debited and negative when account is credited."
msgstr ""
"Le montant en devise doit être négatif pour un montant au crédit et positif "
"pour un montant au débit."

#. module: account
#: code:addons/account/models/account.py:804
#, python-format
msgid ""
"The application scope of taxes in a group must be either the same as the "
"group or \"None\"."
msgstr ""
"La portée de l'application des taxes dans un groupe doit soit être du le "
"même que le groupe soit \"Aucun\"."

#. module: account
#: code:addons/account/models/account.py:452
#, python-format
msgid ""
"The bank account of a bank journal must belong to the same company (%s)."
msgstr ""
"Le compte en banque d'un journal bancaire doit appartenir à la même société "
"(%s)."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_statement_id
msgid "The bank statement used for bank reconciliation"
msgstr "Le relevé de banque utilisé pour le lettrage bancaire"

#. module: account
#: code:addons/account/models/account_invoice.py:1165
#, python-format
msgid ""
"The cash rounding cannot be computed because the difference must be added on the biggest tax found and no tax are specified.\n"
"Please set up a tax or change the cash rounding method."
msgstr ""
"L'arrondi de trésorerie ne peut être calculé car la différence doit être ajoutée au montant de taxe le plus important trouvé et aucune taxe n'est spécifiée.\n"
"Veuillez configurer une taxe ou modifier la méthode d'arrondi de trésorerie."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_chart_template_id
msgid "The chart template for the company (if any)"
msgstr "Le modèle de tableau pour la société (le cas échéant)"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
msgid "The closing balance is different than the computed one!"
msgstr "La balance de clôture est différent celui encodé!"

#. module: account
#: sql_constraint:account.journal:0
msgid "The code and name of the journal must be unique per company !"
msgstr "Le code et nom du journal doivent être uniques par société!"

#. module: account
#: sql_constraint:account.account:0
msgid "The code of the account must be unique per company !"
msgstr "Le code du compte doit être unique par société!"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_commercial_partner_id
msgid ""
"The commercial entity that will be used on Journal Entries for this invoice"
msgstr ""
"L'entité légale qui sera utilisée dans les écritures comptables relatives à "
"cette facture"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_currency_id
msgid "The currency used to enter statement"
msgstr "La devise utilisée pour entrer les relevés"

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree1
msgid ""
"The discussions with your customer are automatically displayed at\n"
"                the bottom of each invoice."
msgstr ""
"La discussion avec votre client est automatiquement affichée en\n"
"bas de chaque facture. "

#. module: account
#: code:addons/account/models/account_bank_statement.py:191
#, python-format
msgid ""
"The ending balance is incorrect !\n"
"The expected balance (%s) is different from the computed one. (%s)"
msgstr ""
"Le solde du relevé est incorrect !\n"
"Le solde attendu (%s) est différent du solde calculé (%s)"

#. module: account
#: model:ir.model.fields,help:account.field_product_category_property_account_expense_categ_id
msgid ""
"The expense is accounted for when a vendor bill is validated, except in "
"anglo-saxon accounting with perpetual inventory valuation in which case the "
"expense (Cost of Goods Sold account) is recognized at the customer invoice "
"validation."
msgstr ""
"La dépense est comptabilisée lorsqu'une facture fournisseur est validée, "
"sauf dans le cadre du système comptable anglo-saxon avec la valorisation "
"perpétuelle de l'inventaire, auquel cas la dépense (compte du coût des biens"
" vendus) est comptabilisée lors de la validation de la facture client."

#. module: account
#: model:ir.model.fields,help:account.field_product_product_property_account_expense_id
#: model:ir.model.fields,help:account.field_product_template_property_account_expense_id
msgid ""
"The expense is accounted for when a vendor bill is validated, except in "
"anglo-saxon accounting with perpetual inventory valuation in which case the "
"expense (Cost of Goods Sold account) is recognized at the customer invoice "
"validation. If the field is empty, it uses the one defined in the product "
"category."
msgstr ""
"La dépense est comptabilisée lorsqu'une facture fournisseur est validée, "
"sauf dans le cadre du système comptable anglo-saxon avec la valorisation "
"perpétuelle de l'inventaire, auquel cas la dépense (compte du coût des biens"
" vendus) est comptabilisée lors de la validation de la facture client. Si le"
" champ est vide, l'élément défini dans la catégorie d'articles est utilisé."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The first step is to set up your company's information. This is mostly used "
"in printed business documents like orders and invoices."
msgstr ""
"La première étape consiste à configurer les informations de votre société. "
"Elles sont principalement utilisées sur les documents imprimés, comme les "
"commandes et les factures."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_account_position_id
#: model:ir.model.fields,help:account.field_res_users_property_account_position_id
msgid ""
"The fiscal position will determine taxes and accounts used for the partner."
msgstr ""
"La position fiscale déterminera les taxes et les comptes comptables utilisés"
" par le partneraire."

#. module: account
#: code:addons/account/models/account.py:456
#, python-format
msgid "The holder of a journal's bank account must be the company (%s)."
msgstr "Le propriétaire d'un journal bancaire doit être l'entreprise (%s)."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_account_id
msgid "The income or expense account related to the selected product."
msgstr "Le compte de revenu ou de dépense associé à l'article sélectionné."

#. module: account
#: code:addons/account/models/account_payment.py:643
#, python-format
msgid "The journal %s does not have a sequence, please specify one."
msgstr "Le journal %s n'a pas de séquence, veuillez en spécifier une."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_code
msgid "The journal entries of this journal will be named using this prefix."
msgstr "Les pièces comptables de ce journal seront nommées avec ce préfixe."

#. module: account
#: model:ir.model.fields,help:account.field_account_opening_opening_move_id
#: model:ir.model.fields,help:account.field_res_company_account_opening_move_id
msgid ""
"The journal entry containing the initial balance of all this company's "
"accounts."
msgstr ""
"La pièce comptable contenant le solde initial de tous les comptes de cette "
"société."

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op_fiscalyear_last_day
#: model:ir.model.fields,help:account.field_account_financial_year_op_fiscalyear_last_month
msgid ""
"The last day of the month will be taken if the chosen day doesn't exist."
msgstr "Le dernier jour du mois sera pris si le jour choisi n'existe pas."

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_form
msgid ""
"The last line's computation type should be \"Balance\" to ensure that the "
"whole amount will be allocated."
msgstr ""
"Le type de calcul de la dernière ligne doit être le \"Solde\" pour assurer "
"que le montant total sera alloué."

#. module: account
#: code:addons/account/models/company.py:94
#, python-format
msgid "The lock date for advisors is irreversible and can't be removed."
msgstr ""
"La date de verrouillage pour les conseillers est irréversible et ne peut "
"être supprimée."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_move_id
msgid "The move of this entry line."
msgstr "Le mouvement de cette ligne d'écriture."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The multi-currency option allows you to send or receive invoices \n"
"                        in difference currencies, set up foreign bank accounts \n"
"                        and run reports on your foreign financial activities."
msgstr ""
"L'option multi-devises vous permet d'envoyer ou de recevoir des factures\n"
"dans différentes devises, configurez les comptes en banques étrangers\n"
"et gérez des rapports sur vos activités financières étrangères."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_name
msgid "The name that will be used on account move lines"
msgstr "Ce nom sera utilisé sur les mouvements des lignes de compte"

#. module: account
#: code:addons/account/models/company.py:98
#, python-format
msgid ""
"The new lock date for advisors must be set after the previous lock date."
msgstr ""
"La nouvelle date de verrouillage pour les conseillers doit être définie "
"après la date de verrouillage précédente."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_refund_sequence_number_next
msgid "The next sequence number will be used for the next credit note."
msgstr "Le prochain numéro de séquence sera utilisé pour le prochain avoir."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_sequence_number_next
msgid "The next sequence number will be used for the next invoice."
msgstr ""
"Le prochain numéro de séquence sera utilisé pour la prochaine facture."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_currency_id
#: model:ir.model.fields,help:account.field_account_move_line_currency_id
msgid "The optional other currency if it is a multi-currency entry."
msgstr "L'autre devise optionelle si c'est une écriture multi devise."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_quantity
msgid ""
"The optional quantity expressed by this line, eg: number of product sold. "
"The quantity is not a legal requirement but is very useful for some reports."
msgstr ""
"La quantité facultative correspondant à cette ligne, par exemple le nombre "
"d'articles vendus. La quantité n'est pas obligatoire légalement, mais elle "
"est très utile dans certains rapports."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_account_id
msgid "The partner account used for this invoice."
msgstr "Le compte partenaire utilisé pour cette facture."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_has_unreconciled_entries
#: model:ir.model.fields,help:account.field_res_users_has_unreconciled_entries
msgid ""
"The partner has at least one unreconciled debit and credit since last time "
"the invoices & payments matching was performed."
msgstr ""
"Le partenaire a au moins un débit et un crédit non lettré depuis le dernier "
"lettrage des factures et des paiements."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_reference
msgid "The partner reference of this invoice."
msgstr "La référence donnée par le partenaire pour cette facture."

#. module: account
#: code:addons/account/models/account.py:522
#, python-format
msgid ""
"The partners of the journal's company and the related bank account mismatch."
msgstr ""
"Les partenaires du journal de la société et le compte bancaire lié ne "
"correspondent pas."

#. module: account
#: code:addons/account/models/account_payment.py:60
#, python-format
msgid "The payment amount cannot be negative."
msgstr "Le montant du paiement ne peut pas être négatif."

#. module: account
#: code:addons/account/models/account_payment.py:487
#, python-format
msgid "The payment cannot be processed because the invoice is not open!"
msgstr "Le paiement ne peut pas être traité car la facture n'est pas ouverte!"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_amount_residual_currency
msgid ""
"The residual amount on a journal item expressed in its currency (possibly "
"not the company currency)."
msgstr ""
"Le montant résiduel d'une pièce comptable dans sa devise (qui peut être "
"différente de la devise de la société)"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_amount_residual
msgid ""
"The residual amount on a journal item expressed in the company currency."
msgstr ""
"Le montant résiduel d'une pièce exprimée dans la devise de la société."

#. module: account
#: code:addons/account/models/account_move.py:493
#, python-format
msgid ""
"The selected account of your Journal Entry forces to provide a secondary "
"currency. You should remove the secondary currency on the account."
msgstr ""
"Le compte sélectionné pour cette ligne d'écriture requiert une devise "
"secondaire. Vous devez supprimer la devise secondaire sur le compte."

#. module: account
#: code:addons/account/models/account_invoice.py:1612
#, python-format
msgid ""
"The selected unit of measure is not compatible with the unit of measure of "
"the product."
msgstr ""
"L'unité de mesure sélectionnée n'est pas compatible avec l'unité de mesure "
"de l'article."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_sequence
#: model:ir.model.fields,help:account.field_account_tax_template_sequence
msgid ""
"The sequence field is used to define order in which the tax lines are "
"applied."
msgstr ""
"Le champ séquence est utilisé pour définir l'ordre d'application des taxes."

#. module: account
#: code:addons/account/models/account_payment.py:645
#, python-format
msgid "The sequence of journal %s is deactivated."
msgstr "La séquence du journal %s est désactivée."

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding_rounding_method
msgid "The tie-breaking rule used for float rounding operations"
msgstr ""
"Règle utilisée en cas d'égalité pour les opérations d'arrondis de nombres "
"décimaux"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid "There are currently no invoices and payments for your account."
msgstr ""
"Il n'y a pas de factures ni de paiements pour votre compte actuellement."

#. module: account
#: code:addons/account/models/company.py:178
#, python-format
msgid ""
"There are still unposted entries in the period you want to lock. You should "
"either post or delete them."
msgstr ""
"Il y a toujours des entrées non comptabilisées dans la période que vous "
"souhaitez verrouiller. Vous devez les comptabiliser ou les supprimer."

#. module: account
#: code:addons/account/models/account_bank_statement.py:179
#, python-format
msgid ""
"There is no account defined on the journal %s for %s involved in a cash "
"difference."
msgstr ""
"Il n'y a pas de compte défini sur le journal %s pour %s impliqué dans une "
"différence en liquide."

#. module: account
#: code:addons/account/wizard/account_validate_account_move.py:18
#, python-format
msgid "There is no journal items in draft state to post."
msgstr "Il n'y a pas de journal dans l'état brouillon à poster."

#. module: account
#: code:addons/account/models/account_move.py:1744
#, python-format
msgid ""
"There is no tax cash basis journal defined for this company: \"%s\" \n"
"Configure it in Accounting/Configuration/Settings"
msgstr ""
"Il n'y a pas de journal de taxes de paiement défini pour cette société : \"%s\"\n"
"Veuillez le configurer dans Comptabilité/Configuration/Paramètres"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:46
#, python-format
msgid "There is nothing to reconcile."
msgstr "Il n'y a rien à réconcilier."

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_error
msgid "There was an error processing this page."
msgstr "Une erreur s'est produite lors du traitement de cette page."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "These taxes are set in any new product created."
msgstr "Ces taxes sont définies dans tout nouvel article créé."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_user_type_id
msgid ""
"These types are defined according to your country. The type contains more "
"information about the account and its specificities."
msgstr ""
"Ces types sont définis par pays. Ils contiennent des indications sur les "
"spécificités des comptes."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "These users handle billing specifically."
msgstr "Ces utilisateurs gèrent spécifiquement la facturation."

#. module: account
#: code:addons/account/models/account_invoice.py:1337
#, python-format
msgid ""
"This %s has been created from: <a href=# data-oe-model=account.invoice data-"
"oe-id=%d>%s</a>"
msgstr ""
"Ce(tte) %s a été créé(e) à partir de : <a href=# data-oe-"
"model=account.invoice data-oe-id=%d>%s</a>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "This Month"
msgstr "Ce mois"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:111
#, python-format
msgid "This Week"
msgstr "Cette semaine"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "This Year"
msgstr "Cette année"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_account_payable_id
#: model:ir.model.fields,help:account.field_res_users_property_account_payable_id
msgid ""
"This account will be used instead of the default one as the payable account "
"for the current partner"
msgstr "Ce compte de tiers remplacera le compte par défaut du partenaire."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_account_receivable_id
#: model:ir.model.fields,help:account.field_res_users_property_account_receivable_id
msgid ""
"This account will be used instead of the default one as the receivable "
"account for the current partner"
msgstr "Ce compte remplacera le compte de tiers par défaut pour ce partenaire"

#. module: account
#: model:ir.model.fields,help:account.field_product_category_property_account_income_categ_id
msgid "This account will be used when validating a customer invoice."
msgstr "Ce compte sera utilisé lors de la validation d'une facture client."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows accountants to manage analytic and crossovered budgets. Once the"
" master budgets and the budgets are defined, the project managers can set "
"the planned amount on each analytic account."
msgstr ""
"Cela permet aux comptables de gérer la comptabilité analytique et les "
"budgets croisés. Une fois les budgets directeurs définis, les gestionnaires "
"de projet peuvent fixer le montant prévu pour chaque compte analytique."

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_module_account_batch_deposit
msgid ""
"This allows you to group received checks before you deposit them to the bank.\n"
"-This installs the module account_batch_deposit."
msgstr ""
"Ceci vous permet de grouper les chèques reçus avant de les déposer à la banque.\n"
"-Ceci installe le module account_batch_deposit."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows you to manage the assets owned by a company or a person. It "
"keeps track of the depreciation occurred on those assets, and creates "
"account move for those depreciation lines."
msgstr ""
"Cette fonction vous permet de gérer les actifs détenus par une société ou "
"une personne. Elle conserve la trace de la dépréciation sur ces actifs, et "
"crée un mouvement de compte pour ces lignes d'amortissement."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows you to manage the revenue recognition on selling products. It "
"keeps track of the installments occurred on those revenue recognitions, and "
"creates account moves for those installment lines."
msgstr ""
"Cela vous permet de gérer la reconnaissance des revenus sur les articles "
"vendus. Il garde une trace des acomptes versés sur ces reconnaissances de "
"revenus, et il crée des mouvements comptables pour ces lignes d'acompte."

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_complete_tax_set
msgid ""
"This boolean helps you to choose if you want to propose to the user to "
"encode the sale and purchase rates or choose from list of taxes. This last "
"choice assumes that the set of tax defined on this template is complete"
msgstr ""
"Cette case à cocher  vous aide à choisir si vous voulez proposer à "
"l'utilisateur d'encoder les taux de vente et  d'achat ou de choisir parmi la"
" liste des taxes. Ce dernier choix suppose que les taxes définies sur ce "
"modèle sont paramétrées"

#. module: account
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_complete_tax_set
msgid ""
"This boolean helps you to choose if you want to propose to the user to "
"encode the sales and purchase rates or use the usual m2o fields. This last "
"choice assumes that the set of tax defined for the chosen template is "
"complete"
msgstr ""
"Cette case à cocher détermine si vous voulez proposer à l'utilisateur "
"d'encoder  les taxes sur les ventes et achats ou utiliser les champs "
"habituels m2o. Ce dernier choix suppose que les taxe définies pour le modèle"
" choisi sont paramétrées"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "This feature is useful if you issue a high amounts of invoices."
msgstr ""
"Cette fonctionnalité est utile si vous émettez une grande quantité de "
"factures."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_refund_sequence_id
msgid ""
"This field contains the information related to the numbering of the credit "
"note entries of this journal."
msgstr ""
"Ce champ contient les informations relatives à la numérotation des écritures"
" d'avoirs de ce journal."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_sequence_id
msgid ""
"This field contains the information related to the numbering of the journal "
"entries of this journal."
msgstr ""
"Cet champ contient les informations relatives à la numérotation des "
"écritures de ce journal."

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model_journal_id
#: model:ir.model.fields,help:account.field_account_reconcile_model_second_journal_id
msgid "This field is ignored in a bank statement reconciliation."
msgstr "Ce champ est ignoré dans un lettrage de relevés bancaires."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_date_maturity
msgid ""
"This field is used for payable and receivable journal entries. You can put "
"the limit date for the payment of this line."
msgstr ""
"Ce champ est utilisé pour les comptes de tiers. Vous pouvez fixer la date "
"d'échéance pour le règlement."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_partner_name
msgid ""
"This field is used to record the third party name when importing bank "
"statement in electronic format, when the partner doesn't exist yet in the "
"database (or cannot be found)."
msgstr ""
"Ce champs est utilisé pour indiquer le nom du tiers lors de l'import "
"électronique du relevé bancaire, si le partenaire n'existe pas encore dans "
"la base de donnée ( ou ne peut être trouvé)."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"This guide will help you get started with Odoo Accounting.\n"
"                        Once you're done, you'll benefit from:"
msgstr ""
"Ce guide vous aidera à débuter avec Odoo Accounting.\n"
"Une fois que c'est fait, vous bénéficierez de :"

#. module: account
#: model:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid ""
"This is the accounting dashboard. If you have not yet\n"
"                installed a chart of account, please install one first."
msgstr ""
"Ceci est le tableau de bord Accounting Si vous n'avez pas encore\n"
"installer un plan comptable, veuillez installer ceci en premier."

#. module: account
#: code:addons/account/models/account.py:494
#, python-format
msgid ""
"This journal already contains items, therefore you cannot modify its "
"company."
msgstr ""
"Ce journal contient déjà des éléments, donc vous ne pouvez pas modifier sa "
"société."

#. module: account
#: code:addons/account/models/account.py:503
#, python-format
msgid ""
"This journal already contains items, therefore you cannot modify its short "
"name."
msgstr ""
"Ce journal contient déjà des éléments, donc vous ne pouvez pas modifier son "
"nom abrégé."

#. module: account
#: model:ir.model.fields,help:account.field_accounting_report_label_filter
msgid ""
"This label will be displayed on report to show the balance computed for the "
"given comparison filter."
msgstr ""
"Cette étiquette va être affichée sur le rapport afin de voir le solde "
"calculé pour les filtres de comparaison donnés."

#. module: account
#: code:addons/account/models/account_payment.py:533
#, python-format
msgid ""
"This method should only be called to process a single invoice's payment."
msgstr ""
"Cette méthode ne doit être utilisée que pour traiter le paiement d'une seule"
" facture."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:395
#, python-format
msgid ""
"This move's amount is higher than the transaction's amount. Click to "
"register a partial payment and keep the payment balance open."
msgstr ""
"Le montant de l'écriture est supérieur au montant de la transaction. Cliquez"
" pour enregistrer un paiement partiel et conserver la balance de paiement "
"ouverte."

#. module: account
#: model:ir.model.fields,help:account.field_accounting_report_debit_credit
msgid ""
"This option allows you to get more details about the way your balances are "
"computed. Because it is space consuming, we do not allow to use it while "
"doing a comparison."
msgstr ""
"Cette option vous permet d'obtenir plus de détail sur la façon dont les "
"soldes sont calculés. A cause de la place que cela prend, nous ne permettons"
" pas de l'utiliser pendant une comparaison."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_chart_template_id
msgid ""
"This optional field allow you to link an account template to a specific "
"chart template that may differ from the one its root parent belongs to. This"
" allow you to define chart templates that extend another and complete it "
"with few new accounts (You don't need to define the whole structure that is "
"common to both several times)."
msgstr ""
"Ce champ facultatif permet de lier un modèle de compte à un modèle de plan "
"comptable spécifique qui peut différer de celui utilisé. Cela vous permet de"
" définir un plan comptable à partir d'un autre et de le compléter avec "
"quelques nouveaux comptes (Vous n'avez pas besoin de définir toute la "
"structure qui est commune aux deux)."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:35
#, python-format
msgid ""
"This page displays all the bank transactions that are to be reconciled and "
"provides with a neat interface to do so."
msgstr ""
"Cette page affiche toutes les transactions bancaires à réconcilier et pour "
"cela vous offre une interface claire."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:240
#, python-format
msgid "This payment is registered but not reconciled."
msgstr "Ce paiement est enregistré mais pas réconcilié."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_supplier_payment_term_id
#: model:ir.model.fields,help:account.field_res_users_property_supplier_payment_term_id
msgid ""
"This payment term will be used instead of the default one for purchase "
"orders and vendor bills"
msgstr ""
"Ce délai de paiement sera utilisé à la place de celui par défaut pour les "
"bons de commande et les factures fournisseurs"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_payment_term_id
#: model:ir.model.fields,help:account.field_res_users_property_payment_term_id
msgid ""
"This payment term will be used instead of the default one for sales orders "
"and customer invoices"
msgstr ""
"Ce délai de paiement sera utilisé à la place de celui par défaut pour les "
"commandes et les factures clients"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"This role is best suited for managing the day to day accounting operations:"
msgstr ""
"Ce rôle est le mieux adapté à la gestion quotidienne des opérations "
"comptables :"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_account_id
msgid ""
"This technical field can be used at the statement line creation/import time "
"in order to avoid the reconciliation process on it later on. The statement "
"line will simply create a counterpart on this account"
msgstr ""
"Ce champs technique peut être utilisé lors de la création/import des lignes "
"de relevé bancaire afin d'éviter la reconciliation ensuite. La ligne de "
"relevé créera simplement une contrepartie sur ce compte"

#. module: account
#: model:ir.actions.act_window,help:account.action_validate_account_move
msgid ""
"This wizard will validate all journal entries selected. Once journal entries"
" are validated, you can not update them anymore."
msgstr ""
"Cet assistant validera toutes les écritures de journal pour un journal "
"particulier. Une fois validées, celles-ci ne peuvent plus être modifiées."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_reconcile_model
msgid ""
"Those can be used to quickly create a journal items when reconciling\n"
"                a bank statement or an account."
msgstr ""
"Ceux-ci seront utilisés pour créer rapidement un journal d'articles lors de la réconciliation\n"
"d'un virmeent banquaire ou d'un compte."

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_account_hide_setup_bar
msgid "Tick if you wish to hide the setup bar on the dashboard"
msgstr ""
"Cochez cette case si vous souhaitez masquer la barre de configuration du "
"tableau de bord"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:31
#, python-format
msgid "Tip: Hit CTRL-Enter to reconcile all the balanced items in the sheet."
msgstr ""
"Astuce : utilisez Ctrl+Entrée pour lettrer tous les entrées équilibrées sur "
"la feuille."

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Title 2 (bold)"
msgstr "Titre 2 (gras)"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Title 3 (bold, smaller)"
msgstr "Titre 3 (gras, plus petit)"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "To Invoice"
msgstr "À facturer"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "To Pay"
msgstr "A payer"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"To manage the tax applied when invoicing a Company, Odoo uses the concept of"
" Fiscal Position: they serve to automatically set the right tax and/or "
"account according to the customer country and state."
msgstr ""
"Pour gérer la taxe appliquée quand on facture une société, Odoo utilise le "
"concept de Position Fiscale: elles servent à automatiquement configurer la "
"bonne taxe et/ou comptes en fonction du pays et de l'état du client."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "To pay"
msgstr "A payer"

#. module: account
#: code:addons/account/models/account_move.py:1017
#, python-format
msgid "To reconcile the entries company should be the same for all entries!"
msgstr ""
"Pour réconcilier les entrées, la société doit être la même pour toutes les "
"entrées!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "To use the <strong>multi-currency option:</strong>"
msgstr "Pour utiliser l'<strong>option Multi-Devises:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Today Activities"
msgstr "Activités du Jour"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_total
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
msgid "Total"
msgstr "Total"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_tree
msgid "Total Amount"
msgstr "Montant total"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_lines_tree
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_tree
msgid "Total Credit"
msgstr "Total au crédit"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_lines_tree
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_tree
msgid "Total Debit"
msgstr "Total au débit"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_total_invoiced
#: model:ir.model.fields,field_description:account.field_res_users_total_invoiced
msgid "Total Invoiced"
msgstr "Total facturé"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_debit
#: model:ir.model.fields,field_description:account.field_res_users_debit
msgid "Total Payable"
msgstr "Total dû"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_credit
#: model:ir.model.fields,field_description:account.field_res_users_credit
msgid "Total Receivable"
msgstr "Total du compte client"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_currency_residual
msgid "Total Residual"
msgstr "Total résiduel"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_price_total
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_currency_price_total
msgid "Total Without Tax"
msgstr "Total hors-taxe"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_price_subtotal_signed
msgid "Total amount in the currency of the company, negative for credit note."
msgstr ""
"Montant total dans la devise de l'entreprise, négatif pour les avoirs."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_amount_total_company_signed
msgid ""
"Total amount in the currency of the company, negative for credit notes."
msgstr ""
"Montant total dans la devise de l'entreprise, négatifs pour les avoirs."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_amount_total_signed
msgid ""
"Total amount in the currency of the invoice, negative for credit notes."
msgstr "Montant total dans la devise de la facture, néagatif pour les avoirs."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_credit
#: model:ir.model.fields,help:account.field_res_users_credit
msgid "Total amount this customer owes you."
msgstr "Montant total que ce client vous doit."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_price_total
msgid "Total amount with taxes"
msgstr "Montant total taxes incluses"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_price_subtotal
msgid "Total amount without taxes"
msgstr "Montant total hors-taxe"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_debit
#: model:ir.model.fields,help:account.field_res_users_debit
msgid "Total amount you have to pay to this vendor."
msgstr "Montant total à payer au fournisseur."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_total_company_signed
msgid "Total in Company Currency"
msgstr "Total dans la devise de l'entreprise"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_total_signed
msgid "Total in Invoice Currency"
msgstr "Total dans la devise de la facture"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_total_entry_encoding
msgid "Total of transaction lines."
msgstr "Total des lignes de transaction."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Track costs &amp; revenues by project, department, etc."
msgstr "Suivez les coûts et revenus par projet, département, etc."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:250
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#, python-format
msgid "Transaction"
msgstr "Transaction"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "Transactions"
msgstr "Transactions"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_total_entry_encoding
msgid "Transactions Subtotal"
msgstr "Sous-total des transactions"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_transfer_account_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_transfer_account_id
msgid "Transfer Account"
msgstr "Compte de transfert"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_destination_journal_id
msgid "Transfer To"
msgstr "Transférer à"

#. module: account
#: code:addons/account/models/account_payment.py:357
#, python-format
msgid "Transfer account not defined on the company."
msgstr "Compte de transfert non défini sur l'entreprise. "

#. module: account
#: code:addons/account/models/account_payment.py:618
#, python-format
msgid "Transfer from %s"
msgstr "Transférer de %s"

#. module: account
#: code:addons/account/models/account_payment.py:699
#, python-format
msgid "Transfer to %s"
msgstr "Transférer à %s"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Transfers"
msgstr "Transferts"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_balance_menu
#: model:ir.actions.report,name:account.action_report_trial_balance
#: model:ir.ui.menu,name:account.menu_general_Balance_report
msgid "Trial Balance"
msgstr "Balance générale"

#. module: account
#: model:ir.model,name:account.model_account_balance_report
msgid "Trial Balance Report"
msgstr "Balance générale"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_user_type_id
#: model:ir.model.fields,field_description:account.field_account_account_type_type
#: model:ir.model.fields,field_description:account.field_account_account_user_type_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_journal_type
#: model:ir.model.fields,field_description:account.field_account_financial_report_type
#: model:ir.model.fields,field_description:account.field_account_invoice_line_invoice_type
#: model:ir.model.fields,field_description:account.field_account_invoice_report_type
#: model:ir.model.fields,field_description:account.field_account_invoice_type
#: model:ir.model.fields,field_description:account.field_account_journal_type
#: model:ir.model.fields,field_description:account.field_account_move_line_user_type_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_value
msgid "Type"
msgstr "Type"

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "UP"
msgstr "HAUT"

#. module: account
#: selection:account.journal,bank_statements_source:0
msgid "Undefined Yet"
msgstr "Indéfini pour le moment"

#. module: account
#: code:addons/account/models/company.py:367
#, python-format
msgid "Undistributed Profits/Losses"
msgstr "Profits/pertes non distribués"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:392
#, python-format
msgid "Undo the partial reconciliation."
msgstr "Annuler la réconciliation partielle."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_unit
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Unit Price"
msgstr "Prix unitaire"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_product_uom_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_uom_id
#: model:ir.model.fields,field_description:account.field_account_move_line_product_uom_id
msgid "Unit of Measure"
msgstr "Unité de mesure"

#. module: account
#: code:addons/account/report/account_aged_partner_balance.py:194
#, python-format
msgid "Unknown Partner"
msgstr "Partenaire inconnu"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Unless you are starting a new business, you probably have a list of "
"customers and vendors you'd like to import."
msgstr ""
"Sauf initialisation d'une nouvelle affaire, vous possédez probablement une "
"liste de clients et fournisseurs à importer."

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model:ir.ui.view,arch_db:account.setup_view_company_form
msgid "Unmark as done"
msgstr "Annuler Marquer comme terminé"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_invoice_opened
msgid "Unpaid Invoices"
msgstr "Factures Impayées"

#. module: account
#: selection:account.move,state:0
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unposted"
msgstr "Non Validé"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Unposted Journal Entries"
msgstr "Écritures non-comptabilisées"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unposted Journal Items"
msgstr "Écritures non-comptabilisées"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:81
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
#, python-format
msgid "Unreconcile"
msgstr "Annuler le lettrage"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_unreconcile
msgid "Unreconcile Entries"
msgstr "Annuler le lettrage"

#. module: account
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
msgid "Unreconcile Transactions"
msgstr "Transactions non réconciliées"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unreconciled"
msgstr "Non lettré"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_acount_move_line_open_unreconciled
msgid "Unreconciled Entries"
msgstr "Entrées non réconciliées"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_untaxed
msgid "Untaxed Amount"
msgstr "Montant HT"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_untaxed_signed
msgid "Untaxed Amount in Company Currency"
msgstr "Montant hors taxe dans la devise de la société"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Update exchange rates automatically"
msgstr "Mettre à jour les taux de change automatiquement"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Use"
msgstr "Usage (...contexte ?)"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_use_anglo_saxon
msgid "Use Anglo-Saxon Accounting"
msgstr "Utilisé la comptabilité anglo-saxonne"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_use_anglo_saxon
msgid "Use Anglo-Saxon accounting"
msgstr "Utiliser la comptabilité anglo-saxone"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_tax_exigibility
msgid "Use Cash Basis"
msgstr "Utiliser sur base des paiements"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_sepa_direct_debit
msgid "Use SEPA Direct Debit"
msgstr "Utilisez le débit direct SEPA"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal_journal_id
msgid "Use Specific Journal"
msgstr "Utiliser un journal spécifique"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_anglo_saxon_accounting
msgid "Use anglo-saxon accounting"
msgstr "Utiliser la comptabilité anglo-saxone"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_batch_deposit
msgid "Use batch deposit"
msgstr "Utiliser dépôt de lot"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Use depreciation boards, automate amortization entries"
msgstr ""
"Utilisez des tableaux de dépréciation, automatiser les entrées "
"d'amortissement"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Use follow-up levels and schedule actions"
msgstr "Utilisez les niveaux de relance et planifier des actions"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Use in conjunction with contracts to calculate your monthly revenue for "
"multi-month contracts."
msgstr ""
"Utilisé en conjonction avec les contrats pour calculer les revenus mensuels "
"de vos contrats multi mensuels."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"Use this option if you want to cancel an invoice and create a new\n"
"                                one. The credit note will be created, validated and reconciled\n"
"                                with the current invoice. A new draft invoice will be created\n"
"                                so that you can edit it."
msgstr ""
"Utilisez cette option si vous voulez annuler une facture et en créer une\n"
" nouvelle. L'avoir sera créé, validé et rapproché\n"
" de la facture actuelle. Un nouveau brouillon de facture \n"
" que vous pourrez modifier sera créé."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"Use this option if you want to cancel an invoice you should not\n"
"                                have issued. The credit note will be created, validated and reconciled\n"
"                                with the invoice. You will not be able to modify the credit note."
msgstr ""
"Utilisez cette option si vous voulez annuler une facture que vous n'auriez pas\n"
"dû émettre. L'avoir sera créée, validée et lettrée\n"
"avec la facture. Vous ne pourrez pas modifier l'avoir."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_type_include_initial_balance
msgid ""
"Used in reports to know if we should consider journal items from the "
"beginning of time instead of from the fiscal year only. Account types that "
"should be reset to zero at each new fiscal year (like expenses, revenue..) "
"should not have this option set."
msgstr ""
"Utilisé dans les rapports pour savoir si nous devrions considérer les éléments du journal depuis le début (dans le temps) à la place du début de l'année fiscale uniquement.\n"
"Les types de comptes qui devraient être remis à zéros à chaque nouvelle année fiscale (comme les dépenses, revenus,...) ne devraient pas avoir cette option configurée."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_reference
msgid ""
"Used to hold the reference of the external mean that created this statement "
"(name of imported file, reference of online synchronization...)"
msgstr ""
"Utilisé pour stocker la source externe qui a créé ce relevé (nom du fichier "
"d'import, référence de la synchronisation, …)"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_sequence
msgid "Used to order Journals in the dashboard view"
msgstr "Utilisé pour trier les journaux dans la vue du tableau de bord"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_loss_account_id
msgid ""
"Used to register a loss when the ending balance of a cash register differs "
"from what the system computes"
msgstr ""
"Utilisé pour enregistrer une perte lorsque le solde final de la caisse est "
"différent de ce qui a été calculé par le système"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_profit_account_id
msgid ""
"Used to register a profit when the ending balance of a cash register differs"
" from what the system computes"
msgstr ""
"Utilisé pour enregistrer un profit lorsque le solde final de la caisse est "
"différent de ce qui a été calculé par le système"

#. module: account
#: model:ir.model.fields,help:account.field_account_analytic_line_company_currency_id
#: model:ir.model.fields,help:account.field_account_bank_statement_line_journal_currency_id
#: model:ir.model.fields,help:account.field_account_move_line_company_currency_id
#: model:ir.model.fields,help:account.field_account_partial_reconcile_company_currency_id
#: model:ir.model.fields,help:account.field_res_partner_currency_id
#: model:ir.model.fields,help:account.field_res_users_currency_id
msgid "Utility field to express amount currency"
msgstr "Champ utile pour indiquer le montant de la monnaie"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_vat_required
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_vat_required
msgid "VAT required"
msgstr "TVA requise"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
msgid "VAT:"
msgstr "TVA :"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:72
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#, python-format
msgid "Validate"
msgstr "Valider"

#. module: account
#: model:ir.model,name:account.model_validate_account_move
msgid "Validate Account Move"
msgstr "Valider les mouvements de compte"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Validate purchase orders and control vendor bills by departments."
msgstr ""
"Valider les ordres d'achat et contrôler les factures fournisseurs par "
"département."

#. module: account
#: selection:account.bank.statement,state:0
#: model:mail.message.subtype,name:account.mt_invoice_validated
msgid "Validated"
msgstr "Validé"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_value_amount
msgid "Value"
msgstr "Valeur"

#. module: account
#: selection:account.abstract.payment,partner_type:0
#: selection:account.payment,partner_type:0
#: selection:account.register.payments,partner_type:0
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_supplier_payment_tree
msgid "Vendor"
msgstr "Fournisseur"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_invoice.py:443
#: code:addons/account/models/account_invoice.py:1215
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Vendor Bill"
msgstr "Facture fournisseur"

#. module: account
#: code:addons/account/models/account_invoice.py:444
#, python-format
msgid "Vendor Bill - %s"
msgstr "Facture fournisseur - %s"

#. module: account
#: code:addons/account/models/chart_template.py:194
#: model:ir.actions.act_window,name:account.action_invoice_tree2
#: model:ir.ui.menu,name:account.menu_action_invoice_tree2
#: model:ir.ui.view,arch_db:account.account_planner
#, python-format
msgid "Vendor Bills"
msgstr "Factures fournisseur"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_invoice.py:445
#: code:addons/account/models/account_payment.py:680
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Vendor Credit Note"
msgstr "Avoir de fournisseur"

#. module: account
#: code:addons/account/models/account_invoice.py:446
#, python-format
msgid "Vendor Credit Note - %s"
msgstr "Avoir de fournisseur - %s"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_in_refund
#: model:ir.ui.menu,name:account.menu_action_invoice_in_refund
msgid "Vendor Credit Notes"
msgstr "Avoirs de fournisseur"

#. module: account
#: code:addons/account/models/account_invoice.py:1217
#, python-format
msgid "Vendor Credit note"
msgstr "Avoir de fournisseur"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Vendor Flow"
msgstr "Flux d'un vendeur"

#. module: account
#: code:addons/account/models/account_payment.py:682
#, python-format
msgid "Vendor Payment"
msgstr "Règlement fournisseur"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_supplier_payment_term_id
#: model:ir.model.fields,field_description:account.field_res_users_property_supplier_payment_term_id
msgid "Vendor Payment Terms"
msgstr "Conditions de Paiement du Vendeur"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_reference
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Vendor Reference"
msgstr "Référence fournisseur"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_product_supplier_taxes_id
#: model:ir.model.fields,field_description:account.field_product_template_supplier_taxes_id
msgid "Vendor Taxes"
msgstr "Taxes fournisseur"

#. module: account
#: model:ir.ui.menu,name:account.menu_account_supplier
msgid "Vendors"
msgstr "Fournisseurs"

#. module: account
#: selection:account.financial.report,type:0
msgid "View"
msgstr "Vue"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "Warning"
msgstr "Avertissement"

#. module: account
#: code:addons/account/models/account_invoice.py:662
#, python-format
msgid "Warning for %s"
msgstr "Avertissement pour %s"

#. module: account
#: model:ir.ui.view,arch_db:account.partner_view_buttons
msgid "Warning on the Invoice"
msgstr "Avertissement sur la Facture"

#. module: account
#: code:addons/account/models/account_invoice.py:1554
#: code:addons/account/models/account_invoice.py:1611
#, python-format
msgid "Warning!"
msgstr "Avertissement!"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_group_warning_account
msgid "Warnings"
msgstr "Avertissements"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"We can handle the whole import process\n"
"                                        for you: simply send your Odoo project\n"
"                                        manager a CSV file containing all your\n"
"                                        data."
msgstr ""
"Nous pouvons gérer le processus complet d'importation\n"
"pour vous : envoyez simplement à votre gestionnaire de projet Odoo\n"
"un fichier au format CSV contenant toutes vos\n"
"données."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"We can handle the whole import process\n"
"                                        for you: simply send your Odoo project\n"
"                                        manager a CSV file containing all your\n"
"                                        products."
msgstr ""
"Nous pouvons nous occuper de tout le processus d'import\n"
"pour vous: envoyez simplement à votre Project Manager Odoo\n"
"un fichier CSV contenant tous les\n"
"produits."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "We hope this tool helped you implement our accounting application."
msgstr ""
"Nous espérons que cet outil vous a aidé à implémenter notre application "
"Comptabilité. "

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Welcome"
msgstr "Bienvenue"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"When inviting users, you will need to define which access rights they are allowed to have. \n"
"                        This is done by assigning a role to each user."
msgstr ""
"Quand vous invitez des utilisateurs, vous devez définir quels droits d'accès ils peuvent avoir.\n"
"Cela peut être fait en assignant un rôle à chaque utilisateur."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_show_on_dashboard
msgid "Whether this journal should be displayed on the dashboard or not"
msgstr "Si ce journal doit être affiché sur le tableau de bord ou non"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_amount_currency
#: model:ir.model.fields,field_description:account.field_account_print_journal_amount_currency
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_amount_currency
msgid "With Currency"
msgstr "Avec devise"

#. module: account
#: selection:account.balance.report,display_account:0
#: selection:account.common.account.report,display_account:0
#: selection:account.report.general.ledger,display_account:0
msgid "With balance is not equal to 0"
msgstr "Avec la balance qui n'est pas égale à 0"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "With balance not equal to zero"
msgstr "Avec les balances non nulles"

#. module: account
#: selection:account.balance.report,display_account:0
#: selection:account.common.account.report,display_account:0
#: selection:account.report.general.ledger,display_account:0
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "With movements"
msgstr "Avec des pièces"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_search
msgid "With tax"
msgstr "Avec taxes"

#. module: account
#: model:ir.model,name:account.model_tax_adjustments_wizard
msgid "Wizard for Tax Adjustments"
msgstr "Assistant pour l'ajustement fiscal"

#. module: account
#: code:addons/account/models/account_move.py:1056
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
#, python-format
msgid "Write-Off"
msgstr "Perte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_journal_id
msgid "Write-Off Journal"
msgstr "Journal des pertes"

#. module: account
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
msgid "Write-Off Move"
msgstr "Mouvement de radiation"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_writeoff_acc_id
msgid "Write-Off account"
msgstr "Compte de radiation"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff
msgid "Write-Off amount"
msgstr "Montant radié"

#. module: account
#: sql_constraint:account.move.line:0
msgid "Wrong credit or debit value in accounting entry !"
msgstr "Valeur erronée au crédit ou au débit de la pièce comptable !"

#. module: account
#: code:addons/account/models/account_move.py:1015
#, python-format
msgid "You are trying to reconcile some entries that are already reconciled!"
msgstr "Vous essayez de lettrer des entrées qui le sont déjà !"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_blocked
msgid ""
"You can check this box to mark this journal item as a litigation with the "
"associated partner"
msgstr ""
"Cocher cette case pour signaler que cette ligne de journal est en litige "
"avec le partenaire correspondant"

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree2
msgid ""
"You can control the invoice from your vendor according to\n"
"                what you purchased or received."
msgstr ""
"Vous pouvez contrôler la facture de votre fournisseur selon\n"
"ce que vous avez acheté ou reçu."

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
msgid "You can define additional accounts here"
msgstr "Vous pouvez définir des comptes additionnels ici"

#. module: account
#: code:addons/account/models/account_payment.py:468
#, python-format
msgid "You can not delete a payment that is already posted"
msgstr "Vous ne pouvez pas supprimer un paiement qui a déjà été comptabilisé"

#. module: account
#: code:addons/account/models/account_invoice.py:1633
#, python-format
msgid "You can only delete an invoice line if the invoice is in draft state."
msgstr ""
"Vous ne pouvez supprimer une ligne de facture que si celle-ci est en statut "
"brouillon."

#. module: account
#: code:addons/account/models/account_payment.py:141
#, python-format
msgid "You can only register payments for open invoices"
msgstr ""
"Vous pouvez enregistrer des paiements uniquement pour des factures ouvertes"

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_report_style_overwrite
msgid ""
"You can set up here the format you want this record to be displayed. If you "
"leave the automatic formatting, it will be computed based on the financial "
"reports hierarchy (auto-computed field 'level')."
msgstr ""
"Vous pouvez déterminer ici le format que vous souhaitez voir affiché par "
"l'enregistrement. Si vous laissez le formatage automatique, il va être "
"établi à partir de la hiérarchie des rapports (champ auto-généré 'niveau')."

#. module: account
#: code:addons/account/models/account_move.py:209
#, python-format
msgid ""
"You cannot add/modify entries prior to and inclusive of the lock date %s"
msgstr ""
"Vous ne pouvez pas ajouter/modifier des entrées précédent et inclusives à la"
" date de blocage %s"

#. module: account
#: code:addons/account/models/account_move.py:211
#, python-format
msgid ""
"You cannot add/modify entries prior to and inclusive of the lock date %s. "
"Check the company settings or ask someone with the 'Adviser' role"
msgstr ""
"Vous ne pouvez pas ajouter/modifier les entrées précédent et inclusives à la"
" date de blocage %s. Vérifiez que les paramètres de la société ou demander à"
" quelqu'un avec le rôle de \"Conseiller\""

#. module: account
#: code:addons/account/models/account_invoice.py:1196
#, python-format
msgid ""
"You cannot cancel an invoice which is partially paid. You need to "
"unreconcile related payment entries first."
msgstr ""
"Vous ne pouvez pas annuler une facture qui est partiellement payée. Vous "
"devez d'abord annuler le lettrage des lignes de paiement correspondantes."

#. module: account
#: code:addons/account/models/company.py:200
#, python-format
msgid ""
"You cannot change the currency of the company since some journal items "
"already exist"
msgstr ""
"Vous ne pouvez pas modifier la devise de l'entreprise car certains articles "
"de journal existent déjà"

#. module: account
#: code:addons/account/models/account.py:235
#, python-format
msgid ""
"You cannot change the owner company of an account that already contains "
"journal items."
msgstr ""
"Vous ne pouvez pas changer la société d'un compte qui contient déjà des "
"écritures."

#. module: account
#: code:addons/account/models/account.py:242
#, python-format
msgid ""
"You cannot change the value of the reconciliation on this account as it "
"already has some moves"
msgstr ""
"Vous ne pouvez pas changer la valeur de réconciliation sur ce compte comme "
"il possède déjà des entrées comptables"

#. module: account
#: code:addons/account/models/account_move.py:500
#, python-format
msgid ""
"You cannot create journal items with a secondary currency without filling "
"both 'currency' and 'amount currency' field."
msgstr ""
"Vous ne pouvez pas créer une ligne de journal avec une deuxième devise sans "
"avoir rempli à la fois les champs 'devise' et 'montant de devise'."

#. module: account
#: code:addons/account/models/company.py:120
#, python-format
msgid ""
"You cannot define stricter conditions on advisors than on users. Please make"
" sure that the lock date on advisor is set before the lock date for users."
msgstr ""
"Vous ne pouvez pas définir des conditions plus strictes sur les conseillers "
"que sur les utilisateurs. merci de vous assurer que la date de verrouillage "
"sur le conseiller est définie avant la date de verrouillage pour les "
"utilisateurs."

#. module: account
#: code:addons/account/models/account_invoice.py:613
#, python-format
msgid ""
"You cannot delete an invoice after it has been validated (and received a "
"number). You can set it back to \"Draft\" state and modify its content, then"
" re-confirm it."
msgstr ""
"Vous ne pouvez pas supprimer une facture après sa validation (quand un "
"numéro lui a été attribué). Vous pouvez la remettre dans l'état "
"\"Brouillon\" pour modifier son contenu, puis la confirmer de nouveau."

#. module: account
#: code:addons/account/models/account_invoice.py:611
#, python-format
msgid ""
"You cannot delete an invoice which is not draft or cancelled. You should "
"create a credit note instead."
msgstr ""
"Impossible de supprimer une facture qui n'est ni en brouillon ni annulée. "
"Vous pouvez par contre créer un avoir."

#. module: account
#: code:addons/account/models/res_config_settings.py:133
#, python-format
msgid ""
"You cannot disable this setting because some of your taxes are cash basis. "
"Modify your taxes first before disabling this setting."
msgstr ""
"Vous ne pouvez pas désactiver ce paramètre car certaines de vos taxes font "
"l'objet d'une comptabilité de caisse. Modifiez vos taxes avant de désactiver"
" ce paramètre."

#. module: account
#: code:addons/account/models/account.py:248
#, python-format
msgid "You cannot do that on an account that contains journal items."
msgstr "Vous ne pouvez pas faire ça dans un compte contenant des écritures."

#. module: account
#: code:addons/account/models/account_move.py:1364
#, python-format
msgid ""
"You cannot do this modification on a posted journal entry, you can just change some non legal fields. You must revert the journal entry to cancel it.\n"
"%s."
msgstr ""
"Vous ne pouvez pas faire cette modification sur une entrée journal comptabilisée, vous pouvez juste changer quelques champs non légaux. Vous devez revenir sur l'entrée du journal pour l'annuler.\n"
"%s."

#. module: account
#: code:addons/account/models/account_move.py:1366
#, python-format
msgid ""
"You cannot do this modification on a reconciled entry. You can just change some non legal fields or you must unreconcile first.\n"
"%s."
msgstr ""
"Vous ne pouvez pas effectuer cette modification sur une écriture lettrée. Vous pouvez uniquement changer certains champs légalement libres, ou bien vous devez d'abord annuler le lettrage l'entrée.\n"
"%s."

#. module: account
#: code:addons/account/models/account.py:518
#, python-format
msgid "You cannot empty the bank account once set."
msgstr "On ne peut pas vider le compte bancaire lorsqu'il a été défini. "

#. module: account
#: code:addons/account/models/account.py:55
#, python-format
msgid ""
"You cannot have a receivable/payable account that is not reconcilable. "
"(account code: %s)"
msgstr ""
"Vous ne pouvez pas avoir de compte client/fournisseur ne pouvant faire "
"l'objet d'un rapprochement. (code de compte : %s)"

#. module: account
#: code:addons/account/models/company.py:109
#, python-format
msgid ""
"You cannot lock a period that is not finished yet. Please make sure that the"
" lock date for advisors is not set after the last day of the previous month."
msgstr ""
"Vous ne pouvez pas verrouiller une période qui n'est pas encore terminée. "
"Veuillez vous assurer que la date de verrouillage des conseillers n’est pas "
"définie après le dernier jour du mois précédent."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:701
#, python-format
msgid "You cannot mix items from receivable and payable accounts."
msgstr ""
"Vous ne pouvez pas mélanger des articles de comptes recevables et payables."

#. module: account
#: code:addons/account/models/account_move.py:172
#, python-format
msgid ""
"You cannot modify a posted entry of this journal.\n"
"First you should set the journal to allow cancelling entries."
msgstr ""
"Vous ne pouvez pas modifier une entrée validée dans ce journal.\n"
"Vous devez, en premier lieu, modifier le journal afin de permettre l'annulation d'entrées."

#. module: account
#: code:addons/account/models/account_invoice.py:789
#, python-format
msgid ""
"You cannot pay an invoice which is partially paid. You need to reconcile "
"payment entries first."
msgstr ""
"Vous ne pouvez pas payer une facture qui est partiellement payée. Vous devez"
" d'abord réconcilier les lignes de paiement."

#. module: account
#: code:addons/account/wizard/pos_box.py:36
#, python-format
msgid "You cannot put/take money in/out for a bank statement which is closed."
msgstr ""
"Vous ne pouvez pas déposer/prendre de l'argent lorsqu'un transfert banquaire"
" est clôturé."

#. module: account
#: code:addons/account/models/account.py:261
#, python-format
msgid ""
"You cannot remove/deactivate an account which is set on a customer or "
"vendor."
msgstr ""
"Vous ne pouvez pas supprimer/désactiver un compte qui est associé à un "
"client ou à un fournisseur."

#. module: account
#: code:addons/account/models/account.py:249
#, python-format
msgid ""
"You cannot set a currency on this account as it already has some journal "
"entries having a different foreign currency."
msgstr ""
"Vous ne pouvez pas choisir une devise pour ce compte car il existe déjà des "
"entrées dans le journal ayant des devises différentes."

#. module: account
#: code:addons/account/models/account_move.py:1368
#, python-format
msgid "You cannot use deprecated account."
msgstr "Vous ne pouvez pas utiliser de compte expiré."

#. module: account
#: code:addons/account/models/account_move.py:1283
#, python-format
msgid ""
"You cannot use this general account in this journal, check the tab 'Entry "
"Controls' on the related journal."
msgstr ""
"Vous ne pouvez pas utiliser ce compte dans ce journal. Vérifiez l'onglet "
"'Contrôle des écritures' dans les paramétrages du journal."

#. module: account
#: code:addons/account/models/account_invoice.py:74
#: code:addons/account/models/account_invoice.py:780
#, python-format
msgid ""
"You cannot validate an invoice with a negative total amount. You should "
"create a credit note instead."
msgstr ""
"Impossible de valider une facture avec un montant total négatif. Vous pouvez"
" par contre créer un avoir."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:158
#, python-format
msgid "You did not configure any reconcile model yet, you can do it"
msgstr ""
"Vous n'avez pas encore configuré de modèle de lettrage, vous pouvez le faire"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "You have"
msgstr "Vous avez"

#. module: account
#: code:addons/account/models/account_payment.py:505
#, python-format
msgid "You have to define a sequence for %s in your company."
msgstr "Vous devez définir une séquence pour %s dans votre société."

#. module: account
#: code:addons/account/wizard/account_report_general_ledger.py:21
#, python-format
msgid "You must define a Start Date"
msgstr "Vous devez définir une date de début"

#. module: account
#: code:addons/account/models/account_invoice.py:1555
#, python-format
msgid "You must first select a partner!"
msgstr "Vous devez d'abord choisir un partenaire !"

#. module: account
#: code:addons/account/wizard/account_report_aged_partner_balance.py:26
#, python-format
msgid "You must set a period length greater than 0."
msgstr "Vous devez indiquer une longueur de période supérieure à 0."

#. module: account
#: code:addons/account/wizard/account_report_aged_partner_balance.py:28
#, python-format
msgid "You must set a start date."
msgstr "Vous devez indiquer une date de début."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "You reconciled"
msgstr "Vous avez lettré"

#. module: account
#: code:addons/account/models/account_move.py:1884
#, python-format
msgid ""
"You should configure the 'Exchange Rate Journal' in the accounting settings,"
" to manage automatically the booking of accounting entries related to "
"differences between exchange rates."
msgstr ""
"Veuillez configurer le compte de Perte de change dans les paramètres "
"comptables afin de permettre un enregistrement comptable automatique de la "
"différence de change."

#. module: account
#: code:addons/account/models/account_move.py:1886
#, python-format
msgid ""
"You should configure the 'Gain Exchange Rate Account' in the accounting "
"settings, to manage automatically the booking of accounting entries related "
"to differences between exchange rates."
msgstr ""
"Veuillez configurer le compte de 'Gain de change' dans les paramètres "
"comptables afin de permettre un enregistrement comptable automatique de la "
"différence de change."

#. module: account
#: code:addons/account/models/account_move.py:1888
#, python-format
msgid ""
"You should configure the 'Loss Exchange Rate Account' in the accounting "
"settings, to manage automatically the booking of accounting entries related "
"to differences between exchange rates."
msgstr ""
"Veuillez configurer le compte de 'Perte de change' dans les paramètres "
"comptables afin de permettre un enregistrement comptable automatique de la "
"différence de change."

#. module: account
#: code:addons/account/wizard/pos_box.py:49
#: code:addons/account/wizard/pos_box.py:67
#, python-format
msgid ""
"You should have defined an 'Internal Transfer Account' in your cash "
"register's journal!"
msgstr ""
"Vous devez avoir défini un \"compte de transfert interne\" dans votre "
"journal de caisse !"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"You will be able to edit and validate this\n"
"                                credit note directly or keep it draft,\n"
"                                waiting for the document to be issued by\n"
"                                your supplier/customer."
msgstr ""
"Vous pourrez éditer et valider cet \n"
"avoir directement ou le conserver en brouillon,\n"
"en attendant que le document soit émis par\n"
"le fournisseur ou le client."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Bank Accounts"
msgstr "Vos comptes bancaires"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Company"
msgstr "Votre société"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Customers"
msgstr "Vos Clients"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Products"
msgstr "Vos articles"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Trial Balance (list of accounts and their balances)."
msgstr "votre situation de comptes (liste des comptes et de leurs soldes)."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your company's legal name, tax ID, address, and logo."
msgstr ""
"la dénomination de votre société, son numéro de TVA, son adresse et son logo"
" ;"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your outstanding invoices, payments, and undeposited funds."
msgstr "Vos factures et paiements en cours et les fonds non déposés."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Zip Range"
msgstr "Intervalle de code postal"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_zip_from
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_zip_from
msgid "Zip Range From"
msgstr "Début de l'intervalle de code postal"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_zip_to
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_zip_to
msgid "Zip Range To"
msgstr "Fin de l'intervalle de code postal"

#. module: account
#: model:ir.model,name:account.model_account_bank_accounts_wizard
msgid "account.bank.accounts.wizard"
msgstr "account.bank.accounts.wizard"

#. module: account
#: model:ir.model,name:account.model_account_financial_year_op
msgid "account.financial.year.op"
msgstr "account.financial.year.op"

#. module: account
#: model:ir.model,name:account.model_account_group
msgid "account.group"
msgstr "account.group"

#. module: account
#: model:ir.model,name:account.model_account_opening
msgid "account.opening"
msgstr "account.opening"

#. module: account
#: model:ir.model,name:account.model_account_reconcile_model_template
msgid "account.reconcile.model.template"
msgstr "account.reconcile.model.template"

#. module: account
#: model:ir.model,name:account.model_account_tax_group
msgid "account.tax.group"
msgstr "account.tax.group"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "activate this feature"
msgstr "activer cette fonctionnalité"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:17
#, python-format
msgid "assign to invoice"
msgstr "attribuer cette facture"

#. module: account
#: model:ir.model,name:account.model_cash_box_in
msgid "cash.box.in"
msgstr "cash.box.in"

#. module: account
#: model:ir.model,name:account.model_cash_box_out
msgid "cash.box.out"
msgstr "cash.box.out"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_error
#: model:ir.ui.view,arch_db:account.portal_invoice_success
msgid "close"
msgstr "fermer"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "code"
msgstr "code"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "create a journal entry"
msgstr "créer une ligne d'écriture"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "days"
msgstr "jours"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "e.g. Bank Fees"
msgstr "par ex : frais bancaires"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "fast recording interface"
msgstr "interface de saisie rapide."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "first)"
msgstr "premier)"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "for this customer. You can allocate them to mark this invoice as paid."
msgstr ""
"pour ce client. Vous pouvez les allouer pour marquer cette facture comme "
"payée."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "for this supplier. You can allocate them to mark this bill as paid."
msgstr ""
"pour ce fournisseur. Vous pouvez les allouer pour marquer cette facture "
"comme payée."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:41
#, python-format
msgid "o_manual_statement"
msgstr "o_manual_statement"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "outstanding debits"
msgstr "débits en circulation"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "outstanding payments"
msgstr "paiements en circulation"

#. module: account
#: model:ir.model,name:account.model_report_account_report_agedpartnerbalance
msgid "report.account.report_agedpartnerbalance"
msgstr "report.account.report_agedpartnerbalance"

#. module: account
#: model:ir.model,name:account.model_report_account_report_financial
msgid "report.account.report_financial"
msgstr "report.account.report_financial"

#. module: account
#: model:ir.model,name:account.model_report_account_report_generalledger
msgid "report.account.report_generalledger"
msgstr "report.account.report_generalledger"

#. module: account
#: model:ir.model,name:account.model_report_account_report_journal
msgid "report.account.report_journal"
msgstr "report.account.report_journal"

#. module: account
#: model:ir.model,name:account.model_report_account_report_overdue
msgid "report.account.report_overdue"
msgstr "report.account.report_overdue"

#. module: account
#: model:ir.model,name:account.model_report_account_report_partnerledger
msgid "report.account.report_partnerledger"
msgstr "report.account.report_partnerledger"

#. module: account
#: model:ir.model,name:account.model_report_account_report_tax
msgid "report.account.report_tax"
msgstr "report.account.report_tax"

#. module: account
#: model:ir.model,name:account.model_report_account_report_trialbalance
msgid "report.account.report_trialbalance"
msgstr "report.account.report_trialbalance"

#. module: account
#: model:ir.model,name:account.model_res_config_settings
msgid "res.config.settings"
msgstr "res.config.settings"

#. module: account
#: code:addons/account/models/account_move.py:238
#, python-format
msgid "reversal of: "
msgstr "extourne de : "

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "seconds per transaction."
msgstr "secondes par transaction."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "send us an email"
msgstr "envoyez nous un email"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "set company logo"
msgstr "choisir le logo de la société"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "setup your bank accounts."
msgstr "configurer vos comptes bancaires."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "the customer list"
msgstr "la liste des clients"

#. module: account
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "the parent company"
msgstr "la société parente"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "the product list"
msgstr "la liste des produits"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:158
#, python-format
msgid "there"
msgstr "là"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "to describe<br/> your experience or to suggest improvements !"
msgstr ""
"pour décrire <br/> votre expérience ou pour suggérer des améliorations!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "to set the balance of all of your accounts."
msgstr "pour renseigner les balances de tous vos comptes."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "transactions in"
msgstr "transactions dans"

#. module: account
#: model:ir.model,name:account.model_wizard_multi_charts_accounts
msgid "wizard.multi.charts.accounts"
msgstr "wizard.multi.charts.accounts"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "→ Count"
msgstr "→ Compter"
