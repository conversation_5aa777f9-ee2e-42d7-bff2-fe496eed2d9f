# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-03-29 09:07+0000\n"
"PO-Revision-Date: 2017-09-20 10:13+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Turkish (https://www.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_state
msgid ""
" * The 'Draft' status is used when a user is encoding a new and unconfirmed Invoice.\n"
" * The 'Open' status is used when user creates invoice, an invoice number is generated. It stays in the open status till the user pays the invoice.\n"
" * The 'Paid' status is set automatically when the invoice is paid. Its related journal entries may or may not be reconciled.\n"
" * The 'Cancelled' status is used when user cancel invoice."
msgstr ""
" * 'Taslak' durumu, bir kullanıcı, yeni yada onaylanmamış bir fatura hazırlıyorsa kullanılır.\n"
" * 'Açık' durumu, kullanıcı bir fatura oluştururken kullanılır, fatura numarası oluşturulur. Kullanıcı faturayı ödeyinceye kadar açık durumdadır.\n"
" * 'Ödendi' durumu, fatura ödenince otomatik olarak ayarlanır. Yevmiye kayıtlarının uzlaştırılmış  yada uzlaştırılmamış olmasıyla ilgilidir.\n"
" * 'İptal edildi' durumu kullanıcı bir faturayı iptal ettiğinde kullanılır."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_code_digits
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_code_digits
msgid "# of Digits"
msgstr "Basamak Sayısı"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_code_digits
msgid "# of Digits *"
msgstr "Basamak Sayısı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_nbr
msgid "# of Lines"
msgstr "Satır Sayısı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_trans_nbr
msgid "# of Transaction"
msgstr "İşlem Sayısı"

#. module: account
#: model:mail.template,report_name:account.mail_template_data_payment_receipt
msgid "${(object.name or '').replace('/','-')}"
msgstr "${(object.name or '').replace('/','-')}"

#. module: account
#: model:mail.template,subject:account.email_template_edi_invoice
msgid "${object.company_id.name} Invoice (Ref ${object.number or 'n/a'})"
msgstr "${object.company_id.name} Fatura (Ref ${object.number or 'n/a'})"

#. module: account
#: model:mail.template,subject:account.mail_template_data_payment_receipt
msgid ""
"${object.company_id.name} Payment Receipt (Ref ${object.name or 'n/a' })"
msgstr ""
"${object.company_id.name} Tahsilat/Ödeme Makbuzu (Ref ${object.name or 'n/a'"
" })"

#. module: account
#: model:mail.template,subject:account.mail_template_data_notification_email_account_invoice
msgid "${object.subject}"
msgstr "${object.subject}"

#. module: account
#: code:addons/account/models/account_bank_statement.py:462
#, python-format
msgid "%d transactions were automatically reconciled."
msgstr "%d  işlemler otomatik olarak uzlaştırıldı"

#. module: account
#: code:addons/account/models/account.py:809
#, python-format
msgid "%s (Copy)"
msgstr "%s (Kopya)"

#. module: account
#: code:addons/account/models/account.py:211
#: code:addons/account/models/account.py:484
#: code:addons/account/models/account.py:485
#, python-format
msgid "%s (copy)"
msgstr "%s (kopya)"

#. module: account
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "&amp;nbsp;<span>on</span>"
msgstr "&amp;nbsp;<span>zaman</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ", if accounting or purchase is installed"
msgstr "muhasebe veya satın alma yüklüyse"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "- First Number:"
msgstr "- İlk Numara:"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "-> Reconcile"
msgstr "-> Uzlaştır"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "-> View partially reconciled entries"
msgstr "-> Kısmi uzlaştırılmış girişleri incele"

#. module: account
#: code:addons/account/models/account_bank_statement.py:463
#, python-format
msgid "1 transaction was automatically reconciled."
msgstr "1 işlem otomatik olarak uzlaştırıldı."

#. module: account
#: model:account.payment.term,name:account.account_payment_term_15days
msgid "15 Days"
msgstr "15 Gün"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_net
msgid "30 Net Days"
msgstr "30 Net Gün"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_advance
msgid "30% Advance End of Following Month"
msgstr "%30 ön ödeme kalanı sonraki ayın sonunda"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "5) For setup, you will need the following information:"
msgstr "5) Kurulum için aşağıdaki bilgilere ihtiyacınız olacaktır:"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid ": General ledger"
msgstr ": Büyük defter"

#. module: account
#: code:addons/account/models/account.py:554
#, python-format
msgid ": Refund"
msgstr ": İade"

#. module: account
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid ": Trial Balance"
msgstr ": Geçici Mizan"

#. module: account
#: model:mail.template,body_html:account.mail_template_data_payment_receipt
msgid ""
"<?xml version=\"1.0\"?>\n"
"<data><p>Dear ${object.partner_id.name},</p>\n"
"<p>Thank you for your payment.<br/>Here is your payment receipt <strong>${(object.name or '').replace('/','-')}</strong> amounting to <strong>${format_amount(object.amount, object.currency_id)}</strong> from ${object.company_id.name}.</p>\n"
"<p>If you have any questions, please do not hesitate to contact us.</p>\n"
"<p>Best regards,\n"
"% if user and user.signature:\n"
"${user.signature | safe}\n"
"% endif\n"
"</p>\n"
"</data>"
msgstr ""
"<?xml version=\"1.0\"?>Sayın ${object.partner_id.name},</p>\n"
"<p>Ödemeniz için teşekkür ederiz.<br/>tutarındaki ödeme<strong>${(object.name or '').replace('/','-')}</strong> makbuzunuz<strong>${format_amount(object.amount, object.currency_id)}</strong> from ${object.company_id.name}.</p>\n"
"<p>Herhangi bir sorunuz varsa, lütfen bizimle iletişime geçmekten çekinmeyin.</p>\n"
"<p>Saygılarımızla,\n"
"% if user and user.signature:\n"
"${user.signature | safe}\n"
"% endif\n"
"</p>\n"
"</data>"

#. module: account
#: model:mail.template,body_html:account.mail_template_data_notification_email_account_invoice
msgid ""
"<div>\n"
"% set record = ctx.get('record')\n"
"% set company = record and record.company_id or ctx.get('company')\n"
"<table border=\"0\" width=\"100%\" cellpadding=\"0\" bgcolor=\"#ededed\" style=\"padding: 20px; background-color: #ededed; border-collapse:separate;\" summary=\"o_mail_notification\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\">\n"
"                        <span style=\"font-size:20px; color:white; font-weight: bold;\">\n"
"                            Invoice ${object.record_name}\n"
"                        </span>\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\">\n"
"                        <img src=\"/logo.png?company=${company.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${company.name}\"/>\n"
"                    </td>\n"
"                </tr>\n"
"          </table>\n"
"    </td>\n"
"  </tr>\n"
"  <!-- CONTENT -->\n"
"  <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"min-width: 590px; background-color: rgb(255, 255, 255); padding: 20px; border-collapse:separate;\">\n"
"                <tbody>\n"
"                    <td valign=\"top\" style=\"font-family:Arial,Helvetica,sans-serif; color: #555; font-size: 14px;\">\n"
"                        ${object.body | safe}\n"
"                    </td>\n"
"                </tbody>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\" align=\"left\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                        ${company.name}<br/>\n"
"                        ${company.phone or ''}\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                        % if company.email:\n"
"                        <a href=\"mailto:${company.email}\" style=\"text-decoration:none; color: white;\">${company.email}</a><br/>\n"
"                        % endif\n"
"                        % if company.website:\n"
"                        <a href=\"${company.website}\" style=\"text-decoration:none; color: white;\">\n"
"                        ${company.website}\n"
"                        </a>\n"
"                        % endif\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <tr>\n"
"        <td align=\"center\">\n"
"            Powered by <a target=\"_blank\" href=\"https://www.odoo.com\">Odoo</a>.\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</div>"
msgstr ""
"<div>\n"
"% set record = ctx.get('record')\n"
"% set company = record and record.company_id or ctx.get('company')\n"
"<table border=\"0\" width=\"100%\" cellpadding=\"0\" bgcolor=\"#ededed\" style=\"padding: 20px; background-color: #ededed; border-collapse:separate;\" summary=\"o_mail_notification\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\">\n"
"                        <span style=\"font-size:20px; color:white; font-weight: bold;\">\n"
"                            Invoice ${object.record_name}\n"
"                        </span>\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\">\n"
"                        <img src=\"/logo.png?company=${company.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${company.name}\"/>\n"
"                    </td>\n"
"                </tr>\n"
"          </table>\n"
"    </td>\n"
"  </tr>\n"
"  <!-- CONTENT -->\n"
"  <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"min-width: 590px; background-color: rgb(255, 255, 255); padding: 20px; border-collapse:separate;\">\n"
"                <tbody>\n"
"                    <td valign=\"top\" style=\"font-family:Arial,Helvetica,sans-serif; color: #555; font-size: 14px;\">\n"
"                        ${object.body | safe}\n"
"                    </td>\n"
"                </tbody>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\" align=\"left\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                        ${company.name}<br/>\n"
"                        ${company.phone or ''}\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                        % if company.email:\n"
"                        <a href=\"mailto:${company.email}\" style=\"text-decoration:none; color: white;\">${company.email}</a><br/>\n"
"                        % endif\n"
"                        % if company.website:\n"
"                        <a href=\"${company.website}\" style=\"text-decoration:none; color: white;\">\n"
"                        ${company.website}\n"
"                        </a>\n"
"                        % endif\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <tr>\n"
"        <td align=\"center\">\n"
"            Powered by <a target=\"_blank\" href=\"https://www.odoo.com\">Odoo</a>.\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</div>"

#. module: account
#: model:mail.template,body_html:account.email_template_edi_invoice
msgid ""
"<div>\n"
"<p>Dear ${object.partner_id.name}\n"
"% set access_action = object.with_context(force_website=True).get_access_action()\n"
"% set is_online = access_action and access_action['type'] == 'ir.actions.act_url'\n"
"% set access_url = object.get_mail_url()\n"
"\n"
"% if object.partner_id.parent_id:\n"
"    (<i>${object.partner_id.parent_id.name}</i>)\n"
"% endif\n"
",</p>\n"
"<p>Here is, in attachment, your \n"
"% if object.number:\n"
"invoice <strong>${object.number}</strong>\n"
"% else:\n"
"invoice\n"
"% endif\n"
"% if object.origin:\n"
"(with reference: ${object.origin})\n"
"% endif\n"
"amounting in <strong>${format_amount(object.amount_total, object.currency_id)}</strong>\n"
"from ${object.company_id.name}.\n"
"</p>\n"
"\n"
"% if is_online:\n"
"    <br/><br/>\n"
"    <center>\n"
"      <a href=\"${access_url}\" style=\"background-color: #1abc9c; padding: 20px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 16px;\" class=\"o_default_snippet_text\">View Invoice</a>\n"
"    </center>\n"
"% endif\n"
"    <br/><br/>\n"
"\n"
"% if object.state=='paid':\n"
"    <p>This invoice is already paid.</p>\n"
"% else:\n"
"    <p>Please remit payment at your earliest convenience.</p>\n"
"% endif\n"
"\n"
"<p>Thank you,</p>\n"
"<p style=\"color:#888888\">\n"
"% if object.user_id and object.user_id.signature:\n"
"    ${object.user_id.signature | safe}\n"
"% endif\n"
"</p>\n"
"</div>"
msgstr ""
"<div>\n"
"<p>Sayın ${object.partner_id.name}\n"
"% set access_action = object.with_context(force_website=True).get_access_action()\n"
"% set is_online = access_action and access_action['type'] == 'ir.actions.act_url'\n"
"% set access_url = object.get_mail_url()\n"
"\n"
"% if object.partner_id.parent_id:\n"
"    (<i>${object.partner_id.parent_id.name}</i>)\n"
"% endif\n"
",</p>\n"
"<p>Ekte,\n"
"% if object.number:\n"
"Faturanız <strong>${object.number}</strong>\n"
"% else:\n"
"Fatura\n"
"% endif\n"
"% if object.origin:\n"
"(with reference: ${object.origin})\n"
"% endif\n"
"tutarında <strong>${format_amount(object.amount_total, object.currency_id)}</strong>\n"
"from ${object.company_id.name}.\n"
"</p>\n"
"\n"
"% if is_online:\n"
"    <br/><br/>\n"
"    <center>\n"
"      <a href=\"${access_url}\" style=\"background-color: #1abc9c; padding: 20px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 16px;\" class=\"o_default_snippet_text\">Faturayı Görüntüle\n"
"    </center>\n"
"% endif\n"
"    <br/><br/>\n"
"\n"
"% if object.state=='paid':\n"
"    <p>Bu fatura daha önce ödendi.</p>\n"
"% else:\n"
"    <p>Lütfen gecikmiş borcunuzu en kısa sürede ödeyiniz.</p>\n"
"% endif\n"
"\n"
"<p>Teşekkür ederiz,</p>\n"
"<p style=\"color:#888888\">\n"
"% if object.user_id and object.user_id.signature:\n"
"    ${object.user_id.signature | safe}\n"
"% endif\n"
"</p>\n"
"</div>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<span class=\"fa fa-arrow-circle-o-down\"/> Install Now"
msgstr "<span class=\"fa fa-arrow-circle-o-down\"/> Şimdi Kur"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<span class=\"fa\"/> Invite Your Users"
msgstr "<span class=\"fa\"/> Kullanıcıları Davet et"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-check\"/><span "
"class=\"hidden-xs\"> Paid</span></span>"
msgstr ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-check\"/><span "
"class=\"hidden-xs\"> Ödenmiş</span></span>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-remove\"/><span "
"class=\"hidden-xs\"> Cancelled</span></span>"
msgstr ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-remove\"/><span "
"class=\"hidden-xs\"> İptal Edilmiş</span></span>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"label label-info\"><i class=\"fa fa-fw fa-clock-o\"/><span "
"class=\"hidden-xs\"> Waiting for Payment</span></span>"
msgstr ""
"<span class=\"label label-info\"><i class=\"fa fa-fw fa-clock-o\"/><span "
"class=\"hidden-xs\"> Ödeme Bekleniyor</span></span>"

#. module: account
#: model:ir.ui.view,arch_db:account.partner_view_buttons
msgid "<span class=\"o_stat_text\">Invoiced</span>"
msgstr "<span class=\"o_stat_text\">Faturalandı</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>Import a file</strong><br/>\n"
"                                        <span class=\"small\">Recommended if &gt;100 products</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>Dosyadan İçe Aktarın</strong><br/>\n"
"                                        <span class=\"small\">&gt;100 ürün için önerilir</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>Import</strong><br/>\n"
"                                        <span class=\"small\">&gt; 200 contacts</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>Dosyadan İçe Aktarın</strong><br/>\n"
"                                        <span class=\"small\">&gt; 200 kontak kartı için önerilir</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> Create manually</strong><br/>\n"
"                                        <span class=\"small\">&lt; 200 contacts</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> Manuel Oluşturun</strong><br/>\n"
"                                        <span class=\"small\">&lt; 200 kontak için önerilir</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> Create manually</strong><br/>\n"
"                                        <span class=\"small\">Recommended if &lt;100 products</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> Elle Oluşturun</strong><br/>\n"
"                                        <span class=\"small\">&lt;100 ürün için önerilir</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Accountant</strong> (Advanced access)\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Muhasebeci</strong> (Geniş Yetkili)\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Advisor</strong> (Full access)\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Müşavir</strong> (Tam Yetkili)\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Billing</strong> (Limited access)\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Faturalama</strong> (Sınırlı Yetki)\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Customer follow-up</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"\n"
"<strong>Tahsilat İzlemesi</strong>\n"
"\n"
"</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Invoice payment by Check</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"<strong>Çek ile fatura ödeme</strong>\n"
"</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Invoice payment by Wire Transfer</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"\n"
"<strong>Hesaba transfer ile Fatura ödemesi</strong>\n"
"\n"
"</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Pay your bills</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Faturalarınızı ödeyin</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Reconcile Bank Statements</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"\n"
"<strong>Banka Hesap Özeti Uzlaştırma</strong>\n"
"\n"
"</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Record your Bills</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Faturalarınızı kaydedin</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span title=\"Balance in Odoo\">Balance in GL</span>"
msgstr "<span title=\"Balance in Odoo\">Hesap Bakiyesi</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span title=\"Latest Statement\">Latest Statement</span>"
msgstr "<span title=\"Latest Statement\">Son Ekstre</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "<span> From </span>"
msgstr "<span> Kimden </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "<span> To </span>"
msgstr "<span> Kime </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Journal Entries</span>"
msgstr "<span>Yevmiye Kayıtları</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Bill</span>"
msgstr "<span>Yeni Fatura</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Invoice</span>"
msgstr "<span>Yeni Fatura</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New</span>"
msgstr "<span>Yeni</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<span>Not due</span>"
msgstr "<span>Vadesi gelmemiş</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Operations</span>"
msgstr "<span>İşlemler</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Reconciliation</span>"
msgstr "<span>Uzlaştırma</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Reporting</span>"
msgstr "<span>Raporlama</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>View</span>"
msgstr "<span>Görünüm</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>-The Odoo Team</strong>"
msgstr "<strong>-Odoo Ekibi</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>1. Register Outstanding Invoices</strong>"
msgstr "<strong>1. Ödenmemiş faturaları kaydet</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>2. Register Unmatched Payments</strong>"
msgstr "<strong>2. Eşleştirilmemiş Ödeme Kayıt Girişi</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_invoice_document_with_payments
msgid "<strong>Amount Due</strong>"
msgstr "<strong>Vadesi Gelen</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Assets Management</strong>"
msgstr "<strong>Varlık Yönetimi</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Automated documents sending:</strong> automatically send your "
"invoices by email or snail mail."
msgstr ""
"<strong>Otomatik belge gönderimi:</strong> fatura kesildiğinde e-posta veya "
"normal posta ile otomatik olarak gönderilmesini sağlayın."

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "<strong>Balance :</strong>"
msgstr "<strong>Bakiye :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Banking interface:</strong> with live bank feed synchronization and "
"bank statement import."
msgstr ""
"<strong> Bankacılık Arayüzü : </strong>Anlık banka hesap özet "
"senkronizasyonu ve banka ekstresi içe aktarımı ile "

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Cash transactions</strong><br/> (for which there is no invoice or "
"bill), should be entered directly into your Cash Registers bank account."
msgstr ""
"<strong>Nakit işlemler</strong><br/> (fatura veya borç bildirimi olmadan), "
"doğrudan Nakit Kasası banka hesabına girilmelidir."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Check the Taxes configuration:</strong>"
msgstr "<strong>Vergi ayarlarınızı kontrol edin:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Check unpaid invoices</strong><br/>\n"
"                                         Run an <i>Aged Receivable report</i> and check which customers still owe you money."
msgstr ""
"<strong>Ödenmemiş fatura kontrolü</strong><br/>\n"
"\n"
"<i>Yaşlandırılmış alacaklar raporu çıkararak</i>size borcu olan müşterilerinizi belirleyin."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Clean customer invoices:</strong> easy to create, beautiful and full"
" featured invoices."
msgstr ""
"<strong>Temiz görünümlü müşteri faturaları:</strong> kolayca oluşturulan, "
"göze hitap eden ve tüm özellikleri içeren faturalar."

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
msgid "<strong>Company:</strong>"
msgstr "<strong>Şirket:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Contracts &amp; Subscriptions</strong>"
msgstr "<strong>Sözleşmeler &amp; Abonelikler</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Create a Customer Invoice</strong>"
msgstr "<strong>Müşteriye fatura kes</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Create a Deposit Ticket</strong><br/>\n"
"                                        This allows you to record the different payments that constitute your bank deposit. (You may need to"
msgstr ""
"<strong>Para Yatırma Çağrı Kaydı Oluşturun</strong><br/>\n"
"\n"
"Banka para yatırma işleminin ilgili olduğu ödemeleri kaydetmeyi sağlar (Gerekli görüldüğünde"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Create the bill in Odoo</strong><br/> with a proper due date, and "
"create the vendor if it doesnt' exist yet."
msgstr ""
"<strong>Borç bildirimini Odoo ile oluşturun</strong><br/> vadeyi belirleyin "
"ve henüz tanımlanmadıysa yeni satıcı kaydını oluşturun."

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
msgid "<strong>Customer Address</strong>"
msgstr "<strong>Müşteri Adresi</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Customer Code:</strong>"
msgstr "<strong>Müşteri Kodu</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Customer: </strong>"
msgstr "<strong>Müşteri: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Date from :</strong>"
msgstr "<strong>Bu tarihten:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Date to :</strong>"
msgstr "<strong>Bu tarihe kadar:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Deposit Tickets</strong>"
msgstr "<strong>Para Yatırma Çağrı Kayıtları</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Description:</strong>"
msgstr "<strong>Açıklama:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Display Account:</strong>"
msgstr "<strong>Hesabı Görüntüle:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "<strong>Display Account</strong>"
msgstr "<strong>Hesabı Görüntüle</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Due Date:</strong>"
msgstr "<strong>Bitiş Tarihi:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Entries Sorted By:</strong>"
msgstr "<strong>Girişleri Sırala:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Expenses</strong>"
msgstr "<strong>Harcamalar</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
msgid "<strong>Fiscal Year End</strong>"
msgstr "<strong>Mali Yıl Sonu</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "<strong>From</strong>"
msgstr "<strong>Başlangıç</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Invoice Date:</strong>"
msgstr "<strong>Fatura Tarihi:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Journal:</strong>"
msgstr "<strong>Yevmiye:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "<strong>Journals:</strong>"
msgstr "<strong>Yevmiyeler:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Mark the bills to pay</strong><br/>\n"
"                                        Group or filter your bills to see those due in the next week, then open each bill individually, click on <strong>'Pay'</strong> and select the payment method you prefer."
msgstr ""
"<strong>Ödenecek borç bildirimlerini işaretleyin</strong><br/>\n"
"\n"
"Bir sonraki hafta vadesi gelen borç bildirimlerini gruplayın veya filtreleyin, seçilen borç bildirimlerini açarak <strong>'Öde'</strong>ye tıklayıp tercih ettiğiniz ödeme yöntemini seçin."

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Memo: </strong>"
msgstr "<strong>Kısa Not: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Next action:</strong><br/>"
msgstr "<strong>Sonraki eylem:</strong><br/>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>On-the-fly payment reconciliation:</strong> automatic suggestions of"
" outstanding payments when creating invoices."
msgstr ""
"<strong>Anlık ödeme uzlaştırma:</strong> fatura oluştururken otomatik öneri "
"olarak getirilen bekleyen ödemeleri görün."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Or generate payment orders</strong><br/>\n"
"                                        Create a Payment Order and select the bills you'd like to pay as Entry lines (only the validated bills will appear)."
msgstr ""
"<strong>Veya ödeme emirleri üretin</strong><br/>\n"
"\n"
"Ödeme Emri oluşturun ve ilgili borç bildirimlerini alt satırlar olarak seçin (sadece onaylanmış borç bildirimleri gözükecektir)."

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<strong>Partner's:</strong>"
msgstr "<strong>İş Ortağı'nın:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Payment Amount: </strong>"
msgstr "<strong>Ödeme Tutarı: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Payment Date: </strong>"
msgstr "<strong>Ödeme Tarihi: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Payment Method: </strong>"
msgstr "<strong>Ödeme Metodu: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<strong>Period Length (days)</strong>"
msgstr "<strong>Dönem Uzunluğu (gün)</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Print checks</strong><br/>\n"
"                                        From the Vendor Payments list, select those you want to pay and click on Print Check (you may first need to activate the check feature in the"
msgstr ""
"<strong>Çek yazdırın</strong><br/>\n"
"\n"
"Satıcı Ödemeleri listesinden kimlere ödeme yapılacağını seçin ve Çek Yazdır'a tıklayın (önce Çekler özelliğini etkinleştirmeniz gerekebilir"

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "<strong>Purchase</strong>"
msgstr "<strong>Satınalma</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Purchases</strong>"
msgstr "<strong>Satınalma</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Reconcile Bank Statement</strong>"
msgstr "<strong>Banka Hesap Özeti Uzlaştır</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Reconcile with existing transaction</strong><br/>\n"
"                                        In this case, Odoo should automatically match the bank statement with the previously recorded check transaction."
msgstr ""
"<strong>Mevcut işlemlerinizle uzlaştırın</strong><br/>\n"
"Bu durumda,Odoo otomatikman hesap özetini önceki işlemlerinizle eşleştirecektir."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Reconcile your Bank Statements</strong>"
msgstr "<strong>Hesap Özetinizi Uzlaştırın</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Record Bank Statement (or import file)</strong><br/>\n"
"                                        Depending on the volume of your transactions, you should be recording your bank statement every week to several times a day."
msgstr ""
"<strong>Hesap özetinizi kaydedin (yada dosya alın)</strong><br/>\n"
"İşlemlerinizin yoğunluğuna bağlı olarak,hesap özetiniz her hafta günde birçok kez kaydedilir."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Record Bank Statement</strong>"
msgstr "<strong>Banka Hesap Özeti Kaydı</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Record a payment by check on the Invoice</strong><br/>\n"
"                                        Simply click on the 'Pay' button."
msgstr ""
"<strong>Faturalardan ödeme kaydı oluştur</strong><br/>\n"
"Sadece \"Ödeme\" tuşuna basın."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Reduced data entry:</strong> no need to manually create invoices, "
"register bank statements, and send payment follow-ups."
msgstr ""
"<strong>Azaltılmış veri girdisi</strong> kullanıcı tarafından "
"faturalandırmaya gerek yoktur,hesap özetinizi kayıt edin, ve ödemeyi izle."

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Reference:</strong>"
msgstr "<strong>Referans:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Revenue Recognition</strong>"
msgstr "<strong>Gelir Tahakkuku</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Send follow-up letters</strong>"
msgstr "<strong>Geciken Bakiye Hatırlatmaları Gönderin</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "<strong>Sorted By:</strong>"
msgstr "<strong>Sıralama:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Source:</strong>"
msgstr "<strong>Kaynak:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<strong>Start Date:</strong>"
msgstr "<strong>Başlama Tarihi:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "<strong>Subtotal :</strong>"
msgstr "<strong>Aratoplam :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Subtotal</strong>"
msgstr "<strong>Aratoplam</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Target Moves:</strong>"
msgstr "<strong>Hedef Hareket:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Test the following three scenarios in order to assist you in "
"familiarizing yourself with Odoo:</strong>"
msgstr ""
"<strong>Odoo'yu tanımanıza yardımcı olmak için aşağıdaki üç senaryoyu test "
"edin:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>There are three different levels of access rights in Odoo:</strong>"
msgstr ""
"<strong>Odoo erişim haklarının üç farklı düzeyi vardır:\n"
"</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "<strong>There is nothing due with this customer.</strong>"
msgstr "<strong>Bu müşteriye uygun birşey yok.</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Total</strong>"
msgstr "<strong>Toplam</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_account_kanban
msgid "<strong>Type: </strong>"
msgstr "<strong>Tür: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Validate the bill</strong><br/> after encoding the products and "
"taxes."
msgstr ""
"<strong>Faturayı onayla</strong><br/> ürünleri ve vergileri kodladıktan "
"sonra."

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Vendor: </strong>"
msgstr "<strong>Tedarikçi: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "<strong>to </strong>"
msgstr "<strong>Bitiş </strong>"

#. module: account
#: model:ir.actions.act_window,help:account.action_view_bank_statement_tree
msgid ""
"A Cash Register allows you to manage cash entries in your cash\n"
"                journals. This feature provides an easy way to follow up cash\n"
"                payments on a daily basis. You can enter the coins that are in\n"
"                your cash box, and then post entries when money comes in or\n"
"                goes out of the cash box."
msgstr ""
"Bir yazar kasa, kasa yevmiyelerinizdeki nakit girdilerini yönetmenizi sağlar.\n"
"Bu özellik nakit ödemeleri günlük olarak takip etmenin kolay bir yolunu sağlar.\n"
"Nakit kasanızdaki paraları girebilir ve para girdiğinde veya nakit kutusundan çıktığında girişleri kaydedebilirsiniz."

#. module: account
#: code:addons/account/models/account_bank_statement.py:383
#, python-format
msgid "A Cash transaction can't have a 0 amount."
msgstr "Bir nakit işleminin tutarı 0 olamaz."

#. module: account
#: code:addons/account/models/account_invoice.py:1699
#, python-format
msgid "A Payment Terms should have its last line of type Balance."
msgstr "Bir ödeme koşulunda, son satır dönem tipi Bakiye olmalıdır."

#. module: account
#: code:addons/account/models/account_invoice.py:1702
#, python-format
msgid "A Payment Terms should have only one line of type Balance."
msgstr "Ödeme Şartları'nın yalnızca bir satır Bakiye türü olması gerekir."

#. module: account
#: code:addons/account/models/account.py:722
#, python-format
msgid "A bank account can only belong to one journal."
msgstr "Bir banka hesabı sadece bir yevmiyeye bağlanabilir."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_tree
msgid ""
"A bank statement is a summary of all financial transactions\n"
"                occurring over a given period of time on a bank account. You\n"
"                should receive this periodicaly from your bank."
msgstr ""
"Bir banka hesap özeti, belirli bir süre boyunca bir banka hesabında meydana gelen tüm finansal işlemlerin ekstresidir.\n"
"Bunu düzenli olarak bankanızdan almalısınız."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_line
msgid "A bank statement line is a financial transaction on a bank account."
msgstr ""
"Banka hesap özeti satırı, bir banka hesabındaki bir finansal işlemdir."

#. module: account
#: model:ir.actions.act_window,help:account.action_move_journal_line
msgid ""
"A journal entry consists of several journal items, each of\n"
"                which is either a debit or a credit transaction."
msgstr ""
"Bir yevmiye kaydı, her biri bir borç veya bir alacak işlemi olan birkaç yevmiye\n"
"öğesinden oluşur."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_journal_form
msgid ""
"A journal is used to record transactions of all accounting data\n"
"                related to the day-to-day business."
msgstr ""
"Bir yevmiye, günlük işle ilgili tüm muhasebe verilerinin işlemlerini "
"kaydetmek için kullanılır."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "A list of common taxes and their rates."
msgstr "Vergiler ve onların oranlarının bir listesi"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "A list of your customer and supplier payment terms."
msgstr "Müşteri ve tedarikçi ödeme koşullarınızın bir listesi."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"A product in Odoo is something you sell or buy \n"
"                        whether or not it is goods, consumables, or services.\n"
"                        Choose how you want to create your products:"
msgstr ""
"Odoo'daki bir ürün, ham madde, sarf malzemesi yada hizmet olmak üzere satın "
"aldığınız veya sattığınız üründür."

#. module: account
#: code:addons/account/models/account_move.py:891
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr "Uzlaştırma, en az 2 hareket satırı içermelidir."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"A rounding per line is advised if your prices are tax-included. That way, "
"the sum of line subtotals equals the total with taxes."
msgstr ""
"Fiyatlarınız vergi dahil ise, her satıra bir yuvarlama önerilir. Bu şekilde,"
" satır alt toplamlarının toplamı, vergilerle toplama eşit olur."

#. module: account
#: code:addons/account/models/account_bank_statement.py:881
#: code:addons/account/models/account_bank_statement.py:884
#, python-format
msgid "A selected move line was already reconciled."
msgstr "Seçilen hareket satırı zaten uzlaştı."

#. module: account
#: code:addons/account/models/account_bank_statement.py:892
#, python-format
msgid "A selected statement line was already reconciled with an account move."
msgstr ""
"Seçili bir hesap özeti satırı, bir hesap hareketiyle zaten uzlaştırılmış."

#. module: account
#: sql_constraint:account.fiscal.position.tax:0
msgid "A tax fiscal position could be defined only once time on same taxes."
msgstr ""
"Bir vergi mali koşulu, aynı vergilerde yalnızca bir kez tanımlanabilir."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_journal_form
msgid ""
"A typical company may use one journal per payment method (cash,\n"
"                bank accounts, checks), one purchase journal, one sales journal\n"
"                and one for miscellaneous information."
msgstr ""
"Tipik bir şirket, ödeme metoduna göre bir yevmiye yayın (nakit, \\ n banka "
"hesapları, çekler), bir satın alma günlüğü, bir satış dergisi \\ n ve diğer "
"bilgiler için bir tane kullanabilir."

#. module: account
#: model:res.groups,name:account.group_warning_account
msgid "A warning can be set on a partner (Account)"
msgstr "Bir ortak üzerinde bir uyarı yapılabilir (Hesap)"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:521
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:523
#: code:addons/account/static/src/xml/account_reconciliation.xml:170
#: code:addons/account/static/src/xml/account_reconciliation.xml:228
#: code:addons/account/static/src/xml/account_reconciliation.xml:253
#: model:ir.model,name:account.model_account_account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_account_id
#: model:ir.model.fields,field_description:account.field_account_move_dummy_account_id
#: model:ir.model.fields,field_description:account.field_account_move_line_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_account_id
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
#: model:ir.ui.view,arch_db:account.view_account_form
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_account_search
#, python-format
msgid "Account"
msgstr "Hesap"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_accountant
msgid "Account Accountant"
msgstr "Hesap Muhasebecisi"

#. module: account
#: model:ir.model,name:account.model_account_aged_trial_balance
msgid "Account Aged Trial balance Report"
msgstr "Yaşlandırılmış Geçici Mizan"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Account Balances"
msgstr "Hesap Bakiyesi"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_cashbox
msgid "Account Bank Statement Cashbox Details"
msgstr "Banka Hesap Özeti Kasa Detayı"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_closebalance
msgid "Account Bank Statement closing balance"
msgstr "Banka Hesap Özeti Kapanış Bakiyesi"

#. module: account
#: model:ir.model,name:account.model_account_common_account_report
msgid "Account Common Account Report"
msgstr "Genel Hesap Raporu"

#. module: account
#: model:ir.model,name:account.model_account_common_journal_report
msgid "Account Common Journal Report"
msgstr "Genel Yevmiye Raporu"

#. module: account
#: model:ir.model,name:account.model_account_common_partner_report
msgid "Account Common Partner Report"
msgstr "Genel İş Ortağı Raporu"

#. module: account
#: model:ir.model,name:account.model_account_common_report
msgid "Account Common Report"
msgstr "Genel Hesap Raporu"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_currency_id
#: model:ir.model.fields,field_description:account.field_account_account_template_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_currency_id
msgid "Account Currency"
msgstr "Hesap Para Birimi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_account_dest_id
msgid "Account Destination"
msgstr "Hesap Hedefi"

#. module: account
#: model:ir.model,name:account.model_account_move
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Account Entry"
msgstr "Muhasebe Kaydı"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_group_form
#: model:ir.ui.view,arch_db:account.view_account_group_tree
msgid "Account Group"
msgstr "Hesap Grubu"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_group_tree
msgid "Account Groups"
msgstr "Hesap Grupları"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_bank_journal_id
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_tree
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#: model:ir.ui.view,arch_db:account.view_account_journal_tree
msgid "Account Journal"
msgstr "Hesap Yevmiyesi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_account_line_id
msgid "Account Line"
msgstr "Hesap Satırı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_account_ids
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Account Mapping"
msgstr "Hesap Eşlemesi"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_reversal
msgid "Account Move Reversal"
msgstr "Hesap Hareketi Ters Kayıt"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_acc_name
msgid "Account Name."
msgstr "Hesap Adı."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_bank_acc_number
msgid "Account Number"
msgstr "Hesap Numarası"

#. module: account
#: model:ir.model,name:account.model_account_report_partner_ledger
msgid "Account Partner Ledger"
msgstr "İş Ortağı Hesabı Defteri"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_account_payable_id
#: model:ir.model.fields,field_description:account.field_res_users_property_account_payable_id
msgid "Account Payable"
msgstr "Borç Hesabı"

#. module: account
#: model:ir.model,name:account.model_account_print_journal
msgid "Account Print Journal"
msgstr "Yevmiye Hesap Yazdırma"

#. module: account
#: model:ir.ui.view,arch_db:account.view_category_property_form
msgid "Account Properties"
msgstr "Hesap Özellikleri"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_account_receivable_id
#: model:ir.model.fields,field_description:account.field_res_users_property_account_receivable_id
msgid "Account Receivable"
msgstr "Alacak Hesabı"

#. module: account
#: model:ir.model,name:account.model_account_financial_report
#: model:ir.model.fields,field_description:account.field_account_financial_report_children_ids
#: model:ir.ui.view,arch_db:account.view_account_financial_report_form
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
#: model:ir.ui.view,arch_db:account.view_account_financial_report_tree
msgid "Account Report"
msgstr "Hesap Raporu"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_account_report_id
#: model:ir.ui.menu,name:account.menu_account_financial_reports_tree
msgid "Account Reports"
msgstr "Hesap Raporları"

#. module: account
#: model:ir.model,name:account.model_account_cash_rounding
msgid "Account Rounding"
msgstr "Hesap Yuvarlama"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_account_src_id
msgid "Account Source"
msgstr "Hesap Kaynağı"

#. module: account
#: model:ir.ui.view,arch_db:account.account_bank_statement_graph
#: model:ir.ui.view,arch_db:account.account_bank_statement_pivot
#: model:ir.ui.view,arch_db:account.account_move_line_graph_date
#: model:ir.ui.view,arch_db:account.account_move_line_graph_date_cash_basis
msgid "Account Statistics"
msgstr "Hesap İstatistikleri"

#. module: account
#: model:ir.model,name:account.model_account_account_tag
msgid "Account Tag"
msgstr "Hesap Etiketi"

#. module: account
#: model:ir.actions.act_window,name:account.account_tag_action
msgid "Account Tags"
msgstr "Hesap Etiketleri"

#. module: account
#: model:ir.ui.view,arch_db:account.view_tax_form
#: model:ir.ui.view,arch_db:account.view_tax_tree
msgid "Account Tax"
msgstr "Vergi Hesabı"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_account_tax_template_tree
msgid "Account Tax Template"
msgstr "Hesap Vergi Şablonu"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_taxcloud
msgid "Account TaxCloud"
msgstr "TaxCloud Hesabı"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
#: model:ir.ui.view,arch_db:account.view_account_template_form
#: model:ir.ui.view,arch_db:account.view_account_template_search
#: model:ir.ui.view,arch_db:account.view_account_template_tree
msgid "Account Template"
msgstr "Hesap Şablonu"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_stock_valuation_account_id
#: model:ir.model.fields,field_description:account.field_res_company_property_stock_valuation_account_id
msgid "Account Template for Stock Valuation"
msgstr "Stok Değerlemesi için Hesap Şablonu"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_template_form
msgid "Account Templates"
msgstr "Hesap Şablonları"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Account Total"
msgstr "Hesap Toplamı"

#. module: account
#: selection:account.financial.report,type:0
#: model:ir.model,name:account.model_account_account_type
#: model:ir.model.fields,field_description:account.field_account_account_type_name
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_account_type
#: model:ir.ui.view,arch_db:account.view_account_search
#: model:ir.ui.view,arch_db:account.view_account_template_search
#: model:ir.ui.view,arch_db:account.view_account_type_form
#: model:ir.ui.view,arch_db:account.view_account_type_search
#: model:ir.ui.view,arch_db:account.view_account_type_tree
msgid "Account Type"
msgstr "Hesap Tipi"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_user_type_id
#: model:ir.model.fields,help:account.field_account_move_line_user_type_id
msgid ""
"Account Type is used for information purpose, to generate country-specific "
"legal reports, and set the rules to close a fiscal year and generate opening"
" entries."
msgstr ""
"Hesap Tipi bilgi amaçlıdır, ülkeye özgü yasal raporlar oluşturmak ve mali "
"yıl kapanış kurallarını ayarlamak ve devir kayıtlarını oluşturmak için "
"kullanılabilir. Sistem iç tipi esas alır."

#. module: account
#: model:ir.actions.act_window,name:account.action_account_type_form
#: model:ir.model.fields,field_description:account.field_account_financial_report_account_type_ids
msgid "Account Types"
msgstr "Hesap Tipleri"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_type_control_ids
msgid "Account Types Allowed"
msgstr "İzin Verilen Hesap Tipleri (denetlenmemesi için boş bırak)"

#. module: account
#: model:ir.model,name:account.model_account_unreconcile
msgid "Account Unreconcile"
msgstr "Hesaptan Uzlaştırmayı Kaldır"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_group_search
msgid "Account group"
msgstr "Hesap Grubu"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_group_search
msgid "Account groups"
msgstr "Hesap Grupları"

#. module: account
#: model:ir.model,name:account.model_account_move_line_reconcile
msgid "Account move line reconcile"
msgstr "Yevmiye kalemi uzlaştır"

#. module: account
#: model:ir.model,name:account.model_account_move_line_reconcile_writeoff
msgid "Account move line reconcile (writeoff)"
msgstr "Muhasebe hareket kalemi uzlaşması (borç silme)"

#. module: account
#: model:ir.model,name:account.model_account_move_reversal
msgid "Account move reversal"
msgstr "Hesap hareketi ters kayıt"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_account_src_id
msgid "Account on Product"
msgstr "Gider Hesabı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_tag_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template_tag_ids
msgid "Account tag"
msgstr "Hesap etiketi"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_refund_account_id
msgid ""
"Account that will be set on invoice tax lines for credit notes. Leave empty "
"to use the expense account."
msgstr ""
"İade/fiyat farkları için fatura vergi satırlarına ayarlanacak hesap. Gider "
"hesabını kullanmak için boş bırakınız."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_account_id
#: model:ir.model.fields,help:account.field_account_tax_template_account_id
msgid ""
"Account that will be set on invoice tax lines for invoices. Leave empty to "
"use the expense account."
msgstr ""
"Faturalar için fatura vergi satırlarında ayarlanacak hesap. Gider hesabını "
"kullanmak için boş bırakın."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_template_refund_account_id
msgid ""
"Account that will be set on invoice tax lines for refunds. Leave empty to "
"use the expense account."
msgstr ""
"İadeler için fatura vergi satırlarında belirlenecek hesap. Gider hesabını "
"kullanmak için boş bırakın."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_account_dest_id
msgid "Account to Use Instead"
msgstr "Yerine Kullanılacak Hesap"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_cash_basis_account
#: model:ir.model.fields,help:account.field_account_tax_template_cash_basis_account
msgid ""
"Account used as counterpart for the journal entry, for taxes eligible based "
"on payments."
msgstr ""
"Hesabınız, vergi ödemelerinin uygunluğu için yevmiye girişlerinde karşıt "
"olarak kullanılır. "

#. module: account
#: model:ir.ui.menu,name:account.account_account_menu
msgid "Accounting"
msgstr "Muhasebe"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Accounting App Options"
msgstr "Muhasebe Uygulaması Seçenekleri "

#. module: account
#: model:ir.ui.view,arch_db:account.view_wizard_multi_chart
msgid "Accounting Application Configuration"
msgstr "Muhasebe Uygulamaları Yapılandırması"

#. module: account
#: model:web.planner,tooltip_planner:account.planner_account
msgid "Accounting Configuration: a step-by-step guide."
msgstr "Muhasebe yapılandırması : adım adım tarif "

#. module: account
#: model:ir.actions.act_window,name:account.open_account_journal_dashboard_kanban
msgid "Accounting Dashboard"
msgstr "Muhasebe Paneli"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_date
msgid "Accounting Date"
msgstr "Muhasebe Tarihi"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Accounting Documents"
msgstr "Muhasebe Belgeleri"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_accounting_entries
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Accounting Entries"
msgstr "Muhasebe Kayıtları"

#. module: account
#: model:ir.model,name:account.model_accounting_report
msgid "Accounting Report"
msgstr "Muhasebe Raporu"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Accounting Settings"
msgstr "Muhasebe Ayarları"

#. module: account
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Accounting-related settings are managed on"
msgstr "Muhasebeyle ilgili ayarlar bundan yönetilir"

#. module: account
#: selection:account.account.tag,applicability:0
#: selection:account.financial.report,type:0
#: model:ir.model.fields,field_description:account.field_account_financial_report_account_ids
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
#: model:ir.ui.view,arch_db:account.view_account_search
msgid "Accounts"
msgstr "Hesaplar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_account_control_ids
msgid "Accounts Allowed"
msgstr "İzinli Hesaplar"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_account
msgid "Accounts Fiscal Position"
msgstr "Hesapların Mali Koşulu"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_template_form
msgid "Accounts Mapping"
msgstr "Hesap Eşleştirmesi"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_actions
msgid "Actions"
msgstr "İşlemler"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Activate Other Currencies"
msgstr "Diğer Para Birimlerini Aktif Hale Getirin"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Activate the option in the"
msgstr "Seçeneğini etkinleştirin"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_active
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_active
#: model:ir.model.fields,field_description:account.field_account_journal_active
#: model:ir.model.fields,field_description:account.field_account_payment_term_active
#: model:ir.model.fields,field_description:account.field_account_tax_active
#: model:ir.model.fields,field_description:account.field_account_tax_template_active
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Active"
msgstr "Aktif"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:17
#, python-format
msgid "Add"
msgstr "Ekle"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid "Add Credit Note"
msgstr "İade/Fiyat Farkı Faturası Ekle"

#. module: account
#: selection:account.cash.rounding,strategy:0
msgid "Add a rounding line"
msgstr "Yuvarlama çizgisi ekleyin."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_has_second_line
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_has_second_line
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Add a second line"
msgstr "Bir ikincil satır ekle"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Add an internal note..."
msgstr "Bir iç not ekle..."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_comment
msgid "Additional Information"
msgstr "Diğer Bilgiler"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Additional notes..."
msgstr "Ek notlar..."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Address"
msgstr "Adres"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_tax_id
msgid "Adjustment Tax"
msgstr "Vergi Düzeltme"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_adjustment_type
msgid "Adjustment Type"
msgstr "Düzeltme Türü"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_tax_form
msgid "Advanced Options"
msgstr "Gelişmiş Seçenekler"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Advanced Settings"
msgstr "Gelişmiş Ayarlar"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries
msgid "Adviser"
msgstr "Müşavir"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_include_base_amount
msgid "Affect Base of Subsequent Taxes"
msgstr "Sonraki vergi tabanını etkiler"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_include_base_amount
msgid "Affect Subsequent Taxes"
msgstr "Ardıl Vergileri Etkiler"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_aged_balance_view
#: model:ir.actions.report,name:account.action_report_aged_partner_balance
#: model:ir.ui.menu,name:account.menu_aged_trial_balance
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Aged Partner Balance"
msgstr "Yaşlandırılmış İş Ortağı Bakiyesi"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
msgid ""
"Aged Partner Balance is a more detailed report of your receivables by "
"intervals. Odoo calculates a table of credit balance by start Date. So if "
"you request an interval of 30 days Odoo generates an analysis of creditors "
"for the past month, past two months, and so on."
msgstr ""
"Yaşlandırılmış İş Ortağı Bakiyesi, alacaklarınızın periyotlara göre bölünmüş"
" ayrıntılı bir raporudur. Odoo başlangıç tarihine göre bir alacak bakiyesi "
"tablosu hesaplar. Örneğin 30 günlük bir periyotta rapor aldığınızda, Odoo, "
"son bir ay, son iki ay gibi periyotlar için bir alacak analizi üretir."

#. module: account
#: selection:account.balance.report,display_account:0
#: selection:account.common.account.report,display_account:0
#: selection:account.report.general.ledger,display_account:0
msgid "All"
msgstr "Hepsi"

#. module: account
#: selection:account.aged.trial.balance,target_move:0
#: selection:account.balance.report,target_move:0
#: selection:account.common.account.report,target_move:0
#: selection:account.common.journal.report,target_move:0
#: selection:account.common.partner.report,target_move:0
#: selection:account.common.report,target_move:0
#: selection:account.print.journal,target_move:0
#: selection:account.report.general.ledger,target_move:0
#: selection:account.report.partner.ledger,target_move:0
#: selection:account.tax.report,target_move:0
#: selection:accounting.report,target_move:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "All Entries"
msgstr "Tüm Kayıtlar"

#. module: account
#: model:ir.actions.act_window,name:account.action_all_partner_invoices
msgid "All Invoices"
msgstr "Tüm Faturalar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_all_lines_reconciled
msgid "All Lines Reconciled"
msgstr "Tüm Kalemler Uzlaştırıldı"

#. module: account
#: selection:account.aged.trial.balance,target_move:0
#: selection:account.balance.report,target_move:0
#: selection:account.common.account.report,target_move:0
#: selection:account.common.journal.report,target_move:0
#: selection:account.common.partner.report,target_move:0
#: selection:account.common.report,target_move:0
#: selection:account.print.journal,target_move:0
#: selection:account.report.general.ledger,target_move:0
#: selection:account.report.partner.ledger,target_move:0
#: selection:account.tax.report,target_move:0
#: selection:accounting.report,target_move:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "All Posted Entries"
msgstr "Tüm Onaylı Kayıtlar"

#. module: account
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "All accounts"
msgstr "Bütün hesaplar"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "All accounts'"
msgstr "Bütün hesaplar'"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:47
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr ""
"Tüm faturalar ve ödemeler eşleştirildi, hesaplarınızın bakiyeleri temiz."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_state
msgid ""
"All manually created new journal entries are usually in the status "
"'Unposted', but you can set the option to skip that status on the related "
"journal. In that case, they will behave as journal entries automatically "
"created by the system on document validation (invoices, bank statements...) "
"and will be created in 'Posted' status."
msgstr ""
"Elle oluşturulan bütün yeni yevmiye kayıtları genelde 'Onay Bekleyen' "
"durumundadır, fakat gerekli ayarları yaparak girişlerin durumunu sistem "
"tarafından otomatik oluşturulmuş girişler gibi varsayılan olarak 'Onaylı' "
"olmasını sağlayabilirsiniz."

#. module: account
#: model:ir.ui.view,arch_db:account.validate_account_move_view
msgid ""
"All selected journal entries will be validated and posted. You won't be able"
" to modify them afterwards."
msgstr ""
"Tüm kayıtlar doğrulanıp onaylanacak. Onaylanmış kayıtlar, onaylama işlemi "
"geri alınmadığı sürece değiştirilemeyecek."

#. module: account
#: code:addons/account/models/account_bank_statement.py:240
#, python-format
msgid ""
"All the account entries lines must be processed in order to close the "
"statement."
msgstr ""
"hesap özetini kapatmak için bütün hesap girişleri satırlarının işlenmesi "
"gerekir."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_update_posted
msgid "Allow Cancelling Entries"
msgstr "Kayıtları İptale İzin Ver"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_reconcile
msgid "Allow Invoices & payments Matching"
msgstr "Faturaları ve ödemeleri Eşleştirmeye izin ver"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_product_margin
msgid "Allow Product Margin"
msgstr "Ürün Marjlarını Analiz Edin (Faturadan Çalışır)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_reconcile
msgid "Allow Reconciliation"
msgstr "Uzlaştırmaya İzin Ver"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_l10n_us_check_printing
msgid "Allow check printing and deposits"
msgstr "Çek yazmaya ve para yatırmaya izin ver"

#. module: account
#: model:res.groups,name:account.group_cash_rounding
msgid "Allow the cash rounding management"
msgstr "Nakit yuvarlama yönetimine izin ver."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allow to configure taxes using cash basis"
msgstr "Nakit esaslı olarak vergileme yapılandırmasına izin ver."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allows you to use the analytic accounting."
msgstr "Analitik Muhasebe kullanmanızı sağlar."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:193
#: code:addons/account/static/src/xml/account_reconciliation.xml:235
#: code:addons/account/static/src/xml/account_reconciliation.xml:252
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_amount
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_subtotal
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_total
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_amount
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_amount_total
#: model:ir.model.fields,field_description:account.field_account_move_amount
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_amount
#: model:ir.model.fields,field_description:account.field_account_tax_amount
#: model:ir.model.fields,field_description:account.field_account_tax_template_amount
#: model:ir.model.fields,field_description:account.field_cash_box_in_amount
#: model:ir.model.fields,field_description:account.field_cash_box_out_amount
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_amount
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_payment_tree
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_account_supplier_payment_tree
#: model:ir.ui.view,arch_db:account.view_move_line_form
#, python-format
msgid "Amount"
msgstr "Tutar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_amount_currency
#: model:ir.model.fields,field_description:account.field_account_analytic_line_analytic_amount_currency
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_amount_currency
#: model:ir.model.fields,field_description:account.field_account_move_line_amount_currency
msgid "Amount Currency"
msgstr "Para Birimi Tutarı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_residual
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid "Amount Due"
msgstr "Ödenecek Tutar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_residual_company_signed
msgid "Amount Due in Company Currency"
msgstr "Şirket Para Birimi Tutarı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_residual_signed
msgid "Amount Due in Invoice Currency"
msgstr "Fatura Para Birimi Tutarı"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Amount Paid"
msgstr "Ödeme Tutarı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_amount_rounding
msgid "Amount Rounding"
msgstr "Yuvarlama Tutarı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_subtotal_signed
msgid "Amount Signed"
msgstr "Tutar imzalandı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_amount_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_amount_type
msgid "Amount Type"
msgstr "Tutar Türü"

#. module: account
#: model:ir.model.fields,help:account.field_account_partial_reconcile_amount
msgid "Amount concerned by this matching. Assumed to be always positive"
msgstr "Bu eşleştirme ile ilgili tutar. Her zaman artı olarak kabul edilir."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_amount_currency
msgid "Amount in Currency"
msgstr "Para Birimi Tutarı"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Amount type"
msgstr "Tutar türü"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:68
#, python-format
msgid "Amount:"
msgstr "Tutar:"

#. module: account
#: sql_constraint:account.fiscal.position.account:0
msgid ""
"An account fiscal position could be defined only once time on same accounts."
msgstr ""
"Bir hesabın mali koşulu aynı hesap üzerinde yalnız bir kez tanımlanabilir."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_form
msgid ""
"An account is part of a ledger allowing your company\n"
"                to register all kinds of debit and credit transactions.\n"
"                Companies present their annual accounts in two main parts: the\n"
"                balance sheet and the income statement (profit and loss\n"
"                account). The annual accounts of a company are required by law\n"
"                to disclose a certain amount of information."
msgstr ""
"Her tür borç ve alacak kayıtlarının kaydedilebileceği Muhasebe \n"
"                hesapları defterlerin parçalarıdır. Şirketler yıllık\n"
"                hesaplarını iki ana bölümde tutarlar : Bilanço, \n"
"                ve gelir tablosu (kâr ve zarar hesapları)"

#. module: account
#: model:ir.actions.act_window,help:account.action_account_type_form
msgid ""
"An account type is used to determine how an account is used in\n"
"                each journal. The deferral method of an account type determines\n"
"                the process for the annual closing. Reports such as the Balance\n"
"                Sheet and the Profit and Loss report use the category\n"
"                (profit/loss or balance sheet)."
msgstr ""
"Hesap tipi bir hesabın her yevmiye defterinde nasıl kullanıldığını\n"
"                belirlemek için kullanılır. Hesap tipindeki erteleme yöntemi\n"
"                yıl sonu kapanış girişleri sırasındaki işleyişi belirler.\n"
"                Bilanço kâr/zarar raporları gibi raporlarda bu alanı kullanır."

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Analytic"
msgstr "Analitik"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:178
#, python-format
msgid "Analytic Acc."
msgstr "Analitik Hes."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_account_analytic_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_account_analytic_id
#: model:ir.model.fields,field_description:account.field_account_move_line_analytic_account_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_analytic_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_analytic_account_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Analytic Account"
msgstr "Analitik Hesap"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_group_analytic_accounting
#: model:ir.ui.menu,name:account.menu_analytic_accounting
msgid "Analytic Accounting"
msgstr "Analitik Muhasebe"

#. module: account
#: model:ir.actions.act_window,name:account.action_open_partner_analytic_accounts
#: model:ir.ui.menu,name:account.account_analytic_def_account
#: model:ir.ui.view,arch_db:account.partner_view_button_contracts_count
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytic Accounts"
msgstr "Analitik Hesaplar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_analytic
msgid "Analytic Cost"
msgstr "Analitik Maliyetler"

#. module: account
#: model:ir.actions.act_window,name:account.analytic_line_reporting_pivot
#: model:ir.ui.menu,name:account.menu_action_analytic_lines_reporting
#: model:ir.ui.menu,name:account.menu_action_analytic_lines_tree
msgid "Analytic Entries"
msgstr "Analitik Kayıtları"

#. module: account
#: model:ir.model,name:account.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analitik Satırı"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Analytic Lines"
msgstr "Analitik Satırları"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_analytic_tag_ids
#: model:ir.ui.menu,name:account.account_analytic_tag_menu
msgid "Analytic Tags"
msgstr "Analitik Etiketleri"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_account_analytic_id
msgid "Analytic account"
msgstr "Analitik Hesap"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_analytic_line_ids
msgid "Analytic lines"
msgstr "Analitik Satırları"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_analytic_tag_ids
msgid "Analytic tags"
msgstr "Analitik etiketler"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytics"
msgstr "Analitikler"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_exigible
msgid "Appears in VAT report"
msgstr "KDV raporunda görünsün"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_applicability
msgid "Applicability"
msgstr "Uygulanabilirlik"

#. module: account
#: selection:tax.adjustments.wizard,adjustment_type:0
msgid "Applied on credit journal item"
msgstr "Alacak yevmiye kalemine uygulandı"

#. module: account
#: selection:tax.adjustments.wizard,adjustment_type:0
msgid "Applied on debit journal item"
msgstr "Borç yevmiye kalemine uygulandı"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_auto_apply
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_auto_apply
msgid "Apply automatically this fiscal position."
msgstr "Bu mali koşulu otomatik olarak uygula."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_country_group_id
msgid "Apply only if delivery or invocing country match the group."
msgstr "Yalnızca sevk ve fatura ülkesi grupla eşleşiyorsa uygula."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_country_group_id
msgid "Apply only if delivery or invoicing country match the group."
msgstr "Sadece teslimat veya faturalama ülkeleri eşleşiyor ise uygula."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_country_id
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_country_id
msgid "Apply only if delivery or invoicing country match."
msgstr "Yalnızca sevk ve fatura ülkesi eşleşiyorsa uygula."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_vat_required
#: model:ir.model.fields,help:account.field_account_fiscal_position_vat_required
msgid "Apply only if partner has a VAT number."
msgstr "Yalnızca iş ortağının bir KDV numarası varsa uygula."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Apply right VAT rates for digital products sold in EU"
msgstr "AB'de satılan dijital ürünler için doğru KDV oranları uygulayın"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "April"
msgstr "Nisan"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Archived"
msgstr "Arşivlendi"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Ask for a Credit Note"
msgstr "İade/Fiyat farkı iste."

#. module: account
#: model:account.financial.report,name:account.account_financial_report_assets0
msgid "Assets"
msgstr "Demirbaşlar"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_asset
msgid "Assets Management"
msgstr "Sabit Kıymet Yönetimi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_account_ids
msgid "Associated Account Templates"
msgstr "Birleşmiş Hesap Şablonları"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_at_least_one_inbound
msgid "At Least One Inbound"
msgstr "En Az Bir Gelen"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_at_least_one_outbound
msgid "At Least One Outbound"
msgstr "En Az Bir Giden"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "August"
msgstr "Ağustos"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Auto-detect"
msgstr "Kendiliğinden algıla"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automate deferred revenues entries for multi-year contracts"
msgstr ""
"1 yılı aşan uzun dönem sözleşmeler için ertelenmiş gelir girişlerini "
"otomatikleştirme"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automated Entries"
msgstr "Otomatik Girişler"

#. module: account
#: code:addons/account/models/company.py:411
#: code:addons/account/wizard/setup_wizards.py:79
#, python-format
msgid "Automatic Balancing Line"
msgstr "Otomatik Denkleştirme Kalemi"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_currency_rate_live
msgid "Automatic Currency Rates"
msgstr "Otomatik Döviz Kurları"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automatic Import"
msgstr "İçe Aktarım"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Automatic formatting"
msgstr "Otomatik biçimleme"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:24
#, python-format
msgid "Automatic reconciliation"
msgstr "Otomatik Uzlaştırma"

#. module: account
#: code:addons/account/models/account_bank_statement.py:468
#, python-format
msgid "Automatically reconciled items"
msgstr "Otomatik Uzlaştırma Öğeleri"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_price_average
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_currency_price_average
msgid "Average Price"
msgstr "Ortalama Fiyat"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Awaiting payments"
msgstr "Bekleyen ödemeler"

#. module: account
#: code:addons/account/models/chart_template.py:194
#, python-format
msgid "BILL"
msgstr "ALM"

#. module: account
#: selection:res.partner,trust:0
msgid "Bad Debtor"
msgstr "Kötü Borçlu"

#. module: account
#: selection:account.payment.term.line,value:0
#: model:ir.model.fields,field_description:account.field_account_move_line_balance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_payment_receipt
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Balance"
msgstr "Bakiye"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_balance_cash_basis
msgid "Balance Cash Basis"
msgstr "Gerçekleşme Esası Bakiyesi"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_balancesheet0
#: model:ir.actions.act_window,name:account.action_account_report_bs
#: model:ir.ui.menu,name:account.menu_account_report_bs
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Balance Sheet"
msgstr "Bilanço"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_balance_end
msgid "Balance as calculated based on Opening Balance and transaction lines"
msgstr "Bakiye, Açılış Bakiyesi ve işlem satırları baz alınarak hesaplanır"

#. module: account
#: selection:account.bank.accounts.wizard,account_type:0
#: selection:account.journal,type:0
#: code:addons/account/models/chart_template.py:758
#: model:ir.model.fields,field_description:account.field_account_journal_bank_id
#: model:ir.model.fields,field_description:account.field_res_partner_bank_account_count
#: model:ir.model.fields,field_description:account.field_res_users_bank_account_count
#, python-format
msgid "Bank"
msgstr "Banka"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Bank &amp; Cash"
msgstr "Banka &amp; Kasa"

#. module: account
#: code:addons/account/models/company.py:226
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_bank_account_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_bank_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_partner_bank_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_partner_bank_id
#: model:ir.model.fields,field_description:account.field_account_journal_bank_account_id
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#, python-format
msgid "Bank Account"
msgstr "Banka Hesabı"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Bank Account Name"
msgstr "Banka Hesap Adı"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_partner_bank_id
msgid ""
"Bank Account Number to which the invoice will be paid. A Company bank "
"account if this is a Customer Invoice or Vendor Credit Note, otherwise a "
"Partner bank account number."
msgstr ""
"Ödenecek fatura için Banka Hesap Numarası. Eğer bir Müşteri Faturası ya da "
"Tedarikçi İade/Fiyat Farkı Faturası ise Şirket Banka Hesap Numarası, aksi "
"taktirde İş Ortağı Banka Hesap Numarası . "

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:21
#: model:ir.actions.act_window,name:account.action_account_bank_journal_form
#: model:ir.model,name:account.model_res_partner_bank
#: model:ir.ui.menu,name:account.menu_action_account_bank_journal_form
#, python-format
msgid "Bank Accounts"
msgstr "Banka Hesapları"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_bank_account_code_prefix
msgid "Bank Accounts Prefix"
msgstr "Banka Hesap Ön Ekleri"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_bank_statements_source
msgid "Bank Feeds"
msgstr "Banka Bilgi Akışı"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_yodlee
msgid "Bank Interface - Sync your bank feeds automatically"
msgstr "Banka Arayüzü - Banka bilgi akışını otomatik olarak senkronize et"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_bank_journal_ids
msgid "Bank Journals"
msgstr "Banka Yevmiyeleri"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Bank Operations"
msgstr "Banka İşlemleri"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_tree
msgid "Bank Reconciliation Move Presets"
msgstr "Banka Uzlaştırma Hareket Önayarları"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_search
msgid "Bank Reconciliation Move preset"
msgstr "Banka Uzlaştırma Hareketi Önayarı"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_bank_data_done
msgid "Bank Setup Marked As Done"
msgstr "Banka kurulumu yapıldı olarak işaretlendi."

#. module: account
#: model:ir.model,name:account.model_account_bank_statement
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Bank Statement"
msgstr "Banka Hesap Özeti"

#. module: account
#: code:addons/account/models/account_bank_statement.py:937
#, python-format
msgid "Bank Statement %s"
msgstr "Hesap Özeti %s"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Banka Hesap Özeti Kalemi"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_line
msgid "Bank Statement Lines"
msgstr "Banka Hesap Özeti Kalemleri"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_tree
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Bank Statements"
msgstr "Banka Hesap Özetleri"

#. module: account
#: model:ir.ui.view,arch_db:account.partner_view_buttons
msgid "Bank account(s)"
msgstr "Banka Hesapları"

#. module: account
#: model:account.account.type,name:account.data_account_type_liquidity
msgid "Bank and Cash"
msgstr "Banka ve Kasa"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_action.js:15
#, python-format
msgid "Bank reconciliation"
msgstr "Banka Mutabakatı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_account_setup_bank_data_done
msgid "Bank setup marked as done"
msgstr "Banka kurulumu yapıldı olarak işaretlendi."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_statement_line_id
msgid "Bank statement line reconciled with this entry"
msgstr "Banka hesap özeti satırı bu kayıt ile uzlaştırılmış"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_bank_statement
msgid "Bank statements"
msgstr "Banka hesap özetleri"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:39
#, python-format
msgid "Bank: Balance"
msgstr "Banka: Bakiye"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_base
#: model:ir.ui.view,arch_db:account.portal_invoice_report
msgid "Base"
msgstr "Temel"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_base_amount
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Base Amount"
msgstr "Matrah Tutarı"

#. module: account
#: selection:account.tax,tax_exigibility:0
#: selection:account.tax.template,tax_exigibility:0
msgid "Based on Invoice"
msgstr "Faturaya dayalı"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_tax_exigibility
#: model:ir.model.fields,help:account.field_account_tax_template_tax_exigibility
msgid ""
"Based on Invoice: the tax is due as soon as the invoice is validated.\n"
"Based on Payment: the tax is due as soon as the payment of the invoice is received."
msgstr ""
"Faturaya dayalı: Fatura onaylandığı gibi vergisi tahsil edilir.\n"
"Ödemeye dayalı: Faturanın ödemesi alındığı gibi vergisi tahsil edilir."

#. module: account
#: selection:account.tax,tax_exigibility:0
#: selection:account.tax.template,tax_exigibility:0
msgid "Based on Payment"
msgstr "Ödemeye Dayalı"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Batch Deposits"
msgstr "Toplu Çek Çıkışı"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Before continuing, you must install the Chart of Account related to your "
"country (or the generic one if your country is not listed)."
msgstr ""
"Devam etmeden önce, ülkenizle ilgili Hesap Planını (veya listelenmemişse "
"genel olanı) yüklemelisiniz."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_belongs_to_company
msgid "Belong to the user's current company"
msgstr "Kullanıcının mevcut şirketine ait"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Bill"
msgstr "Fatura"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "Bill Date"
msgstr "Fatura Tarihi"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Bill lines"
msgstr "Fatura satırları"

#. module: account
#: model:res.groups,name:account.group_account_invoice
msgid "Billing"
msgstr "Faturalama"

#. module: account
#: model:res.groups,name:account.group_account_manager
msgid "Billing Manager"
msgstr "Fatura Yöneticisi"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills"
msgstr "Faturalar"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills Analysis"
msgstr "Fatura Analizleri"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:186
#, python-format
msgid "Bills to pay"
msgstr "Ödenecek faturalar"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "Blocking Message"
msgstr "Engelleme"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_type_include_initial_balance
msgid "Bring Accounts Balance Forward"
msgstr "Hesapları Geri Vardıra Getir"

#. module: account
#: model:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid "Browse available countries."
msgstr "Mevcut ülkeleri tara."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_budget
msgid "Budget Management"
msgstr "Bütçe Yönetimi"

#. module: account
#: model:ir.ui.menu,name:account.account_reports_business_intelligence_menu
msgid "Business Intelligence"
msgstr "Kayıt Analizleri"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_name
msgid "Button Label"
msgstr "Tuş Etiketi"

#. module: account
#: model:ir.filters,name:account.filter_invoice_country
msgid "By Country"
msgstr "Ülkeye Göre"

#. module: account
#: model:ir.filters,name:account.filter_invoice_refund
msgid "By Credit Note"
msgstr "İade/Fiyat Farkı ile"

#. module: account
#: model:ir.filters,name:account.filter_invoice_product
msgid "By Product"
msgstr "Ürüne Göre"

#. module: account
#: model:ir.filters,name:account.filter_invoice_product_category
msgid "By Product Category"
msgstr "Ürün Kategorisine Göre"

#. module: account
#: model:ir.filters,name:account.filter_invoice_salespersons
msgid "By Salespersons"
msgstr "Satış Personellerine Göre"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_active
msgid ""
"By unchecking the active field, you may hide a fiscal position without "
"deleting it."
msgstr ""
"Bu kutunun onayını kaldırırsanız, mali koşulu silmeden gizleyebilirsiniz."

#. module: account
#: code:addons/account/models/chart_template.py:173
#: code:addons/account/models/chart_template.py:197
#, python-format
msgid "CABA"
msgstr "CABA"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "CAMT Import"
msgstr "CAMT İçe Aktarımı"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "CSV Import"
msgstr "CSV İçe Aktarımı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_visible
msgid "Can be Visible?"
msgstr "Görünebilir mi?"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.account_common_report_view
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
#: model:ir.ui.view,arch_db:account.cash_box_in_form
#: model:ir.ui.view,arch_db:account.cash_box_out_form
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
#: model:ir.ui.view,arch_db:account.validate_account_move_view
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
#: model:ir.ui.view,arch_db:account.view_account_move_reversal
#: model:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Cancel"
msgstr "İptal"

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Cancel: create credit note and reconcile"
msgstr "İptal: İade/fiyat farkı ve uzlaştırma oluşturur."

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: selection:account.payment,state:0
msgid "Cancelled"
msgstr "İptal Edildi"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Cancelled Invoice"
msgstr "İptal edilmiş fatura"

#. module: account
#: code:addons/account/wizard/account_invoice_refund.py:56
#, python-format
msgid ""
"Cannot create a credit note for the invoice which is already reconciled, "
"invoice should be unreconciled first, then only you can add credit note for "
"this invoice."
msgstr ""
"Uzlaştırılmış fatura üzerinde iade/fiyat farkı yapılamaz. Öncelikle fatura "
"uzlaştırması kaldırılır, daha sonra bu fatura için iade/fiyat farkı "
"oluşturulabilir. "

#. module: account
#: code:addons/account/wizard/account_invoice_refund.py:54
#, python-format
msgid "Cannot create credit note for the draft/cancelled invoice."
msgstr "Taslak / İptal edilen fatura için iade/fiyat farkı oluşturulamaz."

#. module: account
#: code:addons/account/models/account_move.py:197
#, python-format
msgid "Cannot create moves for different companies."
msgstr "Farklı şirketler için hareketler oluşturulamıyor."

#. module: account
#: code:addons/account/models/account_move.py:229
#, python-format
msgid "Cannot create unbalanced journal entry."
msgstr "Borç-Alacak eşitliği olmayan bir yevmiye kaydı oluşturulamaz"

#. module: account
#: code:addons/account/models/account_invoice.py:641
#, python-format
msgid ""
"Cannot find a chart of accounts for this company, You should configure it. \n"
"Please go to Account Configuration."
msgstr ""
"Bu şirket için bir hesap planı bulunamadı. Bir tane yapılandırmalısınız. \n"
"Lütfen Muhasebe Yapılandırmasına gidin."

#. module: account
#: code:addons/account/models/account.py:594
#, python-format
msgid "Cannot generate an unused account code."
msgstr "Kullanılmayan bir hesap kodu oluşturulamıyor."

#. module: account
#: code:addons/account/models/account.py:624
#, python-format
msgid ""
"Cannot generate an unused journal code. Please fill the 'Shortcode' field."
msgstr ""
"Kullanılmayan bir hesap kodu oluşturulamıyor. Lütfen 'Kısa Kod' alanını "
"doldurun."

#. module: account
#: selection:account.bank.accounts.wizard,account_type:0
#: selection:account.journal,type:0
#: code:addons/account/models/chart_template.py:758
#, python-format
msgid "Cash"
msgstr "Kasa"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_cash_account_code_prefix
msgid "Cash Accounts Prefix"
msgstr "Kasa Hesap Ön Ekleri"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_tax_exigibility
msgid "Cash Basis"
msgstr "Gerçekleşme Esası (Cash Basis)"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_tax_cash_basis_journal_id
msgid "Cash Basis Journal"
msgstr "Gerçekleşme Esasına Göre Vergi Yevmiyesi"

#. module: account
#: code:addons/account/models/chart_template.py:197
#, python-format
msgid "Cash Basis Tax Journal"
msgstr "Tahsil Esaslı Vergi Yevmiyesi"

#. module: account
#: code:addons/account/models/account_bank_statement.py:210
#: model:ir.actions.act_window,name:account.action_view_account_bnk_stmt_cashbox
#, python-format
msgid "Cash Control"
msgstr "Kasa Kontrolü"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Cash Operations"
msgstr "Kasa İşlemleri"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_bank_statement_tree
msgid "Cash Registers"
msgstr "Yazar Kasalar"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_group_cash_rounding
msgid "Cash Rounding"
msgstr "Nakit Yuvarlama"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_cash_rounding_id
msgid "Cash Rounding Method"
msgstr "Nakit Yuvarlama Yöntemi"

#. module: account
#: model:ir.actions.act_window,name:account.rounding_list_action
#: model:ir.ui.menu,name:account.menu_action_rounding_form_view
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Cash Roundings"
msgstr "Nakit Yuvarlamaları"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Cash Statements"
msgstr "Nakit Durumları"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_bank_account_ids
msgid "Cash and Banks"
msgstr "Kasa ve Bankalar"

#. module: account
#: code:addons/account/models/account_bank_statement.py:185
#, python-format
msgid "Cash difference observed during the counting (%s)"
msgstr "Sayım sırasında gözlemlenen nakit farkı ( %s )"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:37
#, python-format
msgid "Cash: Balance"
msgstr "Nakit: Bakiye"

#. module: account
#: model:ir.model,name:account.model_account_cashbox_line
msgid "CashBox Line"
msgstr "Nakit Kasa Satırı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_cashbox_id
msgid "Cashbox"
msgstr "Nakit Kasa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_cashbox_lines_ids
msgid "Cashbox Lines"
msgstr "Kasa Satırları"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_expense_categ_id
msgid "Category of Expense Account"
msgstr "Gider Hesabı Kategorisi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_income_categ_id
msgid "Category of Income Account"
msgstr "Gelir Hesabı Kategorisi"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Change"
msgstr "Değiştir"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_writeoff_label
msgid "Change label of the counterpart that will hold the payment difference"
msgstr "Ödeme farkını tutacak karşı hesabın etiketini değiştirin"

#. module: account
#: code:addons/account/controllers/portal.py:146
#, python-format
msgid ""
"Changing VAT number is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"Hesabınız için düzenlenen bir fatura varsa Vergi No değişikliğine izin "
"verilemez. Lütfen bu değişiklik için bizimle iletişime geçin."

#. module: account
#: code:addons/account/controllers/portal.py:149
#, python-format
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"Hesabınız için düzenlenen bir fatura varsa isminizi değiştirmenize izin "
"verilemez. Lütfen bu değişiklik için bizimle iletişime geçin."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_res_company_chart_template_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_chart_template_id
msgid "Chart Template"
msgstr "Hesap Planı Şablonu"

#. module: account
#: model:ir.actions.act_window,name:account.open_account_charts_modules
msgid "Chart Templates"
msgstr "Hesap Planı Şablonu"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_coa_done
msgid "Chart of Account Checked"
msgstr "Hesap planı kontrol edildi."

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:278
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:13
#: model:ir.actions.act_window,name:account.action_account_form
#: model:ir.ui.menu,name:account.menu_action_account_form
#: model:ir.ui.view,arch_db:account.account_planner
#, python-format
msgid "Chart of Accounts"
msgstr "Hesap Planı"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_form
#: model:ir.ui.view,arch_db:account.view_account_chart_template_tree
msgid "Chart of Accounts Template"
msgstr "Hesap Planı Şablonu"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_chart_template_form
msgid "Chart of Accounts Templates"
msgstr "Hesap Planı Şablonları"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Chart of Accounts has been\n"
"                            installed. You should review it and create any additional accounts."
msgstr ""
"Standart Hesap Planı yüklendi.\n"
"İhtiyaçlarınıza göre ek hesaplar oluşturabilirsiniz."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_list
msgid "Chart of accounts"
msgstr "Hesap Planı"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_account_bnk_stmt_check
msgid "Check Closing Balance"
msgstr "Kapanış Bakiyesi Kontrol"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_is_difference_zero
msgid "Check if difference is zero."
msgstr "Farkın sıfır olup olmadığını kontrol edin."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_reconcile
msgid ""
"Check this box if this account allows invoices & payments matching of "
"journal items."
msgstr ""
"Bu hesap için, yevmiye öğelerini fatura ve ödemelerde eşleştirmeye izin "
"vermek istiyorsanız bu kutuyu işaretleyin."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_refund_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" credit notes made from this journal"
msgstr ""
"Bu yevmiyeden aynı sıradaki faturaları ve iade/fiyat farklarını görmek "
"istemiyorsanız lütfen bu seçim alanını işaretleyin."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_update_posted
msgid ""
"Check this box if you want to allow the cancellation the entries related to "
"this journal or of the invoice related to this journal"
msgstr ""
"Bu yevmiyeyle ilişkili kayıtların ya da bu yevmiyeyle ilişkili faturaların "
"iptal edilmesine izin vermek istiyorsanız bu kutuyu işaretleyin"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_price_include
#: model:ir.model.fields,help:account.field_account_tax_template_price_include
msgid ""
"Check this if the price you use on the product and invoices includes this "
"tax."
msgstr ""
"Ürün ve faturada kullandığınız fiyat vergi dahil fiyat ise işaretleyin."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_reconcile
msgid ""
"Check this option if you want the user to reconcile entries in this account."
msgstr ""
"Bu hesaptaki kayıtların kullanıcı tarafından uzlaştırılmasını istiyorsanız "
"bu seçeneği işaretleyin."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Checks"
msgstr "Kontroller"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_children_tax_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template_children_tax_ids
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_tax_form
msgid "Children Taxes"
msgstr "Alt Vergiler"

#. module: account
#: code:addons/account/models/chart_template.py:155
#: model:ir.actions.act_window,name:account.action_wizard_multi_chart
#, python-format
msgid "Choose Accounting Template"
msgstr "Hesap Şablonu Seç"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:131
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr "Karşı hesap seçin yada borcu silin"

#. module: account
#: model:ir.actions.act_window,help:account.action_account_bank_journal_form
msgid "Click to add a bank account."
msgstr "Yeni bir banka hesabı eklemek için tıklayın."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_journal_form
msgid "Click to add a journal."
msgstr "Yeni bir yevmiye eklemek için tıklayın."

#. module: account
#: model:ir.actions.act_window,help:account.account_tag_action
msgid "Click to add a new tag."
msgstr "Yeni bir etiket eklemek için tıklayın."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_form
msgid "Click to add an account."
msgstr "Yeni bir hesap eklemek için tıklayın."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_out_refund
msgid "Click to create a credit note."
msgstr "Bir İade/Fiyat Farkı faturası oluşturmak için tıklayın."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree1
msgid "Click to create a customer invoice."
msgstr "Yeni bir müşteri faturası oluşturmak için tıklayın."

#. module: account
#: model:ir.actions.act_window,help:account.action_move_journal_line
msgid "Click to create a journal entry."
msgstr "Yeni bir yevmiye kaydı oluşturmak için tıklayın."

#. module: account
#: model:ir.actions.act_window,help:account.action_view_bank_statement_tree
msgid "Click to create a new cash log."
msgstr "Yeni bir kasa günlüğü oluşturmak için tıklayın."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_reconcile_model
msgid "Click to create a reconciliation model."
msgstr "Yeni bir uzlaşma modeli oluşturmak için tıklayın."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_type_form
msgid "Click to define a new account type."
msgstr "Yeni bir hesap türü tanımlamak için tıklayın."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree2
msgid "Click to record a new vendor bill."
msgstr "Yeni bir tedarikçi faturası kaydetmek için tıklayın."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_in_refund
msgid "Click to record a new vendor credit note."
msgstr "Yeni bir tedarikçi iade/fiyat farkı eklemek için tıklayın."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_tree
msgid "Click to register a bank statement."
msgstr "Yeni bir hesap özeti girmek için tıklayın."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_payments
#: model:ir.actions.act_window,help:account.action_account_payments_payable
msgid "Click to register a payment"
msgstr "Yeni bir ödeme kaydetmek için tıklayın."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:260
#, python-format
msgid "Close"
msgstr "Kapat"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:60
#, python-format
msgid "Close statement"
msgstr "Kapalı durum"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_date_done
msgid "Closed On"
msgstr "Kapanış"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_method_code
#: model:ir.model.fields,field_description:account.field_account_account_code
#: model:ir.model.fields,field_description:account.field_account_account_template_code
#: model:ir.model.fields,field_description:account.field_account_analytic_line_code
#: model:ir.model.fields,field_description:account.field_account_payment_method_code
#: model:ir.model.fields,field_description:account.field_account_payment_payment_method_code
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_method_code
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Code"
msgstr "Kod"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group_code_prefix
msgid "Code Prefix"
msgstr "Kod Öneki"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_coin_value
msgid "Coin/Bill Value"
msgstr "Para Parası / Fatura Değeri"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Collect customer payments in one-click using Euro SEPA Service."
msgstr "Euro SEPA Servis ile bir tıkla müşteri ödemelerini tahsil et."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_color
#: model:ir.model.fields,field_description:account.field_account_journal_color
msgid "Color Index"
msgstr "Renk İndeksi"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_label_filter
msgid "Column Label"
msgstr "Sütun Etiketi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_comment
msgid "Comment"
msgstr "Yorum"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_commercial_partner_id
#: model:ir.model.fields,help:account.field_account_invoice_report_commercial_partner_id
msgid "Commercial Entity"
msgstr "Ticari Varlık"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_common_menu
msgid "Common Report"
msgstr "Ortak Rapor"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Communication"
msgstr "İletişim"

#. module: account
#: model:ir.model,name:account.model_res_company
msgid "Companies"
msgstr "Şirketler"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_ref_company_ids
#: model:ir.model.fields,field_description:account.field_res_users_ref_company_ids
msgid "Companies that refers to partner"
msgstr "İş Ortağını ilgilendiren şirketler"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_company_id
#: model:ir.model.fields,field_description:account.field_account_account_company_id
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_company_id
#: model:ir.model.fields,field_description:account.field_account_balance_report_company_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_company_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_company_id
#: model:ir.model.fields,field_description:account.field_account_chart_template_company_id
#: model:ir.model.fields,field_description:account.field_account_common_account_report_company_id
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_company_id
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_company_id
#: model:ir.model.fields,field_description:account.field_account_common_report_company_id
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_company_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_company_id
#: model:ir.model.fields,field_description:account.field_account_journal_company_id
#: model:ir.model.fields,field_description:account.field_account_move_company_id
#: model:ir.model.fields,field_description:account.field_account_move_line_company_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_company_id
#: model:ir.model.fields,field_description:account.field_account_opening_company_id
#: model:ir.model.fields,field_description:account.field_account_payment_company_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_company_id
#: model:ir.model.fields,field_description:account.field_account_print_journal_company_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_company_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_company_id
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_company_id
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_company_id
#: model:ir.model.fields,field_description:account.field_account_tax_company_id
#: model:ir.model.fields,field_description:account.field_account_tax_report_company_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_company_id
#: model:ir.model.fields,field_description:account.field_accounting_report_company_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_company_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Company"
msgstr "Şirket"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line_company_currency_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_company_currency_id
msgid "Company Currency"
msgstr "Şirket Para Birimi"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:210
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:25
#, python-format
msgid "Company Data"
msgstr "Şirket Bilgileri"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_company_data_done
msgid "Company Setup Marked As Done"
msgstr "Şirket kurulumu yapıldı olarak işaretlendi."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_has_chart_of_accounts
msgid "Company has a chart of accounts"
msgstr "Şirketin bir hesap planı var"

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_company_id
#: model:ir.model.fields,help:account.field_account_bank_statement_company_id
#: model:ir.model.fields,help:account.field_account_bank_statement_line_company_id
#: model:ir.model.fields,help:account.field_account_journal_company_id
#: model:ir.model.fields,help:account.field_account_move_company_id
#: model:ir.model.fields,help:account.field_account_payment_company_id
#: model:ir.model.fields,help:account.field_account_register_payments_company_id
msgid "Company related to this journal"
msgstr "Bu yevmiyeyle ilişkili şirket"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Compare actual revenues &amp; costs with budgets"
msgstr "Gerçekleşen gelir ve maliyetleri bütçeler ile karşılaştırın"

#. module: account
#: model:ir.ui.view,arch_db:account.accounting_report_view
msgid "Comparison"
msgstr "Karşılaştır"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_complete_tax_set
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_complete_tax_set
msgid "Complete Set of Taxes"
msgstr "Tam Vergi Seti"

#. module: account
#: code:addons/account/models/account_invoice.py:575
#, python-format
msgid "Compose Email"
msgstr "Eposta Yaz"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Compute tax rates based on U.S. ZIP codes"
msgstr "ABD posta kodlarına dayalı vergi oranlarını hesaplayın"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_counterpart
msgid ""
"Compute the counter part accounts of this journal item for this journal "
"entry. This can be needed in reports."
msgstr ""
"Bu yevmiye girişinin bu yevmiye maddesinin karşı taraf hesaplarını "
"hesaplayın. Bu, raporlarda gerekebilir."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_balance_end
msgid "Computed Balance"
msgstr "Hesaplanan Bakiye"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_configuration
msgid "Configuration"
msgstr "Yapılandırma"

#. module: account
#: code:addons/account/models/account_payment.py:643
#: code:addons/account/models/account_payment.py:645
#, python-format
msgid "Configuration Error !"
msgstr "Yapılandırma Hatası!"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:29
#, python-format
msgid "Configuration Steps:"
msgstr "Yapılandırma:"

#. module: account
#: code:addons/account/models/account_invoice.py:462
#, python-format
msgid ""
"Configuration error!\n"
"Could not find any account to create the invoice, are you sure you have a chart of account installed?"
msgstr ""
"Yapılandırma hatası!\n"
"Faturayı oluşturmak için herhangi bir hesap bulunamadı, bir hesap planının yüklü olduğunuzdan emin misiniz?"

#. module: account
#: code:addons/account/models/account.py:443
#, python-format
msgid ""
"Configuration error!\n"
"The currency of the journal should be the same than the default credit account."
msgstr ""
"Yapılandırma hatası!\n"
"Yevmiye para birimi, varsayılan alacak hesabıyla aynı olmalıdır."

#. module: account
#: code:addons/account/models/account.py:445
#, python-format
msgid ""
"Configuration error!\n"
"The currency of the journal should be the same than the default debit account."
msgstr ""
"Yapılandırma hatası!\n"
"Yevmiye para birimi, varsayılan borç hesabı ile aynı olmalıdır."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Configuration menu"
msgstr "Yapılandırma menüsü"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Configure"
msgstr "Yapılandırma"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
#: model:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Confirm"
msgstr "Onayla"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_confirm
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid "Confirm Draft Invoices"
msgstr "Taslak Faturaları Onayla"

#. module: account
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid "Confirm Invoices"
msgstr "Faturaları Onayla"

#. module: account
#: model:ir.actions.server,name:account.action_account_confirm_payments
msgid "Confirm Payments"
msgstr "Ödemeleri Doğrulayın"

#. module: account
#: model:ir.model,name:account.model_account_invoice_confirm
msgid "Confirm the selected invoices"
msgstr "Seçili faturaları doğrula"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Confirmed"
msgstr "Onaylandı"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
msgid ""
"Confirming this will create automatically a journal entry with the "
"difference in the profit/loss account set on the cash journal."
msgstr ""
"Bunu doğrulamak, banka/kasa yevmiyesinde belirlenen kar / zarar hesabındaki "
"fark için otomatik olarak bir yevmiye kaydı oluşturacaktır."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:52
#, python-format
msgid "Congrats, you're all done!"
msgstr "Tebrikler, her şey bitti!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Congratulations, you're done!"
msgstr "Tebrikler, bitirdiniz."

#. module: account
#: model:ir.model,name:account.model_res_partner
msgid "Contact"
msgstr "Kontak"

#. module: account
#: model:ir.model,name:account.model_account_abstract_payment
msgid ""
"Contains the logic shared between models which allows to register payments"
msgstr ""
"Ödemeleri yapmasına izin veren modeller arasında paylaşılan mantığı içerir."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_contract_ids
#: model:ir.model.fields,field_description:account.field_res_partner_contracts_count
#: model:ir.model.fields,field_description:account.field_res_users_contract_ids
#: model:ir.model.fields,field_description:account.field_res_users_contracts_count
msgid "Contracts"
msgstr "Sözleşmeler"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Control-Access"
msgstr "Kontrol-Hesap"

#. module: account
#: model:account.account.type,name:account.data_account_type_direct_costs
msgid "Cost of Revenue"
msgstr "Gelir Maliyeti"

#. module: account
#: code:addons/account/models/chart_template.py:873
#, python-format
msgid ""
"Could not install new chart of account as there are already accounting "
"entries existing"
msgstr ""
"Daha önce muhasebe kayıtları oluşturduğunuz için yeni hesap planı "
"yüklenemedi."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_counterpart
msgid "Counterpart"
msgstr "Karşı Hesap"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_account_id
msgid "Counterpart Account"
msgstr "Karşı Hesap"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_country_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_country_id
msgid "Country"
msgstr "Ülke"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_country_group_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_country_group_id
msgid "Country Group"
msgstr "Ülke Grubu"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_country_id
msgid "Country of the Partner Company"
msgstr "İş Ortağı Firmanın Ülkesi"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Create Customers"
msgstr "Müşteri Oluştur"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Create Vendors"
msgstr "Tedarikçi Oluştur"

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Create a draft credit note"
msgstr "Taslak İade/Fiyat Farkı oluşturun"

#. module: account
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Create and post move"
msgstr "Oluştur ve Onayla"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:288
#, python-format
msgid "Create cash statement"
msgstr "Nakit Hesap Özeti Ekle"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:274
#, python-format
msgid "Create invoice/bill"
msgstr "Fatura Oluştur"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:163
#, python-format
msgid "Create model"
msgstr "Model Oluştur"

#. module: account
#: model:ir.actions.act_window,help:account.rounding_list_action
msgid "Create the first cash rounding"
msgstr "İlk nakit yuvarlamasını oluştur"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Create your products"
msgstr "Ürünlerinizi oluşturun"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_create_uid
#: model:ir.model.fields,field_description:account.field_account_account_tag_create_uid
#: model:ir.model.fields,field_description:account.field_account_account_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_account_type_create_uid
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_create_uid
#: model:ir.model.fields,field_description:account.field_account_balance_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_create_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_account_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_financial_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_create_uid
#: model:ir.model.fields,field_description:account.field_account_group_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_create_uid
#: model:ir.model.fields,field_description:account.field_account_journal_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal_create_uid
#: model:ir.model.fields,field_description:account.field_account_opening_create_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal_create_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_create_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_register_payments_create_uid
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_create_uid
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile_create_uid
#: model:ir.model.fields,field_description:account.field_accounting_report_create_uid
#: model:ir.model.fields,field_description:account.field_cash_box_in_create_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out_create_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_create_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move_create_uid
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_create_date
#: model:ir.model.fields,field_description:account.field_account_account_tag_create_date
#: model:ir.model.fields,field_description:account.field_account_account_template_create_date
#: model:ir.model.fields,field_description:account.field_account_account_type_create_date
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_create_date
#: model:ir.model.fields,field_description:account.field_account_balance_report_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_create_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_create_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_create_date
#: model:ir.model.fields,field_description:account.field_account_chart_template_create_date
#: model:ir.model.fields,field_description:account.field_account_common_account_report_create_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_create_date
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_create_date
#: model:ir.model.fields,field_description:account.field_account_common_report_create_date
#: model:ir.model.fields,field_description:account.field_account_financial_report_create_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_create_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_create_date
#: model:ir.model.fields,field_description:account.field_account_group_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_line_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_create_date
#: model:ir.model.fields,field_description:account.field_account_journal_create_date
#: model:ir.model.fields,field_description:account.field_account_move_create_date
#: model:ir.model.fields,field_description:account.field_account_move_line_create_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_create_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_create_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal_create_date
#: model:ir.model.fields,field_description:account.field_account_opening_create_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_method_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_create_date
#: model:ir.model.fields,field_description:account.field_account_print_journal_create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_create_date
#: model:ir.model.fields,field_description:account.field_account_register_payments_create_date
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_create_date
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_group_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_report_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_template_create_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile_create_date
#: model:ir.model.fields,field_description:account.field_accounting_report_create_date
#: model:ir.model.fields,field_description:account.field_cash_box_in_create_date
#: model:ir.model.fields,field_description:account.field_cash_box_out_create_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_create_date
#: model:ir.model.fields,field_description:account.field_validate_account_move_create_date
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_create_date
msgid "Created on"
msgstr "Oluşturulma"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_credit
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Credit"
msgstr "Alacak"

#. module: account
#: model:account.account.type,name:account.data_account_type_credit_card
msgid "Credit Card"
msgstr "Kredi Kartı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_credit_cash_basis
msgid "Credit Cash Basis"
msgstr "Gerçekleşme Esası (Alacak)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_credit_move_id
msgid "Credit Move"
msgstr "Alacak Hareketi"

#. module: account
#: code:addons/account/models/account_invoice.py:441
#: code:addons/account/models/account_invoice.py:1216
#: code:addons/account/wizard/account_invoice_refund.py:111
#: model:ir.actions.act_window,name:account.action_account_invoice_refund
#: model:ir.model,name:account.model_account_invoice_refund
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#, python-format
msgid "Credit Note"
msgstr "İade/Fiyat Farkı"

#. module: account
#: code:addons/account/models/account_invoice.py:442
#, python-format
msgid "Credit Note - %s"
msgstr "İade/Fiyat Farkı - %s"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Credit Note Bill"
msgstr "İade/Fiyat Farkı Faturası"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_date_invoice
msgid "Credit Note Date"
msgstr "İade/Fiyat Farkı Faturası Tarihi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_refund_sequence_id
msgid "Credit Note Entry Sequence"
msgstr "İade/Fiyat Farkı Giriş Sırası"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Credit Notes"
msgstr "İade/Fiyat Farkları"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_refund_sequence_number_next
msgid "Credit Notes: Next Number"
msgstr "İade/Fiyat Farkları : Sıradaki Numara"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_credit_account_id
msgid "Credit account"
msgstr "Alacak hesabı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_credit
msgid "Credit amount"
msgstr "Alacak tutarı"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_matched_credit_ids
msgid "Credit journal items that are matched with this journal item."
msgstr "Bu yevmiye öğesiyle eşleşen alacak yevmiyesi öğeleri."

#. module: account
#: model:ir.ui.menu,name:account.menu_action_currency_form
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Currencies"
msgstr "Para Birimleri"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_currency_id
#: model:ir.model.fields,field_description:account.field_account_analytic_line_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_journal_currency_id
#: model:ir.model.fields,field_description:account.field_account_chart_template_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_currency_id
#: model:ir.model.fields,field_description:account.field_account_journal_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line_currency_id
#: model:ir.model.fields,field_description:account.field_account_opening_currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_company_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_currency_id
#: model:ir.model.fields,field_description:account.field_account_payment_currency_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_currency_id
#: model:ir.model.fields,field_description:account.field_res_config_settings_currency_id
#: model:ir.model.fields,field_description:account.field_res_partner_currency_id
#: model:ir.model.fields,field_description:account.field_res_users_currency_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_currency_id
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Currency"
msgstr "Para Birimi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_currency_rate
msgid "Currency Rate"
msgstr "Para Birimi Çevrim Oranı"

#. module: account
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_currency_id
msgid "Currency as per company's country."
msgstr "Şirket Para Birimi Kuru"

#. module: account
#: code:addons/account/models/account_move.py:1663
#: code:addons/account/models/account_move.py:1675
#, python-format
msgid "Currency exchange rate difference"
msgstr "Döviz kur farkı"

#. module: account
#: code:addons/account/models/account.py:451
#, python-format
msgid ""
"Currency field should only be set if the journal's currency is different "
"from the company's. Leave the field blank to use company currency."
msgstr ""
"Para birimi alanı yalnızca yevmiyenin para birimi şirketten farklıysa "
"ayarlanmalıdır. Şirketin para birimini kullanmak için alanı boş bırakın."

#. module: account
#: model:account.account.type,name:account.data_account_type_current_assets
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Current Assets"
msgstr "Dönen Varlıklar"

#. module: account
#: model:account.account.type,name:account.data_account_type_current_liabilities
msgid "Current Liabilities"
msgstr "Kısa Vadeli Borçlar"

#. module: account
#: model:account.account.type,name:account.data_unaffected_earnings
msgid "Current Year Earnings"
msgstr "Cari Yıldaki Kazanç"

#. module: account
#: selection:account.abstract.payment,partner_type:0
#: selection:account.payment,partner_type:0
#: selection:account.register.payments,partner_type:0
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_payment_tree
msgid "Customer"
msgstr "Müşteri"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_payment.py:677
#, python-format
msgid "Customer Credit Note"
msgstr "Müşteri İade/Fiyat Farkı Faturası"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_out_refund
#: model:ir.ui.menu,name:account.menu_action_invoice_out_refund
msgid "Customer Credit Notes"
msgstr "Müşteri İade/Fiyat Farkı Faturaları"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Customer Flow"
msgstr "Müşteri Akışı"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
msgid "Customer Invoice"
msgstr "Müşteri Faturası"

#. module: account
#: code:addons/account/models/chart_template.py:193
#: model:ir.actions.act_window,name:account.action_invoice_tree1
#: model:ir.ui.menu,name:account.menu_action_invoice_tree1
#: model:ir.ui.view,arch_db:account.account_planner
#, python-format
msgid "Customer Invoices"
msgstr "Müşteri Faturaları"

#. module: account
#: code:addons/account/models/account_payment.py:675
#, python-format
msgid "Customer Payment"
msgstr "Müşteri Ödemesi"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_payment_term_id
#: model:ir.model.fields,field_description:account.field_res_users_property_payment_term_id
msgid "Customer Payment Terms"
msgstr "Müşteri Ödeme Koşulları"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Customer Payments"
msgstr "Müşteri Ödemeleri"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_product_taxes_id
#: model:ir.model.fields,field_description:account.field_product_template_taxes_id
msgid "Customer Taxes"
msgstr "Müşteri Vergileri"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Customer ref:"
msgstr "Müşteri ref:"

#. module: account
#: model:ir.ui.menu,name:account.menu_account_customer
msgid "Customers"
msgstr "Müşteriler"

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "DOWN"
msgstr "AŞAĞIYA"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/account_dashboard_setup_bar.js:214
#: model:ir.ui.menu,name:account.menu_board_journal_1
#, python-format
msgid "Dashboard"
msgstr "Panel"

#. module: account
#. openerp-web
#: selection:account.print.journal,sort_selection:0
#: selection:account.report.general.ledger,sortby:0
#: selection:accounting.report,filter_cmp:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:233
#: code:addons/account/static/src/xml/account_reconciliation.xml:248
#: model:ir.model.fields,field_description:account.field_account_bank_statement_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_date
#: model:ir.model.fields,field_description:account.field_account_invoice_report_date
#: model:ir.model.fields,field_description:account.field_account_move_date
#: model:ir.model.fields,field_description:account.field_account_move_line_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_date_p
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_date
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_overdue_document
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Date"
msgstr "Tarih"

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_opening_date
msgid ""
"Date at which the opening entry of this company's accounting has been "
"posted."
msgstr "Şirket muhasebe kaydının açılış tarihi gönderilmiştir"

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op_opening_date
msgid ""
"Date from which the accounting is managed in Odoo. It is the date of the "
"opening entry."
msgstr "Odoo'da yönetilen muhasebe açılış kayıt tarihi. "

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:56
#, python-format
msgid "Date:"
msgstr "Tarih:"

#. module: account
#: model:ir.ui.view,arch_db:account.accounting_report_view
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Dates"
msgstr "Tarihler"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Day(s) after the end of the invoice month (Net EOM)"
msgstr "Fatura ayının bitiminden sonraki gün (Net EOM)"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Day(s) after the invoice date"
msgstr "Fatura tarihinden sonraki gün (ler)"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Deactivate setup bar on the dashboard"
msgstr "Panel üzerindeki kurulum barını devre dışı bırak"

#. module: account
#: code:addons/account/models/company.py:45
#, python-format
msgid ""
"Dear Sir/Madam,\n"
"\n"
"Our records indicate that some payments on your account are still due. Please find details below.\n"
"If the amount has already been paid, please disregard this notice. Otherwise, please forward us the total amount stated below.\n"
"If you have any queries regarding your account, Please contact us.\n"
"\n"
"Thank you in advance for your cooperation.\n"
"Best Regards,"
msgstr ""
"Sayın Yetkili,\n"
"\n"
"Kayıtlarımıza göre hesabınızda halen yapılmamış ödemeler bulunmaktadır. Lütfen aşağıdaki ayrıntıları inceleyin.\n"
"Eğer ödeme yaptıysanız, lütfen bu yazımızı dikkate almayın. Aksi durumda, aşağıda belirtilen tutarı tarafımıza ödemenizi rica ederiz.\n"
"Hesabınızla ilgili herhangi bir sorunuz olursa, lütfen bize danışın.\n"
"\n"
"İşbirliğiniz için şimdiden teşekkür ederiz.\n"
"\n"
"Saygılarımızla,"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_debit
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Debit"
msgstr "Borç"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_debit_cash_basis
msgid "Debit Cash Basis"
msgstr "Gerçekleşme Esası (Borç)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_inbound_payment_method_ids
msgid "Debit Methods"
msgstr "Borç Metodu"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_debit_move_id
msgid "Debit Move"
msgstr "Borç Hareketi"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_debit_account_id
msgid "Debit account"
msgstr "Borç Hesabı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_debit
msgid "Debit amount"
msgstr "Borç Tutarı"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_matched_debit_ids
msgid "Debit journal items that are matched with this journal item."
msgstr "Bu yemiye öğesi ile eşleştirilen borç yevmiye öğeleri."

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "December"
msgstr "Aralık"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_refund_sequence
msgid "Dedicated Credit Note Sequence"
msgstr "İade/Fiyat Farkı Sırası"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_default_credit_account_id
msgid "Default Credit Account"
msgstr "Varsayılan Alacak Hesabı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_default_debit_account_id
msgid "Default Debit Account"
msgstr "Varsayılan Borç Hesabı"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_default_purchase_tax_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_purchase_tax_id
msgid "Default Purchase Tax"
msgstr "Varsayılan Satınalma Vergisi"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_default_sale_tax_id
msgid "Default Sale Tax"
msgstr "Varsayılan Satış Vergisi"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_sale_tax_id
msgid "Default Sales Tax"
msgstr "Varsayılan Satış Vergisi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tax_ids
#: model:ir.model.fields,field_description:account.field_account_account_template_tax_ids
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_account_chart_template_form
#: model:ir.ui.view,arch_db:account.view_account_template_form
msgid "Default Taxes"
msgstr "Varsayılan Vergiler"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Default taxes applied to local transactions"
msgstr "Yerel işlemlere uygulanan varsayılan vergiler"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Deferred Revenues Management"
msgstr "Ertelenmiş Gelir Yönetimi"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash."
msgstr "Nakit olarak kullanılmak üzere en küçük para birimini tanımla"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_cash_rounding_id
msgid ""
"Defines the smallest coinage of the currency that can be used to pay by "
"cash."
msgstr "Nakit olarak kullanılmak üzere en küçük para birimini tanımla"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_tax_form
msgid "Definition"
msgstr "Tanım"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_trust
#: model:ir.model.fields,field_description:account.field_res_users_trust
msgid "Degree of trust you have in this debtor"
msgstr "Bu borçlu için güven seviyesi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_deprecated
msgid "Deprecated"
msgstr "Süresi Dolmuş"

#. module: account
#: model:account.account.type,name:account.data_account_type_depreciation
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Depreciation"
msgstr "Amortisman"

#. module: account
#: model:ir.ui.view,arch_db:account.cash_box_out_form
msgid "Describe why you take money from the cash register:"
msgstr "Nakit kasadan neden para aldığınızı açıklayın:"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:251
#: model:ir.model.fields,field_description:account.field_account_account_type_note
#: model:ir.model.fields,field_description:account.field_account_invoice_line_name
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_account_type_form
#, python-format
msgid "Description"
msgstr "Açıklama"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_note
msgid "Description on the Invoice"
msgstr "Fatura Açıklaması"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_destination_account_id
msgid "Destination Account"
msgstr "Hesap Kartı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_auto_apply
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_auto_apply
msgid "Detect Automatically"
msgstr "Kendiliğinden Algıla"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_template_type_tax_use
#: model:ir.model.fields,help:account.field_account_tax_type_tax_use
msgid ""
"Determines where the tax is selectable. Note : 'None' means a tax can't be "
"used by itself, however it can still be used in a group."
msgstr ""
"Vergi seçilebilecek yerleri belirler. Not: 'Hiçbiri', bir vergi tek başına "
"kullanılamaz anlamına gelir, ancak yine de bir grupta kullanılabilir."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_difference
msgid "Difference"
msgstr "Farkı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_writeoff_account_id
msgid "Difference Account"
msgstr "Fark Hesabı"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_difference
msgid ""
"Difference between the computed ending balance and the specified ending "
"balance."
msgstr ""
"Hesaplanan bitiş bakiyesi ile belirtilen bitiş bakiyesi arasındaki fark."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Direct connection to your bank"
msgstr "Bankanıza doğrudan bağlantı"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Disc.(%)"
msgstr "İnd.(%)"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_wizard_form
msgid "Discard"
msgstr "Vazgeç"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_discount
msgid "Discount (%)"
msgstr "İndirim (%)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_balance_report_display_account
#: model:ir.model.fields,field_description:account.field_account_common_account_report_display_account
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_display_account
msgid "Display Accounts"
msgstr "Hesapları Göster"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_debit_credit
msgid "Display Debit/Credit Columns"
msgstr "Borç/Alacak Sütünlarını Göster"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_display_name
#: model:ir.model.fields,field_description:account.field_account_account_display_name
#: model:ir.model.fields,field_description:account.field_account_account_tag_display_name
#: model:ir.model.fields,field_description:account.field_account_account_template_display_name
#: model:ir.model.fields,field_description:account.field_account_account_type_display_name
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_display_name
#: model:ir.model.fields,field_description:account.field_account_balance_report_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_display_name
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_display_name
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_display_name
#: model:ir.model.fields,field_description:account.field_account_chart_template_display_name
#: model:ir.model.fields,field_description:account.field_account_common_account_report_display_name
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_display_name
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_display_name
#: model:ir.model.fields,field_description:account.field_account_common_report_display_name
#: model:ir.model.fields,field_description:account.field_account_financial_report_display_name
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_display_name
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_display_name
#: model:ir.model.fields,field_description:account.field_account_group_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_line_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_report_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_display_name
#: model:ir.model.fields,field_description:account.field_account_journal_display_name
#: model:ir.model.fields,field_description:account.field_account_move_display_name
#: model:ir.model.fields,field_description:account.field_account_move_line_display_name
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_display_name
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_display_name
#: model:ir.model.fields,field_description:account.field_account_move_reversal_display_name
#: model:ir.model.fields,field_description:account.field_account_opening_display_name
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_method_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_term_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_display_name
#: model:ir.model.fields,field_description:account.field_account_print_journal_display_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_display_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_display_name
#: model:ir.model.fields,field_description:account.field_account_register_payments_display_name
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_display_name
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_group_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_report_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_template_display_name
#: model:ir.model.fields,field_description:account.field_account_unreconcile_display_name
#: model:ir.model.fields,field_description:account.field_accounting_report_display_name
#: model:ir.model.fields,field_description:account.field_cash_box_in_display_name
#: model:ir.model.fields,field_description:account.field_cash_box_out_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_financial_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_generalledger_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_journal_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_overdue_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_partnerledger_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_tax_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_trialbalance_display_name
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_display_name
#: model:ir.model.fields,field_description:account.field_validate_account_move_display_name
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: account
#: selection:account.financial.report,display_detail:0
msgid "Display children flat"
msgstr "Alt hesapları düz görüntüle"

#. module: account
#: selection:account.financial.report,display_detail:0
msgid "Display children with hierarchy"
msgstr "Alt hesapları sıradüzeni ile göster"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_display_detail
msgid "Display details"
msgstr "Ayrıntıları görüntüle"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_description
msgid "Display on Invoices"
msgstr "Faturalarda Görüntüle"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_print_docsaway
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Docsaway"
msgstr "Docsaway"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid ""
"Document: Customer account statement<br/>\n"
"                    Date:"
msgstr ""
"Belge: Müşteri hesap özeti <br/>\n"
"            Tarih:"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_payables_documents
#: model:ir.ui.menu,name:account.menu_finance_receivables_documents
msgid "Documents"
msgstr "Belgeler"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Don't hesitate to"
msgstr "Çekinmeden"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_page
msgid "Download"
msgstr "İndir"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Download the"
msgstr "İndir"

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: selection:account.payment,state:0
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Draft"
msgstr "Taslak"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Draft Bill"
msgstr "Taslak Fatura"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Draft Credit Note"
msgstr "Taslak İade/Fiyat Farkı"

#. module: account
#: code:addons/account/models/account_invoice.py:439
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Draft Invoice"
msgstr "Taslak Fatura"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Draft Invoices"
msgstr "Taslak Faturalar"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Draft bills"
msgstr "Taslak fatura"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_draft_tree
msgid "Draft statements"
msgstr "Taslak hesap özetleri"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Due"
msgstr "Vade"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_residual
msgid "Due Amount"
msgstr "Ödeme Tutarı"

#. module: account
#. openerp-web
#: code:addons/account/controllers/portal.py:71
#: code:addons/account/static/src/xml/account_reconciliation.xml:234
#: model:ir.model.fields,field_description:account.field_account_invoice_date_due
#: model:ir.model.fields,field_description:account.field_account_invoice_report_date_due
#: model:ir.ui.view,arch_db:account.portal_my_invoices
#: model:ir.ui.view,arch_db:account.report_overdue_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Due Date"
msgstr "Vade Tarihi"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Due Date Computation"
msgstr "Vade Tarihi Hesaplaması"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Due Month"
msgstr "Vade Ayı"

#. module: account
#: model:ir.actions.report,name:account.action_report_print_overdue
msgid "Due Payments"
msgstr "Vadesi Gelen Ödeme"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_tree
msgid "Due Type"
msgstr "Vade Tipi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_date_maturity
msgid "Due date"
msgstr "Vade Tarihi"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Duplicate"
msgstr "Kopyala"

#. module: account
#: code:addons/account/models/account_invoice.py:1194
#, python-format
msgid ""
"Duplicated vendor reference detected. You probably encoded twice the same "
"vendor bill/credit note."
msgstr ""
"Çoğaltılmış tedarikçi referansı tespit edildi. Muhtemelen aynı tedarikçiden "
"iki kez iade/fiyat farkını kodladınız."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_reports
msgid "Dynamic Reports"
msgstr "Dinamik Raporlama"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_l10n_eu_service
msgid "EU Digital Goods VAT"
msgstr "AB Dijital Ürünler Katma Değer Vergisi"

#. module: account
#: code:addons/account/models/chart_template.py:171
#: code:addons/account/models/chart_template.py:186
#: code:addons/account/models/chart_template.py:196
#, python-format
msgid "EXCH"
msgstr "KRFRK"

#. module: account
#: code:addons/account/models/account_move.py:1052
#, python-format
msgid "Either pass both debit and credit or none."
msgstr "Ya borç ve alacağı komple geçebilir ya da hiçbirini geçemezsiniz."

#. module: account
#: model:ir.model,name:account.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Eposta yazma sihirbazı"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_enable_filter
msgid "Enable Comparison"
msgstr "Karşılaştırmayı Aç"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "End"
msgstr "Son"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_date_to
#: model:ir.model.fields,field_description:account.field_account_balance_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_account_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_report_date_to
#: model:ir.model.fields,field_description:account.field_account_print_journal_date_to
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_date_to
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_date_to
#: model:ir.model.fields,field_description:account.field_account_tax_report_date_to
#: model:ir.model.fields,field_description:account.field_accounting_report_date_to
#: model:ir.model.fields,field_description:account.field_accounting_report_date_to_cmp
msgid "End Date"
msgstr "Bitiş Tarihi"

#. module: account
#: model:account.payment.term,name:account.account_payment_term
msgid "End of Following Month"
msgstr "Sonraki Ayın Sonu"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_balance_end_real
msgid "Ending Balance"
msgstr "Kapanış Bakiyesi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_end_id
msgid "Ending Cashbox"
msgstr "Kasa Sonu"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Enjoy your Odoo experience,"
msgstr "Odoo deneyiminin keyfini çıkarın,"

#. module: account
#: model:ir.actions.act_window,name:account.action_move_line_form
msgid "Entries"
msgstr "Kayıtlar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_print_journal_sort_selection
msgid "Entries Sorted by"
msgstr "Kayıtları Sıralama"

#. module: account
#: code:addons/account/models/account_move.py:1019
#, python-format
msgid "Entries are not of the same account!"
msgstr "Girişler aynı hesapta değil!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Entries to Review"
msgstr "Gözden Geçirilecek Girdiler"

#. module: account
#: code:addons/account/models/account_analytic_line.py:58
#, python-format
msgid "Entries: "
msgstr "Kayıtlar: "

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "Entry Label"
msgstr "Kayıt Etiketi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_sequence_id
msgid "Entry Sequence"
msgstr "Kayıt Sırası"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_move_line_ids
msgid "Entry lines"
msgstr "Kayıt kalemleri"

#. module: account
#: model:account.account.type,name:account.data_account_type_equity
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Equity"
msgstr "Özsermaye"

#. module: account
#: code:addons/account/models/res_config_settings.py:132
#, python-format
msgid "Error!"
msgstr "Hata!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Excel template"
msgstr "Excel şablonları"

#. module: account
#: code:addons/account/models/chart_template.py:196
#, python-format
msgid "Exchange Difference"
msgstr "Kur Farkı"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_currency_exchange_journal_id
#: model:ir.model.fields,field_description:account.field_res_config_settings_currency_exchange_journal_id
msgid "Exchange Gain or Loss Journal"
msgstr "Kur Kazanç veya Zararı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_exchange_move_id
msgid "Exchange Move"
msgstr "Kur Farkı Yevmiye Kaydı"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Exchange rates can be automatically updated once a day from <strong>Yahoo "
"Finance</strong> or the <strong>European Central Bank</strong>. You can "
"activate this feature in the bottom of the"
msgstr ""
"Döviz kurları, günde bir kez otomatik olarak güncellenebilir. Bu özelliği "
"alt kısmında etkinleştirebilirsiniz."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_expects_chart_of_accounts
msgid "Expects a Chart of Accounts"
msgstr "Hesap Planı Bekliyor"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_expense0
msgid "Expense"
msgstr "Masraf"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_category_property_account_expense_categ_id
#: model:ir.model.fields,field_description:account.field_product_product_property_account_expense_id
#: model:ir.model.fields,field_description:account.field_product_template_property_account_expense_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Expense Account"
msgstr "Gider Hesabı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_expense_id
msgid "Expense Account on Product Template"
msgstr "Ürün Şablonunda Gider Hesabı"

#. module: account
#: model:account.account.type,name:account.data_account_type_expenses
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Expenses"
msgstr "GİDER"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_reference
msgid "External Reference"
msgstr "Harici Referans"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Extra Features"
msgstr "İlave Özellikler"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Favorites"
msgstr "Sık Kullanılanlar"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "February"
msgstr "Şubat"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_state_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_state_ids
msgid "Federal States"
msgstr "İller/Eyaletler"

#. module: account
#: selection:account.journal,bank_statements_source:0
msgid "File Import"
msgstr "Dosya İçe Aktarma"

#. module: account
#: model:ir.ui.view,arch_db:account.cash_box_in_form
msgid "Fill in this form if you put money in the cash register:"
msgstr "Eğer nakit kasaya para eklereniz bu formu doldurun:"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_filter_cmp
msgid "Filter by"
msgstr "Buna göre Süz"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:141
#, python-format
msgid "Filter..."
msgstr "Süzgeç..."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_general_account_id
#: model:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
msgid "Financial Account"
msgstr "Mali Hesap"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_style_overwrite
msgid "Financial Report Style"
msgstr "Mali Rapor Biçimi"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_financial_report_tree
#: model:ir.actions.act_window,name:account.action_account_report
#: model:ir.ui.menu,name:account.menu_account_reports
msgid "Financial Reports"
msgstr "Mali Raporlar"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_fy_data_done
msgid "Financial Year Setup Marked As Done"
msgstr "Finansal Yılı Kurulumu tamamlandı olarak işaretlendi"

#. module: account
#: model:ir.actions.report,name:account.action_report_financial
msgid "Financial report"
msgstr "Mali rapor"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_account_setup_fy_data_done
msgid "Financial year setup marked as done"
msgstr "Finansal Yılı Kurulumu tamamlandı olarak işaretlendi"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "First, register any outstanding customer invoices and vendor bills:"
msgstr ""
"Öncelikle, bekleyen müşteri faturalarını ve tedarikçi faturalarını kaydedin:"

#. module: account
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Fiscal Information"
msgstr "Mali Bilgi"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Fiscal Localization"
msgstr "Mali Koşul Yerelleştirmesi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_position_id
msgid "Fiscal Mapping"
msgstr "Mali Eşleştirme"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Fiscal Periods"
msgstr "Mali Dönemler"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_fiscal_position_template_form
#: model:ir.model,name:account.model_account_fiscal_position
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_position_id
#: model:ir.model.fields,field_description:account.field_account_invoice_fiscal_position_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_fiscal_position_id
#: model:ir.model.fields,field_description:account.field_res_partner_property_account_position_id
#: model:ir.model.fields,field_description:account.field_res_users_property_account_position_id
#: model:ir.ui.view,arch_db:account.view_account_position_form
#: model:ir.ui.view,arch_db:account.view_account_position_template_search
#: model:ir.ui.view,arch_db:account.view_account_position_template_tree
#: model:ir.ui.view,arch_db:account.view_account_position_tree
msgid "Fiscal Position"
msgstr "Mali Koşul"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_name
#: model:ir.ui.view,arch_db:account.view_account_position_template_form
#: model:ir.ui.view,arch_db:account.view_account_position_template_search
msgid "Fiscal Position Template"
msgstr "Mali Koşul Şablonu"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_fiscal_position_form
#: model:ir.ui.menu,name:account.menu_action_account_fiscal_position_form
msgid "Fiscal Positions"
msgstr "Mali Koşullar"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:251
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:17
#, python-format
msgid "Fiscal Year"
msgstr "Mali Yıl"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_fiscalyear_last_day
#: model:ir.model.fields,field_description:account.field_res_company_fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "Mali Yıl Son Gün"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_fiscalyear_last_month
#: model:ir.model.fields,field_description:account.field_res_company_fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "Mali Yıl Son Ay"

#. module: account
#: selection:account.reconcile.model,amount_type:0
#: selection:account.reconcile.model,second_amount_type:0
#: selection:account.reconcile.model.template,amount_type:0
#: selection:account.reconcile.model.template,second_amount_type:0
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Fixed"
msgstr "Sabit"

#. module: account
#: selection:account.payment.term.line,value:0
msgid "Fixed Amount"
msgstr "Sabit Tutar"

#. module: account
#: model:account.account.type,name:account.data_account_type_fixed_assets
msgid "Fixed Assets"
msgstr "Duran Varlıklar"

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_second_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template_second_amount
msgid ""
"Fixed amount will count as a debit if it is negative, as a credit if it is "
"positive."
msgstr ""
"Sabit tutar, negatif bir değer olması halinde bir borç olarak kabul edilir, "
"eğer pozitif bir değer ise bir alacak olarak sayılır."

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_receivables_follow_up
msgid "Follow-up"
msgstr "Takip"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_reports_followup
msgid "Follow-up Levels"
msgstr "Takip Düzeyleri"

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_report_sign
msgid ""
"For accounts that are typically more debited than credited and that you "
"would like to print as negative amounts in your reports, you should reverse "
"the sign of the balance; e.g.: Expense account. The same applies for "
"accounts that are typically more credited than debited and that you would "
"like to print as positive amounts in your reports; e.g.: Income account."
msgstr ""
"Eğer hesaplar tipik olarak borcun alacaktan daha çok olması durumundaysa ve "
"raporlarınızda tutarları eksi değerlerde yazmak istemiyorsanız, bakiyenin "
"işaretini ters çevirmelisiniz; örn.: Gider hesabı. Tıpkı tipik olarak "
"alacağın borçlardan çok olması durumu gibi örn.: Gelir hesabı."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line_value_amount
msgid "For percent enter a ratio between 0-100."
msgstr "Yüzde için 0-1 arasında bir oran girin."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"For the Odoo Team,<br/>\n"
"                            Fabien Pinckaers, Founder"
msgstr ""
"Odoo Ekibi adına,<br/>\n"
"                            Fabien Pinckaers, CEO"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_currency_id
msgid "Forces all moves for this account to have this account currency."
msgstr ""
"Bu hesap için bütün hareketlerin ikincil para biriminde olmasına zorlar."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_currency_id
#: model:ir.model.fields,help:account.field_account_bank_accounts_wizard_currency_id
msgid "Forces all moves for this account to have this secondary currency."
msgstr ""
"Bu hesap için bütün hareketlerin ikincil para biriminde olmasına zorlar."

#. module: account
#: code:addons/account/report/account_aged_partner_balance.py:205
#: code:addons/account/report/account_balance.py:64
#: code:addons/account/report/account_general_ledger.py:114
#: code:addons/account/report/account_journal.py:100
#: code:addons/account/report/account_partner_ledger.py:74
#: code:addons/account/report/account_report_financial.py:149
#: code:addons/account/report/account_tax.py:13
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr "Form içeriği eksik, bu rapor yazdırılamıyor."

#. module: account
#: code:addons/account/models/account_invoice.py:96
#, python-format
msgid "Free Reference"
msgstr "Serbest Referans"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "From Payable accounts"
msgstr "Borç Hesaplarından"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "From Receivable accounts"
msgstr "Alacak Hesaplarından"

#. module: account
#: model:ir.actions.act_window,help:account.action_account_invoice_report_all_supp
msgid ""
"From this report, you can have an overview of the amount invoiced from your "
"vendors. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"Bu rapordan, müşterilerinize kestiğiniz faturaların tutarlarını gözden "
"geçirebilirsiniz. Arama aracı ile Fatura raporlarını da ihtiyaçlarınıza göre"
" kişiselleştirebilirsiniz, ve böylece gereksinimize göre analiz "
"seçebilirsiniz."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_invoice_report_all
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customers. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"Bu rapordan, müşterilerinize kestiğiniz faturaların tutarlarını gözden "
"geçirebilirsiniz. Arama aracı ile Fatura raporlarını da ihtiyaçlarınıza göre"
" kişiselleştirebilirsiniz, ve böylece gereksinimize göre analiz "
"seçebilirsiniz."

#. module: account
#: model:ir.model,name:account.model_account_full_reconcile
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_full_reconcile_id
msgid "Full Reconcile"
msgstr "Tam Uzlaşma"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:113
#, python-format
msgid "Future"
msgstr "Gelecek"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Future Activities"
msgstr "Sonraki Aktiviteler"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "GROSS PROFIT"
msgstr "BRÜT KÂR"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_income_currency_exchange_account_id
#: model:ir.model.fields,field_description:account.field_res_company_income_currency_exchange_account_id
msgid "Gain Exchange Rate Account"
msgstr "Döviz Kuru Farkı Kazanımı Hesabı"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_general_ledger_menu
#: model:ir.actions.report,name:account.action_report_general_ledger
#: model:ir.ui.menu,name:account.menu_general_ledger
msgid "General Ledger"
msgstr "Büyük Defter"

#. module: account
#: model:ir.model,name:account.model_account_report_general_ledger
msgid "General Ledger Report"
msgstr "Büyük Defter"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_generate_entries
msgid "Generate Entries"
msgstr "Kayıtları Oluştur"

#. module: account
#: model:ir.ui.menu,name:account.account_reports_legal_statements_menu
msgid "Generic Statements"
msgstr "US GAAP"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Get started"
msgstr "Başla"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Get warnings when invoicing specific customers"
msgstr "Belirli müşteriler için fatura keserken uyarılar alın"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Get your bank statements automatically imported every 4 hours, or in one-"
"click, using Yodlee and Plaid services. Once installed, set “Bank Feeds” to "
"“Bank Synchronization” in bank account settings. Then, click “Configure” on "
"the online account to enter your bank credentials."
msgstr ""
"Yodlee ve Plaid hizmetlerini kullanarak banka ekstrelerinizi her 4 saatte "
"bir otomatik olarak içe aktarın veya tek tıkla alın. Yüklendikten sonra, "
"banka hesap ayarlarında \"Banka Beslemeleri\" ni \"Banka Eşitlemesi\" olarak"
" ayarlayın. Ardından, banka kimlik bilgilerinizi girmek için çevrimiçi "
"hesaptaki \"Yapılandır\" ı tıklayın."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_sequence
msgid "Gives the sequence of this line when displaying the invoice."
msgstr "Faturayı gösterirken bu satırın sırasını verir."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_sequence
msgid ""
"Gives the sequence order when displaying a list of bank statement lines."
msgstr ""
"Banka hesap özeti kalemleri listelenirken kullanılacak sıralamayı verir."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_tax_sequence
msgid "Gives the sequence order when displaying a list of invoice tax."
msgstr "Fatura vergilerini listelerken sırayı verir."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line_sequence
msgid ""
"Gives the sequence order when displaying a list of payment terms lines."
msgstr ""
"Ödeme koşulları satırlarının bir listesini görüntülerken diziliş sırası "
"verir."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:57
#, python-format
msgid "Go to bank statement(s)"
msgstr "Banka ekstrelerine git"

#. module: account
#: code:addons/account/models/account_invoice.py:642
#, python-format
msgid "Go to the configuration panel"
msgstr "Yapılandırma paneline git"

#. module: account
#: selection:res.partner,trust:0
msgid "Good Debtor"
msgstr "İyi Borçlu"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:46
#, python-format
msgid "Good Job!"
msgstr "Harika!"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_group_id
#: model:ir.model.fields,field_description:account.field_account_account_template_group_id
msgid "Group"
msgstr "Grup"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_search
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#: model:ir.ui.view,arch_db:account.view_account_template_search
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Group By"
msgstr "Grupla"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_group_invoice_lines
msgid "Group Invoice Lines"
msgstr "Fatura Kalemlerini Grupla"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Group of Taxes"
msgstr "Vergi Grupları"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Group received checks before depositing them to the bank"
msgstr "Bankaya vermeden önce, alınan çekleri grupla "

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "HALF-UP"
msgstr "HALF-UP"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_has_accounting_entries
msgid "Has Accounting Entries"
msgstr "Muhasebe kayıtları var"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_has_invoices
msgid "Has Invoices"
msgstr "Faturalar Var"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_has_outstanding
msgid "Has Outstanding"
msgstr "Olağanüstü Oldu"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_has_unreconciled_entries
#: model:ir.model.fields,field_description:account.field_res_users_has_unreconciled_entries
msgid "Has Unreconciled Entries"
msgstr "Eşleşmeyen Girişler Var"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_hide_payment_method
#: model:ir.model.fields,field_description:account.field_account_payment_hide_payment_method
#: model:ir.model.fields,field_description:account.field_account_register_payments_hide_payment_method
msgid "Hide Payment Method"
msgstr "Ödeme Yönetimi Gizle"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_account_hide_setup_bar
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Hide Setup Bar"
msgstr "Kurulum barını gizle"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_hide_tax_exigibility
msgid "Hide Use Cash Basis Option"
msgstr "Gerçekleşme esası kullanım seçeneklerini gizle"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "How do account types affect your reports?"
msgstr "Hesap türleri mali tablolarınızı etkileyeceği için çok önemlidir !"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "How total tax amount is computed in orders and invoices"
msgstr "Siparişlerde ve faturalarda toplam vergi tutarı nasıl hesaplanacak?"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_id
#: model:ir.model.fields,field_description:account.field_account_account_id
#: model:ir.model.fields,field_description:account.field_account_account_tag_id
#: model:ir.model.fields,field_description:account.field_account_account_template_id
#: model:ir.model.fields,field_description:account.field_account_account_type_id
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_id
#: model:ir.model.fields,field_description:account.field_account_balance_report_id
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_id
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_id
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_id
#: model:ir.model.fields,field_description:account.field_account_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_common_account_report_id
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_id
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_id
#: model:ir.model.fields,field_description:account.field_account_common_report_id
#: model:ir.model.fields,field_description:account.field_account_financial_report_id
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_id
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_id
#: model:ir.model.fields,field_description:account.field_account_group_id
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_id
#: model:ir.model.fields,field_description:account.field_account_invoice_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_id
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_id
#: model:ir.model.fields,field_description:account.field_account_journal_id
#: model:ir.model.fields,field_description:account.field_account_move_id
#: model:ir.model.fields,field_description:account.field_account_move_line_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_id
#: model:ir.model.fields,field_description:account.field_account_move_reversal_id
#: model:ir.model.fields,field_description:account.field_account_opening_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_id
#: model:ir.model.fields,field_description:account.field_account_payment_id
#: model:ir.model.fields,field_description:account.field_account_payment_method_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_id
#: model:ir.model.fields,field_description:account.field_account_print_journal_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_id
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_id
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_id
#: model:ir.model.fields,field_description:account.field_account_tax_group_id
#: model:ir.model.fields,field_description:account.field_account_tax_id
#: model:ir.model.fields,field_description:account.field_account_tax_report_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_id
#: model:ir.model.fields,field_description:account.field_account_unreconcile_id
#: model:ir.model.fields,field_description:account.field_accounting_report_id
#: model:ir.model.fields,field_description:account.field_cash_box_in_id
#: model:ir.model.fields,field_description:account.field_cash_box_out_id
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance_id
#: model:ir.model.fields,field_description:account.field_report_account_report_financial_id
#: model:ir.model.fields,field_description:account.field_report_account_report_generalledger_id
#: model:ir.model.fields,field_description:account.field_report_account_report_journal_id
#: model:ir.model.fields,field_description:account.field_report_account_report_overdue_id
#: model:ir.model.fields,field_description:account.field_report_account_report_partnerledger_id
#: model:ir.model.fields,field_description:account.field_report_account_report_tax_id
#: model:ir.model.fields,field_description:account.field_report_account_report_trialbalance_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_id
#: model:ir.model.fields,field_description:account.field_validate_account_move_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_id
msgid "ID"
msgstr "ID"

#. module: account
#: code:addons/account/models/chart_template.py:193
#, python-format
msgid "INV"
msgstr "SAT"

#. module: account
#: code:addons/account/models/account_bank_statement.py:389
#, python-format
msgid "If \"Amount Currency\" is specified, then \"Amount\" must be as well."
msgstr "Eğer \"Tutar Para Birimi\" belirtilirse, o zaman \"Tutar\" da olmalıdır."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_nocreate
msgid ""
"If checked, the new chart of accounts will not contain this by default."
msgstr ""
"Eğer işaretliyse, yeni hesap planı varsayılan olarak bunu içermeyecektir."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_reversal_journal_id
msgid "If empty, uses the journal of the journal entry to be reversed."
msgstr "Boş ise, geri alınacak yevmiye kaydının yevmiye ayarını kullanır."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_include_base_amount
#: model:ir.model.fields,help:account.field_account_tax_template_include_base_amount
msgid ""
"If set, taxes which are computed after this one will be computed based on "
"the price tax included."
msgstr ""
"Belirlenirse, bunun ardından hesaplanan vergiler, vergi dahil fiyat baz "
"alınarak hesaplanır."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_analytic
#: model:ir.model.fields,help:account.field_account_tax_template_analytic
msgid ""
"If set, the amount computed by this tax will be assigned to the same "
"analytic account as the invoice line (if any)"
msgstr ""
"Belirlenirse, bu vergi ile hesaplanan tutar fatura satırı ile aynı analitik "
"hesaba (eğer varsa) atanır."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_active
msgid ""
"If the active field is set to False, it will allow you to hide the payment "
"terms without removing it."
msgstr ""
"Etkin alanı 'Yanlış' olarak ayarlanırsa, ödeme koşullarını kaldırmadan "
"gizleyebilirsiniz."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_group_invoice_lines
msgid ""
"If this box is checked, the system will try to group the accounting lines "
"when generating them from invoices."
msgstr ""
"Bu kutu işaretlenirse, faturalardan muhasebe kalemleri oluşturulurken onları"
" gruplandırmaya çalışacaktır."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you check this box, you will be able to collect payments using SEPA "
"Direct Debit mandates."
msgstr ""
"Eğer bu kutuyu işaretlerseniz, ödemelerinizi SEPA Direct Debit'e talimat "
"vererek alabilirsiniz"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you check this box, you will be able to register your payment using SEPA."
msgstr ""
"Bu kutuyu işaretlerseniz, ödemenizi SEPA kullanarak kayıt edebileceksiniz."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"If you have less than 200 contacts, we recommend you\n"
"                                        create them manually."
msgstr ""
"200 kişiden daha az kişiniz varsa bunları manuel olarak oluşturmanızı \\ n "
"öneririz."

#. module: account
#: model:ir.model.fields,help:account.field_account_report_general_ledger_initial_balance
msgid ""
"If you selected date, this field allow you to add a row to display the "
"amount of debit/credit/balance that precedes the filter you've set."
msgstr ""
"Tarih seçtiyseniz, bu alan belirlediğiniz filtrenin öncesinde borç / alacak "
"/ bakiye miktarını görüntülemek için bir satır eklemenize izin verir."

#. module: account
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
msgid ""
"If you unreconcile transactions, you must also verify all the actions that "
"are linked to those transactions because they will not be disabled"
msgstr ""
"Bir hareketin uzlaşmasını kaldırdığınızda, o işleme bağlı eylemler devre "
"dışı bırakılmayacağı için o işleme bağlı bütün eylemleri de kontrol "
"etmelisiniz."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_payment_term_id
msgid ""
"If you use payment terms, the due date will be computed automatically at the"
" generation of accounting entries. If you keep the payment terms and the due"
" date empty, it means direct payment. The payment terms may compute several "
"due dates, for example 50% now, 50% in one month."
msgstr ""
"Ödeme koşullarını kullanırsanız, ödeme tarihi itibariyle hesaplama tarihi "
"girişi otomatik olarak hesaplanır. Ödeme koşullarını ve vadesini boş "
"tutarsanız, doğrudan ödeme anlamına gelir. Ödeme şartları çeşitli son "
"tarihleri ​​hesaplayabilir, örneğin %50'si Peşin,%50'si 30 Gün vadeli."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_date_due
msgid ""
"If you use payment terms, the due date will be computed automatically at the"
" generation of accounting entries. The Payment terms may compute several due"
" dates, for example 50% now and 50% in one month, but if you want to force a"
" due date, make sure that the payment term is not set on the invoice. If you"
" keep the Payment terms and the due date empty, it means direct payment."
msgstr ""
"Ödeme koşullarını kullanırsanız, ödeme tarihi itibariyle hesaplama tarihi "
"girişi otomatik olarak hesaplanır. Ödeme şartları birkaç son teslim tarihini"
" hesaplayabilir, örneğin %50'si Peşin,%50'si 30 Gün vadeli. Ancak bir vade "
"tarihine zorlamak istiyorsanız, faturada ödeme koşulunun seçilmediğinden "
"emin olun. Ödeme koşulları ve vade tarihi boş ise bu doğrudan peşin ödeme "
"anlamına gelir."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"If you want to be able to send customer statements from Odoo, you must:"
msgstr ""
"Odoo'dan müşteri hesap özeti bildirimleri gönderebilmek istiyorsanız, "
"şunları yapmanız gerekir:"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"If you want to be able to send your customers their statements \n"
"                        from Odoo, you first need to record all outstanding transactions \n"
"                        in the payable and receivable accounts. These would be invoices \n"
"                        that have not been paid or payments that have not been reconciled."
msgstr ""
"Müşterilerinize hesap özeti bildirimlerini Odoo'dan gönderebilmek istiyorsanız, önce tüm ödenmemiş işlemleri borç ve alacak hesaplarına kaydetmeniz gerekir.\n"
"Bunlar ödenmemiş faturalar ya da uzlaştırılmamış ödemeler olacaktır."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "If you want to do it yourself:"
msgstr "Eğer bunu kendiniz yapmak isterseniz:"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you're selling digital goods to customers in the EU, you must charge VAT "
"based on your customers' locations. This rule applies regardless of you are "
"located. Digital goods are defined in the legislation as broadcasting, "
"telecommunications, and services that are electronically supplied instead of"
" shipped. Gift cards sent online are not included in the definition."
msgstr ""
"AB'deki müşterilere dijital ürünler satıyorsanız, KDV'yi müşterilerinizin "
"bulunduğu yere göre belirlemelisiniz. Bu kural bulunduğunuzdan bağımsız "
"olarak geçerlidir. Dijital ürünler mevzuatta yayınlanan, telekomünikasyon ve"
" elektronik olarak gönderilen hizmetler olarak tanımlanmaktadır. Çevrimiçi "
"olarak gönderilen hediye kartları tanıma dahil değildir."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_product_image
msgid ""
"Image of the product variant (Big-sized image of product template if false)."
" It is automatically resized as a 1024x1024px image, with aspect ratio "
"preserved."
msgstr ""
"Varyant ürün resmi (Ürün şablonunun büyük boyutlu resmi). En boy oranı "
"korunarak otomatik olarak 1024x1024px resim olarak ölçeklendirilir."

#. module: account
#: model:account.payment.term,name:account.account_payment_term_immediate
msgid "Immediate Payment"
msgstr "Peşin Ödeme"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_qif
msgid "Import .qif files"
msgstr ".qif biçiminde içe aktar"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_csv
msgid "Import in .csv format"
msgstr ".csv biçiminde içe aktar"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_ofx
msgid "Import in .ofx format"
msgstr ".ofx biçiminde içe aktar"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_camt
msgid "Import in CAMT.053 format"
msgstr "CAMT.053 formatında içe aktarım"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Import using the \"Import\" button on the top left corner of"
msgstr "İçeri veri aktarmak için sol üstteki \"İçe Aktar\" butonunna basınız."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements automatically"
msgstr "Banka hesap özetlerini otomatik olarak içe aktarın"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in CAMT.053"
msgstr "Banka Hareketlerinizi CAMT.053 formatında içe aktarın"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in CSV"
msgstr "Banka Hareketlerinizi CSV formatında içe aktarın"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in OFX"
msgstr "Banka Hareketlerinizi QFX formatında içe aktarın"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in QIF"
msgstr "Banka Hareketlerinizi QIF formatında içe aktarın"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Importing your statements in via a supported file format (QIF, OFX, CODA or "
"CSV format)"
msgstr ""
"Banka hareketlerinizin desteklenen dosya formatlarında (QIF, OFX, CODA, "
"MT940, CSV) içe aktarımı"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "In Odoo,"
msgstr "Odoo'da"

#. module: account
#: code:addons/account/models/account_bank_statement.py:409
#, python-format
msgid ""
"In order to delete a bank statement line, you must first cancel it to delete"
" related journal items."
msgstr ""
"Bir banka hesap özeti kalemine ait muhasebe yevmiye kaydını silebilmek için "
"öncelikle banka hesap özeti kalemini iptal etmelisiniz."

#. module: account
#: code:addons/account/models/account_bank_statement.py:199
#, python-format
msgid ""
"In order to delete a bank statement, you must first cancel it to delete "
"related journal items."
msgstr ""
"Bir banka hesap özetine ait muhasebe yevmiye kaydını silebilmek için "
"öncelikle banka hesap özeti kalemini iptal etmelisiniz."

#. module: account
#: code:addons/account/models/account_payment.py:144
#, python-format
msgid ""
"In order to pay multiple invoices at once, they must use the same currency."
msgstr ""
"Çoklu fatura ödemek isterseniz, faturalarınızın aynı para biriminden olması "
"gerekmektedir"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "In your old accounting software, print a trial balance"
msgstr "Eski muhasebe yazılımınızda bir deneme bakiyesi yazdırın"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Inactive"
msgstr "Pasif"

#. module: account
#: selection:account.payment.method,payment_type:0
msgid "Inbound"
msgstr "Gelen"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_initial_balance
msgid "Include Initial Balances"
msgstr "Başlangıç Bakiyelerini İçer"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_analytic
msgid "Include in Analytic Cost"
msgstr "Analitik Maliyetlere Ekle"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_price_include
#: model:ir.model.fields,field_description:account.field_account_tax_template_price_include
msgid "Included in Price"
msgstr "Fiyata Dahil Edilir"

#. module: account
#: model:account.account.type,name:account.data_account_type_revenue
#: model:account.financial.report,name:account.account_financial_report_income0
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Income"
msgstr "Gelir"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_category_property_account_income_categ_id
#: model:ir.model.fields,field_description:account.field_product_product_property_account_income_id
#: model:ir.model.fields,field_description:account.field_product_template_property_account_income_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Income Account"
msgstr "Gelir Hesabı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_income_id
msgid "Income Account on Product Template"
msgstr "Ürün Şablonundaki Gelir Hesabı"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:700
#, python-format
msgid "Incorrect Operation"
msgstr "Geçersiz İşlem"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Information"
msgstr "Bilgi"

#. module: account
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
msgid "Information addendum"
msgstr "Ek bilgi"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:296
#: code:addons/account/models/company.py:311
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:9
#, python-format
msgid "Initial Balances"
msgstr "Başlangıç Bakiyesi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_stock_account_input_categ_id
#: model:ir.model.fields,field_description:account.field_res_company_property_stock_account_input_categ_id
msgid "Input Account for Stock Valuation"
msgstr "Stok Envanter Değeri için hesaba ekle"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Install Chart of Account"
msgstr "Hesap planını indir"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Install More Packages"
msgstr "Daha Fazla Paket Yükleyin"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_transfer_account_id
msgid "Inter-Banks Transfer Account"
msgstr "Inter-Bankalar Transfer Hesabı"

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_transfer_account_id
#: model:ir.model.fields,help:account.field_res_company_transfer_account_id
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_transfer_account_id
msgid ""
"Intermediary account used when moving money from a liquidity account to "
"another"
msgstr ""
"Bir likidite hesabından diğerine para aktarma işlemi sırasında kullanılan "
"aracı hesap"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_narration
msgid "Internal Note"
msgstr "İç Not"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_note
msgid "Internal Notes"
msgstr "İç Notlar"

#. module: account
#: selection:account.payment,payment_type:0
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Internal Transfer"
msgstr "İç Transfer"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_internal_type
msgid "Internal Type"
msgstr "İç Tip"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_template_form
msgid "Internal notes..."
msgstr "İç notlar..."

#. module: account
#: code:addons/account/models/partner.py:45
#, python-format
msgid "Invalid \"Zip Range\", please configure it properly."
msgstr "Zip kodunuz yanlış, lütfen doğru bir zip kodu giriniz"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Invite Users"
msgstr "Kullanıcıları Davet Et"

#. module: account
#: code:addons/account/models/account_invoice.py:1214
#: model:ir.model,name:account.model_account_invoice
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_invoice_id
#: model:ir.model.fields,field_description:account.field_account_move_line_invoice_id
#: model:ir.model.fields,field_description:account.field_res_partner_invoice_warn
#: model:ir.model.fields,field_description:account.field_res_users_invoice_warn
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:res.request.link,name:account.req_link_invoice
#, python-format
msgid "Invoice"
msgstr "Fatura"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid "Invoice #"
msgstr "Fatura #"

#. module: account
#: code:addons/account/models/account_invoice.py:440
#, python-format
msgid "Invoice - %s"
msgstr "Fatura - %s"

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_created
#: model:mail.message.subtype,name:account.mt_invoice_created
msgid "Invoice Created"
msgstr "Fatura Oluşturuldu"

#. module: account
#: code:addons/account/controllers/portal.py:70
#: model:ir.model.fields,field_description:account.field_account_invoice_date_invoice
#: model:ir.ui.view,arch_db:account.portal_my_invoices
#: model:ir.ui.view,arch_db:account.report_payment_receipt
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Invoice Date"
msgstr "Fatura Tarihi"

#. module: account
#: model:ir.model,name:account.model_account_invoice_line
#: model:ir.ui.view,arch_db:account.view_invoice_line_form
#: model:ir.ui.view,arch_db:account.view_invoice_line_tree
msgid "Invoice Line"
msgstr "Fatura Satırı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_invoice_line_ids
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Invoice Lines"
msgstr "Fatura Satırları"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Invoice Number"
msgstr "Fatura Numarası"

#. module: account
#: sql_constraint:account.invoice:0
msgid "Invoice Number must be unique per Company!"
msgstr "Fatura numarası her şirket için benzersiz olmalıdır!"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Invoice Number:"
msgstr "Fatura Numarası :"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_invoice_id
msgid "Invoice Reference"
msgstr "Fatura Referans"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_state
msgid "Invoice Status"
msgstr "Fatura Durumu"

#. module: account
#: model:ir.model,name:account.model_account_invoice_tax
msgid "Invoice Tax"
msgstr "Fatura Vergisi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_invoice_id
msgid "Invoice for which this invoice is the credit note"
msgstr "İade/fiyat farkı faturası"

#. module: account
#: code:addons/account/models/account_invoice.py:753
#, python-format
msgid "Invoice must be cancelled in order to reset it to draft."
msgstr "Taslak duruma geri dönmek için önce fatura iptal edilmelidir."

#. module: account
#: code:addons/account/models/account_invoice.py:801
#, python-format
msgid "Invoice must be in draft or open state in order to be cancelled."
msgstr "İptal etmek için fatura taslak veya açık durumda olmalıdır."

#. module: account
#: code:addons/account/models/account_invoice.py:775
#, python-format
msgid "Invoice must be in draft state in order to validate it."
msgstr "Onaylamak için fatura taslak durumunda olmalıdır."

#. module: account
#: code:addons/account/models/account_invoice.py:795
#, python-format
msgid "Invoice must be paid in order to set it to register payment."
msgstr "Ödeme kaydetmek için fatura ödenmiş olmalıdır."

#. module: account
#: code:addons/account/models/account_invoice.py:787
#, python-format
msgid "Invoice must be validated in order to set it to register payment."
msgstr "Ödeme kaydetmek için faturanın onaylı olması gerekir."

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_paid
msgid "Invoice paid"
msgstr "Fatura ödendi"

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_validated
msgid "Invoice validated"
msgstr "Fatura doğrulandı"

#. module: account
#: model:mail.template,report_name:account.email_template_edi_invoice
msgid ""
"Invoice_${(object.number or '').replace('/','_')}_${object.state == 'draft' "
"and 'draft' or ''}"
msgstr ""
"Invoice_${(object.number or '').replace('/','_')}_${object.state == 'draft' "
"and 'draft' or ''}"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Invoiced"
msgstr "Faturalandı"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_refund_out_tree
#: model:ir.actions.act_window,name:account.action_invoice_tree
#: model:ir.actions.report,name:account.account_invoices
#: model:ir.model.fields,field_description:account.field_account_payment_invoice_ids
#: model:ir.model.fields,field_description:account.field_account_register_payments_invoice_ids
#: model:ir.model.fields,field_description:account.field_res_partner_invoice_ids
#: model:ir.model.fields,field_description:account.field_res_users_invoice_ids
#: model:ir.ui.menu,name:account.menu_action_account_invoice_report_all
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.portal_my_home_invoice
#: model:ir.ui.view,arch_db:account.portal_my_home_menu_invoice
#: model:ir.ui.view,arch_db:account.portal_my_invoices
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_account_payment_form
#: model:ir.ui.view,arch_db:account.view_account_payment_graph
#: model:ir.ui.view,arch_db:account.view_invoice_graph
#: model:ir.ui.view,arch_db:account.view_invoice_line_calendar
#: model:ir.ui.view,arch_db:account.view_invoice_pivot
msgid "Invoices"
msgstr "Faturalar"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_report_all
#: model:ir.actions.act_window,name:account.action_account_invoice_report_all_supp
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_graph
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_pivot
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Invoices Analysis"
msgstr "Faturaların Analizi"

#. module: account
#: model:ir.model,name:account.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Fatura İsatistikleri"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:186
#, python-format
msgid "Invoices owed to you"
msgstr "Fatura borçları"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Invoices to validate"
msgstr "Doğrulanacak faturalar"

#. module: account
#: model:ir.actions.report,name:account.account_invoices_without_payment
msgid "Invoices without Payment"
msgstr "Ödeme yapılmayan faturalar"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance
#: model:ir.ui.view,arch_db:account.product_template_form_view
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Invoicing"
msgstr "Faturalama"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_is_unaffected_earnings_line
msgid "Is Unaffected Earnings Line"
msgstr "Beklenmeyen Kazançlar Kalemi"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_is_rounding_line
msgid "Is a rounding line in case of cash rounding."
msgstr "Nakit yuvarlaması olduğu takdirde yuvarlama türü."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_is_difference_zero
msgid "Is zero"
msgstr "Sıfır"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_default_credit_account_id
#: model:ir.model.fields,help:account.field_res_company_income_currency_exchange_account_id
msgid "It acts as a default account for credit amount"
msgstr "Alacak tutarı için varsayılan hesap olarak davranır"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_default_debit_account_id
#: model:ir.model.fields,help:account.field_res_company_expense_currency_exchange_account_id
msgid "It acts as a default account for debit amount"
msgstr "Borç tutarı için varsayılan hesap olarak davranır"

#. module: account
#: model:ir.model.fields,help:account.field_account_report_partner_ledger_amount_currency
msgid ""
"It adds the currency column on report if the currency differs from the "
"company currency."
msgstr ""
"Rapordaki para birimi şirket para biriminden farklıysa rapora döviz kolonu "
"ekler."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_reconciled
msgid ""
"It indicates that the invoice has been paid and the journal entry of the "
"invoice has been reconciled with one or several journal entries of payment."
msgstr ""
"Faturanın ödenmiş olduğunu ve faturanın yevmiye kaydının bir ya da daha "
"fazla ödeme ile uzlaştırıldığını gösterir."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_sent
msgid "It indicates that the invoice has been sent."
msgstr "Faturanın gönderildiğini gösterir."

#. module: account
#: code:addons/account/models/account_move.py:1050
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr ""
"Bir mahsup yaratmak için bir hesap ve bir yevmiye belirtmek zorunludur."

#. module: account
#: code:addons/account/models/account_payment.py:470
#, python-format
msgid ""
"It is not allowed to delete a payment that already created a journal entry "
"since it would create a gap in the numbering. You should create the journal "
"entry again and cancel it thanks to a regular revert."
msgstr ""
"Numaralandırma boşluğu yaratacağı için zaten bir yevmiye girişi oluşturan "
"bir ödemeyi silmesine izin verilmez. Yevmiye girişini tekrar oluşturmalı ve "
"düzenli geri dönüş sayesinde iptal etmeliyiz."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"It's common practice to change your accounting software \n"
"                            at the end of a fiscal year. This allows you to have less \n"
"                            data to import and balances to set. If you plan to do so, \n"
"                            we recommend you start using Odoo for invoicing and payments \n"
"                            now, and then move all other accounting transactions at a later time."
msgstr ""
"Muhasebe yazılımınızı bir mali yılın sonunda değiştirmeniz yaygın bir uygulamadır.\n"
"Bu, içe aktarılması gereken daha az veri ve ayarların yapılmasını sağlar.\n"
"Bunu yapmayı planlıyorsanız şimdi Odoo'yu faturalandırma ve ödemeler için kullanmaya başlamanızı,\n"
"Diğer tüm muhasebe işlemlerini daha sonra taşımanızı öneririz."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"It's recommended that you do not delete any accounts, even if they are not "
"relevant. Simply make them inactive."
msgstr ""
"Alakalı olmasalar bile herhangi bir hesabı silmemenizi öneririz. Sadece "
"onları pasif hale getirin."

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Italic Text (smaller)"
msgstr "Yatık Metin (daha küçük)"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Items"
msgstr "Öğeler"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
msgid "JRNL"
msgstr "JRNL"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "January"
msgstr "Ocak"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:185
#: code:addons/account/static/src/xml/account_reconciliation.xml:229
#: model:ir.model,name:account.model_account_journal
#: model:ir.model.fields,field_description:account.field_account_bank_statement_journal_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_journal_id
#: model:ir.model.fields,field_description:account.field_account_invoice_journal_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_journal_id
#: model:ir.model.fields,field_description:account.field_account_move_journal_id
#: model:ir.model.fields,field_description:account.field_account_move_line_journal_id
#: model:ir.model.fields,field_description:account.field_account_opening_journal_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_journal_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_journal_id
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_journal_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Journal"
msgstr "Yevmiye"

#. module: account
#: selection:account.report.general.ledger,sortby:0
msgid "Journal & Partner"
msgstr "Yevmiye & İş Ortağı"

#. module: account
#: code:addons/account/models/account_bank_statement.py:254
#: model:ir.actions.act_window,name:account.action_move_journal_line
#: model:ir.actions.act_window,name:account.action_move_select
#: model:ir.ui.menu,name:account.menu_action_move_journal_line_form
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#: model:ir.ui.view,arch_db:account.view_move_tree
#, python-format
msgid "Journal Entries"
msgstr "Yevmiye Kayıtları"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Journal Entries by Month"
msgstr "Aylık Yevmiye Kayıtları"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_move_id
#: model:ir.model.fields,field_description:account.field_account_move_line_move_id
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Journal Entry"
msgstr "Yevmiye Kaydı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_move_name
#: model:ir.model.fields,field_description:account.field_account_invoice_move_name
#: model:ir.model.fields,field_description:account.field_account_payment_move_name
msgid "Journal Entry Name"
msgstr "Yevmiye Kayıt Adı"

#. module: account
#: selection:account.print.journal,sort_selection:0
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Journal Entry Number"
msgstr "Yevmiye Kayıt Numarası"

#. module: account
#: model:ir.model,name:account.model_account_move_line
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Journal Item"
msgstr "Yevmiye Kalemleri"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_writeoff_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_label
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Journal Item Label"
msgstr "Yevmiye Öğesi Etiketi"

#. module: account
#: code:addons/account/models/account_payment.py:414
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_move_line
#: model:ir.actions.act_window,name:account.act_account_move_to_account_move_line_open
#: model:ir.actions.act_window,name:account.action_account_moves_all_a
#: model:ir.actions.act_window,name:account.action_account_moves_all_tree
#: model:ir.actions.act_window,name:account.action_move_line_graph
#: model:ir.actions.act_window,name:account.action_move_line_graph_cash_basis
#: model:ir.actions.act_window,name:account.action_move_line_graph_cash_basis_posted
#: model:ir.actions.act_window,name:account.action_move_line_graph_posted
#: model:ir.actions.act_window,name:account.action_move_line_select
#: model:ir.actions.act_window,name:account.action_move_line_select_by_partner
#: model:ir.actions.act_window,name:account.action_move_line_select_by_type
#: model:ir.actions.act_window,name:account.action_move_line_select_tax_audit
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_journal_entry_ids
#: model:ir.model.fields,field_description:account.field_account_move_line_ids
#: model:ir.model.fields,field_description:account.field_res_partner_journal_item_count
#: model:ir.model.fields,field_description:account.field_res_users_journal_item_count
#: model:ir.ui.menu,name:account.menu_action_account_moves_all
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.setup_opening_move_lines_tree
#: model:ir.ui.view,arch_db:account.view_account_payment_form
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_pivot
#: model:ir.ui.view,arch_db:account.view_move_line_tree
#, python-format
msgid "Journal Items"
msgstr "Yevmiye Kalemleri"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_action.js:323
#: model:ir.actions.client,name:account.action_manual_reconcile
#, python-format
msgid "Journal Items to Reconcile"
msgstr "Uzlaştırılacak Yevmiye Kalemleri"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_name
msgid "Journal Name"
msgstr "Yevmiye Adı"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "Journal and Partner"
msgstr "Yevmiye ve İş Ortağı"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Journal invoices with period in current year"
msgstr "Geçerli yıldaki döneme ait fatura günlükleri"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Journal items where matching number isn't set"
msgstr "Eşleşen numaranın ayarlanmadığı yevmiye kalemleri"

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_opening_journal_id
msgid ""
"Journal where the opening entry of this company's accounting has been "
"posted."
msgstr "Şirket muhasebesinde açılan yevmiye gönderilmiştir."

#. module: account
#: model:ir.actions.act_window,name:account.action_account_journal_form
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_journal_ids
#: model:ir.model.fields,field_description:account.field_account_balance_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_account_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_print_journal_journal_ids
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_journal_ids
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_journal_ids
#: model:ir.model.fields,field_description:account.field_account_tax_report_journal_ids
#: model:ir.model.fields,field_description:account.field_accounting_report_journal_ids
#: model:ir.ui.menu,name:account.menu_action_account_journal_form
msgid "Journals"
msgstr "Yevmiyeler"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_print_journal_menu
#: model:ir.actions.report,name:account.action_report_journal
#: model:ir.ui.menu,name:account.menu_print_journal
msgid "Journals Audit"
msgstr "Yevmiye Denetimi"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "July"
msgstr "Temmuz"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "June"
msgstr "Haziran"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_reason
msgid "Justification"
msgstr "Gerekçe"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_kanban_dashboard
msgid "Kanban Dashboard"
msgstr "Kanban Paneli"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "Kanban Panel Grafiği"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Keep empty for no control"
msgstr "Kontrol yapmayacaksanız boş tutun"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_date_invoice
msgid "Keep empty to use the current date"
msgstr "Geçerli tarihi kullanmak için boş bırakın"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_date
msgid "Keep empty to use the invoice date."
msgstr "Fatura tarihini kullanmak için boş bırakın"

#. module: account
#: selection:account.payment,payment_difference_handling:0
msgid "Keep open"
msgstr "Açık bırakın"

#. module: account
#: model:ir.model.fields,help:account.field_product_product_property_account_income_id
#: model:ir.model.fields,help:account.field_product_template_property_account_income_id
msgid ""
"Keep this field empty to use the default value from the product category."
msgstr ""
"Ürün kategorisinde varsayılan değerleri kullanmak için bu alanı boş bırakın."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:522
#: code:addons/account/static/src/xml/account_reconciliation.xml:189
#: code:addons/account/static/src/xml/account_reconciliation.xml:230
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_name
#: model:ir.model.fields,field_description:account.field_account_move_line_name
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#, python-format
msgid "Label"
msgstr "Etiket"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_description
msgid "Label on Invoices"
msgstr "Faturalarda Etiket"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment___last_update
#: model:ir.model.fields,field_description:account.field_account_account___last_update
#: model:ir.model.fields,field_description:account.field_account_account_tag___last_update
#: model:ir.model.fields,field_description:account.field_account_account_template___last_update
#: model:ir.model.fields,field_description:account.field_account_account_type___last_update
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance___last_update
#: model:ir.model.fields,field_description:account.field_account_balance_report___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line___last_update
#: model:ir.model.fields,field_description:account.field_account_cash_rounding___last_update
#: model:ir.model.fields,field_description:account.field_account_cashbox_line___last_update
#: model:ir.model.fields,field_description:account.field_account_chart_template___last_update
#: model:ir.model.fields,field_description:account.field_account_common_account_report___last_update
#: model:ir.model.fields,field_description:account.field_account_common_journal_report___last_update
#: model:ir.model.fields,field_description:account.field_account_common_partner_report___last_update
#: model:ir.model.fields,field_description:account.field_account_common_report___last_update
#: model:ir.model.fields,field_description:account.field_account_financial_report___last_update
#: model:ir.model.fields,field_description:account.field_account_financial_year_op___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template___last_update
#: model:ir.model.fields,field_description:account.field_account_full_reconcile___last_update
#: model:ir.model.fields,field_description:account.field_account_group___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_line___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_refund___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_report___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_tax___last_update
#: model:ir.model.fields,field_description:account.field_account_journal___last_update
#: model:ir.model.fields,field_description:account.field_account_move___last_update
#: model:ir.model.fields,field_description:account.field_account_move_line___last_update
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile___last_update
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff___last_update
#: model:ir.model.fields,field_description:account.field_account_move_reversal___last_update
#: model:ir.model.fields,field_description:account.field_account_opening___last_update
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile___last_update
#: model:ir.model.fields,field_description:account.field_account_payment___last_update
#: model:ir.model.fields,field_description:account.field_account_payment_method___last_update
#: model:ir.model.fields,field_description:account.field_account_payment_term___last_update
#: model:ir.model.fields,field_description:account.field_account_payment_term_line___last_update
#: model:ir.model.fields,field_description:account.field_account_print_journal___last_update
#: model:ir.model.fields,field_description:account.field_account_reconcile_model___last_update
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template___last_update
#: model:ir.model.fields,field_description:account.field_account_register_payments___last_update
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger___last_update
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger___last_update
#: model:ir.model.fields,field_description:account.field_account_tax___last_update
#: model:ir.model.fields,field_description:account.field_account_tax_group___last_update
#: model:ir.model.fields,field_description:account.field_account_tax_report___last_update
#: model:ir.model.fields,field_description:account.field_account_tax_template___last_update
#: model:ir.model.fields,field_description:account.field_account_unreconcile___last_update
#: model:ir.model.fields,field_description:account.field_accounting_report___last_update
#: model:ir.model.fields,field_description:account.field_cash_box_in___last_update
#: model:ir.model.fields,field_description:account.field_cash_box_out___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_financial___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_generalledger___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_journal___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_overdue___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_partnerledger___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_tax___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_trialbalance___last_update
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard___last_update
#: model:ir.model.fields,field_description:account.field_validate_account_move___last_update
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts___last_update
msgid "Last Modified on"
msgstr "Son Güncelleme"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Last Month"
msgstr "Geçen Ay"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:119
#, python-format
msgid "Last Reconciliation:"
msgstr "Son uzlaştırma"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_write_uid
#: model:ir.model.fields,field_description:account.field_account_account_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_account_type_write_uid
#: model:ir.model.fields,field_description:account.field_account_account_write_uid
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_write_uid
#: model:ir.model.fields,field_description:account.field_account_balance_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_write_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_write_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_account_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_financial_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_write_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_write_uid
#: model:ir.model.fields,field_description:account.field_account_group_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_write_uid
#: model:ir.model.fields,field_description:account.field_account_journal_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_write_uid
#: model:ir.model.fields,field_description:account.field_account_opening_write_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_write_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal_write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_write_uid
#: model:ir.model.fields,field_description:account.field_account_register_payments_write_uid
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_write_uid
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_write_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile_write_uid
#: model:ir.model.fields,field_description:account.field_accounting_report_write_uid
#: model:ir.model.fields,field_description:account.field_cash_box_in_write_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out_write_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_write_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move_write_uid
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_write_date
#: model:ir.model.fields,field_description:account.field_account_account_template_write_date
#: model:ir.model.fields,field_description:account.field_account_account_type_write_date
#: model:ir.model.fields,field_description:account.field_account_account_write_date
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_write_date
#: model:ir.model.fields,field_description:account.field_account_balance_report_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_write_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_write_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_write_date
#: model:ir.model.fields,field_description:account.field_account_chart_template_write_date
#: model:ir.model.fields,field_description:account.field_account_common_account_report_write_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_write_date
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_write_date
#: model:ir.model.fields,field_description:account.field_account_common_report_write_date
#: model:ir.model.fields,field_description:account.field_account_financial_report_write_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_write_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_write_date
#: model:ir.model.fields,field_description:account.field_account_group_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_line_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_write_date
#: model:ir.model.fields,field_description:account.field_account_journal_write_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_write_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_write_date
#: model:ir.model.fields,field_description:account.field_account_move_line_write_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal_write_date
#: model:ir.model.fields,field_description:account.field_account_move_write_date
#: model:ir.model.fields,field_description:account.field_account_opening_write_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_method_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_write_date
#: model:ir.model.fields,field_description:account.field_account_print_journal_write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_write_date
#: model:ir.model.fields,field_description:account.field_account_register_payments_write_date
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_write_date
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_group_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_report_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_template_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_write_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile_write_date
#: model:ir.model.fields,field_description:account.field_accounting_report_write_date
#: model:ir.model.fields,field_description:account.field_cash_box_in_write_date
#: model:ir.model.fields,field_description:account.field_cash_box_out_write_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_write_date
#: model:ir.model.fields,field_description:account.field_validate_account_move_write_date
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Last day of current month"
msgstr "Bu ayın son günü"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Last day of following month"
msgstr "Sonraki ayın son günü"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_last_time_entries_checked
#: model:ir.model.fields,help:account.field_res_users_last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed for this partner. "
"It is set either if there's not at least an unreconciled debit and an "
"unreconciled credit or if you click the \"Done\" button."
msgstr ""
"Bu iş ortağı için son kez eşleşen fatura ve ödemeler. En azından bir "
"uzlaşılamayan borç ve alacak yoksa veya \"Bitti\" düğmesini tıklarsanız "
"ayarlanır."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed on this account. It"
" is set either if there's not at least an unreconciled debit and an "
"unreconciled credit Or if you click the \"Done\" button."
msgstr ""
"Bu iş ortağı için son kez eşleşen fatura ve ödemeler. En azından bir "
"uzlaşılamayan borç ve alacak yoksa veya \"Bitti\" düğmesini tıklarsanız "
"ayarlanır."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Late Activities"
msgstr "Gecikmiş Aktiviteler"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_last_time_entries_checked
#: model:ir.model.fields,field_description:account.field_res_partner_last_time_entries_checked
#: model:ir.model.fields,field_description:account.field_res_users_last_time_entries_checked
msgid "Latest Invoices & Payments Matching Date"
msgstr "Son Faturalar ve Ödemeler Eşleştirme Tarihi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group_parent_left
msgid "Left Parent"
msgstr "Sol Üst"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Legal Name"
msgstr "Yasal Ad"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Legal Notes..."
msgstr "Yasal notlar..."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_note
msgid "Legal mentions that have to be printed on the invoices."
msgstr "Faturalarda basılması gereken yasal bildirimler."

#. module: account
#: code:addons/account/models/account_invoice.py:216
#, python-format
msgid "Less Payment"
msgstr "Daha Kısa Ödeme"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:24
#, python-format
msgid "Let odoo try to reconcile entries for the user"
msgstr "Odoo kullanıcı girdilerini mutabık kılmaya çalışsın"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Let your customers pay their invoices online"
msgstr "Müşterilerinizin faturalarını online ödemesine izin verin."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_level
msgid "Level"
msgstr "Seviye"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_liability0
#: model:account.financial.report,name:account.account_financial_report_liabilitysum0
msgid "Liability"
msgstr "Borç"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_move_id
msgid "Link to the automatically generated Journal Items."
msgstr "Otomatik oluşturulan Yevmiye Kalemlerine bağla."

#. module: account
#: selection:account.account.type,type:0
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Liquidity"
msgstr "Likidite"

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_tax_template_ids
msgid "List of all the taxes that have to be installed by the wizard"
msgstr "Sihirbaz tarafından kurulmuş tüm vergileri listele"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Litigation"
msgstr "İhtilaf"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:29
#, python-format
msgid "Load more"
msgstr "Daha fazla yükle"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_fiscalyear_lock_date
msgid "Lock Date"
msgstr "Tarihi Kilitle"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "Müşavir Hariç Kilit Tarihi"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Logo"
msgstr "Logo"

#. module: account
#: code:addons/account/models/account_bank_statement.py:173
#, python-format
msgid "Loss"
msgstr "Zarar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_loss_account_id
msgid "Loss Account"
msgstr "Zarar Hesabı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_expense_currency_exchange_account_id
#: model:ir.model.fields,field_description:account.field_res_company_expense_currency_exchange_account_id
msgid "Loss Exchange Rate Account"
msgstr "Döviz Kuru Oranı Kaybı Hesabı"

#. module: account
#: code:addons/account/models/chart_template.py:195
#, python-format
msgid "MISC"
msgstr "CSTL"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Mail your invoices in one-click using"
msgstr "Docsaway kullanarak tek tıklamayla faturalarınızı postayla gönderin"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Main Currency"
msgstr "Ana Para Birimi"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Main Title 1 (bold, underlined)"
msgstr "Ana Başlık 1 (koyu, altçizgili)"

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_currency_id
msgid "Main currency of the company."
msgstr "Şirketinizin ana para birimi"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Main currency of your company"
msgstr "Şirketinizin ana para birimi"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Manage time &amp; material contracts or fixed-price recurring subscriptions."
msgstr ""
"Zamanı yönetin &amp; Malzeme sözleşmeleri veya sabit fiyatlı tekrar eden "
"abonelikler."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Manage your employee expenses, from encoding, to payments and reporting."
msgstr ""
"Personel masraflarınızdan masraf listeleri oluşturup yönetici onayları alın "
"ve geri ödemelerini yönetin."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Manage your various fixed assets, such as buildings, machinery, materials, "
"cars, etc..., and calculate their associated depreciation over time."
msgstr ""
"Binalar, makineler, malzemeler, otomobiller gibi çeşitli sabit "
"varlıklarınızı yönetin ve amortismanlarını otomatik olarak hesaplayın ve "
"muhasebeleştirin."

#. module: account
#: model:ir.ui.menu,name:account.account_management_menu
#: model:ir.ui.menu,name:account.account_reports_management_menu
#: model:ir.ui.menu,name:account.menu_finance_entries_management
msgid "Management"
msgstr "Finansal Yönetim"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Managing bank fees"
msgstr "Banka masraflarını yönetme"

#. module: account
#: model:account.payment.method,name:account.account_payment_method_manual_in
#: model:account.payment.method,name:account.account_payment_method_manual_out
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_manual
msgid "Manual"
msgstr "Manuel"

#. module: account
#: model:ir.ui.view,arch_db:account.view_invoice_tax_form
#: model:ir.ui.view,arch_db:account.view_invoice_tax_tree
msgid "Manual Invoice Taxes"
msgstr "Elle Vergi Girilen Fatura"

#. module: account
#: model:ir.actions.client,name:account.action_manual_reconciliation
#: model:ir.ui.menu,name:account.menu_action_manual_reconciliation
msgid "Manual Reconciliation"
msgstr "Manuel Uzlaştırma"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_inbound_payment_method_ids
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo,you are suggested to reconcile the transaction with the batch deposit. Enable this option from the settings."
msgstr ""
"Manuel: Odoo dışında nakit, çek ya da bir başka bir yöntemle ödemelerinizi alın.\n"
"Elektronik: Müşteri online olarak alış-veriş yaptığında ya da  abone olduğunda, müşteri tarafından kaydedilen bir kart üzerinde işlem talep ederek ödemelerinizi otomatik olarak alın (ödeme token'ı).\n"
"Toplu Kayıt: Bankanıza göndermek için bir toplu ödeme oluşturarak bir kerede birkaç müşteri çeki imzalayın. Odoo'da  banka hesabınızı kodladığınızda, toplu çek çıkış işlemi için uzlaştırma önerilir. Bu seçeneğe ayarlar menüsünden ulaşılabilir."

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_payment_method_id
#: model:ir.model.fields,help:account.field_account_payment_payment_method_id
#: model:ir.model.fields,help:account.field_account_register_payments_payment_method_id
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Check: Pay bill by check and print it from Odoo.\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo, you are suggested to reconcile the transaction with the batch deposit.To enable batch deposit,module account_batch_deposit must be installed.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. To enable sepa credit transfer, module account_sepa must be installed "
msgstr ""
"Manuel:  Odoo dışında nakit, çek ya da bir başka bir yöntemle ödemelerinizi yapın.\n"
"Electronic: Müşteri online olarak alış-veriş yaptığında ya da abone olduğunda, müşteri tarafından kaydedilen bir kart üzerinde işlem talep ederek ödemelerinizi otomatik olarak alın (ödeme token'ı).\n"
"Batch Deposit:  Bankanıza göndermek için bir toplu ödeme oluşturarak bir kerede birkaç müşteri çeki imzalayın. Odoo'da banka hesabınızı kodladığınızda, toplu çek çıkış işlemi için uzlaştırma önerilir.  Toplu çek çıkış ayarına ulaşmak için account_batch_deposit modülünün yüklenmiş olması gerekmektedir.\n"
"SEPA Credit Transfer: Bankanıza gönderdiğiniz bir SEPA Credit Transfer dosyası ile ödeme yapın. SEPA credit transfere ulaşmak için account_sepa modülünün yüklenmiş olması gerekmektedir."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_outbound_payment_method_ids
msgid ""
"Manual:Pay bill by cash or any other method outside of Odoo.\n"
"Check:Pay bill by check and print it from Odoo.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. Enable this option from the settings."
msgstr ""
"Manuel: Odoo dışında fatura ödemelerini nakit ya da başka bir yöntemle yap.\n"
"Çek: Çek ile fatura ödemesini yap ve Odoo'dan çıktısını al.\n"
"SEPA Credit Transfer: Bankanıza gönderdiğiniz bir  SEPA Credit Transfer dosyası ile ödemenizi yapın. Bu seçeneğe ayarlar menüsünden ulaşılabilir."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Manually enter your transactions using our"
msgstr "İşlemlerimizi manuel girelim."

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "March"
msgstr "Mart"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Margin Analysis"
msgstr "Marj Analizi"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model:ir.ui.view,arch_db:account.setup_view_company_form
msgid "Mark as done"
msgstr "Yapıldı olarak işaretle"

#. module: account
#: selection:account.payment,payment_difference_handling:0
msgid "Mark invoice as fully paid"
msgstr "Fatura tamamen ödenmiş olarak işaretle"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_payables_master_data
#: model:ir.ui.menu,name:account.menu_finance_receivables_master_data
msgid "Master Data"
msgstr "Ana Veri"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_matched_credit_ids
msgid "Matched Credit"
msgstr "Eşleştirilmiş Alacak"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_matched_debit_ids
msgid "Matched Debit"
msgstr "Eşleştirilmiş Borç"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_reconciled_line_ids
#: model:ir.ui.view,arch_db:account.view_full_reconcile_form
msgid "Matched Journal Items"
msgstr "Eşleşen Yevmiye Öğeleri"

#. module: account
#: model:ir.ui.view,arch_db:account.view_full_reconcile_form
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Matching"
msgstr "Eşleştirme"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_full_reconcile_id
msgid "Matching Number"
msgstr "Eşleşen Numara"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_max_date
msgid "Max Date of Matched Lines"
msgstr "Eşleşen Kalemlerin Maksimum Tarihi"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "May"
msgstr "Mayıs"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_communication
#: model:ir.model.fields,field_description:account.field_account_payment_communication
#: model:ir.model.fields,field_description:account.field_account_register_payments_communication
msgid "Memo"
msgstr "Kısa Not"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:64
#, python-format
msgid "Memo:"
msgstr "Kısa Not:"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_invoice_warn_msg
#: model:ir.model.fields,field_description:account.field_res_users_invoice_warn_msg
msgid "Message for Invoice"
msgstr "Fatura Mesajı"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Cost of Revenue"
msgstr "-Gelir Maliyeti"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Credit Card Accounts"
msgstr "-Kredi Kartı Hesapları"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Current Liabilities"
msgstr "-Kısa Vadeli Borçlar"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Expenses"
msgstr "(-) Giderler"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Non-Current Liabilities"
msgstr "(-) Uzun Vadeli Borçlar"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Payable Accounts"
msgstr "Eksi Borç Hesapları"

#. module: account
#: selection:account.journal,type:0
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Miscellaneous"
msgstr "Diğer"

#. module: account
#: code:addons/account/models/chart_template.py:195
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#, python-format
msgid "Miscellaneous Operations"
msgstr "Diğer İşlemler"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:164
#, python-format
msgid "Modify models"
msgstr "Modelleri değiştir"

#. module: account
#: selection:account.cash.rounding,strategy:0
msgid "Modify tax amount"
msgstr "Vergi tutarı değiştirme"

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Modify: create credit note, reconcile and create a new draft invoice"
msgstr "Değiştir: İade/fiyat farkı ve yeni taslak fatura oluştur."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Monitor your product margins from invoices"
msgstr "Ürün marjlarını faturalardan hesaplayın"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_invoice_partner_relation
msgid "Monthly Turnover"
msgstr "Aylık Ciro"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Most currencies are already created by default. If you plan\n"
"                        to use some of them, you should check their <strong>Active</strong>\n"
"                        field."
msgstr ""
"Çoğu para birimi zaten varsayılan olarak oluşturulmuştur.\n"
"Bazılarını kullanmayı planlıyorsanız, 'Etkin' alanını kontrol etmeniz gerekir."

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Move"
msgstr "Hareket"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_move_id
#: model:ir.model.fields,field_description:account.field_account_payment_move_line_ids
msgid "Move Line"
msgstr "Hareket Kalemi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_move_line_count
msgid "Move Line Count"
msgstr "Hareket Kalemi Hesabı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_move_reconciled
msgid "Move Reconciled"
msgstr "Uzlaştırılmış Kayıt"

#. module: account
#: code:addons/account/models/account_move.py:1362
#, python-format
msgid "Move name (id): %s (%s)"
msgstr "Hareket adı (id): %s (%s)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_register_payments_multi
msgid "Multi"
msgstr "Çoklu"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Multi Currency"
msgstr "Çoklu Para Birimli"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Multi-Currencies"
msgstr "Çoklu Para Birimleri"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "My Activities"
msgstr "Aktivitelerim"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "My Invoices"
msgstr "Faturalarım"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "NET ASSETS"
msgstr "NET VARLIKLAR"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "NET PROFIT"
msgstr "NET KAR"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_name
#: model:ir.model.fields,field_description:account.field_account_account_tag_name
#: model:ir.model.fields,field_description:account.field_account_account_template_name
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_name
#: model:ir.model.fields,field_description:account.field_account_chart_template_name
#: model:ir.model.fields,field_description:account.field_account_group_name
#: model:ir.model.fields,field_description:account.field_account_payment_method_name
#: model:ir.model.fields,field_description:account.field_account_payment_name
#: model:ir.model.fields,field_description:account.field_account_tax_group_name
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Name"
msgstr "İsim"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:52
#, python-format
msgid "Name:"
msgstr "Adı:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_narration
msgid "Narration"
msgstr "Öyküleme"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Navigate easily through reports and see what is behind the numbers"
msgstr ""
"Raporlar arasında kolayca dolaşın ve rakamların arkasında neler olduğunu "
"görün"

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "Net"
msgstr "Net"

#. module: account
#: selection:account.bank.statement,state:0
msgid "New"
msgstr "Yeni"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "New Statement"
msgstr "Yeni Ekstre"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "New Transactions"
msgstr "Yeni İşlem"

#. module: account
#: code:addons/account/models/account_move.py:1339
#, python-format
msgid "New expected payment date: "
msgstr "Beklenen yeni ödeme günü"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_sequence_number_next
#: model:ir.model.fields,field_description:account.field_account_invoice_sequence_number_next_prefix
#: model:ir.model.fields,field_description:account.field_account_journal_sequence_number_next
msgid "Next Number"
msgstr "Sonraki Sayı"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Next, register any unmatched payments:<br/>"
msgstr "Ardından, eşleşmeyen ödemeleri kaydettirin: <br/>"

#. module: account
#: selection:accounting.report,filter_cmp:0
msgid "No Filters"
msgstr "Süzgeç Yok"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_blocked
msgid "No Follow-up"
msgstr "Takip Yok"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "No Message"
msgstr "Uyarı Yok"

#. module: account
#: selection:account.financial.report,display_detail:0
msgid "No detail"
msgstr "Ayrıntı yok"

#. module: account
#: code:addons/account/models/account.py:116
#, python-format
msgid "No opening move defined !"
msgstr "Açılış hareketi tanımlanmadı !"

#. module: account
#: model:ir.model.fields,help:account.field_res_company_fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""
"Müşavirler de dahil, hiçbir kullanıcı bu tarihten önce ve bu tarih dahil "
"hesapları düzenleyemeyecek. Örneğin, mali yıl kilitleme için kullanın."

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_code_digits
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_code_digits
msgid "No. of Digits to use for account code"
msgstr "Hesap kodu için kullanılacak basamak sayısı"

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_code_digits
msgid "No. of digits to use for account code"
msgstr "Hesap kodu için kullanılacak basamak sayısı"

#. module: account
#: model:account.account.type,name:account.data_account_type_non_current_assets
msgid "Non-current Assets"
msgstr "Duran Varlıklar"

#. module: account
#: model:account.account.type,name:account.data_account_type_non_current_liabilities
msgid "Non-current Liabilities"
msgstr "Uzun Vadeli Borçlar"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
msgid "None"
msgstr "Hiçbiri"

#. module: account
#: selection:res.partner,trust:0
msgid "Normal Debtor"
msgstr "Normal Borçlu"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Normal Text"
msgstr "Normal Metin"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:254
#: model:ir.model.fields,field_description:account.field_account_account_template_note
#, python-format
msgid "Note"
msgstr "Bilgi"

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_out_refund
msgid ""
"Note that the easiest way to create a credit note is to do it directly form\n"
"                the customer invoice, to refund it totally or partially."
msgstr ""
"Bir İade/Fiyat Farkı oluşturmanın en kolay yolunun, müşteri faturası "
"oluşturarak bunun tamamen veya kısmen iadesi olduğuna dikkat edin."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_note
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_note
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_note
#: model:ir.ui.view,arch_db:account.view_account_template_form
msgid "Notes"
msgstr "Notlar"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Nothing to Reconcile"
msgstr "Uzlaştırma Yok"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:34
#, python-format
msgid "Nothing to do!"
msgstr "Yapacak bir şey yok"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "November"
msgstr "Kasım"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_name
#: model:ir.model.fields,field_description:account.field_account_invoice_number
#: model:ir.model.fields,field_description:account.field_account_move_name
msgid "Number"
msgstr "Numara"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Number (Move)"
msgstr "Numara (Hareket)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_number
msgid "Number of Coins/Bills"
msgstr "Madeni Para / Bono Adedi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_days
msgid "Number of Days"
msgstr "Günlerin Sayısı"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_accounts_code_digits
msgid "Number of digits in an account code"
msgstr "Hesap kodundaki basamak sayısı"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "OFX Import"
msgstr "OFX İçe Aktarımı"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:17
#, python-format
msgid "OK"
msgstr "Tamam"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "October"
msgstr "Ekim"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Odoo Accounting has many extra-features:"
msgstr "Odoo Muhasebe birçok ekstra özelliğe sahiptir:"

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_line
msgid ""
"Odoo allows you to reconcile a statement line directly with\n"
"                the related sale or puchase invoice(s)."
msgstr ""
"Odoo, bir hesap özeti satırı ile ilişkili alım veya satış faturası ile "
"uzlaştırma yapmanızı sağlar."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_tree
msgid ""
"Odoo allows you to reconcile a statement line directly with\n"
"                the related sale or puchase invoices."
msgstr ""
"Odoo, bir hesap özeti satırı ile ilişkili alım veya satış faturası ile "
"uzlaştırma yapmanızı sağlar."

#. module: account
#: model:ir.actions.act_window,help:account.action_move_journal_line
msgid ""
"Odoo automatically creates one journal entry per accounting\n"
"                document: invoice, refund, vendor payment, bank statements,\n"
"                etc. So, you should record journal entries manually only/mainly\n"
"                for miscellaneous operations."
msgstr ""
"Odoo şu muhasebe işlemlerin tamamı için bir tam entegre bir yevmiye girişini (muhasebe fişi) otomatik oluşturur;\n"
"Fatura, İade, Tedarikçi Ödemeleri, Banka Hareketleri vb..\n"
"Dolayısıyla, yevmiye girişlerini elle yapıyorsanız bunlar sadece otomatik olmayan diğer işlemlerle ilgili olmalıdır."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Odoo can manage multiple companies, but we suggest to setup everything for "
"your first company before configuring the other ones."
msgstr ""
"Odoo'da birden fazla grup şirketinizi yönetebilirsiniz, ancak diğer grup "
"şirketleri yapılandırmadan önce ilk şirketiniz için her şeyi konfigüre "
"etmeniz ve ayarları tam yapmış olmanız önerilir."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Odoo has already preconfigured a few taxes according to your "
"country.<br/>Simply review them and check if you need more."
msgstr ""
"Odoo'da ülkenize uygun bazı vergi hesapları kurulum sırasında ayarlanmıştır."
" Bunları gözden geçirip daha fazlasına ihtiyacınız varsa oluşturabilirsiniz."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Odoo should do most of the reconciliation work automatically, so you'll only"
" need to review a few of them when a <strong>'Reconcile Items'</strong> "
"button appears on your Vendor Bills dash."
msgstr ""
"Odoo, uzlaştırma işlemlerinin çoğunu otomatik olarak yapar\n"
"Bu nedenle Tedarikçi Faturaları panelinde <strong>'Kayıtları Uzlaştır'</strong> butonuna basarak gözden geçirmeniz gereken kayıtları görebilirsiniz."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree1
msgid ""
"Odoo's electronic invoicing allows to ease and fasten the\n"
"                collection of customer payments. Your customer receives the\n"
"                invoice by email and he can pay online and/or import it\n"
"                in his own system."
msgstr ""
"Odoo'nun elektronik faturalaması müşteri ödemelerini de kolaylaştırır.\n"
"Müşterileriniz faturayı e-posta ile alır ve online olarak ödeme yapabilir."

#. module: account
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid ""
"Once draft invoices are confirmed, you will not be able\n"
"                        to modify them. The invoices will receive a unique\n"
"                        number and journal items will be created in your chart\n"
"                        of accounts."
msgstr ""
"Taslak faturalar onaylandığında artık faturaları değiştiremeyeceksiniz.\n"
"                        Faturalar özgün numara alacak ve yevmiye kalemleri\n"
"                        hesap planınızda oluşturulacaktır."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Once installed, set 'Bank Feeds' to 'File Import' in bank account "
"settings.This adds a button to import from the Accounting dashboard."
msgstr ""
"Yüklendikten sonra, banka hesabı ayarlarında Banka Hareketlerini \"Dosya İçe Aktar\" olarak ayarlayın.\n"
"Böylece, Muhasebe panelinden banka hareketlerinin içe aktarılması için bir düğme eklenir."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Once you have created your chart of accounts, you will need to update your "
"account balances."
msgstr ""
"Hesap planınızı oluşturduktan sonra hesapların açılış bakiyelerini "
"güncellemeniz gerekecek."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Once your bank accounts are registered, you will be able \n"
"                        to access your statements from the Accounting Dashboard. \n"
"                        The available methods for synchronization are as follows."
msgstr ""
"Banka hesaplarınızı tanımladıktan sonra muhasebe panelinden banka hareketlerine erişebilirsiniz.\n"
"Mevcut senkronizasyon yöntemleri aşağıdaki gibidir."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Once your company information is correct, you should"
msgstr "Şirket bilgileriniz doğru olduğunda,"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "One bank statement for each bank account you hold."
msgstr "Tuttuğunuz her banka hesabı için bir banka hesap özeti."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_payment
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Online Payment"
msgstr "Online Ödeme"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_only_one_chart_template
msgid "Only One Chart Template Available"
msgstr "Yalnızca Bir Hesap Şablonu Mevcuttur"

#. module: account
#: code:addons/account/models/account_payment.py:484
#, python-format
msgid "Only a draft payment can be posted."
msgstr "Sadece bir taslak ödeme postalanabilir."

#. module: account
#: code:addons/account/models/chart_template.py:866
#, python-format
msgid "Only administrators can change the settings"
msgstr "Yalnızca yöneticiler bu ayarları değiştirebilir"

#. module: account
#: model:ir.model.fields,help:account.field_res_company_period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""
"Yalnızca 'Müşavir' rolüne sahip kullanıcılar bu tarihten önce ve bu tarih "
"dahil hesapları düzenleyebilir. Örneğin, açık bir mali yıl içinde dönem "
"kilitlemesi için kullanın."

#. module: account
#. openerp-web
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: code:addons/account/static/src/xml/account_payment.xml:82
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Open"
msgstr "Açık"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:664
#: code:addons/account/static/src/xml/account_reconciliation.xml:131
#, python-format
msgid "Open balance"
msgstr "Bakiyeyi göster"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_opening_date
#: model:ir.model.fields,field_description:account.field_account_opening_date
#: model:ir.model.fields,field_description:account.field_res_company_account_opening_date
msgid "Opening Date"
msgstr "Açılış Tarihi"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_opening_journal_id
msgid "Opening Journal"
msgstr "Devir Yevmiyesi"

#. module: account
#: code:addons/account/models/company.py:339
#: model:ir.model.fields,field_description:account.field_account_opening_opening_move_id
#: model:ir.model.fields,field_description:account.field_res_company_account_opening_move_id
#, python-format
msgid "Opening Journal Entry"
msgstr "Açılış Yevmiye Kaydı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_opening_opening_move_line_ids
msgid "Opening Journal Items"
msgstr "Devir Yevmiye Kayıtları"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_opening_move_posted
msgid "Opening Move Posted"
msgstr "Devir Hareketi İşlendi"

#. module: account
#: model:ir.model.fields,help:account.field_account_cashbox_line_number
msgid "Opening Unit Numbers"
msgstr "Açılış Birim Numaraları"

#. module: account
#: code:addons/account/models/account.py:138
#, python-format
msgid "Opening balance"
msgstr "Devir Bakiyesi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_opening_credit
msgid "Opening credit"
msgstr "Devir Alacak"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_opening_credit
msgid "Opening credit value for this account."
msgstr "Bu hesap için devir alacak değeri."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_opening_debit
msgid "Opening debit"
msgstr "Devir Borç"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_opening_debit
msgid "Opening debit value for this account."
msgstr "Bu hesap için devir borç değeri."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Operation Templates"
msgstr "Operasyon Şablonları"

#. module: account
#: code:addons/account/models/account_bank_statement.py:1014
#, python-format
msgid ""
"Operation not allowed. Since your statement line already received a number, "
"you cannot reconcile it entirely with existing journal entries otherwise it "
"would make a gap in the numbering. You should book an entry and make a "
"regular revert of it in case you want to cancel it."
msgstr ""
"Operasyona izin verilmiyor. İfade satırınıza bir numara zaten ulaştığından "
"bunu mevcut yevmiye girişleriyle tamamen eşleştiremezsiniz, aksi takdirde "
"numaralandırmada bir boşluk oluşur. İptal etmek isterseniz bir giriş kitabı "
"hazırlamalı ve düzenli olarak iade etmeliyiz."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_nocreate
msgid "Optional Create"
msgstr "İsteğe Bağlı Oluştur"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag_ids
#: model:ir.model.fields,help:account.field_account_account_template_tag_ids
#: model:ir.model.fields,help:account.field_account_tax_tag_ids
#: model:ir.model.fields,help:account.field_account_tax_template_tag_ids
msgid "Optional tags you may want to assign for custom reporting"
msgstr "Özel raporlama için atamak isteyebileceğiniz isteğe bağlı etiketler"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_option
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Options"
msgstr "Seçenekler"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Original Amount"
msgstr "İlk Tutar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_payment_id
msgid "Originator Payment"
msgstr "Üretici Ödemesi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_line_id
msgid "Originator tax"
msgstr "Orijinatör Vergisi"

#. module: account
#: model:account.account.type,name:account.data_account_type_other_income
msgid "Other Income"
msgstr "Diğer Gelirler"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Other Info"
msgstr "Diğer Bilgiler"

#. module: account
#: selection:account.payment.method,payment_type:0
msgid "Outbound"
msgstr "Giden"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_stock_account_output_categ_id
#: model:ir.model.fields,field_description:account.field_res_company_property_stock_account_output_categ_id
msgid "Output Account for Stock Valuation"
msgstr "Stok Değerlemesi İçin Çıkış Hesabı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_outstanding_credits_debits_widget
msgid "Outstanding Credits Debits Widget"
msgstr "Açık Borç Alacak Widget'ı"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Outstanding Transactions"
msgstr "Açık İşlemler"

#. module: account
#: code:addons/account/models/account_invoice.py:131
#, python-format
msgid "Outstanding credits"
msgstr "Tahsil Edilmeyen Alacaklar"

#. module: account
#: code:addons/account/models/account_invoice.py:134
#, python-format
msgid "Outstanding debits"
msgstr "Ödenmeyen Borçlar"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Overdue"
msgstr "Geciken"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_overdue_msg
msgid "Overdue Payments Message"
msgstr "Geciken Bakiye Hatırlatma Mesajı"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Overdue invoices, maturity date passed"
msgstr "Gecikmiş faturalar, günü geçmiş"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_legal_statement
msgid "PDF Reports"
msgstr "PDF Çıktıları"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Package"
msgstr "Paket"

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: model:ir.ui.view,arch_db:account.report_overdue_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:mail.message.subtype,name:account.mt_invoice_paid
msgid "Paid"
msgstr "Ödendi"

#. module: account
#: code:addons/account/models/account_payment.py:430
#, python-format
msgid "Paid Invoices"
msgstr "Ödenmiş Faturalar"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:28
#: model:ir.ui.view,arch_db:account.report_invoice_document_with_payments
#, python-format
msgid "Paid on"
msgstr "Ödendi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_reconciled
msgid "Paid/Reconciled"
msgstr "Ödendi/Uzlaştırıldı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_parent_id
#: model:ir.model.fields,field_description:account.field_account_group_parent_id
msgid "Parent"
msgstr "Üst"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_parent_id
msgid "Parent Chart Template"
msgstr "Üst Hesap Planı Şablonu"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
msgid "Parent Report"
msgstr "Üst Rapor"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_parent_state
msgid "Parent State"
msgstr "Üst Durum"

#. module: account
#: model:ir.model,name:account.model_account_partial_reconcile
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Partial Reconcile"
msgstr "Kısmi Uzlaştır"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:232
#: code:addons/account/static/src/xml/account_reconciliation.xml:249
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_partner_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_partner_id
#: model:ir.model.fields,field_description:account.field_account_move_line_partner_id
#: model:ir.model.fields,field_description:account.field_account_move_partner_id
#: model:ir.model.fields,field_description:account.field_account_payment_partner_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_partner_id
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#, python-format
msgid "Partner"
msgstr "İş Ortağı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_commercial_partner_id
msgid "Partner Company"
msgstr "İş Ortağı Şirketi"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_partner_ledger_menu
#: model:ir.actions.report,name:account.action_report_partnerledger
#: model:ir.ui.menu,name:account.menu_partner_ledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
msgid "Partner Ledger"
msgstr "İş Ortağı Defteri"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_partner_name
msgid "Partner Name"
msgstr "İş Ortağı Adı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_partner_type
#: model:ir.model.fields,field_description:account.field_account_payment_partner_type
#: model:ir.model.fields,field_description:account.field_account_register_payments_partner_type
msgid "Partner Type"
msgstr "İş Ortağı Türü"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_result_selection
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_result_selection
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_result_selection
msgid "Partner's"
msgstr "İş Ortağı'nın"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Partners"
msgstr "İş Ortakları"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:106
#, python-format
msgid "Past"
msgstr "Geçmiş"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Pay your bills in one-click using Euro SEPA service"
msgstr "Ödemelerinizi bir tıkla Euro SEPA servisi ile yapın."

#. module: account
#: model:account.account.type,name:account.data_account_type_payable
#: selection:account.account.type,type:0
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Payable"
msgstr "Borç"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_payable_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Payable Account"
msgstr "Borç Hesabı"

#. module: account
#: selection:account.aged.trial.balance,result_selection:0
#: selection:account.common.partner.report,result_selection:0
#: selection:account.report.partner.ledger,result_selection:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.view_account_search
msgid "Payable Accounts"
msgstr "Borç Hesapları"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_debit_limit
#: model:ir.model.fields,field_description:account.field_res_users_debit_limit
msgid "Payable Limit"
msgstr "Borç Limiti"

#. module: account
#: model:ir.ui.view,arch_db:account.product_template_form_view
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Payables"
msgstr "Borçlar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_amount
#: model:ir.model.fields,field_description:account.field_account_payment_amount
#: model:ir.model.fields,field_description:account.field_account_register_payments_amount
msgid "Payment Amount"
msgstr "Ödeme Miktarı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_date
#: model:ir.model.fields,field_description:account.field_account_payment_payment_date
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_date
msgid "Payment Date"
msgstr "Ödeme Tarihi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_payment_difference
#: model:ir.model.fields,field_description:account.field_account_payment_payment_difference_handling
msgid "Payment Difference"
msgstr "Ödeme Farkı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_journal_id
#: model:ir.model.fields,field_description:account.field_account_payment_journal_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_journal_id
msgid "Payment Journal"
msgstr "Ödeme Yevmiyesi"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Payment Matching"
msgstr "Ödeme Eşleme"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Payment Method"
msgstr "Ödeme Yöntemi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_method_id
#: model:ir.model.fields,field_description:account.field_account_payment_payment_method_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_method_id
msgid "Payment Method Type"
msgstr "Ödeme Yöntem Türü"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:60
#, python-format
msgid "Payment Method:"
msgstr "Ödeme Yöntemi:"

#. module: account
#: model:ir.model,name:account.model_account_payment_method
#: model:ir.model.fields,field_description:account.field_account_journal_outbound_payment_method_ids
msgid "Payment Methods"
msgstr "Ödeme Yöntemleri"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_payment_move_line_ids
msgid "Payment Move Lines"
msgstr "Ödeme Hareket Satırları"

#. module: account
#: model:ir.actions.report,name:account.action_report_payment_receipt
msgid "Payment Receipt"
msgstr "Ödeme Makbuzu"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Payment Receipt:"
msgstr "Ödeme Makbuzu:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_reference_type
#: model:ir.model.fields,field_description:account.field_account_payment_payment_reference
msgid "Payment Reference"
msgstr "Ödeme Referansı"

#. module: account
#: model:ir.actions.act_window,name:account.action_payment_term_form
#: model:ir.model,name:account.model_account_payment_term
#: model:ir.model.fields,field_description:account.field_account_invoice_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_payment_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_name
#: model:ir.ui.menu,name:account.menu_action_payment_term_form
#: model:ir.ui.view,arch_db:account.account_planner
#: model:ir.ui.view,arch_db:account.view_payment_term_form
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
#: model:ir.ui.view,arch_db:account.view_payment_term_line_tree
#: model:ir.ui.view,arch_db:account.view_payment_term_search
#: model:ir.ui.view,arch_db:account.view_payment_term_tree
msgid "Payment Terms"
msgstr "Ödeme Koşulları"

#. module: account
#: model:ir.model,name:account.model_account_payment_term_line
msgid "Payment Terms Line"
msgstr "Ödeme Koşulu Satırları"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_type
#: model:ir.model.fields,field_description:account.field_account_payment_method_payment_type
#: model:ir.model.fields,field_description:account.field_account_payment_payment_type
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_type
msgid "Payment Type"
msgstr "Ödeme Türü"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_form
msgid "Payment terms explanation for the customer..."
msgstr "Müşteri için ödeme koşulu açıklaması ..."

#. module: account
#: model:account.payment.term,note:account.account_payment_term_15days
msgid "Payment terms: 15 Days"
msgstr "Ödeme Koşulu: 15 Gün"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_net
msgid "Payment terms: 30 Net Days"
msgstr "Ödeme Koşulu: 30 Net Gün"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_advance
msgid "Payment terms: 30% Advance End of Following Month"
msgstr "Ödeme şartları: Takip eden ayın sonu %30 avans."

#. module: account
#: model:account.payment.term,note:account.account_payment_term
msgid "Payment terms: End of Following Month"
msgstr "Ödeme koşulları: Takip eden ayın sonu"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_immediate
msgid "Payment terms: Immediate Payment"
msgstr "Ödeme Koşulu: Anında Ödeme"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_payment_id
msgid "Payment that created this entry"
msgstr "Bu kayıtta oluşturulan ödeme"

#. module: account
#: code:addons/account/models/account_payment.py:229
#: model:ir.actions.act_window,name:account.action_account_payments
#: model:ir.actions.act_window,name:account.action_account_payments_payable
#: model:ir.model,name:account.model_account_payment
#: model:ir.model.fields,field_description:account.field_account_invoice_payment_ids
#: model:ir.ui.menu,name:account.menu_action_account_payments_payable
#: model:ir.ui.menu,name:account.menu_action_account_payments_receivable
#: model:ir.ui.view,arch_db:account.partner_view_buttons
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#, python-format
msgid "Payments"
msgstr "Ödemeler"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "Ödemeleri Eşleştirme"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_payments_widget
msgid "Payments Widget"
msgstr "Ödemeler Widget"

#. module: account
#: model:ir.actions.act_window,help:account.action_account_payments
#: model:ir.actions.act_window,help:account.action_account_payments_payable
msgid ""
"Payments are used to register liquidity movements (send, collect or transfer money).\n"
"                  You can then process those payments by your own means or by using installed facilities."
msgstr ""
"Ödemeler likidite hareketlerini kaydetmek (para göndermek, toplamak veya "
"aktarmak) için kullanılır. \\ N Sonra bu ödemeleri kendi imkanlarıyla veya "
"kurulu tesisler kullanarak işleyebilirsiniz."

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Payments to do"
msgstr "Yapılacak ödemeler"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_tree_pending_invoice
msgid "Pending Invoice"
msgstr "Bekleyen Fatura"

#. module: account
#: selection:account.payment.term.line,value:0
msgid "Percent"
msgstr "Yüzde"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_matched_percentage
msgid "Percentage Matched"
msgstr "Eşleşen Yüzde"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Percentage of Price"
msgstr "Fiyat Yüzdesi"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Percentage of Price Tax Included"
msgstr "Fiyat Dahil Vergi Yüzdesi"

#. module: account
#: selection:account.reconcile.model,second_amount_type:0
#: selection:account.reconcile.model.template,second_amount_type:0
msgid "Percentage of amount"
msgstr "Tutar yüzdesi"

#. module: account
#: selection:account.reconcile.model,amount_type:0
#: selection:account.reconcile.model.template,amount_type:0
msgid "Percentage of balance"
msgstr "Bakiye Yüzdesi"

#. module: account
#: code:addons/account/models/account_invoice.py:1776
#, python-format
msgid "Percentages for Payment Terms Line must be between 0 and 100."
msgstr ""
"Ödeme Koşulları Satırı yüzde (%) tanımı, 0 ile 100 arasında bir değer "
"olmalıdır."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Period"
msgstr "Dönem"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_period_length
msgid "Period Length (days)"
msgstr "Dönem Süresi (gün)"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_plaid
msgid "Plaid Connector"
msgstr "Örgülü Bağlayıcı"

#. module: account
#: model:ir.model,name:account.model_web_planner
msgid "Planner"
msgstr "Planlayıcı"

#. module: account
#: code:addons/account/wizard/pos_box.py:27
#, python-format
msgid "Please check that the field 'Journal' is set on the Bank Statement"
msgstr "Banka Hesap Özetinde 'Yevmiye' alanının ayarlandığından emin olun"

#. module: account
#: code:addons/account/wizard/pos_box.py:29
#, python-format
msgid "Please check that the field 'Transfer Account' is set on the company."
msgstr ""
"Lütfen 'Transfer Hesabı' alanının şirket ayarlarında tanımlandığına emin "
"olun."

#. module: account
#: code:addons/account/models/account_invoice.py:1070
#, python-format
msgid "Please create some invoice lines."
msgstr "Lütfen birkaç fatura satırı oluşturun."

#. module: account
#: code:addons/account/models/account_move.py:157
#, python-format
msgid "Please define a sequence for the credit notes"
msgstr "İade/Fiyat Farkları için bir sıra numarası belirleyin"

#. module: account
#: code:addons/account/models/account_move.py:162
#, python-format
msgid "Please define a sequence on the journal."
msgstr "Yevmiye için lütfen bir seri no tanımlayın."

#. module: account
#: code:addons/account/models/account_invoice.py:1068
#, python-format
msgid "Please define sequence on the journal related to this invoice."
msgstr "Bu faturayla ilgili yevmiyeye lütfen bir sıra tanımlayın."

#. module: account
#: code:addons/account/models/company.py:336
#, python-format
msgid ""
"Please install a chart of accounts or create a miscellaneous journal before "
"proceeding."
msgstr ""
"Devam etmeden önce lütfen muhasebe kartınızı yükleyin ya da  bir muhtelif "
"yevmiye kaydı oluşturun."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Bank"
msgstr "+Banka"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Fixed Assets"
msgstr "(+) Duran Varlıklar"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Net Profit"
msgstr "+Net Kâr"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Non-Current Assets"
msgstr "+Dönen Varlıklar"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Other Income"
msgstr "+Diğer Gelirler"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_wizard_form
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Post"
msgstr "Onayla"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Post All Entries"
msgstr "Tüm Kayıtları Onayla"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Post Difference In"
msgstr "Sonrası Fark"

#. module: account
#: model:ir.actions.act_window,name:account.action_validate_account_move
#: model:ir.ui.view,arch_db:account.validate_account_move_view
msgid "Post Journal Entries"
msgstr "Yevmiye Kayıtlarını Onayla"

#. module: account
#: selection:account.move,state:0 selection:account.payment,state:0
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Posted"
msgstr "Onaylandı"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Posted Journal Entries"
msgstr "Onaylı Yevmiye Kayıtları"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Posted Journal Items"
msgstr "Onaylı Yevmiye Kalemleri"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_bank_account_code_prefix
#: model:ir.model.fields,field_description:account.field_res_company_bank_account_code_prefix
msgid "Prefix of the bank accounts"
msgstr "Banka hesaplarının ön eki"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_cash_account_code_prefix
msgid "Prefix of the cash accounts"
msgstr "Nakit hesapların ön eki"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_cash_account_code_prefix
msgid "Prefix of the main cash accounts"
msgstr "Ana nakit hesaplarının ön eki"

#. module: account
#: model:account.account.type,name:account.data_account_type_prepayments
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Prepayments"
msgstr "Avans Ödemeleri"

#. module: account
#: selection:account.financial.report,sign:0
msgid "Preserve balance sign"
msgstr "Bakiye işaretini koru"

#. module: account
#: model:ir.model,name:account.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""
"Fatura ve ödemeler eşleştirilirken yevmiye girişleri oluşturmak üzere "
"önceden ayarlanmış"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.account_common_report_view
msgid "Print"
msgstr "Yazdır"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Print Invoice"
msgstr "Faturayı Yazdır"

#. module: account
#: model:ir.model.fields,help:account.field_account_common_journal_report_amount_currency
#: model:ir.model.fields,help:account.field_account_print_journal_amount_currency
msgid ""
"Print Report with the currency column if the currency differs from the "
"company currency."
msgstr ""
"Eğer para birimi, şirket para biriminden farklıysa Raporu para birimi "
"sütununu içerecek şekilde yazdırın."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Print checks to pay your vendors"
msgstr "Tedarikçilerinize ödeme yapmak için çek yazdırın"

#. module: account
#: model:ir.model,name:account.model_product_product
#: model:ir.model.fields,field_description:account.field_account_analytic_line_product_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_product_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_product_id
#: model:ir.model.fields,field_description:account.field_account_move_line_product_id
#: model:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
msgid "Product"
msgstr "Ürün"

#. module: account
#: model:ir.model,name:account.model_product_category
#: model:ir.model.fields,field_description:account.field_account_invoice_report_categ_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Product Category"
msgstr "Ürün Kategorisi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_product_image
msgid "Product Image"
msgstr "Ürün Görseli"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_product_qty
msgid "Product Quantity"
msgstr "Ürün Miktarı"

#. module: account
#: model:ir.model,name:account.model_product_template
msgid "Product Template"
msgstr "Ürün Şablonu"

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action
#: model:ir.ui.view,arch_db:account.product_product_view_tree
msgid "Products"
msgstr "Ürünler"

#. module: account
#: code:addons/account/models/account_bank_statement.py:177
#, python-format
msgid "Profit"
msgstr "Kâr"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Profit &amp; Loss"
msgstr "Kar &amp; Zarar"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_profitloss_toreport0
msgid "Profit (Loss) to report"
msgstr "Raporlanacak Kar (Zarar)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_profit_account_id
msgid "Profit Account"
msgstr "Kar Hesabı"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_profitandloss0
#: model:ir.actions.act_window,name:account.action_account_report_pl
#: model:ir.ui.menu,name:account.menu_account_report_pl
msgid "Profit and Loss"
msgstr "Gelir Tablosu"

#. module: account
#: code:addons/account/models/account_payment.py:135
#, python-format
msgid ""
"Programming error: wizard action executed without active_ids in context."
msgstr "Programlama hatası: Sihirbaz eylemi active_ids olmadan gerçekleşmiş."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_form
msgid "Properties"
msgstr "Özellikler"

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action_purchasable
#: model:ir.ui.menu,name:account.product_product_menu_purchasable
msgid "Purchasable Products"
msgstr "Satın Alınabilir Ürünler"

#. module: account
#: selection:account.journal,type:0
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Purchase"
msgstr "Satınalma"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_wizard_multi_chart
msgid "Purchase Tax"
msgstr "Satınalma Vergisi"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_purchase_tax_rate
msgid "Purchase Tax(%)"
msgstr "Satınalma Vergisi(%)"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:35
#, python-format
msgid "Purchase: Untaxed Total"
msgstr "Alış: Vergi Hariç Toplam"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
#: model:ir.ui.menu,name:account.menu_finance_payables
#: model:ir.ui.view,arch_db:account.view_account_journal_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Purchases"
msgstr "Satınalma"

#. module: account
#: model:ir.actions.act_window,name:account.action_cash_box_in
#: model:ir.ui.view,arch_db:account.cash_box_in_form
msgid "Put Money In"
msgstr "...'ya parayı yatır"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Python Code"
msgstr "Python Kodu"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "QIF Import"
msgstr "QIF İçe Aktarımı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_quantity
#: model:ir.model.fields,field_description:account.field_account_move_line_quantity
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Quantity"
msgstr "Miktar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_description
#: model:ir.model.fields,field_description:account.field_cash_box_in_name
#: model:ir.model.fields,field_description:account.field_cash_box_out_name
msgid "Reason"
msgstr "Sebep"

#. module: account
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Reason..."
msgstr "Sebep..."

#. module: account
#: model:account.account.type,name:account.data_account_type_receivable
#: selection:account.account.type,type:0
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Receivable"
msgstr "Alacak"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_receivable_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Receivable Account"
msgstr "Alacak Hesabı"

#. module: account
#: selection:account.aged.trial.balance,result_selection:0
#: selection:account.common.partner.report,result_selection:0
#: selection:account.report.partner.ledger,result_selection:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.view_account_form
#: model:ir.ui.view,arch_db:account.view_account_search
msgid "Receivable Accounts"
msgstr "Alacak Hesapları"

#. module: account
#: selection:account.aged.trial.balance,result_selection:0
#: selection:account.common.partner.report,result_selection:0
#: selection:account.report.partner.ledger,result_selection:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Receivable and Payable Accounts"
msgstr "Alacak ve Borç Hesapları"

#. module: account
#: model:ir.ui.view,arch_db:account.product_template_form_view
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Receivables"
msgstr "Alacaklar"

#. module: account
#: selection:account.abstract.payment,payment_type:0
#: selection:account.payment,payment_type:0
#: selection:account.register.payments,payment_type:0
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Receive Money"
msgstr "Para Al"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:73
#: code:addons/account/static/src/xml/account_reconciliation.xml:105
#: code:addons/account/static/src/xml/account_reconciliation.xml:106
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#, python-format
msgid "Reconcile"
msgstr "Uzlaştır"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_account_move_line_reconcile
msgid "Reconcile Entries"
msgstr "Girişleri Uzlaştır"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Reconcile With Write-Off"
msgstr "Borç Silme ile Uzlaştır"

#. module: account
#: code:addons/account/wizard/account_reconcile.py:86
#, python-format
msgid "Reconcile Writeoff"
msgstr "Borç Silme Uzlaştır"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/tour_bank_statement_reconciliation.js:11
#, python-format
msgid "Reconcile the demo bank statement"
msgstr "Demo banka hesap özetini uzlaştır"

#. module: account
#: selection:account.payment,state:0
#: model:ir.model.fields,field_description:account.field_account_move_line_reconciled
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Reconciled"
msgstr "Uzlaştırılmış"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_reconciled
msgid "Reconciled Entries"
msgstr "Uzlaştırılmış Girişler"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Reconciled entries"
msgstr "Uzlaştırılmış Girişler"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Reconciliation"
msgstr "Uzlaştırma"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_reconcile_model
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Reconciliation Models"
msgstr "Uzlaştırma Modelleri"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_partial_reconcile_ids
msgid "Reconciliation Parts"
msgstr "Uzlaştırma Parçaları"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Reconciliation Transactions"
msgstr "Uzlaştırma İşlemleri"

#. module: account
#: model:ir.actions.client,name:account.action_bank_reconcile
#: model:ir.actions.client,name:account.action_bank_reconcile_bank_statements
msgid "Reconciliation on Bank Statements"
msgstr "Banka Hesap Özetlerinin Uzlaştırılması"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Reconciling journal entries"
msgstr "Yevmiye girişlerinin uzlaştırılması"

#. module: account
#: selection:account.journal,bank_statements_source:0
msgid "Record Manually"
msgstr "Manuel Kaydet"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Record transactions in foreign currencies"
msgstr "Yabancı para cinsinden işlem kaydı yapın"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Recording invoices"
msgstr "Faturaları Kaydetme"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:231
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#, python-format
msgid "Ref"
msgstr "Ref"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_ref
msgid "Ref."
msgstr "Ref."

#. module: account
#: code:addons/account/controllers/portal.py:72
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_ref
#: model:ir.model.fields,field_description:account.field_account_bank_statement_name
#: model:ir.model.fields,field_description:account.field_account_move_line_ref
#: model:ir.model.fields,field_description:account.field_account_move_ref
#: model:ir.model.fields,field_description:account.field_cash_box_in_ref
#, python-format
msgid "Reference"
msgstr "Referans"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_uom_name
msgid "Reference Unit of Measure"
msgstr "Referans Ölçü Birimi"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Reference number"
msgstr "Referans numarası"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_origin
#: model:ir.model.fields,help:account.field_account_invoice_origin
msgid "Reference of the document that produced this invoice."
msgstr "Bu faturayı oluşturan belgenin referansı."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_payment_reference
msgid ""
"Reference of the document used to issue this payment. Eg. check number, file"
" name, etc."
msgstr "Bu ödemeyi yapan belgenin referansı. Örneğin. Numara, dosya adı vb."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_name
msgid "Reference/Description"
msgstr "Belge No"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_invoice_ids
msgid "Refund Invoices"
msgstr "İade faturaları"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_filter_refund
msgid "Refund Method"
msgstr "İade Yöntemi"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_refund_filter_refund
msgid ""
"Refund base on this type. You can not Modify and Cancel if the invoice is "
"already reconciled"
msgstr ""
"İade matrahı bu tiptedir. Eğer faturada halihazırda uzlaşma yapılmışsa "
"değişiklik ve iptal yapamazsınız."

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_payment
#: model:ir.actions.act_window,name:account.action_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.view_account_payment_form
#: model:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Register Payment"
msgstr "Ödeme Kaydet"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Register Payments"
msgstr "Ödeme Kaydet"

#. module: account
#: model:ir.model,name:account.model_account_register_payments
msgid "Register payments on multiple invoices"
msgstr "Ödemeleri birden fazla faturaya kaydedin"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Registering outstanding invoices and payments can be a huge undertaking, \n"
"                        but you can start using Odoo without it by:"
msgstr ""
"Olağanüstü fatura ve ödemelerin kaydedilmesi büyük bir girişim olabilir, \\ "
"n Ancak Odoo'yu olmadan olmadan şu işlevleri kullanmaya başlayabilirsiniz:"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Registering payments related to outstanding invoices separately in a "
"different account (e.g. Account Receivables 2014)"
msgstr ""
"Ödenmemiş faturalarla ilgili ödemelerin farklı bir hesaba ayrı ayrı "
"kaydedilmesi (örneğin, 2014 Hesap Alacakları)"

#. module: account
#: selection:account.account.type,type:0
msgid "Regular"
msgstr "Düzenli"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_residual_company_signed
msgid "Remaining amount due in the currency of the company."
msgstr "Şirketin para birimi cinsinden kalan kalan tutar."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_residual_signed
msgid "Remaining amount due in the currency of the invoice."
msgstr "Kalan tutar, faturanın para birimi cinsindendir."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_residual
msgid "Remaining amount due."
msgstr "Kalan ödenecek tutar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_tax_dest_id
msgid "Replacement Tax"
msgstr "İkame Vergisi"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_financial_report_form
msgid "Report"
msgstr "Çıktı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_name
msgid "Report Name"
msgstr "Çıktı Adı"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.account_common_report_view
msgid "Report Options"
msgstr "Çıktı Seçenekleri"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
msgid "Report Type"
msgstr "Çıktı Türü"

#. module: account
#: selection:account.financial.report,type:0
#: model:ir.model.fields,field_description:account.field_account_financial_report_account_report_id
msgid "Report Value"
msgstr "Çıktı Değeri"

#. module: account
#: model:ir.ui.menu,name:account.account_report_folder
#: model:ir.ui.menu,name:account.menu_finance_reports
msgid "Reporting"
msgstr "Raporlama"

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding_rounding
msgid "Represent the non-zero value smallest coinage (for example, 0.05)."
msgstr "Sıfır olmayan en küçük madeni parayı tanımlayın (örneğin, 0.05)."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Reset to Draft"
msgstr "Taslağa Ayarla"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:236
#, python-format
msgid "Residual"
msgstr "Kalan"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_amount_residual
msgid "Residual Amount"
msgstr "Kalan Tutar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_amount_residual_currency
msgid "Residual Amount in Currency"
msgstr "Döviz Kalan"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_user_id
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Responsible"
msgstr "Sorumlu"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_deferred_revenue
msgid "Revenue Recognition"
msgstr "Gelirlerin Muhasebeleştirilmesi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal_date
msgid "Reversal date"
msgstr "Ters Kayıt Tarihi"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Reverse Entry"
msgstr "Ters Kayıt Girişi"

#. module: account
#: code:addons/account/wizard/account_move_reversal.py:20
#: model:ir.actions.act_window,name:account.action_view_account_move_reversal
#: model:ir.ui.view,arch_db:account.view_account_move_reversal
#, python-format
msgid "Reverse Moves"
msgstr "Ters Kayıt Hareketleri"

#. module: account
#: selection:account.financial.report,sign:0
msgid "Reverse balance sign"
msgstr "Ters bakiye imi"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review Fiscal Positions"
msgstr "Mali Koşulları Gözden Geçir"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review Terms"
msgstr "Koşulları Gözden Geçiş"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review existing Taxes"
msgstr "Mevcut Vergilerin Gözden Geçirilmesi"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review the Chart of Accounts"
msgstr "Hesap Planını Gözden Geçir"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review the list of available currencies (from the"
msgstr "Mevcut para birimleri listesini gözden geçirin ("

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group_parent_right
msgid "Right Parent"
msgstr "Sağ Üst"

#. module: account
#: selection:res.company,tax_calculation_rounding_method:0
msgid "Round Globally"
msgstr "Genelden Yuvarla"

#. module: account
#: selection:res.company,tax_calculation_rounding_method:0
msgid "Round per Line"
msgstr "Satır Satır Yuvarla"

#. module: account
#: model:ir.ui.view,arch_db:account.rounding_form_view
msgid "Rounding Form"
msgstr "Yuvarlama Şekli"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_is_rounding_line
msgid "Rounding Line"
msgstr "Yuvarlama Kalemi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_rounding_method
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Rounding Method"
msgstr "Yuvarlama Yöntemi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_rounding
msgid "Rounding Precision"
msgstr "Yuvarlama Hassasiyeti"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_strategy
msgid "Rounding Strategy"
msgstr "Yuvarlama Stratejisi"

#. module: account
#: model:ir.ui.view,arch_db:account.rounding_tree_view
msgid "Rounding Tree"
msgstr "Yuvarlama Ağacı"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_sepa
msgid "SEPA Credit Transfer (SCT)"
msgstr "SEPA Alacak Transferi"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "SEPA Direct Debit (SDD)"
msgstr "SEPA Direk Ödeme"

#. module: account
#: selection:account.journal,type:0
#: model:ir.ui.view,arch_db:account.report_tax
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Sale"
msgstr "Satış"

#. module: account
#: model:ir.ui.view,arch_db:account.view_wizard_multi_chart
msgid "Sale Tax"
msgstr "Satış Vergisi"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
#: model:ir.ui.menu,name:account.menu_finance_receivables
#: model:ir.ui.view,arch_db:account.view_account_journal_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Sales"
msgstr "Satış"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Sales Tax"
msgstr "Satış Vergisi"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_sale_tax_rate
msgid "Sales Tax(%)"
msgstr "Satış Vergisi(%)"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:33
#, python-format
msgid "Sales: Untaxed Total"
msgstr "Satış: Vergi Hariç Toplam"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_id
#: model:ir.model.fields,field_description:account.field_account_invoice_user_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Salesperson"
msgstr "Satış Temsilcisi"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model:ir.ui.view,arch_db:account.setup_opening_move_wizard_form
#: model:ir.ui.view,arch_db:account.setup_posted_move_form
#: model:ir.ui.view,arch_db:account.setup_view_company_form
msgid "Save"
msgstr "Kaydet"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:199
#, python-format
msgid "Save and New"
msgstr "Kaydet ve Yeni"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "Bu sayfayı kaydedin ve özelliği ayarlamak için buraya dönün."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Search Account Journal"
msgstr "Hesap Yevmiyesi Ara"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_template_search
msgid "Search Account Templates"
msgstr "Hesap Şablonlarını Ara"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Search Bank Statements"
msgstr "Banka Hesap Özeti Ara"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_line_search
msgid "Search Bank Statements Line"
msgstr "Banka Hesap Özeti Satırını Ara"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Search Chart of Account Templates"
msgstr "Hesap Planı Şablonu Ara"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Search Invoice"
msgstr "Fatura Ara"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Search Journal Items"
msgstr "Yevmiye Öğelerini Ara"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Search Move"
msgstr "Hareket Ara"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Search Operations"
msgstr "Banka Kayıtları"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Search Tax Templates"
msgstr "Vergi Şablonu Ara"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Search Taxes"
msgstr "Vergileri Ara"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_account_id
msgid "Second Account"
msgstr "İkinci Hesap"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_amount
msgid "Second Amount"
msgstr "İkinci Miktar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_amount_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_amount_type
msgid "Second Amount type"
msgstr "İkinci Miktar tipi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_analytic_account_id
msgid "Second Analytic Account"
msgstr "İkinci Analitik Hesap"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_journal_id
msgid "Second Journal"
msgstr "İkinci Yevmiye"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_label
msgid "Second Journal Item Label"
msgstr "İkinci Yevmiye Öğe Etiketi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_tax_id
msgid "Second Tax"
msgstr "İkinci Vergi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_access_token
msgid "Security Token"
msgstr "Güvenlik Token"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_type
msgid ""
"Select 'Sale' for customer invoices journals.\n"
"Select 'Purchase' for vendor bills journals.\n"
"Select 'Cash' or 'Bank' for journals that are used in customer or vendor payments.\n"
"Select 'General' for miscellaneous operations journals."
msgstr ""
"Müşteri faturaları yevmiyesi için 'Satış' seçeneğini seçin.\n"
"Tedarikçi fatura yevmiyeleri için 'Satınalma' seçeneğini seçin.\n"
"Müşteri veya tedarikçi ödemelerinde kullanılan yevmiyeler için \"Nakit\" veya \"Banka\" seçeneğini seçin.\n"
"Muhtelif işlem yevmiyeleri için 'Genel' seçeneğini seçin."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:74
#, python-format
msgid "Select a partner or choose a counterpart"
msgstr "Bir iş ortağı veya kaşı hesap seçin"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line_value
msgid "Select here the kind of valuation related to this payment terms line."
msgstr "Burada, bu ödeme koşulları satırı ile ilgili değerleme türünü seçin."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Select this if the taxes should use cash basis, which will create an entry "
"for such taxes on a given account during reconciliation."
msgstr ""
"Eğer vergiler gerçekleşme esasını kullanıyorsa, ve bu vergiler için "
"uzlaştırma süresince belirtilen hesapta bir giriş oluşturacaksa bunu seçin."

#. module: account
#: code:addons/account/wizard/account_invoice_state.py:21
#, python-format
msgid ""
"Selected invoice(s) cannot be confirmed as they are not in 'Draft' state."
msgstr "Seçilen fatura (lar), 'Taslak' durumunda olmadığı için onaylanamaz."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_invoice_warn
#: model:ir.model.fields,help:account.field_res_users_invoice_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"\"Uyarı\" seçeneğini seçmek kullanıcıyı mesajla uyaracaktır. \"Mesaj "
"Engelleme\"yi seçmek mesajla ilgili bir kuraldışı durum oluşturacak ve akışı"
" durduracaktır. Mesaj bir sonraki alana yazılmalıdır."

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action_sellable
#: model:ir.ui.menu,name:account.product_product_menu_sellable
msgid "Sellable Products"
msgstr "Satılabilir Ürünler"

#. module: account
#: selection:account.abstract.payment,payment_type:0
#: selection:account.payment,payment_type:0
#: selection:account.register.payments,payment_type:0
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Send Money"
msgstr "Para Gönder"

#. module: account
#: model:ir.actions.act_window,name:account.account_send_payment_receipt_by_email_action
msgid "Send Receipt By Email"
msgstr "E-postayla Makbuz Gönder"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Send by Email"
msgstr "E-posta ile Gönder"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Sending customer statements (outstanding invoices) manually during the "
"transition period"
msgstr ""
"Geçiş döneminde müşteri hesap özeti bildirimlerini (ödenmemiş faturalar) "
"elle göndermek"

#. module: account
#: selection:account.payment,state:0
#: model:ir.model.fields,field_description:account.field_account_invoice_sent
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Sent"
msgstr "Gönderilmiş"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "September"
msgstr "Eylül"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_sequence
#: model:ir.model.fields,field_description:account.field_account_financial_report_sequence
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_sequence
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_sequence
#: model:ir.model.fields,field_description:account.field_account_invoice_line_sequence
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_sequence
#: model:ir.model.fields,field_description:account.field_account_journal_sequence
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_sequence
#: model:ir.model.fields,field_description:account.field_account_payment_term_sequence
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_sequence
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_sequence
#: model:ir.model.fields,field_description:account.field_account_tax_group_sequence
#: model:ir.model.fields,field_description:account.field_account_tax_sequence
#: model:ir.model.fields,field_description:account.field_account_tax_template_sequence
msgid "Sequence"
msgstr "Sıra"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Set To Draft"
msgstr "Taslağa Ayarla"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag_active
msgid "Set active to false to hide the Account Tag without removing it."
msgstr ""
"Hesap Etiketlerini kaldırmadan gizlemek için etkin seçeneğini yanlış olarak "
"ayarla."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_active
msgid "Set active to false to hide the Journal without removing it."
msgstr ""
"Yevmiyeyi kaldırmadan gizlemek için etkin seçeneğini yanlış olarak ayarla."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_active
#: model:ir.model.fields,help:account.field_account_tax_template_active
msgid "Set active to false to hide the tax without removing it."
msgstr ""
"Veriyi kaldırmadan saklamaya çalışmak için etkin durumunu false olarak "
"ayarlayın."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Set the default Sales and Purchase taxes"
msgstr "Varsayılan Satış ve Satın Alma vergilerini ayarlama"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_tax_adjustment
msgid ""
"Set this field to true if this tax can be used in the tax adjustment wizard,"
" used to manually fill some data in the tax declaration"
msgstr ""
"Vergi düzeltme sihirbazında bu vergi kullanılabilirse ve vergi beyanındaki "
"bazı verileri manüel olarak doldurmak için bu alanı ayarlayın."

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_visible
msgid ""
"Set this to False if you don't want this template to be used actively in the"
" wizard that generate Chart of Accounts from templates, this is useful when "
"you want to generate accounts of this template only when loading its child "
"template."
msgstr ""
"Eğer bu şablonun, şablonlardan Hesap Tablosu oluşturan sihirbazda etkin "
"olarak kullanılmasını istemiyorsanız Yanlış olarak ayarlayın, yalnızca alt "
"şablondan yükleyerek bu şablona ait hesap oluşturmak istediğinizde "
"kullanışlı olacaktır."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Set to Draft"
msgstr "Taslak olarak ayarla"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_config
#: model:ir.ui.menu,name:account.menu_account_config
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Settings"
msgstr "Ayarlar"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Setup"
msgstr "Ayarlar"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_bar_closed
msgid "Setup Bar Closed"
msgstr "Kurulum Barı Kapalı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_code
msgid "Short Code"
msgstr "Kısa Kod"

#. module: account
#: model:res.groups,name:account.group_account_user
msgid "Show Full Accounting Features"
msgstr "Bütün Muhasebe Hesaplarını Göster"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Show active taxes"
msgstr "Etkin vergileri göster"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Show all records which has next action date is before today"
msgstr "Bir sonraki eylem tarihi bugünden önce olan tüm kayıtları göster"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Show inactive taxes"
msgstr "Etkin olmayan vergileri göster"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_show_on_dashboard
msgid "Show journal on dashboard"
msgstr "Panelde yevmiyeyi göster"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_sign
msgid "Sign on Reports"
msgstr "Raporlardaki İmza"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Simplify your cash, checks, or credit cards deposits with an integrated "
"batch payment function."
msgstr ""
"Nakit, çek veya alacak kartı depozitolarınızı entegre toplu ödeme "
"fonksiyonuyla basitleştirin."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:107
#, python-format
msgid "Skip"
msgstr "Atla"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Smallest Text"
msgstr "En Küçük Metin"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_sortby
msgid "Sort by"
msgstr "Sıralandır"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_origin
#: model:ir.model.fields,field_description:account.field_account_invoice_origin
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Source Document"
msgstr "Kaynak Belge"

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding_strategy
msgid ""
"Specify which way will be used to round the invoice amount to the rounding "
"precision"
msgstr ""
"Fatura miktarındaki yuvarlama hassasiyeti için kullanılacak yolu belirleyin."
" "

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_date_from
#: model:ir.model.fields,field_description:account.field_account_balance_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_account_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_report_date_from
#: model:ir.model.fields,field_description:account.field_account_print_journal_date_from
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_date_from
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_date_from
#: model:ir.model.fields,field_description:account.field_account_tax_report_date_from
#: model:ir.model.fields,field_description:account.field_accounting_report_date_from
#: model:ir.model.fields,field_description:account.field_accounting_report_date_from_cmp
msgid "Start Date"
msgstr "Başlangıç Tarihi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_balance_start
msgid "Starting Balance"
msgstr "Açılış Bakiyesi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_start_id
msgid "Starting Cashbox"
msgstr "Nakit Para Kasasını Başlatma"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "State"
msgstr "Durum"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_parent_state
msgid "State of the parent account.move"
msgstr "Üst account.move durumu"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_statement_id
#: model:ir.model.fields,field_description:account.field_account_move_line_statement_id
msgid "Statement"
msgstr "Ekstre"

#. module: account
#: code:addons/account/models/account_bank_statement.py:245
#, python-format
msgid "Statement %s confirmed, journal items were created."
msgstr "Hesap özeti %s onaylandı, yevmiye kalemleri oluşturuldu."

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_line_form
msgid "Statement Line"
msgstr "Hesap Özeti Kalemi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_ids
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_line_tree
msgid "Statement lines"
msgstr "Ekstre satırları"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_tree
msgid "Statements"
msgstr "Ekstreler"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "States"
msgstr "Durum"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_states_count
msgid "States Count"
msgstr "Devlet Sayısı"

#. module: account
#: code:addons/account/controllers/portal.py:73
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_state
#: model:ir.model.fields,field_description:account.field_account_bank_statement_state
#: model:ir.model.fields,field_description:account.field_account_invoice_state
#: model:ir.model.fields,field_description:account.field_account_move_state
#: model:ir.model.fields,field_description:account.field_account_payment_state
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Status"
msgstr "Durumu"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_subtotal
msgid "Subtotal"
msgstr "Ara Toplam"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Supplier Payments"
msgstr "Tedarikçi Para Birimi"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "TOTAL ASSETS"
msgstr "TOPLAM VARLIKLAR"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "TOTAL EQUITY"
msgstr "TOPLAM ÖZKAYNAK"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_ids
#: model:ir.model.fields,field_description:account.field_account_tax_tag_ids
#: model:ir.ui.view,arch_db:account.account_tag_view_form
msgid "Tags"
msgstr "Etiketler"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Tags for Multidimensional Analytics"
msgstr "Çok Boyutlu Analitikler için Etiketler"

#. module: account
#: model:ir.actions.act_window,name:account.action_cash_box_out
#: model:ir.ui.view,arch_db:account.cash_box_out_form
msgid "Take Money Out"
msgstr "Para Al"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_target_move
#: model:ir.model.fields,field_description:account.field_account_balance_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_account_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_report_target_move
#: model:ir.model.fields,field_description:account.field_account_print_journal_target_move
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_target_move
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_target_move
#: model:ir.model.fields,field_description:account.field_account_tax_report_target_move
#: model:ir.model.fields,field_description:account.field_accounting_report_target_move
msgid "Target Moves"
msgstr "Hedef Hareketler"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:174
#: model:ir.model,name:account.model_account_tax
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_tax
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_tax_id
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_tax
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#, python-format
msgid "Tax"
msgstr "Vergi"

#. module: account
#: code:addons/account/models/chart_template.py:842
#: code:addons/account/models/chart_template.py:845
#, python-format
msgid "Tax %.2f%%"
msgstr "Vergi %.2f%%"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_account_id
msgid "Tax Account"
msgstr "Vergi Hesabı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_refund_account_id
msgid "Tax Account on Credit Notes"
msgstr "İade/Fiyat Farkı için Vergi Hesabı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_refund_account_id
msgid "Tax Account on Refunds"
msgstr "İade Vergi Hesapları"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_tax_adjustment
#: model:ir.model.fields,field_description:account.field_account_tax_template_tax_adjustment
msgid "Tax Adjustment"
msgstr "Vergi Düzeltmeleri"

#. module: account
#: model:ir.actions.act_window,name:account.tax_adjustments_form
#: model:ir.ui.menu,name:account.menu_action_tax_adjustment
msgid "Tax Adjustments"
msgstr "Manuel Vergi Düzeltmeleri"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Tax Amount"
msgstr "Vergi Tutarı"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Tax Application"
msgstr "Vergi Erteleme Başvurusu"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_tax_calculation_rounding_method
msgid "Tax Calculation Rounding Method"
msgstr "Vergi Hesaplaması Yuvarlama Yöntemi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_tax_cash_basis_rec_id
msgid "Tax Cash Basis Entry of"
msgstr "Gerçekleşme Esasına Göre Vergi Kaydı"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_tax_cash_basis_journal_id
msgid "Tax Cash Basis Journal"
msgstr "Gerçekleşme Esasına Göre Vergi Yevmiyesi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_amount_type
#: model:ir.model.fields,field_description:account.field_account_tax_template_amount_type
msgid "Tax Computation"
msgstr "Vergi Hesaplaması"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Tax Declaration"
msgstr "Vergi Bildirimi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_name
msgid "Tax Description"
msgstr "Vergi Açıklaması"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_tax_exigibility
#: model:ir.model.fields,field_description:account.field_account_tax_template_tax_exigibility
msgid "Tax Due"
msgstr "Ödenecek Vergi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_tax_group_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_tax_group_id
msgid "Tax Group"
msgstr "Vergi Grubu"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Tax ID"
msgstr "Vergi ID"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_line_ids
msgid "Tax Lines"
msgstr "Vergi Satırları"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_tax_ids
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Tax Mapping"
msgstr "Vergi Eşleştirmesi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_name
#: model:ir.model.fields,field_description:account.field_account_tax_template_name
msgid "Tax Name"
msgstr "Vergi Adı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_cash_basis_account
#: model:ir.model.fields,field_description:account.field_account_tax_template_cash_basis_account
msgid "Tax Received Account"
msgstr "Vergi Alınan Hesap"

#. module: account
#: model:ir.actions.report,name:account.action_report_account_tax
#: model:ir.model,name:account.model_account_tax_report
#: model:ir.ui.menu,name:account.menu_account_report
#: model:ir.ui.view,arch_db:account.report_tax
msgid "Tax Report"
msgstr "Vergi Raporu"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_tax_report
msgid "Tax Reports"
msgstr "Vergi Raporları"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_type_tax_use
#: model:ir.model.fields,field_description:account.field_account_tax_type_tax_use
msgid "Tax Scope"
msgstr "Vergi Kapsamı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_tax_src_id
msgid "Tax Source"
msgstr "Vergi Kaynağı"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Tax Template"
msgstr "Vergi Şablonu"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_tax_template_ids
msgid "Tax Template List"
msgstr "Vergi Şablonu Listesi"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_tax_template_form
msgid "Tax Templates"
msgstr "Vergi Şablonları"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr "Vergi hesaplama yuvarlama yöntemi"

#. module: account
#: sql_constraint:account.tax:0 sql_constraint:account.tax.template:0
msgid "Tax names must be unique !"
msgstr "Vergi isimleri daha önce kullanılmış olmamalıdır"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_tax_src_id
msgid "Tax on Product"
msgstr "Üründeki Vergi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_tax_dest_id
msgid "Tax to Apply"
msgstr "Uygulanacak Vergi"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "TaxCloud"
msgstr "VergiBulutu"

#. module: account
#: selection:account.account.tag,applicability:0
#: model:account.tax.group,name:account.tax_group_taxes
#: model:ir.actions.act_window,name:account.action_tax_form
#: model:ir.model.fields,field_description:account.field_account_invoice_line_invoice_line_tax_ids
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_ids
#: model:ir.ui.menu,name:account.menu_action_tax_form
#: model:ir.ui.view,arch_db:account.account_planner
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Taxes"
msgstr "Vergiler"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_tax
msgid "Taxes Fiscal Position"
msgstr "Vergilerin Mali Koşulu"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_template_form
msgid "Taxes Mapping"
msgstr "Vergi Eşleştirmesi"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Taxes used in Purchases"
msgstr "Satın alımlarda kullanılmış vergiler"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Taxes used in Sales"
msgstr "Satışta kullanılan Vergiler"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Taxes, fiscal positions, chart of accounts &amp; legal statements for your "
"country"
msgstr ""
"Ülkeniz için vergiler, mali koşullar, hesap planı ve yasal bildirimler"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_balance
msgid ""
"Technical field holding the debit - credit in order to open meaningful graph"
" views from reports"
msgstr ""
"Raporlardan anlamlı grafikler görüntüsü açmak için borç kredisi alan teknik "
"alan"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_balance_cash_basis
msgid ""
"Technical field holding the debit_cash_basis - credit_cash_basis in order to"
" open meaningful graph views from reports"
msgstr ""
"Raporlardan anlamlı grafikler görüntüsü açmak için debit_cash_basis - "
"credit_cash_basis alan teknik alan"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_move_name
msgid ""
"Technical field holding the number given to the invoice, automatically set "
"when the invoice is validated then stored to set the same number again if "
"the invoice is cancelled, set to draft and re-validated."
msgstr ""
"Faturaya verilen numara tutulan teknik alan, fatura onaylandığında otomatik "
"olarak ayarlanır ve sonra fatura iptal edilir, taslak haline getirilir ve "
"yeniden doğrulanırsa aynı numarayı tekrar ayarlamak üzere saklanır."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_move_name
#: model:ir.model.fields,help:account.field_account_payment_move_name
msgid ""
"Technical field holding the number given to the journal entry, automatically"
" set when the statement line is reconciled then stored to set the same "
"number again if the line is cancelled, set to draft and re-processed again."
msgstr ""
"Dergiye girilen numarayı tutan teknik alan, bildirim satırı mutabık kılınca "
"otomatik olarak ayarlanır ve daha sonra, hat iptal edilir, taslak haline "
"getirilir ve tekrar yeniden işlenirse aynı sayıyı tekrar ayarlamak için "
"saklanır."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_bank_data_done
msgid "Technical field holding the status of the bank setup step."
msgstr "Banka kurulum adımlarının durumunu tutan teknik alan."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_coa_done
msgid "Technical field holding the status of the chart of account setup step."
msgstr "Muhasebe kurulum adımlarının durumunu tutan teknik alan."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_company_data_done
msgid "Technical field holding the status of the company setup step."
msgstr "Şirket kurulum adımlarının durumunu tutan teknik alan."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_fy_data_done
msgid "Technical field holding the status of the financial year setup step."
msgstr "Mali yıl kurulum adımlarının durumunu tutan teknik alan."

#. module: account
#: model:ir.model.fields,help:account.field_account_register_payments_multi
msgid ""
"Technical field indicating if the user selected invoices from multiple "
"partners or from different types."
msgstr ""
"Kullanıcının bir çok partnerden faturası varsa, ya da farklı tiplerde "
"faturası varsa, bunu belirten teknik alan."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_bar_closed
msgid ""
"Technical field set to True when setup bar has been closed by the user."
msgstr ""
"Kurulum barı kullanıcı tarafından kaldırıldığında Doğru 'yu ayarlayan teknik"
" alan."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_refund_only
msgid ""
"Technical field to hide filter_refund in case invoice is partially paid"
msgstr ""
"Fatura kısmen ödenmesi durumunda filtre_refund gizlemek için teknik alan"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_journal_type
#: model:ir.model.fields,help:account.field_account_payment_has_invoices
msgid "Technical field used for usability purposes"
msgstr "Kullanılabilirlik için kullanılan teknik alan"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_matched_percentage
msgid "Technical field used in cash basis method"
msgstr "Gerçekleşme Esası metodu için kullanılan teknik alan"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_account_setup_bank_data_done
msgid "Technical field used in the special view for the setup bar step."
msgstr "Kurulum barı adımlarının özel görünümü için kullanılan teknik alan."

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_payment_method_code
#: model:ir.model.fields,help:account.field_account_payment_payment_method_code
#: model:ir.model.fields,help:account.field_account_register_payments_payment_method_code
msgid ""
"Technical field used to adapt the interface to the payment type selected."
msgstr ""
"Ara birimi seçilen ödeme türüne uyarlamak için kullanılan teknik alan."

#. module: account
#: model:ir.model.fields,help:account.field_account_partial_reconcile_max_date
msgid ""
"Technical field used to determine at which date this reconciliation needs to"
" be shown on the aged receivable/payable reports."
msgstr ""
"Hangi tarihin zamanı gelen alacak/borç raporlarında gösterilmesi gerektiğini"
" belirleyen teknik alan."

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_hide_payment_method
#: model:ir.model.fields,help:account.field_account_payment_hide_payment_method
#: model:ir.model.fields,help:account.field_account_register_payments_hide_payment_method
msgid ""
"Technical field used to hide the payment method if the selected journal has "
"only one available which is 'manual'"
msgstr ""
"Seçilen günlükte yalnızca bir tane mevcutsa ödeme yöntemini gizlemek için "
"kullanılan teknik alan 'manuel'"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_tax_cash_basis_rec_id
msgid ""
"Technical field used to keep track of the tax cash basis reconciliation. "
"This is needed when cancelling the source: it will post the inverse journal "
"entry to cancel that part too."
msgstr ""
"Gerçekleşme esaslı vergi uzlaştırmalarını takip etmek için kullanılan teknik"
" alan. Kaynak iptalinde şu gereklidir: bu bölümü de iptal etmek için ters "
"yevmiye girişi gönderecektir."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_tax_exigible
msgid ""
"Technical field used to mark a tax line as exigible in the vat report or not"
" (only exigible journal items are displayed). By default all new journal "
"items are directly exigible, but with the feature cash_basis on taxes, some "
"will become exigible only when the payment is recorded."
msgstr ""
"Bir vergi hattını, kazan raporuna uygun olarak işaretlemek için kullanılan "
"teknik alan (yalnızca geçerli yevmiye öğeleri görüntülenir). Varsayılan "
"olarak, tüm yeni yevmiye öğeleri doğrudan geçerlidir, ancak vergilerle "
"ilgili nakit_baz özelliğiyle, bazıları yalnızca ödeme kaydedildiğinde "
"anlaşılabilir hale gelecektir."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_is_unaffected_earnings_line
msgid ""
"Tells whether or not this line belongs to an unaffected earnings account"
msgstr "Bu kalemin etkilenmemiş kazanç hesabına ait olup olmadığını söyler."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_chart_template_id
msgid "Template"
msgstr "Şablon"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_account_template
msgid "Template Account Fiscal Mapping"
msgstr "Mali Hesap Eşleştirme Şablonu"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_tax_template
msgid "Template Tax Fiscal Position"
msgstr "Vergi Mali Koşul Şablonu"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "Mali Koşul Şablonu"

#. module: account
#: model:ir.model,name:account.model_account_chart_template
msgid "Templates for Account Chart"
msgstr "Hesap Planı Şablonu"

#. module: account
#: model:ir.model,name:account.model_account_account_template
msgid "Templates for Accounts"
msgstr "Hesap Şablonları"

#. module: account
#: model:ir.model,name:account.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Vergi Şablonları"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Term Type"
msgstr "Dönem Tipi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_ids
#: model:ir.ui.view,arch_db:account.view_payment_term_form
msgid "Terms"
msgstr "Kullanım koşulları"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Terms &amp; Conditions"
msgstr "Şartlar &amp; Koşullar"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Terms and conditions..."
msgstr "Şartlar ve koşullar..."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "That's on average"
msgstr "Ortalamadır"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_internal_type
#: model:ir.model.fields,help:account.field_account_account_type_type
msgid ""
"The 'Internal Type' is used for features available on different types of "
"accounts: liquidity type is for cash or bank accounts, payable/receivable is"
" for vendor/customer accounts."
msgstr ""
"'İçsel Tür', farklı hesap türlerinde bulunan özellikler için kullanılır: "
"likidite türü nakit veya banka hesapları için, borçlu / alacak satıcı / "
"müşteri hesapları içindir."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The Advisors have full access to the Accounting application, \n"
"                                        plus access to miscellaneous operations such as salary and asset management."
msgstr ""
"Danışmanlar Muhasebe uygulamasına tam erişime sahiptir, ayrıca maaş ve "
"varlık yönetimi gibi çeşitli işlemleri de içerir."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The Deposit Ticket module can also be used to settle credit card batch made "
"of multiple transactions."
msgstr ""
"Para Yatırma Bilet modülü, birden fazla işlemden oluşan alacak kartı toplu "
"işini halletmek için kullanılabilir."

#. module: account
#: code:addons/account/models/account_move.py:1209
#, python-format
msgid "The account %s (%s) is deprecated !"
msgstr "Hesap %s (%s) kullanımdan kaldırıldı!"

#. module: account
#: code:addons/account/models/account_move.py:1021
#, python-format
msgid "The account %s (%s) is not marked as reconciliable !"
msgstr "Hesap %s ( %s ) mutabık kılınmış olarak işaretlenmedi!"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_bank_journal_id
msgid "The accounting journal corresponding to this bank account."
msgstr "Bu banka hesabına karşılık gelen muhasebe günlüğü."

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_currency_exchange_journal_id
msgid ""
"The accounting journal where automatic exchange differences will be "
"registered"
msgstr "Otomatik değişim farklarının kaydedileceği yevmiye hesabı"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_amount_currency
#: model:ir.model.fields,help:account.field_account_move_line_amount_currency
msgid ""
"The amount expressed in an optional other currency if it is a multi-currency"
" entry."
msgstr ""
"Eğer çok-para birimliyse, tutar seçmeli olarak başka para biriminde "
"belirtilir."

#. module: account
#: model:ir.model.fields,help:account.field_account_analytic_line_amount_currency
#: model:ir.model.fields,help:account.field_account_analytic_line_analytic_amount_currency
msgid ""
"The amount expressed in the related account currency if not equal to the "
"company one."
msgstr ""
"Para birimi şirketin para birimine eşit değilse ilgili hesap para birimi "
"tutarı ifadesi."

#. module: account
#: code:addons/account/models/account_move.py:508
#, python-format
msgid ""
"The amount expressed in the secondary currency must be positive when account"
" is debited and negative when account is credited."
msgstr ""
"İkincil para biriminde tanımlanan tutar, hesap borçlu olduğunda artı, "
"alacaklı olduğunda eksi olmalıdır."

#. module: account
#: code:addons/account/models/account.py:804
#, python-format
msgid ""
"The application scope of taxes in a group must be either the same as the "
"group or \"None\"."
msgstr ""
"Bir gruptaki vergilerin uygulama kapsamı ya grup ya da \\ \"Yok \" ile aynı "
"olmalıdır."

#. module: account
#: code:addons/account/models/account.py:452
#, python-format
msgid ""
"The bank account of a bank journal must belong to the same company (%s)."
msgstr "Bir banka günlüğünün banka hesabı aynı şirkete ait olmalıdır ( %s )."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_statement_id
msgid "The bank statement used for bank reconciliation"
msgstr "Banka uzlaştırması için kullanılan banka hesap özeti"

#. module: account
#: code:addons/account/models/account_invoice.py:1165
#, python-format
msgid ""
"The cash rounding cannot be computed because the difference must be added on the biggest tax found and no tax are specified.\n"
"Please set up a tax or change the cash rounding method."
msgstr ""
"Nakit yuvarlama hesaplanamadı, çünkü fark bulunan en büyük vergiye eklenmeli ve vergi belirtilmemelidir.\n"
"Lütfen vergi ayarınızı yapın ya da yuvarlama yönteminizi değiştirin."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_chart_template_id
msgid "The chart template for the company (if any)"
msgstr "Şirket için hesap planı şablonu (varsa)"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
msgid "The closing balance is different than the computed one!"
msgstr "Kapanış bakiyesi hesaplanandan farklı"

#. module: account
#: sql_constraint:account.journal:0
msgid "The code and name of the journal must be unique per company !"
msgstr "Derginin kodu ve adı firma başına eşsiz olmalıdır!"

#. module: account
#: sql_constraint:account.account:0
msgid "The code of the account must be unique per company !"
msgstr "Hesap Kodu her şirket için eşsiz olmalı !"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_commercial_partner_id
msgid ""
"The commercial entity that will be used on Journal Entries for this invoice"
msgstr "Bu faturaya ait Yevmiye Girişlerinde kullanılacak ticari varlık"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_currency_id
msgid "The currency used to enter statement"
msgstr "Hesap özetine girilecek para birimi"

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree1
msgid ""
"The discussions with your customer are automatically displayed at\n"
"                the bottom of each invoice."
msgstr ""
"Müşterinizle yapılan görüşmeler her faturanın alt kısmında \\ n otomatik "
"olarak gösterilir."

#. module: account
#: code:addons/account/models/account_bank_statement.py:191
#, python-format
msgid ""
"The ending balance is incorrect !\n"
"The expected balance (%s) is different from the computed one. (%s)"
msgstr ""
"Biten bakiye yanlış! \\ N Beklenen bakiye ( %s ), hesaplanan değerden "
"farklı. ( %s )"

#. module: account
#: model:ir.model.fields,help:account.field_product_category_property_account_expense_categ_id
msgid ""
"The expense is accounted for when a vendor bill is validated, except in "
"anglo-saxon accounting with perpetual inventory valuation in which case the "
"expense (Cost of Goods Sold account) is recognized at the customer invoice "
"validation."
msgstr ""
"Kalıcı stok değerlemesine sahip anglo-saxon hesabı hariç, tedarikçi faturası"
" onaylandığında harcamalarınız muhasebeleştirilir, bu durumda harcama "
"(Satılan Malların Maliyeti muhasebesi) müşteri fatura onayı ile "
"kararlaştırılır."

#. module: account
#: model:ir.model.fields,help:account.field_product_product_property_account_expense_id
#: model:ir.model.fields,help:account.field_product_template_property_account_expense_id
msgid ""
"The expense is accounted for when a vendor bill is validated, except in "
"anglo-saxon accounting with perpetual inventory valuation in which case the "
"expense (Cost of Goods Sold account) is recognized at the customer invoice "
"validation. If the field is empty, it uses the one defined in the product "
"category."
msgstr ""
"Kalıcı stok değerlemesine sahip anglo-saxon hesabı hariç, tedarikçi faturası"
" onaylandığında harcamalarınız muhasebeleştirilir, bu durumda harcama "
"(Satılan Malların Maliyeti muhasebesi) müşteri fatura onayı ile "
"kararlaştırılır. Eğer alan boşsa, tanımlanmış ürün kategorisi "
"kullanılacaktır."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The first step is to set up your company's information. This is mostly used "
"in printed business documents like orders and invoices."
msgstr ""
"İlk adım şirketinizin bilgilerini oluşturmaktır. Bu, çoğunlukla siparişler "
"ve faturalar gibi basılı iş belgelerinde kullanılır."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_account_position_id
#: model:ir.model.fields,help:account.field_res_users_property_account_position_id
msgid ""
"The fiscal position will determine taxes and accounts used for the partner."
msgstr ""
"Mali koşul, iş ortağı için kullanılacak vergileri ve hesapları belirlemek "
"için kullanılır."

#. module: account
#: code:addons/account/models/account.py:456
#, python-format
msgid "The holder of a journal's bank account must be the company (%s)."
msgstr "Bir derginin banka hesabı sahibi firma olmalıdır ( %s )."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_account_id
msgid "The income or expense account related to the selected product."
msgstr "Seçilen ürünle ilgili gelir ve gider hesabı."

#. module: account
#: code:addons/account/models/account_payment.py:643
#, python-format
msgid "The journal %s does not have a sequence, please specify one."
msgstr "%s adlı yevmiyede bir sıralama yok, lütfen bir tane belirtin."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_code
msgid "The journal entries of this journal will be named using this prefix."
msgstr ""
"Bu yevmiyenin kayıt girişleri bu önek kullanılarak isimlendirilecektir."

#. module: account
#: model:ir.model.fields,help:account.field_account_opening_opening_move_id
#: model:ir.model.fields,help:account.field_res_company_account_opening_move_id
msgid ""
"The journal entry containing the initial balance of all this company's "
"accounts."
msgstr ""
"Yevmiye kayıtları şirketteki bütün hesapların başlangıç bakiyesini içerir."

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op_fiscalyear_last_day
#: model:ir.model.fields,help:account.field_account_financial_year_op_fiscalyear_last_month
msgid ""
"The last day of the month will be taken if the chosen day doesn't exist."
msgstr "Seçilen gün yoksa ayın son günü alınır."

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_form
msgid ""
"The last line's computation type should be \"Balance\" to ensure that the "
"whole amount will be allocated."
msgstr ""
"Son satırın hesaplama türü, tüm tutarı tahsis etmek için \\ \"Bakiye \" "
"olmalıdır."

#. module: account
#: code:addons/account/models/company.py:94
#, python-format
msgid "The lock date for advisors is irreversible and can't be removed."
msgstr "Müşavirler için kilit tarihleri geri alınamaz ve değiştirilemez."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_move_id
msgid "The move of this entry line."
msgstr "Bu kayıt satırının muhasebe hareketi."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The multi-currency option allows you to send or receive invoices \n"
"                        in difference currencies, set up foreign bank accounts \n"
"                        and run reports on your foreign financial activities."
msgstr ""
"Çok para seçeneği, farklı para birimlerinde \\ n faturalar göndermenize veya"
" bunları almanıza, yabancı banka hesaplarını ayarlamanıza ve yabancı "
"finansal faaliyetleriniz hakkında raporlar vermenize izin verir."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_name
msgid "The name that will be used on account move lines"
msgstr "Hesap hareket satırlarında kullanılacak ad"

#. module: account
#: code:addons/account/models/company.py:98
#, python-format
msgid ""
"The new lock date for advisors must be set after the previous lock date."
msgstr ""
"Müşavirler için yeni kilit tarihi, önceki kilit tarihinden sonraya "
"ayarlanmalıdır."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_refund_sequence_number_next
msgid "The next sequence number will be used for the next credit note."
msgstr "Sonraki İade/Fiyat Farkı için sıradaki numara kullanılacak"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_sequence_number_next
msgid "The next sequence number will be used for the next invoice."
msgstr "Sıradaki fatura için sıradaki numara kullanılacaktır."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_currency_id
#: model:ir.model.fields,help:account.field_account_move_line_currency_id
msgid "The optional other currency if it is a multi-currency entry."
msgstr "Eğer çok para birimli ise seçmeli diğer para biriminin kaydı."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_quantity
msgid ""
"The optional quantity expressed by this line, eg: number of product sold. "
"The quantity is not a legal requirement but is very useful for some reports."
msgstr ""
"Bu satırda belirtilen seçmeli miktar, ör: satılan ürün sayısı. Miktar yasal "
"zorunlu bir alan değildir ama bazı raporlar için çok kullanışlıdır."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_account_id
msgid "The partner account used for this invoice."
msgstr "Bu fatura için kullanılan iş ortağı hesabı."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_has_unreconciled_entries
#: model:ir.model.fields,help:account.field_res_users_has_unreconciled_entries
msgid ""
"The partner has at least one unreconciled debit and credit since last time "
"the invoices & payments matching was performed."
msgstr ""
"İş ortağının son fatura ve ödemelerinin eşleştirmesinden bu yana en az bir "
"uzlaşmamış borç ve alacak var."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_reference
msgid "The partner reference of this invoice."
msgstr "Bu faturaya ait iş ortağı referansı"

#. module: account
#: code:addons/account/models/account.py:522
#, python-format
msgid ""
"The partners of the journal's company and the related bank account mismatch."
msgstr "şirket yevmiye partneri ve ilgili banka hesabı uyuşmazlığı."

#. module: account
#: code:addons/account/models/account_payment.py:60
#, python-format
msgid "The payment amount cannot be negative."
msgstr "Ödeme miktarı negatif olamaz."

#. module: account
#: code:addons/account/models/account_payment.py:487
#, python-format
msgid "The payment cannot be processed because the invoice is not open!"
msgstr "Fatura açık olmadığı için ödeme işlemi yapılamıyor!"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_amount_residual_currency
msgid ""
"The residual amount on a journal item expressed in its currency (possibly "
"not the company currency)."
msgstr ""
"Bir yevmiye öğesinin para biriminde (muhtemelen şirket para birimi değil) "
"ifade edilen kalıntı tutarı."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_amount_residual
msgid ""
"The residual amount on a journal item expressed in the company currency."
msgstr ""
"Şirket para birimi cinsinden ifade edilen bir yevmiye öğesinin kalan "
"miktarı."

#. module: account
#: code:addons/account/models/account_move.py:493
#, python-format
msgid ""
"The selected account of your Journal Entry forces to provide a secondary "
"currency. You should remove the secondary currency on the account."
msgstr ""
"Dergisi Girişinizin seçili hesabı, ikincil bir para birimi sağlamak için "
"zorlar. Hesabınızdaki ikincil para birimini kaldırmalısınız."

#. module: account
#: code:addons/account/models/account_invoice.py:1612
#, python-format
msgid ""
"The selected unit of measure is not compatible with the unit of measure of "
"the product."
msgstr "Seçilen ölçü birimi ürünün ölçü birimiyle uyumlu değil."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_sequence
#: model:ir.model.fields,help:account.field_account_tax_template_sequence
msgid ""
"The sequence field is used to define order in which the tax lines are "
"applied."
msgstr ""
"Sıra alanı, vergi hatlarının uygulanacağı sırayı tanımlamak için kullanılır."

#. module: account
#: code:addons/account/models/account_payment.py:645
#, python-format
msgid "The sequence of journal %s is deactivated."
msgstr "Yevmiye %s sırası devre dışı bırakıldı."

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding_rounding_method
msgid "The tie-breaking rule used for float rounding operations"
msgstr "Kaydırmalı yuvarlama operasyonu için Tie-breaking kuralı kullanılır."

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid "There are currently no invoices and payments for your account."
msgstr "Hesabınız için bir fatura ve ödeme görülmemektedir."

#. module: account
#: code:addons/account/models/company.py:178
#, python-format
msgid ""
"There are still unposted entries in the period you want to lock. You should "
"either post or delete them."
msgstr ""
"Kilitlemek istediğiniz dönemde hala gönderilmemiş girişler var. Onları "
"postalamanız veya silmelisiniz."

#. module: account
#: code:addons/account/models/account_bank_statement.py:179
#, python-format
msgid ""
"There is no account defined on the journal %s for %s involved in a cash "
"difference."
msgstr "Dergi üzerinde tanımlı hesap yok %s için %s dahil nakit fark."

#. module: account
#: code:addons/account/wizard/account_validate_account_move.py:18
#, python-format
msgid "There is no journal items in draft state to post."
msgstr "Taslak haldeki hiçbir yevmiye öğesi yayınlanmaz."

#. module: account
#: code:addons/account/models/account_move.py:1744
#, python-format
msgid ""
"There is no tax cash basis journal defined for this company: \"%s\" \n"
"Configure it in Accounting/Configuration/Settings"
msgstr ""
"Bu şirket için tanımlanmış bir vergi matrahı yok: \" %s \"\n"
"Muhasebe / Yapılandırma / Ayarlar bölümünde yapılandırın"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:46
#, python-format
msgid "There is nothing to reconcile."
msgstr "Uzlaştırma Yok"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_error
msgid "There was an error processing this page."
msgstr "Bu sayfa işlenirken bir hata oluştu."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "These taxes are set in any new product created."
msgstr "Bu vergiler, yaratılan yeni bir üründe bulunur."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_user_type_id
msgid ""
"These types are defined according to your country. The type contains more "
"information about the account and its specificities."
msgstr ""
"Bu tipler ülkenize göre tanımlanmıştır. Bu tip, hesap ve özellikleri "
"hakkında daha çok bilgi içerir."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "These users handle billing specifically."
msgstr "Bu kullanıcılar özellikle faturalandırmayı işlemektedir."

#. module: account
#: code:addons/account/models/account_invoice.py:1337
#, python-format
msgid ""
"This %s has been created from: <a href=# data-oe-model=account.invoice data-"
"oe-id=%d>%s</a>"
msgstr ""
"Bu %s şu adresten oluşturuldu: <a href=# data-oe-model=account.invoice data-"
"oe-id=%d>%s</a>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "This Month"
msgstr "Bu Ay"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:111
#, python-format
msgid "This Week"
msgstr "Bu Hafta"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "This Year"
msgstr "Bu Yıl"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_account_payable_id
#: model:ir.model.fields,help:account.field_res_users_property_account_payable_id
msgid ""
"This account will be used instead of the default one as the payable account "
"for the current partner"
msgstr ""
"Bu hesap, geçerli iş ortağının ödemeler hesabında varsayılan yerine "
"kullanılacaktır"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_account_receivable_id
#: model:ir.model.fields,help:account.field_res_users_property_account_receivable_id
msgid ""
"This account will be used instead of the default one as the receivable "
"account for the current partner"
msgstr ""
"Bu hesap, geçerli İş Ortağı için alacak hesabı olarak varsayılanın yerine "
"kullanılacaktır."

#. module: account
#: model:ir.model.fields,help:account.field_product_category_property_account_income_categ_id
msgid "This account will be used when validating a customer invoice."
msgstr "Bu hesap müşteri faturası doğrulandığında kullanılacak."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows accountants to manage analytic and crossovered budgets. Once the"
" master budgets and the budgets are defined, the project managers can set "
"the planned amount on each analytic account."
msgstr ""
"Bu, muhasebecilerin analitik ve çapraz bütçelerini yönetmesine olanak tanır."
" Ana bütçeler ve bütçeler tanımlandıktan sonra, proje yöneticileri planlanan"
" tutarı her analitik hesapta ayarlayabilir."

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_module_account_batch_deposit
msgid ""
"This allows you to group received checks before you deposit them to the bank.\n"
"-This installs the module account_batch_deposit."
msgstr ""
"Bu, alınan çekleri bankaya yatırmadan önce gruplandırmanıza olanak tanır. \\"
" N-account_batch_deposit modülü yüklenir."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows you to manage the assets owned by a company or a person. It "
"keeps track of the depreciation occurred on those assets, and creates "
"account move for those depreciation lines."
msgstr ""
"Bu, bir şirketin veya bir kişinin sahip olduğu varlıkları yönetmenizi "
"sağlar. Bu varlıklar üzerinde oluşan amortismanı takip eder ve bu amortisman"
" hatları için hesap hareketini yaratır."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows you to manage the revenue recognition on selling products. It "
"keeps track of the installments occurred on those revenue recognitions, and "
"creates account moves for those installment lines."
msgstr ""
"Bu, ürün satışı üzerindeki geliri tahakkukunu yönetmenizi sağlar. Gelir "
"tahakkuklarında gerçekleşen taksitleri takip eder ve bu taksit kalemleri "
"için yevmiye kayıtları oluşturur."

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_complete_tax_set
msgid ""
"This boolean helps you to choose if you want to propose to the user to "
"encode the sale and purchase rates or choose from list of taxes. This last "
"choice assumes that the set of tax defined on this template is complete"
msgstr ""
"Kullanıcıya satış ve satın alma vergi oranlarını kodlamayı ya da vergi "
"listesinden seçmeyi önerecekseniz bu seçimi kullanabilirsiniz. İkinci "
"seçenek bu şablonda bütün vergilerin tam olarak tanımlandığını varsayar."

#. module: account
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_complete_tax_set
msgid ""
"This boolean helps you to choose if you want to propose to the user to "
"encode the sales and purchase rates or use the usual m2o fields. This last "
"choice assumes that the set of tax defined for the chosen template is "
"complete"
msgstr ""
"Eğer kullanıcıya satış ve satın alma oranlarını şifrelemeyi ya da olağan m2o"
" alanlarını kullanmayı önerecekseniz bu mantıksal işlem size yardım eder. Bu"
" son seçim, seçilen şablon için vergi setinin tamamlandığını var sayar."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "This feature is useful if you issue a high amounts of invoices."
msgstr ""
"Bu özellik, çok miktarda fatura kesiyorsanız alacaklarınızın takibi için "
"kullanışlıdır."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_refund_sequence_id
msgid ""
"This field contains the information related to the numbering of the credit "
"note entries of this journal."
msgstr ""
"Bu alan, bu yevmiyenin iade/fiyat farkı kayıtları numaralandırılması ile "
"ilgili bilgileri içerir."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_sequence_id
msgid ""
"This field contains the information related to the numbering of the journal "
"entries of this journal."
msgstr ""
"Bu alan, bu yevmiyeye ait yevmiye girişleri numaralarının verilmesiyle "
"ilgili bilgi içerir."

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model_journal_id
#: model:ir.model.fields,help:account.field_account_reconcile_model_second_journal_id
msgid "This field is ignored in a bank statement reconciliation."
msgstr "Bu alan bir banka hesap özeti uzlaştırmasında dikkate alınmaz."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_date_maturity
msgid ""
"This field is used for payable and receivable journal entries. You can put "
"the limit date for the payment of this line."
msgstr ""
"Bu alan borç ve alacak yevmiye kayıtları için kullanılır. Bu satıra ait "
"ödemeye ödeme sınırlama tarihi koyabilirsiniz."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_partner_name
msgid ""
"This field is used to record the third party name when importing bank "
"statement in electronic format, when the partner doesn't exist yet in the "
"database (or cannot be found)."
msgstr ""
"Bu alan, elektronik biçimde banka hesap özeti içe aktarılırken, eğer İş "
"Ortağı veritabanında yoksa (yada bulunamazsa) üçüncü partinin adını "
"kaydetmek için kullanılır."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"This guide will help you get started with Odoo Accounting.\n"
"                        Once you're done, you'll benefit from:"
msgstr ""
"Bu kılavuz, Odoo Muhasebe ile başlamanıza yardımcı olacaktır. \\ N İşiniz "
"bittiğinde, aşağıdakilerden yararlanacaksınız:"

#. module: account
#: model:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid ""
"This is the accounting dashboard. If you have not yet\n"
"                installed a chart of account, please install one first."
msgstr ""
"Bu muhasebe panelidir. Henüz bir hesap planı yüklemediyseniz, \n"
"lütfen bir tane yükleyiniz."

#. module: account
#: code:addons/account/models/account.py:494
#, python-format
msgid ""
"This journal already contains items, therefore you cannot modify its "
"company."
msgstr ""
"Bu yevmiyede zaten öğeler var, bu nedenle şirketini değiştiremezsiniz."

#. module: account
#: code:addons/account/models/account.py:503
#, python-format
msgid ""
"This journal already contains items, therefore you cannot modify its short "
"name."
msgstr "Bu günlüğe zaten öğeler var, bu nedenle kısa adını değiştiremezsiniz."

#. module: account
#: model:ir.model.fields,help:account.field_accounting_report_label_filter
msgid ""
"This label will be displayed on report to show the balance computed for the "
"given comparison filter."
msgstr ""
"Bu etiket, verilen karşılaştırma süzgeci için hesaplanan bakiyeyi gösteren "
"raporda görüntülenecektir."

#. module: account
#: code:addons/account/models/account_payment.py:533
#, python-format
msgid ""
"This method should only be called to process a single invoice's payment."
msgstr "Bu yöntem sadece bir fatura ödemesini işlemek için çağrılmalıdır. "

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:395
#, python-format
msgid ""
"This move's amount is higher than the transaction's amount. Click to "
"register a partial payment and keep the payment balance open."
msgstr ""
"Bu hareketin tutarı, işlemin tutarı kadar yüksek. Kısmi bir ödemeyi "
"kaydetmek ve ödeme bakiyesini açık tutmak için tıklayın."

#. module: account
#: model:ir.model.fields,help:account.field_accounting_report_debit_credit
msgid ""
"This option allows you to get more details about the way your balances are "
"computed. Because it is space consuming, we do not allow to use it while "
"doing a comparison."
msgstr ""
"Bu seçenek bakiyelerinizin hesaplama yolu için daha çok ayrıntı almanıza "
"izin verir. Fazla yer kapladığı için karşılaştırma yapılırken kullanımına "
"izin vermiyoruz."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_chart_template_id
msgid ""
"This optional field allow you to link an account template to a specific "
"chart template that may differ from the one its root parent belongs to. This"
" allow you to define chart templates that extend another and complete it "
"with few new accounts (You don't need to define the whole structure that is "
"common to both several times)."
msgstr ""
"Bu seçime bağlı alan,  bir hesap şablonunu ait olduğu ana kökten farklı "
"olabilecek belirli bir tablo şablonuna bağlamanızı sağlar. Bu, başka bir "
"hesaba genişlemesini ve bir kaç yeni hesapla tamamlanacak tablo şablonları "
"tanımlamanızı sağlar (Birçok kez her ikisinde de ortak olan tüm yapıyı "
"tanımlamanız gerekmez)."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:35
#, python-format
msgid ""
"This page displays all the bank transactions that are to be reconciled and "
"provides with a neat interface to do so."
msgstr ""
"Bu sayfa uzlaştırılacak tüm banka işlemlerini gösterir ve bu işlemi rahat "
"bir şekilde yapmanız için temiz bir arayüz sunar."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:240
#, python-format
msgid "This payment is registered but not reconciled."
msgstr "Bu ödeme kaydedilmiş ancak mutabık kılınmamıştır."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_supplier_payment_term_id
#: model:ir.model.fields,help:account.field_res_users_property_supplier_payment_term_id
msgid ""
"This payment term will be used instead of the default one for purchase "
"orders and vendor bills"
msgstr ""
"Bu ödeme koşulu satınalma siparişleri ve satıcı faturaları için varsayılan "
"fiyat yerine kullanılacaktır"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_payment_term_id
#: model:ir.model.fields,help:account.field_res_users_property_payment_term_id
msgid ""
"This payment term will be used instead of the default one for sales orders "
"and customer invoices"
msgstr ""
"Bu ödeme dönemi, satış siparişleri ve müşteri faturaları için varsayılan "
"olarak kullanılacaktır."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"This role is best suited for managing the day to day accounting operations:"
msgstr ""
"Bu rol, yevmiye muhasebe işlemlerini yönetmek için en uygun yöntemdir:"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_account_id
msgid ""
"This technical field can be used at the statement line creation/import time "
"in order to avoid the reconciliation process on it later on. The statement "
"line will simply create a counterpart on this account"
msgstr ""
"Bu teknik alan, hesap özeti satırı oluşturulduğu/içe aktarıldığı zaman daha "
"sonra üzerinde uzlaştırma olmaması için kullanılır. Hesap özeti satırı "
"basitçe bu hesap üzerinde bir karşı hesap oluşturacaktır"

#. module: account
#: model:ir.actions.act_window,help:account.action_validate_account_move
msgid ""
"This wizard will validate all journal entries selected. Once journal entries"
" are validated, you can not update them anymore."
msgstr ""
"Bu sihirbaz, seçilen tüm yevmiye girişlerini doğrulayacaktır. Yevmiye "
"girdileri doğrulandıktan sonra bunları artık güncelleyemezsiniz."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_reconcile_model
msgid ""
"Those can be used to quickly create a journal items when reconciling\n"
"                a bank statement or an account."
msgstr ""
"Bunlar, banka hesap özetini veya bir hesabı uzlaştırırken bir yevmiye "
"öğeleri oluşturmak için kullanılabilir."

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_account_hide_setup_bar
msgid "Tick if you wish to hide the setup bar on the dashboard"
msgstr "Eğer panel üzerinde kurulum barını gizlemek isterseniz tikleyiniz."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:31
#, python-format
msgid "Tip: Hit CTRL-Enter to reconcile all the balanced items in the sheet."
msgstr ""
"İpucu: Sayfadaki dengelenmiş öğeleri mutabık kalmak için CTRL-Enter tuşuna "
"basın."

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Title 2 (bold)"
msgstr "Başlık 2 (koyu)"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Title 3 (bold, smaller)"
msgstr "Başlık 3 (koyu, daha küçük)"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "To Invoice"
msgstr "Faturalanacak"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "To Pay"
msgstr "Öde"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"To manage the tax applied when invoicing a Company, Odoo uses the concept of"
" Fiscal Position: they serve to automatically set the right tax and/or "
"account according to the customer country and state."
msgstr ""
"Odoo, bir şirketi fatura ederken uygulanan vergiyi yönetmek için Mali Durum "
"kavramını kullanır: müşterinin ülkesine ve eyaletine göre otomatik olarak "
"doğru vergi ve / veya hesabı ayarlar."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "To pay"
msgstr "Öde"

#. module: account
#: code:addons/account/models/account_move.py:1017
#, python-format
msgid "To reconcile the entries company should be the same for all entries!"
msgstr "Kayıtları eşitlemek için şirket tüm kayıtlar için aynı olmalıdır!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "To use the <strong>multi-currency option:</strong>"
msgstr "<Strong> Çok Para Birimi Seçeneği'ni kullanmak için: </ strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Today Activities"
msgstr "Bugünkü Aktiviteler"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_total
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
msgid "Total"
msgstr "Toplam"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_tree
msgid "Total Amount"
msgstr "Toplam Tutar"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_lines_tree
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_tree
msgid "Total Credit"
msgstr "Toplam Alacak"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_lines_tree
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_tree
msgid "Total Debit"
msgstr "Toplam Borç"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_total_invoiced
#: model:ir.model.fields,field_description:account.field_res_users_total_invoiced
msgid "Total Invoiced"
msgstr "Toplam Faturalanan"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_debit
#: model:ir.model.fields,field_description:account.field_res_users_debit
msgid "Total Payable"
msgstr "Toplam Borç"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_credit
#: model:ir.model.fields,field_description:account.field_res_users_credit
msgid "Total Receivable"
msgstr "Toplam Alacak"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_currency_residual
msgid "Total Residual"
msgstr "Toplam Kalan"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_price_total
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_currency_price_total
msgid "Total Without Tax"
msgstr "Vergisiz Toplam"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_price_subtotal_signed
msgid "Total amount in the currency of the company, negative for credit note."
msgstr ""
"Şirketinizin para birimindeki toplam miktar, kredi notu için olumsuzdur."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_amount_total_company_signed
msgid ""
"Total amount in the currency of the company, negative for credit notes."
msgstr ""
"Şirketinizin para birimindeki toplam miktar, kredi notu için olumsuzdur."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_amount_total_signed
msgid ""
"Total amount in the currency of the invoice, negative for credit notes."
msgstr "Fatura para biriminin toplam miktarı, kredi notu için olumsuzdur."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_credit
#: model:ir.model.fields,help:account.field_res_users_credit
msgid "Total amount this customer owes you."
msgstr "Bu müşterinin size toplam borç tutarı"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_price_total
msgid "Total amount with taxes"
msgstr "Vergi Dahil Tutar"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_price_subtotal
msgid "Total amount without taxes"
msgstr "Vergi Hariç Tutar"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_debit
#: model:ir.model.fields,help:account.field_res_users_debit
msgid "Total amount you have to pay to this vendor."
msgstr "Bu tedarikçiye ödenmesi gereken toplam meblağ."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_total_company_signed
msgid "Total in Company Currency"
msgstr "Şirket Para Birimi Toplamı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_total_signed
msgid "Total in Invoice Currency"
msgstr "Fatura Para Birimi Toplam"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_total_entry_encoding
msgid "Total of transaction lines."
msgstr "İşlem satırlarının toplamı."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Track costs &amp; revenues by project, department, etc."
msgstr "Proje ve departmanlara göre maliyet ve gelir takibi."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:250
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#, python-format
msgid "Transaction"
msgstr "İşlem"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "Transactions"
msgstr "İşlemler"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_total_entry_encoding
msgid "Transactions Subtotal"
msgstr "İşlemler Alttoplamı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_transfer_account_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_transfer_account_id
msgid "Transfer Account"
msgstr "Transfer Hesabı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_destination_journal_id
msgid "Transfer To"
msgstr "... ya havale yap"

#. module: account
#: code:addons/account/models/account_payment.py:357
#, python-format
msgid "Transfer account not defined on the company."
msgstr "Transfer hesabı şirket ayarlarında tanımlanmamış."

#. module: account
#: code:addons/account/models/account_payment.py:618
#, python-format
msgid "Transfer from %s"
msgstr "%s aktarma"

#. module: account
#: code:addons/account/models/account_payment.py:699
#, python-format
msgid "Transfer to %s"
msgstr "%s 'e Havale Yap"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Transfers"
msgstr "Havaleler"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_balance_menu
#: model:ir.actions.report,name:account.action_report_trial_balance
#: model:ir.ui.menu,name:account.menu_general_Balance_report
msgid "Trial Balance"
msgstr "Geçici Mizan"

#. module: account
#: model:ir.model,name:account.model_account_balance_report
msgid "Trial Balance Report"
msgstr "Geçici Mizan"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_user_type_id
#: model:ir.model.fields,field_description:account.field_account_account_type_type
#: model:ir.model.fields,field_description:account.field_account_account_user_type_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_journal_type
#: model:ir.model.fields,field_description:account.field_account_financial_report_type
#: model:ir.model.fields,field_description:account.field_account_invoice_line_invoice_type
#: model:ir.model.fields,field_description:account.field_account_invoice_report_type
#: model:ir.model.fields,field_description:account.field_account_invoice_type
#: model:ir.model.fields,field_description:account.field_account_journal_type
#: model:ir.model.fields,field_description:account.field_account_move_line_user_type_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_value
msgid "Type"
msgstr "Tür"

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "UP"
msgstr "UP"

#. module: account
#: selection:account.journal,bank_statements_source:0
msgid "Undefined Yet"
msgstr "Tanımlanmamış"

#. module: account
#: code:addons/account/models/company.py:367
#, python-format
msgid "Undistributed Profits/Losses"
msgstr "Dağıtılmamış Kâr / Zararlar"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:392
#, python-format
msgid "Undo the partial reconciliation."
msgstr "Kısmi uzlaştırmayı geri al."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_unit
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Unit Price"
msgstr "Birim Fiyat"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_product_uom_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_uom_id
#: model:ir.model.fields,field_description:account.field_account_move_line_product_uom_id
msgid "Unit of Measure"
msgstr "Ölçü Birimi"

#. module: account
#: code:addons/account/report/account_aged_partner_balance.py:194
#, python-format
msgid "Unknown Partner"
msgstr "Bilinmeyen İş Ortağı"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Unless you are starting a new business, you probably have a list of "
"customers and vendors you'd like to import."
msgstr ""
"Eğer yeni bir işe başlamazsanız, muhtemelen ithalat yapmak istediğiniz "
"müşterilerin ve sağlayıcıların bir listesi muhtemelen vardır."

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model:ir.ui.view,arch_db:account.setup_view_company_form
msgid "Unmark as done"
msgstr "Yapıldı işaretini kaldır"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_invoice_opened
msgid "Unpaid Invoices"
msgstr "Ödenmemiş Faturalar"

#. module: account
#: selection:account.move,state:0
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unposted"
msgstr "Onay Bekleyen"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Unposted Journal Entries"
msgstr "Onay Bekleyen Yevmiye Girişleri"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unposted Journal Items"
msgstr "Onay Bekleyen Yevmiye Kalemleri"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:81
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
#, python-format
msgid "Unreconcile"
msgstr "Uzlaşmayı Kaldır"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_unreconcile
msgid "Unreconcile Entries"
msgstr "Kayıtlardan Uzlaştırmayı Kaldır"

#. module: account
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
msgid "Unreconcile Transactions"
msgstr "İşlemlerin Uzlaştırmasını kaldır"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unreconciled"
msgstr "Uzlaştırılmamış"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_acount_move_line_open_unreconciled
msgid "Unreconciled Entries"
msgstr "Uzlaştırılmamış Girişler"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_untaxed
msgid "Untaxed Amount"
msgstr "Vergi Hariç Tutar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_untaxed_signed
msgid "Untaxed Amount in Company Currency"
msgstr "Şirket Para Biriminde Vergi Hariç Tutar"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Update exchange rates automatically"
msgstr "Döviz kurlarını otomatik olarak güncelle"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Use"
msgstr "Kullan"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_use_anglo_saxon
msgid "Use Anglo-Saxon Accounting"
msgstr "Anglo-Saxon muhasebe biçimini kullan"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_use_anglo_saxon
msgid "Use Anglo-Saxon accounting"
msgstr "Anglo-Saxon muhasebe biçimini kullan"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_tax_exigibility
msgid "Use Cash Basis"
msgstr "Gerçekleşme Esası (Cash Basis) Kullan"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_sepa_direct_debit
msgid "Use SEPA Direct Debit"
msgstr "SEPA ödemelerini kullan"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal_journal_id
msgid "Use Specific Journal"
msgstr "Özel Yevmiye Kullan"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_anglo_saxon_accounting
msgid "Use anglo-saxon accounting"
msgstr ""
"Anglo-Saxon muhasebe kullanın (Türkiye'deki yasal mevzuata uygun değildir)"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_batch_deposit
msgid "Use batch deposit"
msgstr "Toplu mevduatı kullan"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Use depreciation boards, automate amortization entries"
msgstr ""
"Amortisman şablonlarını kullanın, amortisman kayıtlarını otomatikleştirin"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Use follow-up levels and schedule actions"
msgstr "Takip düzeyleri ve otomatik eylemleri kullanın"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Use in conjunction with contracts to calculate your monthly revenue for "
"multi-month contracts."
msgstr ""
"Çok aylık sözleşmeler için aylık gelirinizi hesaplamak için sözleşmelerle "
"birlikte kullanın."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"Use this option if you want to cancel an invoice and create a new\n"
"                                one. The credit note will be created, validated and reconciled\n"
"                                with the current invoice. A new draft invoice will be created\n"
"                                so that you can edit it."
msgstr ""
"Eğer bir faturayı iptal etmek ve yeni bir tane oluşturmak istiyorsanız bu "
"seçeneği seçin. Güncel fatura ile birlikte iade/fiyat farkı oluşturulacak, "
"doğrulanacak ve uzlaştırılacaktır. Yeni bir taslak fatura oluşturulacak, bu "
"sayede düzenleme yapabilirsiniz."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"Use this option if you want to cancel an invoice you should not\n"
"                                have issued. The credit note will be created, validated and reconciled\n"
"                                with the invoice. You will not be able to modify the credit note."
msgstr ""
"Verememeniz gereken bir faturayı iptal etmek istiyorsanız bu seçeneği "
"kullanın. Kredi notu fatura ile oluşturulacak, onaylanacak ve mutabık "
"kılınacaktır \\ n. Kredi notunu değiştiremezsiniz."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_type_include_initial_balance
msgid ""
"Used in reports to know if we should consider journal items from the "
"beginning of time instead of from the fiscal year only. Account types that "
"should be reset to zero at each new fiscal year (like expenses, revenue..) "
"should not have this option set."
msgstr ""
"Raporlarda yalnızca yevmiye olarak değil, zamanın başından güncül "
"kalemlerini düşünmemiz gerektiğini bilmek için kullanılır. Her yeni mali "
"yılda sıfıra sıfırlanması gereken hesap türleri (giderler, gelirler gibi) bu"
" seçeneğin ayarlanmaması gerekir."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_reference
msgid ""
"Used to hold the reference of the external mean that created this statement "
"(name of imported file, reference of online synchronization...)"
msgstr ""
"Bu ifadeyi oluşturan dış ortalamanın referansını tutmak için kullanılır (içe"
" aktarılan dosyanın adı, çevrimiçi senkronizasyon referans ...)"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_sequence
msgid "Used to order Journals in the dashboard view"
msgstr "Panelde yevmiyeleri sıralamak için kullanılır"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_loss_account_id
msgid ""
"Used to register a loss when the ending balance of a cash register differs "
"from what the system computes"
msgstr ""
"Kasa biten bakiyesi sistemin hesapladığı değerden farklı olduğunda zarar "
"kaydetmek için kullanılır"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_profit_account_id
msgid ""
"Used to register a profit when the ending balance of a cash register differs"
" from what the system computes"
msgstr ""
"Kasa biten bakiyesi sistemin hesapladığı değerden farklı olduğunda kar "
"kaydetmek için kullanılır"

#. module: account
#: model:ir.model.fields,help:account.field_account_analytic_line_company_currency_id
#: model:ir.model.fields,help:account.field_account_bank_statement_line_journal_currency_id
#: model:ir.model.fields,help:account.field_account_move_line_company_currency_id
#: model:ir.model.fields,help:account.field_account_partial_reconcile_company_currency_id
#: model:ir.model.fields,help:account.field_res_partner_currency_id
#: model:ir.model.fields,help:account.field_res_users_currency_id
msgid "Utility field to express amount currency"
msgstr "Miktar para birimini ifade etmek için kullanım alanı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_vat_required
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_vat_required
msgid "VAT required"
msgstr "Vergi No Zorunlu"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
msgid "VAT:"
msgstr "Vergi No :"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:72
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#, python-format
msgid "Validate"
msgstr "Doğrula"

#. module: account
#: model:ir.model,name:account.model_validate_account_move
msgid "Validate Account Move"
msgstr "Hesap Hareketini Doğrula"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Validate purchase orders and control vendor bills by departments."
msgstr ""
"Satınalma siparişlerini doğrulayın ve satıcı faturaları departmanlara göre "
"kontrol edin."

#. module: account
#: selection:account.bank.statement,state:0
#: model:mail.message.subtype,name:account.mt_invoice_validated
msgid "Validated"
msgstr "Doğrulanmış"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_value_amount
msgid "Value"
msgstr "Değer"

#. module: account
#: selection:account.abstract.payment,partner_type:0
#: selection:account.payment,partner_type:0
#: selection:account.register.payments,partner_type:0
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_supplier_payment_tree
msgid "Vendor"
msgstr "Tedarikçi"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_invoice.py:443
#: code:addons/account/models/account_invoice.py:1215
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Vendor Bill"
msgstr "Tedarikçi Faturası"

#. module: account
#: code:addons/account/models/account_invoice.py:444
#, python-format
msgid "Vendor Bill - %s"
msgstr "Tedarikçi Faturası - %s"

#. module: account
#: code:addons/account/models/chart_template.py:194
#: model:ir.actions.act_window,name:account.action_invoice_tree2
#: model:ir.ui.menu,name:account.menu_action_invoice_tree2
#: model:ir.ui.view,arch_db:account.account_planner
#, python-format
msgid "Vendor Bills"
msgstr "Tedarikçi  Faturaları"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_invoice.py:445
#: code:addons/account/models/account_payment.py:680
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Vendor Credit Note"
msgstr "Tedarikçi İade/Fiyat Farkı"

#. module: account
#: code:addons/account/models/account_invoice.py:446
#, python-format
msgid "Vendor Credit Note - %s"
msgstr "Tedarikçi İade/Fiyat Farkı - %s"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_in_refund
#: model:ir.ui.menu,name:account.menu_action_invoice_in_refund
msgid "Vendor Credit Notes"
msgstr "Tedarikçi İade/Fiyat Farkı Faturaları"

#. module: account
#: code:addons/account/models/account_invoice.py:1217
#, python-format
msgid "Vendor Credit note"
msgstr "Tedarikçi İade/Fiyat Farkı"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Vendor Flow"
msgstr "Tedarikçi Akışı"

#. module: account
#: code:addons/account/models/account_payment.py:682
#, python-format
msgid "Vendor Payment"
msgstr "Tedarikçi Ödemesi"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_supplier_payment_term_id
#: model:ir.model.fields,field_description:account.field_res_users_property_supplier_payment_term_id
msgid "Vendor Payment Terms"
msgstr "Tedarikçi Ödeme Şartları"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_reference
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Vendor Reference"
msgstr "Tedarikçi Referansı"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_product_supplier_taxes_id
#: model:ir.model.fields,field_description:account.field_product_template_supplier_taxes_id
msgid "Vendor Taxes"
msgstr "Tedarikçi Vergileri"

#. module: account
#: model:ir.ui.menu,name:account.menu_account_supplier
msgid "Vendors"
msgstr "Tedarikçiler"

#. module: account
#: selection:account.financial.report,type:0
msgid "View"
msgstr "Görünüm"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "Warning"
msgstr "Uyarı"

#. module: account
#: code:addons/account/models/account_invoice.py:662
#, python-format
msgid "Warning for %s"
msgstr "%s için uyarı"

#. module: account
#: model:ir.ui.view,arch_db:account.partner_view_buttons
msgid "Warning on the Invoice"
msgstr "Faturada Uyarı"

#. module: account
#: code:addons/account/models/account_invoice.py:1554
#: code:addons/account/models/account_invoice.py:1611
#, python-format
msgid "Warning!"
msgstr "Uyarı!"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_group_warning_account
msgid "Warnings"
msgstr "Uyarılar"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"We can handle the whole import process\n"
"                                        for you: simply send your Odoo project\n"
"                                        manager a CSV file containing all your\n"
"                                        data."
msgstr ""
"Bütün içe aktarma işleminizi sizin yerinize halledebiliriz: Odoo proje "
"yöneticinize tüm verilerinizi içeren bir CSV dosyası göndermeniz yeterlidir"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"We can handle the whole import process\n"
"                                        for you: simply send your Odoo project\n"
"                                        manager a CSV file containing all your\n"
"                                        products."
msgstr ""
"Tüm alma işleminizi sizin yerinize yapabiliriz \\ n: Basitçe Odoo project \\"
" n yöneticinizi tüm \\ n ürünlerinizi içeren bir CSV dosyasına gönderin."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "We hope this tool helped you implement our accounting application."
msgstr ""
"Umarız bu araç, muhasebe uygulamamızı gerçekleştirmenize yardımcı olmuştur."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Welcome"
msgstr "Hoş Geldiniz"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"When inviting users, you will need to define which access rights they are allowed to have. \n"
"                        This is done by assigning a role to each user."
msgstr ""
"Kullanıcıları davet ederken, izin verilen erişim haklarını tanımlamanız "
"gerekir. \\ N Bu, her bir kullanıcıya bir rol atayarak yapılır."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_show_on_dashboard
msgid "Whether this journal should be displayed on the dashboard or not"
msgstr "Bu yevmiyenin gösterge tablosunda gösterilip gösterilmemesi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_amount_currency
#: model:ir.model.fields,field_description:account.field_account_print_journal_amount_currency
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_amount_currency
msgid "With Currency"
msgstr "Para Birimi ile"

#. module: account
#: selection:account.balance.report,display_account:0
#: selection:account.common.account.report,display_account:0
#: selection:account.report.general.ledger,display_account:0
msgid "With balance is not equal to 0"
msgstr "Sıfır bakiyeler hariç"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "With balance not equal to zero"
msgstr "Sıfır bakiyeler hariç"

#. module: account
#: selection:account.balance.report,display_account:0
#: selection:account.common.account.report,display_account:0
#: selection:account.report.general.ledger,display_account:0
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "With movements"
msgstr "Hareketlerle"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_search
msgid "With tax"
msgstr "Vergili"

#. module: account
#: model:ir.model,name:account.model_tax_adjustments_wizard
msgid "Wizard for Tax Adjustments"
msgstr "Manuel Vergi Düzeltmeleri Sihirbazı"

#. module: account
#: code:addons/account/models/account_move.py:1056
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
#, python-format
msgid "Write-Off"
msgstr "Borç Silme"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_journal_id
msgid "Write-Off Journal"
msgstr "Borç Silme Yevmiyesi"

#. module: account
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
msgid "Write-Off Move"
msgstr "Borç Silme Hareketi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_writeoff_acc_id
msgid "Write-Off account"
msgstr "Borç Silme Hesabı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff
msgid "Write-Off amount"
msgstr "Borç silme tutarı"

#. module: account
#: sql_constraint:account.move.line:0
msgid "Wrong credit or debit value in accounting entry !"
msgstr "Muhasebe kaydında hatalı alacak ya da Borç değeri!"

#. module: account
#: code:addons/account/models/account_move.py:1015
#, python-format
msgid "You are trying to reconcile some entries that are already reconciled!"
msgstr "Mutabık kalmış bazı girişleri mutabık kalmaya çalışıyorsunuz!"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_blocked
msgid ""
"You can check this box to mark this journal item as a litigation with the "
"associated partner"
msgstr ""
"İlşikili iş ortağıyla ilgili bir ihtilaf sözkonusu ise bu kutuyu "
"işaretleyerek bu yevmiye maddesine not düşebilirsiniz"

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree2
msgid ""
"You can control the invoice from your vendor according to\n"
"                what you purchased or received."
msgstr ""
"Satıcınıza faturayı, \\ n satın aldığınız ya da aldığınız göre kontrol "
"edebilirsiniz."

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
msgid "You can define additional accounts here"
msgstr "Burada ek hesapları tanımlayabilirsiniz."

#. module: account
#: code:addons/account/models/account_payment.py:468
#, python-format
msgid "You can not delete a payment that is already posted"
msgstr "İşlenmiş bir ödemeyi silemezsiniz"

#. module: account
#: code:addons/account/models/account_invoice.py:1633
#, python-format
msgid "You can only delete an invoice line if the invoice is in draft state."
msgstr "Fatura taslağı durumunda fatura satırını silebilirsiniz."

#. module: account
#: code:addons/account/models/account_payment.py:141
#, python-format
msgid "You can only register payments for open invoices"
msgstr "Yalnızca açık faturalar için ödemeleri kaydedebilirsiniz"

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_report_style_overwrite
msgid ""
"You can set up here the format you want this record to be displayed. If you "
"leave the automatic formatting, it will be computed based on the financial "
"reports hierarchy (auto-computed field 'level')."
msgstr ""
"Bu kaydın görüntülenmesini istediğiniz biçimi buradan ayarlayabilirsiniz. "
"Eğer otomatik biçimlemede bırakırsanız, mali tabloların sıradüzeni yapısına "
"göre hesaplanacaktır (otomatik hesaplanan alan 'seviye')."

#. module: account
#: code:addons/account/models/account_move.py:209
#, python-format
msgid ""
"You cannot add/modify entries prior to and inclusive of the lock date %s"
msgstr ""
"Kilitlenme tarihi %s öncesinde ve bu tarihin kapsanmadığı girdileri "
"ekleyemez / değiştiremezsiniz"

#. module: account
#: code:addons/account/models/account_move.py:211
#, python-format
msgid ""
"You cannot add/modify entries prior to and inclusive of the lock date %s. "
"Check the company settings or ask someone with the 'Adviser' role"
msgstr ""
"Kilit tarihi %s öncesinde ve bu tarihin kapsanmadığı girdileri ekleyemez / "
"değiştiremezsiniz. Şirket ayarlarını kontrol edin veya 'Müşavir' rolüyle "
"birine sorun"

#. module: account
#: code:addons/account/models/account_invoice.py:1196
#, python-format
msgid ""
"You cannot cancel an invoice which is partially paid. You need to "
"unreconcile related payment entries first."
msgstr ""
"Kısmen ödenmiş bir faturayı iptal edemezsiniz! Önce ödeme kayıtlarından "
"uzlaşmayı kaldırmalısınız!"

#. module: account
#: code:addons/account/models/company.py:200
#, python-format
msgid ""
"You cannot change the currency of the company since some journal items "
"already exist"
msgstr ""
"Bazı yevmiye kalemleri oluşturulduktan sonra şirketinizin para birimini "
"değiştiremezsiniz."

#. module: account
#: code:addons/account/models/account.py:235
#, python-format
msgid ""
"You cannot change the owner company of an account that already contains "
"journal items."
msgstr "Hareket görmüş bir hesabın bağlı olduğu şirketi değiştiremezsiniz."

#. module: account
#: code:addons/account/models/account.py:242
#, python-format
msgid ""
"You cannot change the value of the reconciliation on this account as it "
"already has some moves"
msgstr ""
"Bu hesaptaki mutabakat değerini değiştiremezsiniz çünkü zaten bazı hamle "
"var."

#. module: account
#: code:addons/account/models/account_move.py:500
#, python-format
msgid ""
"You cannot create journal items with a secondary currency without filling "
"both 'currency' and 'amount currency' field."
msgstr ""
"Hem 'para birimi' hem de \"miktar para birimi\" alanını doldurmadan ikincil "
"bir para birimiyle yevmiye öğeleri oluşturamazsınız."

#. module: account
#: code:addons/account/models/company.py:120
#, python-format
msgid ""
"You cannot define stricter conditions on advisors than on users. Please make"
" sure that the lock date on advisor is set before the lock date for users."
msgstr ""
"Müşavirler için, kullanıcılardan daha katı kurallar tanımlayamazsınız. "
"Lütfen, müşavirler için tanımladığınız kilit tarihinin, kullanıcılar için "
"tanımlanan kilit tarihinden önce olmadığından emin olun."

#. module: account
#: code:addons/account/models/account_invoice.py:613
#, python-format
msgid ""
"You cannot delete an invoice after it has been validated (and received a "
"number). You can set it back to \"Draft\" state and modify its content, then"
" re-confirm it."
msgstr ""
"Onaylandıktan (ve bir numara aldıktan sonra) bir faturayı silemezsiniz. \\ "
"\"Taslak \" durumuna geri getirebilir ve içeriğini değiştirebilir, ardından "
"yeniden onaylayabilirsiniz."

#. module: account
#: code:addons/account/models/account_invoice.py:611
#, python-format
msgid ""
"You cannot delete an invoice which is not draft or cancelled. You should "
"create a credit note instead."
msgstr ""
"Durumu Taslak veya İptal olmayan bir faturayı iptal edemezsiniz. Bunun "
"yerine bir fiyat farkı oluşturabilirsiniz."

#. module: account
#: code:addons/account/models/res_config_settings.py:133
#, python-format
msgid ""
"You cannot disable this setting because some of your taxes are cash basis. "
"Modify your taxes first before disabling this setting."
msgstr ""
"Bu ayarı devre dışı bırakamazsınız, çünkü vergilerinizin bir kısmı "
"gerçekleşme esaslı. Bu ayarı devre dışı bırakmadan önce vergilerinizi "
"değiştiriniz."

#. module: account
#: code:addons/account/models/account.py:248
#, python-format
msgid "You cannot do that on an account that contains journal items."
msgstr "Bunu, yevmiye öğeleri içeren bir hesapta yapamazsınız."

#. module: account
#: code:addons/account/models/account_move.py:1364
#, python-format
msgid ""
"You cannot do this modification on a posted journal entry, you can just change some non legal fields. You must revert the journal entry to cancel it.\n"
"%s."
msgstr ""
"Bu değişikliği uzlaştırılmış yevmiye kaydında yapamazsınız, diğer bazı alanları değiştirebilirsiniz. İptal etmek için yevmiye kaydını geri almalısınız. \n"
"%s ."

#. module: account
#: code:addons/account/models/account_move.py:1366
#, python-format
msgid ""
"You cannot do this modification on a reconciled entry. You can just change some non legal fields or you must unreconcile first.\n"
"%s."
msgstr ""
"Uzlaştırılmış bir kayıt için bu değişikliği yapamazsınız! Diğer bazı alanları değiştirebilirsiniz ya da önce uzlaşmayı kaldırmalısınız\n"
"%s"

#. module: account
#: code:addons/account/models/account.py:518
#, python-format
msgid "You cannot empty the bank account once set."
msgstr "Ayarlar yapıldıktan sonra banka hesaplarını boşaltamazsınız. "

#. module: account
#: code:addons/account/models/account.py:55
#, python-format
msgid ""
"You cannot have a receivable/payable account that is not reconcilable. "
"(account code: %s)"
msgstr ""
"Uzlaştırılamayan bir alacak / borç hesabına sahip olamazsınız. (hesap kodu: "
"%s)"

#. module: account
#: code:addons/account/models/company.py:109
#, python-format
msgid ""
"You cannot lock a period that is not finished yet. Please make sure that the"
" lock date for advisors is not set after the last day of the previous month."
msgstr ""
"Henüz bitmemiş bir dönemi kilitleyemezsiniz. Lütfen müşavirler için kilit "
"tarihini, önceki ayın son gününden sonraya ayarlamadığınızdan emin olun."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:701
#, python-format
msgid "You cannot mix items from receivable and payable accounts."
msgstr "Alacaklı ve borç hesaplarındaki maddeleri karıştıramazsınız."

#. module: account
#: code:addons/account/models/account_move.py:172
#, python-format
msgid ""
"You cannot modify a posted entry of this journal.\n"
"First you should set the journal to allow cancelling entries."
msgstr ""
"Bu yevmiyeye ait işlenmiş bir kaydı değiştiremezsiniz !\n"
"Bunu yapmak istiyorsanız ilgili yevmiye üzerinden yevmiye kayıtlarının iptaline izin vermelisiniz."

#. module: account
#: code:addons/account/models/account_invoice.py:789
#, python-format
msgid ""
"You cannot pay an invoice which is partially paid. You need to reconcile "
"payment entries first."
msgstr ""
"Kısmen ödenen bir faturayı ödeyemezsiniz. Önce ödeme girişlerini mutabık "
"kılmanız gerekir."

#. module: account
#: code:addons/account/wizard/pos_box.py:36
#, python-format
msgid "You cannot put/take money in/out for a bank statement which is closed."
msgstr ""
"Kapalı bir banka hesap özetinde para gönderme ve alma işlemi yapamazsınız."

#. module: account
#: code:addons/account/models/account.py:261
#, python-format
msgid ""
"You cannot remove/deactivate an account which is set on a customer or "
"vendor."
msgstr ""
"Bir müşteri veya satıcı üzerinde ayarlanmış bir hesabı kaldıramaz / devre "
"dışı bırakamazsınız."

#. module: account
#: code:addons/account/models/account.py:249
#, python-format
msgid ""
"You cannot set a currency on this account as it already has some journal "
"entries having a different foreign currency."
msgstr ""
"Farklı para biriminde yevmiye kayıtları olduğu için bu hesap üzerinde bir "
"para birimi tanımlayamazsınız."

#. module: account
#: code:addons/account/models/account_move.py:1368
#, python-format
msgid "You cannot use deprecated account."
msgstr "Kullanımdan kaldırılmış hesap kullanılamaz."

#. module: account
#: code:addons/account/models/account_move.py:1283
#, python-format
msgid ""
"You cannot use this general account in this journal, check the tab 'Entry "
"Controls' on the related journal."
msgstr ""
"Bu yevmiyede bu genel hesabı kullanamazsınız, ilgili yevmiye üzerinde 'Giriş"
" Denetimleri' sekmesini kontrol ediniz."

#. module: account
#: code:addons/account/models/account_invoice.py:74
#: code:addons/account/models/account_invoice.py:780
#, python-format
msgid ""
"You cannot validate an invoice with a negative total amount. You should "
"create a credit note instead."
msgstr ""
"Negatif toplam tutar ile fatura doğrulayamazsınız. Onun yerine iade/fiyat "
"farkı oluşturmalısınız."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:158
#, python-format
msgid "You did not configure any reconcile model yet, you can do it"
msgstr ""
"Henüz herhangi bir uzlaştırma modeli yapılandırmadınız, bunu yapabilirsiniz"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "You have"
msgstr "Var"

#. module: account
#: code:addons/account/models/account_payment.py:505
#, python-format
msgid "You have to define a sequence for %s in your company."
msgstr "Şirketiniz için %s bir dizi tanımlamanız gerekmekte."

#. module: account
#: code:addons/account/wizard/account_report_general_ledger.py:21
#, python-format
msgid "You must define a Start Date"
msgstr "Bir başlangıç günü belirlemelisiniz."

#. module: account
#: code:addons/account/models/account_invoice.py:1555
#, python-format
msgid "You must first select a partner!"
msgstr "Önce bir iş ortağı seçmelisiniz !"

#. module: account
#: code:addons/account/wizard/account_report_aged_partner_balance.py:26
#, python-format
msgid "You must set a period length greater than 0."
msgstr "Dönem uzunluğunu 0 dan büyük ayarlamalısınız."

#. module: account
#: code:addons/account/wizard/account_report_aged_partner_balance.py:28
#, python-format
msgid "You must set a start date."
msgstr "Bir başlangıç tarihi girmelisiniz."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "You reconciled"
msgstr "Uzlaştırdınız"

#. module: account
#: code:addons/account/models/account_move.py:1884
#, python-format
msgid ""
"You should configure the 'Exchange Rate Journal' in the accounting settings,"
" to manage automatically the booking of accounting entries related to "
"differences between exchange rates."
msgstr ""
"Döviz kurları arasındaki farklara bağlı muhasebe kayıtlarının rezervasyonunu"
" otomatik olarak yönetmek için, muhasebe ayarlarında 'Döviz Kuru Dergisi'ni "
"yapılandırmanız gerekir."

#. module: account
#: code:addons/account/models/account_move.py:1886
#, python-format
msgid ""
"You should configure the 'Gain Exchange Rate Account' in the accounting "
"settings, to manage automatically the booking of accounting entries related "
"to differences between exchange rates."
msgstr ""
"Muhasebe girişlerini kur oranlarındaki değişimle ilişkili olarak otomatik "
"olarak yönetmek için 'Döviz Kuru Oranı Kazancı Hesabı'nı muhasebe "
"ayarlarından yapılandırmalısınız."

#. module: account
#: code:addons/account/models/account_move.py:1888
#, python-format
msgid ""
"You should configure the 'Loss Exchange Rate Account' in the accounting "
"settings, to manage automatically the booking of accounting entries related "
"to differences between exchange rates."
msgstr ""
"Muhasebe girişlerini kur oranlarındaki değişimle ilişkili olarak otomatik "
"olarak yönetmek için 'Döviz Kuru Oranı Zarar Hesabı'nı muhasebe ayarlarından"
" yapılandırmalısınız."

#. module: account
#: code:addons/account/wizard/pos_box.py:49
#: code:addons/account/wizard/pos_box.py:67
#, python-format
msgid ""
"You should have defined an 'Internal Transfer Account' in your cash "
"register's journal!"
msgstr "Kasa hesabınızda bir 'İç Transfer Hesabı' tanımlamalısınız!"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"You will be able to edit and validate this\n"
"                                credit note directly or keep it draft,\n"
"                                waiting for the document to be issued by\n"
"                                your supplier/customer."
msgstr ""
" Bu iade/fiyat farkını direk olarak, ya da taslak tutarak düzenleyebiliyor "
"ve doğrulayabiliyor olacaksınız. Tedarikçiniz/müşteriniz tarafından "
"verilecek belgeyi beklemek.."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Bank Accounts"
msgstr "Banka Hesaplarınız"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Company"
msgstr "Şirketiniz"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Customers"
msgstr "Müşterileriniz"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Products"
msgstr "Ürünleriniz"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Trial Balance (list of accounts and their balances)."
msgstr "Deneme Bakiyeniz (hesapların listesi ve bunların bakiyeleri)."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your company's legal name, tax ID, address, and logo."
msgstr "Şirketinizin yasal adı, vergi kimliği, adresi ve logosu."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your outstanding invoices, payments, and undeposited funds."
msgstr "Ödenmemiş faturalarınız, ödemeleriniz ve ayrılmamış fonlar."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Zip Range"
msgstr "Posta Kodu Aralığı"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_zip_from
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_zip_from
msgid "Zip Range From"
msgstr "PK Başlangıç"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_zip_to
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_zip_to
msgid "Zip Range To"
msgstr "PK Bitiş"

#. module: account
#: model:ir.model,name:account.model_account_bank_accounts_wizard
msgid "account.bank.accounts.wizard"
msgstr "account.bank.accounts.wizard"

#. module: account
#: model:ir.model,name:account.model_account_financial_year_op
msgid "account.financial.year.op"
msgstr "account.financial.year.op"

#. module: account
#: model:ir.model,name:account.model_account_group
msgid "account.group"
msgstr "account.group"

#. module: account
#: model:ir.model,name:account.model_account_opening
msgid "account.opening"
msgstr "account.opening"

#. module: account
#: model:ir.model,name:account.model_account_reconcile_model_template
msgid "account.reconcile.model.template"
msgstr "account.reconcile.model.template"

#. module: account
#: model:ir.model,name:account.model_account_tax_group
msgid "account.tax.group"
msgstr "account.tax.group"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "activate this feature"
msgstr "özelliği aktif et"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:17
#, python-format
msgid "assign to invoice"
msgstr "Fatura için ata"

#. module: account
#: model:ir.model,name:account.model_cash_box_in
msgid "cash.box.in"
msgstr "cash.box.in"

#. module: account
#: model:ir.model,name:account.model_cash_box_out
msgid "cash.box.out"
msgstr "cash.box.out"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_error
#: model:ir.ui.view,arch_db:account.portal_invoice_success
msgid "close"
msgstr "kapat"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "code"
msgstr "kod"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "create a journal entry"
msgstr "bir yevmiye kaydı oluştur"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "days"
msgstr "gün"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "e.g. Bank Fees"
msgstr "Örnek: Banka Masrafları"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "fast recording interface"
msgstr "hızlı kayıt arayüzü"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "first)"
msgstr "İlk)"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "for this customer. You can allocate them to mark this invoice as paid."
msgstr ""
"Bu müşteri için. Bu faturayı ödenmiş olarak işaretlemek için bunları tahsis "
"edebilirsiniz."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "for this supplier. You can allocate them to mark this bill as paid."
msgstr ""
"Bu tedarikçi için. Bu faturayı ödenmiş olarak işaretlemek için bunları "
"tahsis edebilirsiniz."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:41
#, python-format
msgid "o_manual_statement"
msgstr "o_manual_statement"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "outstanding debits"
msgstr "Olağanüstü borçlar"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "outstanding payments"
msgstr "Tamamlanmamış Ödemeler"

#. module: account
#: model:ir.model,name:account.model_report_account_report_agedpartnerbalance
msgid "report.account.report_agedpartnerbalance"
msgstr "report.account.report_agedpartnerbalance"

#. module: account
#: model:ir.model,name:account.model_report_account_report_financial
msgid "report.account.report_financial"
msgstr "report.account.report_financial"

#. module: account
#: model:ir.model,name:account.model_report_account_report_generalledger
msgid "report.account.report_generalledger"
msgstr "report.account.report_generalledger"

#. module: account
#: model:ir.model,name:account.model_report_account_report_journal
msgid "report.account.report_journal"
msgstr "report.account.report_journal"

#. module: account
#: model:ir.model,name:account.model_report_account_report_overdue
msgid "report.account.report_overdue"
msgstr "report.account.report_overdue"

#. module: account
#: model:ir.model,name:account.model_report_account_report_partnerledger
msgid "report.account.report_partnerledger"
msgstr "report.account.report_partnerledger"

#. module: account
#: model:ir.model,name:account.model_report_account_report_tax
msgid "report.account.report_tax"
msgstr "report.account.report_tax"

#. module: account
#: model:ir.model,name:account.model_report_account_report_trialbalance
msgid "report.account.report_trialbalance"
msgstr "report.account.report_trialbalance"

#. module: account
#: model:ir.model,name:account.model_res_config_settings
msgid "res.config.settings"
msgstr "res.config.settings"

#. module: account
#: code:addons/account/models/account_move.py:238
#, python-format
msgid "reversal of: "
msgstr "Geri çevrilmesi: "

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "seconds per transaction."
msgstr "işlem başına saniye"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "send us an email"
msgstr "bize bir e-posta gönderin"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "set company logo"
msgstr "şirket logosunu ayarlayın"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "setup your bank accounts."
msgstr "banka hesaplarınızı ayarlayın"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "the customer list"
msgstr "müşteri listesi"

#. module: account
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "the parent company"
msgstr "ana şirket"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "the product list"
msgstr "ürün listesi"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:158
#, python-format
msgid "there"
msgstr "Orada"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "to describe<br/> your experience or to suggest improvements !"
msgstr "tarif etmek <br/> Deneyiminizi veya iyileştirmeler önermek için!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "to set the balance of all of your accounts."
msgstr "Tüm hesaplarınızın dengesini ayarlamak için."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "transactions in"
msgstr "İşlemler"

#. module: account
#: model:ir.model,name:account.model_wizard_multi_charts_accounts
msgid "wizard.multi.charts.accounts"
msgstr "wizard.multi.charts.accounts"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "→ Count"
msgstr "→ Say"
