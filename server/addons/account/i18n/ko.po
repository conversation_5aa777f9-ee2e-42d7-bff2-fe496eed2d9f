# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
# <PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON>, <PERSON>eon G<PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <PERSON>, 2018
# Goh <PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-03-29 09:07+0000\n"
"PO-Revision-Date: 2017-09-20 10:13+0000\n"
"Last-Translator: JH CHOI <<EMAIL>>, 2019\n"
"Language-Team: Korean (https://www.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_state
msgid ""
" * The 'Draft' status is used when a user is encoding a new and unconfirmed Invoice.\n"
" * The 'Open' status is used when user creates invoice, an invoice number is generated. It stays in the open status till the user pays the invoice.\n"
" * The 'Paid' status is set automatically when the invoice is paid. Its related journal entries may or may not be reconciled.\n"
" * The 'Cancelled' status is used when user cancel invoice."
msgstr ""
" * The 'Draft' status is used when a user is encoding a new and unconfirmed Invoice.\n"
" * The 'Pro-forma' status is used when the invoice does not have an invoice number.\n"
" * The 'Open' status is used when user creates invoice, an invoice number is generated. It stays in the open status till the user pays the invoice.\n"
" * The 'Paid' status is set automatically when the invoice is paid. Its related journal entries may or may not be reconciled.\n"
" * The 'Cancelled' status is used when user cancel invoice."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_code_digits
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_code_digits
msgid "# of Digits"
msgstr "자릿수"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_code_digits
msgid "# of Digits *"
msgstr "자릿수*"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_nbr
msgid "# of Lines"
msgstr "줄 수"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_trans_nbr
msgid "# of Transaction"
msgstr "거래 수"

#. module: account
#: model:mail.template,report_name:account.mail_template_data_payment_receipt
msgid "${(object.name or '').replace('/','-')}"
msgstr "${(object.name or '').replace('/','-')}"

#. module: account
#: model:mail.template,subject:account.email_template_edi_invoice
msgid "${object.company_id.name} Invoice (Ref ${object.number or 'n/a'})"
msgstr "${object.company_id.name} 청구서 (Ref ${object.number or 'n/a'})"

#. module: account
#: model:mail.template,subject:account.mail_template_data_payment_receipt
msgid ""
"${object.company_id.name} Payment Receipt (Ref ${object.name or 'n/a' })"
msgstr "${object.company_id.name} 결제 영수증 (Ref ${object.name or 'n/a' })"

#. module: account
#: model:mail.template,subject:account.mail_template_data_notification_email_account_invoice
msgid "${object.subject}"
msgstr "${object.subject}"

#. module: account
#: code:addons/account/models/account_bank_statement.py:462
#, python-format
msgid "%d transactions were automatically reconciled."
msgstr "%d개 거래가 자동으로 조정되었습니다."

#. module: account
#: code:addons/account/models/account.py:809
#, python-format
msgid "%s (Copy)"
msgstr "%s (복사)"

#. module: account
#: code:addons/account/models/account.py:211
#: code:addons/account/models/account.py:484
#: code:addons/account/models/account.py:485
#, python-format
msgid "%s (copy)"
msgstr "%s (복사)"

#. module: account
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "&amp;nbsp;<span>on</span>"
msgstr "&amp;nbsp;<span>대상</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ", if accounting or purchase is installed"
msgstr ", 회계 또는 매입이 설치되었을 경우"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "- First Number:"
msgstr "- 첫 번째 숫자:"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "-> Reconcile"
msgstr "-> 조정"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "-> View partially reconciled entries"
msgstr "-> 부분 조정된 항목 보기"

#. module: account
#: code:addons/account/models/account_bank_statement.py:463
#, python-format
msgid "1 transaction was automatically reconciled."
msgstr "거래 1개가 자동으로 조정되었습니다."

#. module: account
#: model:account.payment.term,name:account.account_payment_term_15days
msgid "15 Days"
msgstr "15일"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_net
msgid "30 Net Days"
msgstr "순기간 30일"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_advance
msgid "30% Advance End of Following Month"
msgstr "익월 말에 30% 선불"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "5) For setup, you will need the following information:"
msgstr "5) 설정에 필요한 정보:"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid ": General ledger"
msgstr ": 총 계정 원장"

#. module: account
#: code:addons/account/models/account.py:554
#, python-format
msgid ": Refund"
msgstr ": 환불"

#. module: account
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid ": Trial Balance"
msgstr ": 시산표"

#. module: account
#: model:mail.template,body_html:account.mail_template_data_payment_receipt
msgid ""
"<?xml version=\"1.0\"?>\n"
"<data><p>Dear ${object.partner_id.name},</p>\n"
"<p>Thank you for your payment.<br/>Here is your payment receipt <strong>${(object.name or '').replace('/','-')}</strong> amounting to <strong>${format_amount(object.amount, object.currency_id)}</strong> from ${object.company_id.name}.</p>\n"
"<p>If you have any questions, please do not hesitate to contact us.</p>\n"
"<p>Best regards,\n"
"% if user and user.signature:\n"
"${user.signature | safe}\n"
"% endif\n"
"</p>\n"
"</data>"
msgstr ""
"<?xml version=\\”1.0\\”?>\n"
"<data><p>${object.partner_id.name} 귀하,</p>\n"
"<p>결제해주셔서 감사합니다.<br/>다음은 결제 영수증 <strong>${(object.name or ‘’).replace(’/’,’-’)}</strong>이며, ${object.company_id.name}에게 <strong>${object.amount} ${object.currency_id.name}</strong>을(를) 수령했습니다.</p>\n"
"<p>질문이 있으면 당사로 언제든지 문의해주십시오.</p>\n"
"<p>감사합니다.\n"
"% if user and user.signature:\n"
"${user.signature | safe}\n"
"% endif\n"
"</p>\n"
"</data>"

#. module: account
#: model:mail.template,body_html:account.mail_template_data_notification_email_account_invoice
msgid ""
"<div>\n"
"% set record = ctx.get('record')\n"
"% set company = record and record.company_id or ctx.get('company')\n"
"<table border=\"0\" width=\"100%\" cellpadding=\"0\" bgcolor=\"#ededed\" style=\"padding: 20px; background-color: #ededed; border-collapse:separate;\" summary=\"o_mail_notification\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\">\n"
"                        <span style=\"font-size:20px; color:white; font-weight: bold;\">\n"
"                            Invoice ${object.record_name}\n"
"                        </span>\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\">\n"
"                        <img src=\"/logo.png?company=${company.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${company.name}\"/>\n"
"                    </td>\n"
"                </tr>\n"
"          </table>\n"
"    </td>\n"
"  </tr>\n"
"  <!-- CONTENT -->\n"
"  <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"min-width: 590px; background-color: rgb(255, 255, 255); padding: 20px; border-collapse:separate;\">\n"
"                <tbody>\n"
"                    <td valign=\"top\" style=\"font-family:Arial,Helvetica,sans-serif; color: #555; font-size: 14px;\">\n"
"                        ${object.body | safe}\n"
"                    </td>\n"
"                </tbody>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\" align=\"left\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                        ${company.name}<br/>\n"
"                        ${company.phone or ''}\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                        % if company.email:\n"
"                        <a href=\"mailto:${company.email}\" style=\"text-decoration:none; color: white;\">${company.email}</a><br/>\n"
"                        % endif\n"
"                        % if company.website:\n"
"                        <a href=\"${company.website}\" style=\"text-decoration:none; color: white;\">\n"
"                        ${company.website}\n"
"                        </a>\n"
"                        % endif\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <tr>\n"
"        <td align=\"center\">\n"
"            Powered by <a target=\"_blank\" href=\"https://www.odoo.com\">Odoo</a>.\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</div>"
msgstr ""
"<div>\n"
"% set record = ctx.get('record')\n"
"% set company = record and record.company_id or user.company_id\n"
"<table border=\"0\" width=\"100%\" cellpadding=\"0\" bgcolor=\"#ededed\" style=\"padding: 20px; background-color: #ededed; border-collapse:separate;\" summary=\"o_mail_notification\">\n"
"<tbody>\n"
" <!-- HEADER -->\n"
" <tr>\n"
" <td align=\"center\" style=\"min-width: 590px;\">\n"
" <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
" <tr>\n"
" <td valign=\"middle\">\n"
" <span style=\"font-size:20px; color:white; font-weight: bold;\">\n"
" Invoice ${object.record_name}\n"
" </span>\n"
" </td>\n"
" <td valign=\"middle\" align=\"right\">\n"
" <img src=\"/logo.png?company=${company.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${company.name}\"/>\n"
" </td>\n"
" </tr>\n"
" </table>\n"
" </td>\n"
" </tr>\n"
" <!-- CONTENT -->\n"
" <tr>\n"
" <td align=\"center\" style=\"min-width: 590px;\">\n"
" <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"min-width: 590px; background-color: rgb(255, 255, 255); padding: 20px; border-collapse:separate;\">\n"
" <tbody>\n"
" <td valign=\"top\" style=\"font-family:Arial,Helvetica,sans-serif; color: #555; font-size: 14px;\">\n"
" ${object.body | safe}\n"
" </td>\n"
" </tbody>\n"
" </table>\n"
" </td>\n"
" </tr>\n"
" <!-- FOOTER -->\n"
" <tr>\n"
" <td align=\"center\" style=\"min-width: 590px;\">\n"
" <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
" <tr>\n"
" <td valign=\"middle\" align=\"left\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
" ${company.name}<br/>\n"
" ${company.phone or ''}\n"
" </td>\n"
" <td valign=\"middle\" align=\"right\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
" % if company.email:\n"
" <a href=\"mailto:${company.email}\" style=\"text-decoration:none; color: white;\">${company.email}</a><br/>\n"
" % endif\n"
" % if company.website:\n"
" <a href=\"${company.website}\" style=\"text-decoration:none; color: white;\">\n"
" ${company.website}\n"
" </a>\n"
" % endif\n"
" </td>\n"
" </tr>\n"
" </table>\n"
" </td>\n"
" </tr>\n"
" <tr>\n"
" <td align=\"center\">\n"
" Powered by <a target=\"_blank\" href=\"https://www.odoo.com\">Odoo</a>.\n"
" </td>\n"
" </tr>\n"
"</tbody>\n"
"</table>\n"
"</div>"

#. module: account
#: model:mail.template,body_html:account.email_template_edi_invoice
msgid ""
"<div>\n"
"<p>Dear ${object.partner_id.name}\n"
"% set access_action = object.with_context(force_website=True).get_access_action()\n"
"% set is_online = access_action and access_action['type'] == 'ir.actions.act_url'\n"
"% set access_url = object.get_mail_url()\n"
"\n"
"% if object.partner_id.parent_id:\n"
"    (<i>${object.partner_id.parent_id.name}</i>)\n"
"% endif\n"
",</p>\n"
"<p>Here is, in attachment, your \n"
"% if object.number:\n"
"invoice <strong>${object.number}</strong>\n"
"% else:\n"
"invoice\n"
"% endif\n"
"% if object.origin:\n"
"(with reference: ${object.origin})\n"
"% endif\n"
"amounting in <strong>${format_amount(object.amount_total, object.currency_id)}</strong>\n"
"from ${object.company_id.name}.\n"
"</p>\n"
"\n"
"% if is_online:\n"
"    <br/><br/>\n"
"    <center>\n"
"      <a href=\"${access_url}\" style=\"background-color: #1abc9c; padding: 20px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 16px;\" class=\"o_default_snippet_text\">View Invoice</a>\n"
"    </center>\n"
"% endif\n"
"    <br/><br/>\n"
"\n"
"% if object.state=='paid':\n"
"    <p>This invoice is already paid.</p>\n"
"% else:\n"
"    <p>Please remit payment at your earliest convenience.</p>\n"
"% endif\n"
"\n"
"<p>Thank you,</p>\n"
"<p style=\"color:#888888\">\n"
"% if object.user_id and object.user_id.signature:\n"
"    ${object.user_id.signature | safe}\n"
"% endif\n"
"</p>\n"
"</div>"
msgstr ""
"<div>\n"
"<p>${object.partner_id.name}\n"
"% set access_action = object.with_context(force_website=True).get_access_action()\n"
"% set is_online = access_action and access_action[’type’] == ‘ir.actions.act_url’\n"
"% set access_url = object.get_mail_url()\n"
"\n"
"% if object.partner_id.parent_id:\n"
" (<i>${object.partner_id.parent_id.name}</i>)\n"
"% endif\n"
"귀하,</p>\n"
"<p>첨부 문서는 귀하의 \n"
"% if object.number:\n"
"청구서 <strong>${object.number}</strong>\n"
"% else:\n"
"청구서\n"
"% endif\n"
"% if object.origin:\n"
"(참조: ${object.origin})\n"
"% endif\n"
"이며, ${object.company_id.name}에서\n"
"<strong>${object.amount_total} ${object.currency_id.name}</strong>을(를) 수령했습니다.\n"
"</p>\n"
"\n"
"% if is_online:\n"
" <br/><br/>\n"
" <center>\n"
" <a href=\"${access_url}\" style=\"background-color: #1abc9c; padding: 20px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 16px;\" class=\"o_default_snippet_text\">청구서 보기</a>\n"
" </center>\n"
"% endif\n"
" <br/><br/>\n"
"\n"
"% if object.state==’paid’:\n"
" <p>이 청구서 금액은 지급되었습니다.</p>\n"
"% else:\n"
" <p>최대한 빠른 시일 내에 대금을 지급해 주십시오.</p>\n"
"% endif\n"
"\n"
"<p>감사합니다.</p>\n"
"<p style=\"color:#888888\">\n"
"% if object.user_id and object.user_id.signature:\n"
" ${object.user_id.signature | safe}\n"
"% endif\n"
"</p>\n"
"</div>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<span class=\"fa fa-arrow-circle-o-down\"/> Install Now"
msgstr "<span class=\"fa fa-arrow-circle-o-down\"/> 지금 설치"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<span class=\"fa\"/> Invite Your Users"
msgstr "<span class=\"fa\"/> 사용자 초대"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-check\"/><span "
"class=\"hidden-xs\"> Paid</span></span>"
msgstr ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-check\"/><span "
"class=\"hidden-xs\"> 지불 완료</span></span>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-remove\"/><span "
"class=\"hidden-xs\"> Cancelled</span></span>"
msgstr ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-remove\"/><span "
"class=\"hidden-xs\"> 취소됨</span></span>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"label label-info\"><i class=\"fa fa-fw fa-clock-o\"/><span "
"class=\"hidden-xs\"> Waiting for Payment</span></span>"
msgstr ""
"<span class=\"label label-info\"><i class=\"fa fa-fw fa-clock-o\"/><span "
"class=\"hidden-xs\"> 지불 대기 중</span></span>"

#. module: account
#: model:ir.ui.view,arch_db:account.partner_view_buttons
msgid "<span class=\"o_stat_text\">Invoiced</span>"
msgstr "<span class=\"o_stat_text\">청구됨</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>Import a file</strong><br/>\n"
"                                        <span class=\"small\">Recommended if &gt;100 products</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>파일 가져오기</strong><br/>\n"
"                                        <span class=\"small\">&gt;제품이 100개일 경우 권장</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>Import</strong><br/>\n"
"                                        <span class=\"small\">&gt; 200 contacts</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>가져오기</strong><br/>\n"
"                                        <span class=\"small\">&gt; 연락처 200개</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> Create manually</strong><br/>\n"
"                                        <span class=\"small\">&lt; 200 contacts</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> 수동 생성</strong><br/>\n"
"                                        <span class=\"small\">&lt; 연락처 200개</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> Create manually</strong><br/>\n"
"                                        <span class=\"small\">Recommended if &lt;100 products</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> 수동 생성</strong><br/>\n"
"                                        <span class=\"small\">&lt;제품이 100개일 경우 권장</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Accountant</strong> (Advanced access)\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>회계사</strong>(고급 액세스)\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Advisor</strong> (Full access)\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>자문</strong>(전체 액세스)\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Billing</strong> (Limited access)\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>청구</strong>(제한된 액세스)\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Customer follow-up</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>고객 후속 관리</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Invoice payment by Check</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>수표로 청구서 지불</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Invoice payment by Wire Transfer</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>계좌 이체로 청구서 지불</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Pay your bills</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>청구 금액 지불</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Reconcile Bank Statements</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>예금거래 명세서 조정</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Record your Bills</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>청구 금액 기록</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span title=\"Balance in Odoo\">Balance in GL</span>"
msgstr "<span title=\"Balance in Odoo\">잔액(GL)</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span title=\"Latest Statement\">Latest Statement</span>"
msgstr "<span title=\"Latest Statement\">최신 명세서</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "<span> From </span>"
msgstr "<span> 시작 </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "<span> To </span>"
msgstr "<span> 종료 </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Journal Entries</span>"
msgstr "<span>분개</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Bill</span>"
msgstr "<span>신규 청구</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Invoice</span>"
msgstr "<span>신규 청구서</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New</span>"
msgstr "<span>신규</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<span>Not due</span>"
msgstr "<span>기한 내</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Operations</span>"
msgstr "<span>영업</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Reconciliation</span>"
msgstr "<span>조정</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Reporting</span>"
msgstr "<span>보고</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>View</span>"
msgstr "<span>보기</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>-The Odoo Team</strong>"
msgstr "<strong>-Odoo 팀</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>1. Register Outstanding Invoices</strong>"
msgstr "<strong>1. 미지급 청구서 등록</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>2. Register Unmatched Payments</strong>"
msgstr "<strong>2. 불일치하는 지불 금액 등록</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_invoice_document_with_payments
msgid "<strong>Amount Due</strong>"
msgstr "<strong>지불해야 할 금액</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Assets Management</strong>"
msgstr "<strong>자산 관리</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Automated documents sending:</strong> automatically send your "
"invoices by email or snail mail."
msgstr "<strong>자동 문서 발송:</strong> 이메일 또는 스네일 메일로 청구서를 자동 발송합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "<strong>Balance :</strong>"
msgstr "<strong>잔액:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Banking interface:</strong> with live bank feed synchronization and "
"bank statement import."
msgstr "<strong>은행 인터페이스:</strong> 라이브 은행 피드 동기화와 예금거래 명세서 가져오기 기능이 있습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Cash transactions</strong><br/> (for which there is no invoice or "
"bill), should be entered directly into your Cash Registers bank account."
msgstr ""
"<strong>현금 거래</strong><br/>(이 경우 청구서 또는 청구가 없음) 금전 등록기 은행 계좌에 직접 입력해야 합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Check the Taxes configuration:</strong>"
msgstr "<strong>세금 구성 확인:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Check unpaid invoices</strong><br/>\n"
"                                         Run an <i>Aged Receivable report</i> and check which customers still owe you money."
msgstr ""
"<strong>미지급 청구서 확인</strong><br/>\n"
"                                         <i>미수금 보고서</i>를 실행하고 아직 대금을 지불하지 않은 고객을 확인합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Clean customer invoices:</strong> easy to create, beautiful and full"
" featured invoices."
msgstr "<strong>깔끔한 고객 청구서:</strong> 쉽게 생성할 수 있으며, 멋지고 완전한 기능을 갖춘 청구서입니다."

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
msgid "<strong>Company:</strong>"
msgstr "<strong>회사:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Contracts &amp; Subscriptions</strong>"
msgstr "<strong>계약 &amp; 구독</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Create a Customer Invoice</strong>"
msgstr "<strong>고객 청구서 생성</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Create a Deposit Ticket</strong><br/>\n"
"                                        This allows you to record the different payments that constitute your bank deposit. (You may need to"
msgstr ""
"<strong>예금 티켓 생성</strong><br/>\n"
"                                        은행 예금을 구성하는 다양한 결제를 기록할 수 있습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Create the bill in Odoo</strong><br/> with a proper due date, and "
"create the vendor if it doesnt' exist yet."
msgstr ""
"<strong>Odoo에서 청구서 생성</strong><br/> 적절한 만기일을 지정하고, 아직 공급업체가 없다면 이를 생성합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
msgid "<strong>Customer Address</strong>"
msgstr "<strong>고객 주소</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Customer Code:</strong>"
msgstr "<strong>고객 코드:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Customer: </strong>"
msgstr "<strong>고객: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Date from :</strong>"
msgstr "<strong>시작일:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Date to :</strong>"
msgstr "<strong>종료일:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Deposit Tickets</strong>"
msgstr "<strong>예금 티켓</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Description:</strong>"
msgstr "<strong>설명:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Display Account:</strong>"
msgstr "<strong>표시 계정:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "<strong>Display Account</strong>"
msgstr "<strong>표시 계정</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Due Date:</strong>"
msgstr "<strong>만기일:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Entries Sorted By:</strong>"
msgstr "<strong>항목 정렬 기준:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Expenses</strong>"
msgstr "<strong>경비</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
msgid "<strong>Fiscal Year End</strong>"
msgstr "<strong>회계연도 종료</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "<strong>From</strong>"
msgstr "<strong>시작</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Invoice Date:</strong>"
msgstr "<strong>청구 날짜:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Journal:</strong>"
msgstr "<strong>분개장:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "<strong>Journals:</strong>"
msgstr "<strong>분개장:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Mark the bills to pay</strong><br/>\n"
"                                        Group or filter your bills to see those due in the next week, then open each bill individually, click on <strong>'Pay'</strong> and select the payment method you prefer."
msgstr ""
"<strong>지급할 청구서 표시</strong><br/>\n"
"                                        청구서를 묶거나 필터링하여 다음 주에 지급해야 할 청구서를 확인한 다음, 각 청구서를 열어서 <strong>'지불'</strong>을 클릭하고 원하는 지급 방법을 선택합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Memo: </strong>"
msgstr "<strong>메모: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Next action:</strong><br/>"
msgstr "<strong>다음 조치:</strong><br/>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>On-the-fly payment reconciliation:</strong> automatic suggestions of"
" outstanding payments when creating invoices."
msgstr "<strong>즉석 지불 조정:</strong> 청구서를 생성할 때 미지급금에 대해 자동 추천을 제공합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Or generate payment orders</strong><br/>\n"
"                                        Create a Payment Order and select the bills you'd like to pay as Entry lines (only the validated bills will appear)."
msgstr ""
"<strong>또는 지불 주문 생성</strong><br/>\n"
"                                        지불 주문을 선택하고 지불하고자 하는 청구서를 항목 라인으로 선택합니다(검증된 청구서만 나타납니다)."

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<strong>Partner's:</strong>"
msgstr "<strong>협력사:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Payment Amount: </strong>"
msgstr "<strong>지불 금액: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Payment Date: </strong>"
msgstr "<strong>지불일: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Payment Method: </strong>"
msgstr "<strong>지불 방법: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<strong>Period Length (days)</strong>"
msgstr "<strong>기간(일)</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Print checks</strong><br/>\n"
"                                        From the Vendor Payments list, select those you want to pay and click on Print Check (you may first need to activate the check feature in the"
msgstr ""
"<strong>전표 인쇄</strong><br/>\n"
"                                        공급업체 지불 목록에서 지불하고자 하는 항목을 선택하고 전표 인쇄를 클릭합니다(먼저 전표 기능을 활성화해야 할 수도 있습니다)."

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "<strong>Purchase</strong>"
msgstr "<strong>매입</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Purchases</strong>"
msgstr "<strong>매입</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Reconcile Bank Statement</strong>"
msgstr "<strong>예금거래 명세서 조정</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Reconcile with existing transaction</strong><br/>\n"
"                                        In this case, Odoo should automatically match the bank statement with the previously recorded check transaction."
msgstr ""
"<strong>기존 거래로 조정</strong><br/>\n"
"                                        이 경우, Odoo는 예금거래 명세서와 앞서 기록한 전표 거래를 자동 일치시킵니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Reconcile your Bank Statements</strong>"
msgstr "<strong>예금거래 명세서 조정</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Record Bank Statement (or import file)</strong><br/>\n"
"                                        Depending on the volume of your transactions, you should be recording your bank statement every week to several times a day."
msgstr ""
"<strong>예금거래 명세서 기록(또는 파일 가져오기)</strong><br/>\n"
"                                        거래량에 따라 매주 또는 하루 여러 번 예금거래 명세서를 기록해야 합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Record Bank Statement</strong>"
msgstr "<strong>예금거래 명세서 기록</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Record a payment by check on the Invoice</strong><br/>\n"
"                                        Simply click on the 'Pay' button."
msgstr ""
"<strong>청구서를 확인하여 지불 기록</strong><br/>\n"
"                                        ‘지불’ 버튼을 클릭하기만 하면 됩니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Reduced data entry:</strong> no need to manually create invoices, "
"register bank statements, and send payment follow-ups."
msgstr ""
"<strong>데이터 입력 감소:</strong> 청구서를 수동으로 생성하고, 예금거래 명세서를 등록하고, 결제 후속 관리 서신을 발송할"
" 필요가 없습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Reference:</strong>"
msgstr "<strong>참조</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Revenue Recognition</strong>"
msgstr "<strong>수익 인식</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Send follow-up letters</strong>"
msgstr "<strong>후속 관리 서신 발송</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "<strong>Sorted By:</strong>"
msgstr "<strong>정렬 기준:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Source:</strong>"
msgstr "<strong>출처:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<strong>Start Date:</strong>"
msgstr "<strong>시작일:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "<strong>Subtotal :</strong>"
msgstr "<strong>소계 :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Subtotal</strong>"
msgstr "<strong>소계</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Target Moves:</strong>"
msgstr "<strong>표적 이동:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Test the following three scenarios in order to assist you in "
"familiarizing yourself with Odoo:</strong>"
msgstr "<strong>다음 세 가지 시나리오를 테스트하고 Odoo를 익히세요.</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>There are three different levels of access rights in Odoo:</strong>"
msgstr "<strong>Odoo의 액세스 권리는 3가지가 있습니다.</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "<strong>There is nothing due with this customer.</strong>"
msgstr "<strong>이 고객은 지불할 금액이 없습니다.</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Total</strong>"
msgstr "<strong>합계</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_account_kanban
msgid "<strong>Type: </strong>"
msgstr "<strong>유형: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Validate the bill</strong><br/> after encoding the products and "
"taxes."
msgstr "<strong>상품과 세금을 인코딩한 후</strong><br/> 청구서를 검증합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Vendor: </strong>"
msgstr "<strong>공급업체: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "<strong>to </strong>"
msgstr "<strong>종료 </strong>"

#. module: account
#: model:ir.actions.act_window,help:account.action_view_bank_statement_tree
msgid ""
"A Cash Register allows you to manage cash entries in your cash\n"
"                journals. This feature provides an easy way to follow up cash\n"
"                payments on a daily basis. You can enter the coins that are in\n"
"                your cash box, and then post entries when money comes in or\n"
"                goes out of the cash box."
msgstr ""
"금전 등록기는 현금 분개에서 현금 항목을\n"
"                관리할 수 있게 해줍니다. 이 기능은 매일 현금 지급의 후속 관리를\n"
"                간편하게 실행할 수 있는 수단을 제공합니다. 금고에 있는 주화를\n"
"                입력한 다음, 금고에 자금이 유입되거나 유출될 때\n"
"                항목을 전기할 수 있습니다."

#. module: account
#: code:addons/account/models/account_bank_statement.py:383
#, python-format
msgid "A Cash transaction can't have a 0 amount."
msgstr "현금 거래는 값이 0일 수 없습니다."

#. module: account
#: code:addons/account/models/account_invoice.py:1699
#, python-format
msgid "A Payment Terms should have its last line of type Balance."
msgstr "지불 조건은 유형의 마지막 라인이 잔액 유형이어야 합니다."

#. module: account
#: code:addons/account/models/account_invoice.py:1702
#, python-format
msgid "A Payment Terms should have only one line of type Balance."
msgstr "지불 조건은 잔액 유형 라인은 1개만 있어야 합니다."

#. module: account
#: code:addons/account/models/account.py:722
#, python-format
msgid "A bank account can only belong to one journal."
msgstr "은행 계좌는 1개 분개에만 속해야 합니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_tree
msgid ""
"A bank statement is a summary of all financial transactions\n"
"                occurring over a given period of time on a bank account. You\n"
"                should receive this periodicaly from your bank."
msgstr ""
"예금거래 명세서는 특정 기간에 은행 계좌에서 발생하는\n"
"                모든 금융 거래의 요약본입니다.\n"
"                은행에서 정기적으로 이를 제공해야 합니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_line
msgid "A bank statement line is a financial transaction on a bank account."
msgstr "예금거래 명세서 라인은 은행 계좌의 금융 거래입니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_move_journal_line
msgid ""
"A journal entry consists of several journal items, each of\n"
"                which is either a debit or a credit transaction."
msgstr ""
"분개는 여러 분개 항목으로 구성되며,\n"
"                각각은 차변 또는 대변 거래입니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_journal_form
msgid ""
"A journal is used to record transactions of all accounting data\n"
"                related to the day-to-day business."
msgstr ""
"분개장은 일상적 거래와 관련된 모든 회계 데이터의\n"
"                트랜잭션을 기록하는 데 사용합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "A list of common taxes and their rates."
msgstr "공동세와 요율 목록입니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "A list of your customer and supplier payment terms."
msgstr "고객 및 공급업체의 지불 조건 목록입니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"A product in Odoo is something you sell or buy \n"
"                        whether or not it is goods, consumables, or services.\n"
"                        Choose how you want to create your products:"
msgstr ""
"Odoo의 제품은 상품, 소비재, 서비스 등\n"
"                        사용자가 판매하거나 구매할 수 있는 재화입니다.\n"
"                        제품 생성 방식을 선택하십시오."

#. module: account
#: code:addons/account/models/account_move.py:891
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr "조정은 이동 라인 2개 이상을 사용해야 합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"A rounding per line is advised if your prices are tax-included. That way, "
"the sum of line subtotals equals the total with taxes."
msgstr ""
"가격에 세금이 포함되어 있다면 라인당 올림을 사용하는 것이 좋습니다. 이렇게 해야 라인 소계 합계가 세금을 포함한 총계와 같아집니다."

#. module: account
#: code:addons/account/models/account_bank_statement.py:881
#: code:addons/account/models/account_bank_statement.py:884
#, python-format
msgid "A selected move line was already reconciled."
msgstr "선택한 이동 라인은 이미 조정되었습니다."

#. module: account
#: code:addons/account/models/account_bank_statement.py:892
#, python-format
msgid "A selected statement line was already reconciled with an account move."
msgstr "선택한 명세서 라인은 이미 계정 이동으로 조정되었습니다."

#. module: account
#: sql_constraint:account.fiscal.position.tax:0
msgid "A tax fiscal position could be defined only once time on same taxes."
msgstr "세금 재정 포지션은 동일한 세금에 대해 1번만 지정할 수 있습니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_journal_form
msgid ""
"A typical company may use one journal per payment method (cash,\n"
"                bank accounts, checks), one purchase journal, one sales journal\n"
"                and one for miscellaneous information."
msgstr ""
"일반 회사는 결제 방법(현금,\n"
"                은행 계좌, 수표)당 분개 1개, 매입 분개 1개,\n"
"                매출 분개 1개, 기타 정보용 분개 1개를 사용합니다."

#. module: account
#: model:res.groups,name:account.group_warning_account
msgid "A warning can be set on a partner (Account)"
msgstr "협력사 (계정)에 경고를 설정할 수 있습니다."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:521
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:523
#: code:addons/account/static/src/xml/account_reconciliation.xml:170
#: code:addons/account/static/src/xml/account_reconciliation.xml:228
#: code:addons/account/static/src/xml/account_reconciliation.xml:253
#: model:ir.model,name:account.model_account_account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_account_id
#: model:ir.model.fields,field_description:account.field_account_move_dummy_account_id
#: model:ir.model.fields,field_description:account.field_account_move_line_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_account_id
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
#: model:ir.ui.view,arch_db:account.view_account_form
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_account_search
#, python-format
msgid "Account"
msgstr "계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_accountant
msgid "Account Accountant"
msgstr "회계사 계정"

#. module: account
#: model:ir.model,name:account.model_account_aged_trial_balance
msgid "Account Aged Trial balance Report"
msgstr "미지급 계정 시산표 보고서"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Account Balances"
msgstr "계좌 잔액"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_cashbox
msgid "Account Bank Statement Cashbox Details"
msgstr "계정 예금거래 명세서 캐시박스 상세 정보"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_closebalance
msgid "Account Bank Statement closing balance"
msgstr "계좌 은행 명세서 마감 잔액"

#. module: account
#: model:ir.model,name:account.model_account_common_account_report
msgid "Account Common Account Report"
msgstr "계정 공통 계정 보고서"

#. module: account
#: model:ir.model,name:account.model_account_common_journal_report
msgid "Account Common Journal Report"
msgstr "계정 공통 분개장 보고서"

#. module: account
#: model:ir.model,name:account.model_account_common_partner_report
msgid "Account Common Partner Report"
msgstr "계정 공통 협력사 보고서"

#. module: account
#: model:ir.model,name:account.model_account_common_report
msgid "Account Common Report"
msgstr "계정 공통 보고서"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_currency_id
#: model:ir.model.fields,field_description:account.field_account_account_template_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_currency_id
msgid "Account Currency"
msgstr "계정 통화"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_account_dest_id
msgid "Account Destination"
msgstr "계정 목적지"

#. module: account
#: model:ir.model,name:account.model_account_move
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Account Entry"
msgstr "계정 항목"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_group_form
#: model:ir.ui.view,arch_db:account.view_account_group_tree
msgid "Account Group"
msgstr "계정 그룹"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_group_tree
msgid "Account Groups"
msgstr "계정 그룹"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_bank_journal_id
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_tree
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#: model:ir.ui.view,arch_db:account.view_account_journal_tree
msgid "Account Journal"
msgstr "계정 분개장"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_account_line_id
msgid "Account Line"
msgstr "계정 라인"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_account_ids
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Account Mapping"
msgstr "계정 매핑"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_reversal
msgid "Account Move Reversal"
msgstr "역방향 계정 이동"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_acc_name
msgid "Account Name."
msgstr "계정 이름."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_bank_acc_number
msgid "Account Number"
msgstr "계정 번호"

#. module: account
#: model:ir.model,name:account.model_account_report_partner_ledger
msgid "Account Partner Ledger"
msgstr "계정 협력사 원장"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_account_payable_id
#: model:ir.model.fields,field_description:account.field_res_users_property_account_payable_id
msgid "Account Payable"
msgstr "계정 미지급금"

#. module: account
#: model:ir.model,name:account.model_account_print_journal
msgid "Account Print Journal"
msgstr "계정 분개장 인쇄"

#. module: account
#: model:ir.ui.view,arch_db:account.view_category_property_form
msgid "Account Properties"
msgstr "계정 속성"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_account_receivable_id
#: model:ir.model.fields,field_description:account.field_res_users_property_account_receivable_id
msgid "Account Receivable"
msgstr "계정 미수금"

#. module: account
#: model:ir.model,name:account.model_account_financial_report
#: model:ir.model.fields,field_description:account.field_account_financial_report_children_ids
#: model:ir.ui.view,arch_db:account.view_account_financial_report_form
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
#: model:ir.ui.view,arch_db:account.view_account_financial_report_tree
msgid "Account Report"
msgstr "계정 보고서"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_account_report_id
#: model:ir.ui.menu,name:account.menu_account_financial_reports_tree
msgid "Account Reports"
msgstr "계정 보고서"

#. module: account
#: model:ir.model,name:account.model_account_cash_rounding
msgid "Account Rounding"
msgstr "계정 올림"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_account_src_id
msgid "Account Source"
msgstr "계정 원본"

#. module: account
#: model:ir.ui.view,arch_db:account.account_bank_statement_graph
#: model:ir.ui.view,arch_db:account.account_bank_statement_pivot
#: model:ir.ui.view,arch_db:account.account_move_line_graph_date
#: model:ir.ui.view,arch_db:account.account_move_line_graph_date_cash_basis
msgid "Account Statistics"
msgstr "계정 통계"

#. module: account
#: model:ir.model,name:account.model_account_account_tag
msgid "Account Tag"
msgstr "계정 태그"

#. module: account
#: model:ir.actions.act_window,name:account.account_tag_action
msgid "Account Tags"
msgstr "계정 태그"

#. module: account
#: model:ir.ui.view,arch_db:account.view_tax_form
#: model:ir.ui.view,arch_db:account.view_tax_tree
msgid "Account Tax"
msgstr "계정 세금"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_account_tax_template_tree
msgid "Account Tax Template"
msgstr "계정 세금 템플릿"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_taxcloud
msgid "Account TaxCloud"
msgstr "계정 택스클라우드"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
#: model:ir.ui.view,arch_db:account.view_account_template_form
#: model:ir.ui.view,arch_db:account.view_account_template_search
#: model:ir.ui.view,arch_db:account.view_account_template_tree
msgid "Account Template"
msgstr "계정 템플릿"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_stock_valuation_account_id
#: model:ir.model.fields,field_description:account.field_res_company_property_stock_valuation_account_id
msgid "Account Template for Stock Valuation"
msgstr "주식 가치 평가용 계정 템플릿"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_template_form
msgid "Account Templates"
msgstr "계정 템플릿"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Account Total"
msgstr "계정 합계"

#. module: account
#: selection:account.financial.report,type:0
#: model:ir.model,name:account.model_account_account_type
#: model:ir.model.fields,field_description:account.field_account_account_type_name
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_account_type
#: model:ir.ui.view,arch_db:account.view_account_search
#: model:ir.ui.view,arch_db:account.view_account_template_search
#: model:ir.ui.view,arch_db:account.view_account_type_form
#: model:ir.ui.view,arch_db:account.view_account_type_search
#: model:ir.ui.view,arch_db:account.view_account_type_tree
msgid "Account Type"
msgstr "계정 유형"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_user_type_id
#: model:ir.model.fields,help:account.field_account_move_line_user_type_id
msgid ""
"Account Type is used for information purpose, to generate country-specific "
"legal reports, and set the rules to close a fiscal year and generate opening"
" entries."
msgstr ""
"계정 유형은 국가별 법적 보고서를 생성하고, 회계연도 마감과 개시된 항목 생성을 위한 규칙을 설정할 때 참고용으로 사용합니다."

#. module: account
#: model:ir.actions.act_window,name:account.action_account_type_form
#: model:ir.model.fields,field_description:account.field_account_financial_report_account_type_ids
msgid "Account Types"
msgstr "계정 유형"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_type_control_ids
msgid "Account Types Allowed"
msgstr "계정 유형 허용됨"

#. module: account
#: model:ir.model,name:account.model_account_unreconcile
msgid "Account Unreconcile"
msgstr "계정 미조정"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_group_search
msgid "Account group"
msgstr "계정 그룹"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_group_search
msgid "Account groups"
msgstr "계정 그룹"

#. module: account
#: model:ir.model,name:account.model_account_move_line_reconcile
msgid "Account move line reconcile"
msgstr "계정 이동 라인 조정"

#. module: account
#: model:ir.model,name:account.model_account_move_line_reconcile_writeoff
msgid "Account move line reconcile (writeoff)"
msgstr "계정 이동 라인 조정(상각)"

#. module: account
#: model:ir.model,name:account.model_account_move_reversal
msgid "Account move reversal"
msgstr "역방향 계정 이동"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_account_src_id
msgid "Account on Product"
msgstr "제품 관련 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_tag_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template_tag_ids
msgid "Account tag"
msgstr "계정 태그"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_refund_account_id
msgid ""
"Account that will be set on invoice tax lines for credit notes. Leave empty "
"to use the expense account."
msgstr "신용 전표에 대해 청구서 세금 라인에 설정될 계정입니다. 경비 계정을 사용하려면 비워두십시오."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_account_id
#: model:ir.model.fields,help:account.field_account_tax_template_account_id
msgid ""
"Account that will be set on invoice tax lines for invoices. Leave empty to "
"use the expense account."
msgstr "청구서에 대해 청구서 세금 라인에 설정될 계정입니다. 경비 계정을 사용하려면 비워두십시오."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_template_refund_account_id
msgid ""
"Account that will be set on invoice tax lines for refunds. Leave empty to "
"use the expense account."
msgstr "환불에 대해 청구서 세금 라인에 설정될 계정입니다. 경비 계정을 사용하려면 비워두십시오."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_account_dest_id
msgid "Account to Use Instead"
msgstr "대신 사용할 계정"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_cash_basis_account
#: model:ir.model.fields,help:account.field_account_tax_template_cash_basis_account
msgid ""
"Account used as counterpart for the journal entry, for taxes eligible based "
"on payments."
msgstr "결제금 기준으로 과세 세금에 대해 분개 대신 사용하는 계정입니다."

#. module: account
#: model:ir.ui.menu,name:account.account_account_menu
msgid "Accounting"
msgstr "회계"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Accounting App Options"
msgstr "회계관리 앱 옵션"

#. module: account
#: model:ir.ui.view,arch_db:account.view_wizard_multi_chart
msgid "Accounting Application Configuration"
msgstr "회계 애플리케이션 구성"

#. module: account
#: model:web.planner,tooltip_planner:account.planner_account
msgid "Accounting Configuration: a step-by-step guide."
msgstr "회계 구성: 단계별 가이드."

#. module: account
#: model:ir.actions.act_window,name:account.open_account_journal_dashboard_kanban
msgid "Accounting Dashboard"
msgstr "회계 대시보드"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_date
msgid "Accounting Date"
msgstr "회계 날짜"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Accounting Documents"
msgstr "회계 문서"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_accounting_entries
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Accounting Entries"
msgstr "회계 항목"

#. module: account
#: model:ir.model,name:account.model_accounting_report
msgid "Accounting Report"
msgstr "회계 보고서"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Accounting Settings"
msgstr "회계 설정"

#. module: account
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Accounting-related settings are managed on"
msgstr "회계 관련 설정은 다음에서 관리됩니다."

#. module: account
#: selection:account.account.tag,applicability:0
#: selection:account.financial.report,type:0
#: model:ir.model.fields,field_description:account.field_account_financial_report_account_ids
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
#: model:ir.ui.view,arch_db:account.view_account_search
msgid "Accounts"
msgstr "계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_account_control_ids
msgid "Accounts Allowed"
msgstr "허용 계정"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_account
msgid "Accounts Fiscal Position"
msgstr "계정 재정 포지션"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_template_form
msgid "Accounts Mapping"
msgstr "계정 매핑"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_actions
msgid "Actions"
msgstr "조치"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Activate Other Currencies"
msgstr "다른 통화 활성화"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Activate the option in the"
msgstr "다음에서 옵션 활성화"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_active
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_active
#: model:ir.model.fields,field_description:account.field_account_journal_active
#: model:ir.model.fields,field_description:account.field_account_payment_term_active
#: model:ir.model.fields,field_description:account.field_account_tax_active
#: model:ir.model.fields,field_description:account.field_account_tax_template_active
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Active"
msgstr "활성"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:17
#, python-format
msgid "Add"
msgstr "추가"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid "Add Credit Note"
msgstr "신용 전표 추가"

#. module: account
#: selection:account.cash.rounding,strategy:0
msgid "Add a rounding line"
msgstr "올림 라인 추가"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_has_second_line
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_has_second_line
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Add a second line"
msgstr "두 번째 라인 추가"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Add an internal note..."
msgstr "내부 메모 추가..."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_comment
msgid "Additional Information"
msgstr "추가 정보"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Additional notes..."
msgstr "추가 메모..."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Address"
msgstr "주소"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_tax_id
msgid "Adjustment Tax"
msgstr "조정 세금"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_adjustment_type
msgid "Adjustment Type"
msgstr "조정 유형"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_tax_form
msgid "Advanced Options"
msgstr "고급 옵션"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Advanced Settings"
msgstr "고급 설정"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries
msgid "Adviser"
msgstr "자문"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_include_base_amount
msgid "Affect Base of Subsequent Taxes"
msgstr "차후 세금 기준에 영향"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_include_base_amount
msgid "Affect Subsequent Taxes"
msgstr "차후 세금에 영향"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_aged_balance_view
#: model:ir.actions.report,name:account.action_report_aged_partner_balance
#: model:ir.ui.menu,name:account.menu_aged_trial_balance
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Aged Partner Balance"
msgstr "미지급 협력사 잔액"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
msgid ""
"Aged Partner Balance is a more detailed report of your receivables by "
"intervals. Odoo calculates a table of credit balance by start Date. So if "
"you request an interval of 30 days Odoo generates an analysis of creditors "
"for the past month, past two months, and so on."
msgstr ""
"미지급 협력사 잔액은 간격 기준으로 제공하는 미수금에 대한 더욱 상세한 보고서입니다. Odoo는 시작일을 기준으로 대변 잔액표를 "
"계산합니다. 30일 간격을 요청하면 Odoo가 지난 달, 지난 2개월 등의 대변 분석을 생성합니다."

#. module: account
#: selection:account.balance.report,display_account:0
#: selection:account.common.account.report,display_account:0
#: selection:account.report.general.ledger,display_account:0
msgid "All"
msgstr "모두"

#. module: account
#: selection:account.aged.trial.balance,target_move:0
#: selection:account.balance.report,target_move:0
#: selection:account.common.account.report,target_move:0
#: selection:account.common.journal.report,target_move:0
#: selection:account.common.partner.report,target_move:0
#: selection:account.common.report,target_move:0
#: selection:account.print.journal,target_move:0
#: selection:account.report.general.ledger,target_move:0
#: selection:account.report.partner.ledger,target_move:0
#: selection:account.tax.report,target_move:0
#: selection:accounting.report,target_move:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "All Entries"
msgstr "모든 항목"

#. module: account
#: model:ir.actions.act_window,name:account.action_all_partner_invoices
msgid "All Invoices"
msgstr "모든 청구서"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_all_lines_reconciled
msgid "All Lines Reconciled"
msgstr "모든 조정된 라인"

#. module: account
#: selection:account.aged.trial.balance,target_move:0
#: selection:account.balance.report,target_move:0
#: selection:account.common.account.report,target_move:0
#: selection:account.common.journal.report,target_move:0
#: selection:account.common.partner.report,target_move:0
#: selection:account.common.report,target_move:0
#: selection:account.print.journal,target_move:0
#: selection:account.report.general.ledger,target_move:0
#: selection:account.report.partner.ledger,target_move:0
#: selection:account.tax.report,target_move:0
#: selection:accounting.report,target_move:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "All Posted Entries"
msgstr "모든 전기된 항목"

#. module: account
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "All accounts"
msgstr "모든 계정"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "All accounts'"
msgstr "모든 계정"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:47
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr "모든 청구서와 지급금이 일치하므로 귀하의 계정 잔액이 깨끗합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_state
msgid ""
"All manually created new journal entries are usually in the status "
"'Unposted', but you can set the option to skip that status on the related "
"journal. In that case, they will behave as journal entries automatically "
"created by the system on document validation (invoices, bank statements...) "
"and will be created in 'Posted' status."
msgstr ""
"수동으로 생성된 모든 신규 분개는 일반적으로 전기되지 않음’ 상태이지만 관련 분개장에서 해당 상태를 건너뛰도록 설정할 수 있습니다. 이 "
"경우, 문서 검증(예: 청구서, 예금거래 명세서) 시 시스템이 자동으로 생성한 분개처럼 동작하고 ‘전기됨’ 상태로 생성될 것입니다."

#. module: account
#: model:ir.ui.view,arch_db:account.validate_account_move_view
msgid ""
"All selected journal entries will be validated and posted. You won't be able"
" to modify them afterwards."
msgstr "모든 선택된 분개는 검증을 받고 전기됩니다. 그 이후에는 수정이 불가능합니다."

#. module: account
#: code:addons/account/models/account_bank_statement.py:240
#, python-format
msgid ""
"All the account entries lines must be processed in order to close the "
"statement."
msgstr "모든 계정 항목 라인을 처리해야 명세서를 마감할 수 있습니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_update_posted
msgid "Allow Cancelling Entries"
msgstr "항목 취소 허용"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_reconcile
msgid "Allow Invoices & payments Matching"
msgstr "청구서 및 결제 매칭 허용"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_product_margin
msgid "Allow Product Margin"
msgstr "제품 마진 허용"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_reconcile
msgid "Allow Reconciliation"
msgstr "조정 허용"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_l10n_us_check_printing
msgid "Allow check printing and deposits"
msgstr "전표 인쇄 및 예금 허용"

#. module: account
#: model:res.groups,name:account.group_cash_rounding
msgid "Allow the cash rounding management"
msgstr "현금 올림 관리 허용"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allow to configure taxes using cash basis"
msgstr "현금주의로 세금 구성 허용"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allows you to use the analytic accounting."
msgstr "분석 계정 사용 허용"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:193
#: code:addons/account/static/src/xml/account_reconciliation.xml:235
#: code:addons/account/static/src/xml/account_reconciliation.xml:252
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_amount
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_subtotal
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_total
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_amount
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_amount_total
#: model:ir.model.fields,field_description:account.field_account_move_amount
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_amount
#: model:ir.model.fields,field_description:account.field_account_tax_amount
#: model:ir.model.fields,field_description:account.field_account_tax_template_amount
#: model:ir.model.fields,field_description:account.field_cash_box_in_amount
#: model:ir.model.fields,field_description:account.field_cash_box_out_amount
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_amount
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_payment_tree
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_account_supplier_payment_tree
#: model:ir.ui.view,arch_db:account.view_move_line_form
#, python-format
msgid "Amount"
msgstr "금액"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_amount_currency
#: model:ir.model.fields,field_description:account.field_account_analytic_line_analytic_amount_currency
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_amount_currency
#: model:ir.model.fields,field_description:account.field_account_move_line_amount_currency
msgid "Amount Currency"
msgstr "화폐 액수"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_residual
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid "Amount Due"
msgstr "지불해야 할 금액"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_residual_company_signed
msgid "Amount Due in Company Currency"
msgstr "회사 통화 기준 지불해야 할 금액"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_residual_signed
msgid "Amount Due in Invoice Currency"
msgstr "청구서 통화 기준 지불해야 할 금액"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Amount Paid"
msgstr "지불된 금액"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_amount_rounding
msgid "Amount Rounding"
msgstr "금액 올림"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_subtotal_signed
msgid "Amount Signed"
msgstr "서명된 금액"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_amount_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_amount_type
msgid "Amount Type"
msgstr "금액 유형"

#. module: account
#: model:ir.model.fields,help:account.field_account_partial_reconcile_amount
msgid "Amount concerned by this matching. Assumed to be always positive"
msgstr "이 매칭과 관련된 금액입니다. 항상 양수로 가정합니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_amount_currency
msgid "Amount in Currency"
msgstr "통화 기준 금액"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Amount type"
msgstr "금액 유형"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:68
#, python-format
msgid "Amount:"
msgstr "금액:"

#. module: account
#: sql_constraint:account.fiscal.position.account:0
msgid ""
"An account fiscal position could be defined only once time on same accounts."
msgstr "계정 재정 포지션은 동일한 계정에 대해 1번만 지정할 수 있습니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_form
msgid ""
"An account is part of a ledger allowing your company\n"
"                to register all kinds of debit and credit transactions.\n"
"                Companies present their annual accounts in two main parts: the\n"
"                balance sheet and the income statement (profit and loss\n"
"                account). The annual accounts of a company are required by law\n"
"                to disclose a certain amount of information."
msgstr ""
"계정은 회사가 각종 차변 및 대변 거래를 등록할 수 있게 해주는\n"
"                일종의 장부입니다.\n"
"                회사는 주로 2가지로 연간 계정을 나타내는데\n"
"                대차대조표와 손익 계산서입니다.\n"
"                회사의 연간 계정은 법적으로\n"
"                일정 정보를 공개해야 합니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_type_form
msgid ""
"An account type is used to determine how an account is used in\n"
"                each journal. The deferral method of an account type determines\n"
"                the process for the annual closing. Reports such as the Balance\n"
"                Sheet and the Profit and Loss report use the category\n"
"                (profit/loss or balance sheet)."
msgstr ""
"계정 유형을 사용하여 각 분개장에서 계정을 사용하는 방법을\n"
"                지정합니다. 계정 유형의 이연법은\n"
"                연간 마감 과정을 결정합니다. 대차대조표,\n"
"                손익 보고서 등의 보고서가 카테고리(손/익 또는\n"
"                대차대조표)를 사용합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Analytic"
msgstr "분석"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:178
#, python-format
msgid "Analytic Acc."
msgstr "분석 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_account_analytic_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_account_analytic_id
#: model:ir.model.fields,field_description:account.field_account_move_line_analytic_account_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_analytic_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_analytic_account_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Analytic Account"
msgstr "분석 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_group_analytic_accounting
#: model:ir.ui.menu,name:account.menu_analytic_accounting
msgid "Analytic Accounting"
msgstr "분석 회계"

#. module: account
#: model:ir.actions.act_window,name:account.action_open_partner_analytic_accounts
#: model:ir.ui.menu,name:account.account_analytic_def_account
#: model:ir.ui.view,arch_db:account.partner_view_button_contracts_count
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytic Accounts"
msgstr "분석 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_analytic
msgid "Analytic Cost"
msgstr "분석 비용"

#. module: account
#: model:ir.actions.act_window,name:account.analytic_line_reporting_pivot
#: model:ir.ui.menu,name:account.menu_action_analytic_lines_reporting
#: model:ir.ui.menu,name:account.menu_action_analytic_lines_tree
msgid "Analytic Entries"
msgstr "분석 항목"

#. module: account
#: model:ir.model,name:account.model_account_analytic_line
msgid "Analytic Line"
msgstr "분석 라인"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Analytic Lines"
msgstr "분석 라인"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_analytic_tag_ids
#: model:ir.ui.menu,name:account.account_analytic_tag_menu
msgid "Analytic Tags"
msgstr "분석 태그"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_account_analytic_id
msgid "Analytic account"
msgstr "분석 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_analytic_line_ids
msgid "Analytic lines"
msgstr "분석 라인"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_analytic_tag_ids
msgid "Analytic tags"
msgstr "분석 태그"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytics"
msgstr "분석"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_exigible
msgid "Appears in VAT report"
msgstr "VAT 보고서에 표시"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_applicability
msgid "Applicability"
msgstr "적용성"

#. module: account
#: selection:tax.adjustments.wizard,adjustment_type:0
msgid "Applied on credit journal item"
msgstr "대변 분개 항목에 적용됨"

#. module: account
#: selection:tax.adjustments.wizard,adjustment_type:0
msgid "Applied on debit journal item"
msgstr "차변 분개 항목에 적용됨"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_auto_apply
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_auto_apply
msgid "Apply automatically this fiscal position."
msgstr "이 재정 포지션을 자동 적용합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_country_group_id
msgid "Apply only if delivery or invocing country match the group."
msgstr "전달 또는 청구 국가가 그룹에 일치할 때만 적용합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_country_group_id
msgid "Apply only if delivery or invoicing country match the group."
msgstr "전달 또는 청구 국가가 그룹에 일치할 때만 적용합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_country_id
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_country_id
msgid "Apply only if delivery or invoicing country match."
msgstr "전달 또는 청구 국가가 일치할 때만 적용합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_vat_required
#: model:ir.model.fields,help:account.field_account_fiscal_position_vat_required
msgid "Apply only if partner has a VAT number."
msgstr "협력사에 VAT 번호가 있을 때만 적용합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Apply right VAT rates for digital products sold in EU"
msgstr "EU에서 판매하는 디지털 상품에 알맞은 VAT요율 적용"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "April"
msgstr "4월"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Archived"
msgstr "아카이브됨"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Ask for a Credit Note"
msgstr "신용 전표 요청"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_assets0
msgid "Assets"
msgstr "자산"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_asset
msgid "Assets Management"
msgstr "자산 관리"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_account_ids
msgid "Associated Account Templates"
msgstr "관련 계정 템플릿"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_at_least_one_inbound
msgid "At Least One Inbound"
msgstr "하나 이상 인바운드"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_at_least_one_outbound
msgid "At Least One Outbound"
msgstr "하나 이상 아웃바운드"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "August"
msgstr "8월"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Auto-detect"
msgstr "자동 탐지"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automate deferred revenues entries for multi-year contracts"
msgstr "다년 계약에 대한 이연 수익 항목 자동화"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automated Entries"
msgstr "자동화된 항목"

#. module: account
#: code:addons/account/models/company.py:411
#: code:addons/account/wizard/setup_wizards.py:79
#, python-format
msgid "Automatic Balancing Line"
msgstr "자동 수지 조정 라인"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_currency_rate_live
msgid "Automatic Currency Rates"
msgstr "자동 환율"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automatic Import"
msgstr "자동 가져오기"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Automatic formatting"
msgstr "자동 서식 지정"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:24
#, python-format
msgid "Automatic reconciliation"
msgstr "자동 조정"

#. module: account
#: code:addons/account/models/account_bank_statement.py:468
#, python-format
msgid "Automatically reconciled items"
msgstr "자동 조정된 항목"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_price_average
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_currency_price_average
msgid "Average Price"
msgstr "평균가"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Awaiting payments"
msgstr "대기 지급금"

#. module: account
#: code:addons/account/models/chart_template.py:194
#, python-format
msgid "BILL"
msgstr "청구"

#. module: account
#: selection:res.partner,trust:0
msgid "Bad Debtor"
msgstr "악성 차변"

#. module: account
#: selection:account.payment.term.line,value:0
#: model:ir.model.fields,field_description:account.field_account_move_line_balance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_payment_receipt
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Balance"
msgstr "잔액"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_balance_cash_basis
msgid "Balance Cash Basis"
msgstr "현금주의로 잔액 계산"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_balancesheet0
#: model:ir.actions.act_window,name:account.action_account_report_bs
#: model:ir.ui.menu,name:account.menu_account_report_bs
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Balance Sheet"
msgstr "대차대조표"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_balance_end
msgid "Balance as calculated based on Opening Balance and transaction lines"
msgstr "개시된 잔액 및 거래 라인을 기준으로 계산한 잔액"

#. module: account
#: selection:account.bank.accounts.wizard,account_type:0
#: selection:account.journal,type:0
#: code:addons/account/models/chart_template.py:758
#: model:ir.model.fields,field_description:account.field_account_journal_bank_id
#: model:ir.model.fields,field_description:account.field_res_partner_bank_account_count
#: model:ir.model.fields,field_description:account.field_res_users_bank_account_count
#, python-format
msgid "Bank"
msgstr "은행"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Bank &amp; Cash"
msgstr "은행 &amp; 현금"

#. module: account
#: code:addons/account/models/company.py:226
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_bank_account_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_bank_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_partner_bank_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_partner_bank_id
#: model:ir.model.fields,field_description:account.field_account_journal_bank_account_id
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#, python-format
msgid "Bank Account"
msgstr "은행 계좌"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Bank Account Name"
msgstr "은행 계좌 이름"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_partner_bank_id
msgid ""
"Bank Account Number to which the invoice will be paid. A Company bank "
"account if this is a Customer Invoice or Vendor Credit Note, otherwise a "
"Partner bank account number."
msgstr ""
"청구서 금액을 지급할 은행 계좌 번호입니다. 고객 청구서 또는 공급업체 신용 전표일 경우, 회사 은행 계좌이고 그렇지 않을 경우에는 "
"협력사 은행 계좌 번호에 해당합니다."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:21
#: model:ir.actions.act_window,name:account.action_account_bank_journal_form
#: model:ir.model,name:account.model_res_partner_bank
#: model:ir.ui.menu,name:account.menu_action_account_bank_journal_form
#, python-format
msgid "Bank Accounts"
msgstr "은행 계좌"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_bank_account_code_prefix
msgid "Bank Accounts Prefix"
msgstr "은행 계정 프리픽스"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_bank_statements_source
msgid "Bank Feeds"
msgstr "은행 피드"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_yodlee
msgid "Bank Interface - Sync your bank feeds automatically"
msgstr "은행 인터페이스 - 은행 피드를 자동 동기화"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_bank_journal_ids
msgid "Bank Journals"
msgstr "은행 분개장"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Bank Operations"
msgstr "은행 영업"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_tree
msgid "Bank Reconciliation Move Presets"
msgstr "은행 조정 이동 사전 설정"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_search
msgid "Bank Reconciliation Move preset"
msgstr "은행 조정 이동 사전 설정"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_bank_data_done
msgid "Bank Setup Marked As Done"
msgstr "은행 설정을 완료로 표시"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Bank Statement"
msgstr "예금거래 명세서"

#. module: account
#: code:addons/account/models/account_bank_statement.py:937
#, python-format
msgid "Bank Statement %s"
msgstr "예금 거래 명세서 %s"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "예금거래 명세서 라인"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_line
msgid "Bank Statement Lines"
msgstr "예금거래 명세서 라인"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_tree
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Bank Statements"
msgstr "예금거래 명세서"

#. module: account
#: model:ir.ui.view,arch_db:account.partner_view_buttons
msgid "Bank account(s)"
msgstr "은행 계좌"

#. module: account
#: model:account.account.type,name:account.data_account_type_liquidity
msgid "Bank and Cash"
msgstr "은행 및 현금"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_action.js:15
#, python-format
msgid "Bank reconciliation"
msgstr "은행 조정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_account_setup_bank_data_done
msgid "Bank setup marked as done"
msgstr "은행 설정을 완료로 표시"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_statement_line_id
msgid "Bank statement line reconciled with this entry"
msgstr "이 항목으로 조정된 예금거래 명세서 라인"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_bank_statement
msgid "Bank statements"
msgstr "예금거래 명세서"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:39
#, python-format
msgid "Bank: Balance"
msgstr "은행: 잔고"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_base
#: model:ir.ui.view,arch_db:account.portal_invoice_report
msgid "Base"
msgstr "기준"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_base_amount
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Base Amount"
msgstr "기준 금액"

#. module: account
#: selection:account.tax,tax_exigibility:0
#: selection:account.tax.template,tax_exigibility:0
msgid "Based on Invoice"
msgstr "청구서 기준"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_tax_exigibility
#: model:ir.model.fields,help:account.field_account_tax_template_tax_exigibility
msgid ""
"Based on Invoice: the tax is due as soon as the invoice is validated.\n"
"Based on Payment: the tax is due as soon as the payment of the invoice is received."
msgstr ""
"청구서 기준:청구서가 검증되는 즉시 세금을 지불해야 합니다.\n"
"결제 기준: 청구서 대금을 받는 즉시 세금을 지불해야 합니다."

#. module: account
#: selection:account.tax,tax_exigibility:0
#: selection:account.tax.template,tax_exigibility:0
msgid "Based on Payment"
msgstr "결제 기준"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Batch Deposits"
msgstr "일괄 예금"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Before continuing, you must install the Chart of Account related to your "
"country (or the generic one if your country is not listed)."
msgstr ""
"계속 진행하기 전에 귀하의 국가와 관련된 계정 차트(또는 자신의 국가가 등록되어 있지 않을 경우 일반 계정 차트)를 설치해야 합니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_belongs_to_company
msgid "Belong to the user's current company"
msgstr "사용자의 현재 회사에 속함"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Bill"
msgstr "청구"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "Bill Date"
msgstr "청구 날짜"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Bill lines"
msgstr "청구 라인"

#. module: account
#: model:res.groups,name:account.group_account_invoice
msgid "Billing"
msgstr "청구"

#. module: account
#: model:res.groups,name:account.group_account_manager
msgid "Billing Manager"
msgstr "청구 관리자"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills"
msgstr "청구"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills Analysis"
msgstr "청구 분석"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:186
#, python-format
msgid "Bills to pay"
msgstr "지급할 청구"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "Blocking Message"
msgstr "메시지 차단"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_type_include_initial_balance
msgid "Bring Accounts Balance Forward"
msgstr "계좌 잔액 이월"

#. module: account
#: model:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid "Browse available countries."
msgstr "이용 가능한 국가를 찾습니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_budget
msgid "Budget Management"
msgstr "예산 관리"

#. module: account
#: model:ir.ui.menu,name:account.account_reports_business_intelligence_menu
msgid "Business Intelligence"
msgstr "사업 정보"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_name
msgid "Button Label"
msgstr "버튼 라벨"

#. module: account
#: model:ir.filters,name:account.filter_invoice_country
msgid "By Country"
msgstr "국가별"

#. module: account
#: model:ir.filters,name:account.filter_invoice_refund
msgid "By Credit Note"
msgstr "신용 전표별"

#. module: account
#: model:ir.filters,name:account.filter_invoice_product
msgid "By Product"
msgstr "제품별"

#. module: account
#: model:ir.filters,name:account.filter_invoice_product_category
msgid "By Product Category"
msgstr "제품 범주별"

#. module: account
#: model:ir.filters,name:account.filter_invoice_salespersons
msgid "By Salespersons"
msgstr "판매원별"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_active
msgid ""
"By unchecking the active field, you may hide a fiscal position without "
"deleting it."
msgstr "활성 필드에서 표시를 제거하면 삭제하지 않고 재정 포지션을 숨길 수 있습니다."

#. module: account
#: code:addons/account/models/chart_template.py:173
#: code:addons/account/models/chart_template.py:197
#, python-format
msgid "CABA"
msgstr "CABA"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "CAMT Import"
msgstr "CAMT 가져오기"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "CSV Import"
msgstr "CSV 가져오기"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_visible
msgid "Can be Visible?"
msgstr "표시할 수 있습니까?"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.account_common_report_view
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
#: model:ir.ui.view,arch_db:account.cash_box_in_form
#: model:ir.ui.view,arch_db:account.cash_box_out_form
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
#: model:ir.ui.view,arch_db:account.validate_account_move_view
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
#: model:ir.ui.view,arch_db:account.view_account_move_reversal
#: model:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Cancel"
msgstr "취소"

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Cancel: create credit note and reconcile"
msgstr "취소: 신용 전표를 생성하고 조정"

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: selection:account.payment,state:0
msgid "Cancelled"
msgstr "취소됨"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Cancelled Invoice"
msgstr "취소된 청구서"

#. module: account
#: code:addons/account/wizard/account_invoice_refund.py:56
#, python-format
msgid ""
"Cannot create a credit note for the invoice which is already reconciled, "
"invoice should be unreconciled first, then only you can add credit note for "
"this invoice."
msgstr ""
"이미 조정된 청구서에 신용 전표를 생성할 수 없습니다. 청구서를 먼저 조정을 해제한 다음, 청구서에 신용 전표를 추가할 수 있습니다."

#. module: account
#: code:addons/account/wizard/account_invoice_refund.py:54
#, python-format
msgid "Cannot create credit note for the draft/cancelled invoice."
msgstr "초안/취소된 청구서는 신용 전표를 생성할 수 없습니다."

#. module: account
#: code:addons/account/models/account_move.py:197
#, python-format
msgid "Cannot create moves for different companies."
msgstr "서로 다른 기업은 이동을 생성할 수 없습니다."

#. module: account
#: code:addons/account/models/account_move.py:229
#, python-format
msgid "Cannot create unbalanced journal entry."
msgstr "잔액이 맞지 않는 분개는 생성할 수 없습니다."

#. module: account
#: code:addons/account/models/account_invoice.py:641
#, python-format
msgid ""
"Cannot find a chart of accounts for this company, You should configure it. \n"
"Please go to Account Configuration."
msgstr ""
"이 회사의 계정 차트를 찾을 수 없으므로 구성해야 합니다.\n"
"계정 구성으로 이동해주십시오."

#. module: account
#: code:addons/account/models/account.py:594
#, python-format
msgid "Cannot generate an unused account code."
msgstr "미사용 계정 코드를 생성할 수 없습니다."

#. module: account
#: code:addons/account/models/account.py:624
#, python-format
msgid ""
"Cannot generate an unused journal code. Please fill the 'Shortcode' field."
msgstr "미사용 분개 코드를 생성할 수 없으므로 ‘Shortcode’ 필드를 작성해주십시오."

#. module: account
#: selection:account.bank.accounts.wizard,account_type:0
#: selection:account.journal,type:0
#: code:addons/account/models/chart_template.py:758
#, python-format
msgid "Cash"
msgstr "현금"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_cash_account_code_prefix
msgid "Cash Accounts Prefix"
msgstr "현금 계정 프리픽스"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_tax_exigibility
msgid "Cash Basis"
msgstr "현금주의"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_tax_cash_basis_journal_id
msgid "Cash Basis Journal"
msgstr "현금주의 분개"

#. module: account
#: code:addons/account/models/chart_template.py:197
#, python-format
msgid "Cash Basis Tax Journal"
msgstr "현금주의 세금 분개"

#. module: account
#: code:addons/account/models/account_bank_statement.py:210
#: model:ir.actions.act_window,name:account.action_view_account_bnk_stmt_cashbox
#, python-format
msgid "Cash Control"
msgstr "현금 관리"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Cash Operations"
msgstr "현금 운영"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_bank_statement_tree
msgid "Cash Registers"
msgstr "금전 등록기"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_group_cash_rounding
msgid "Cash Rounding"
msgstr "현금 올림"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_cash_rounding_id
msgid "Cash Rounding Method"
msgstr "현금 올림법"

#. module: account
#: model:ir.actions.act_window,name:account.rounding_list_action
#: model:ir.ui.menu,name:account.menu_action_rounding_form_view
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Cash Roundings"
msgstr "현금 올림"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Cash Statements"
msgstr "현금 계산서"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_bank_account_ids
msgid "Cash and Banks"
msgstr "현금 및 은행"

#. module: account
#: code:addons/account/models/account_bank_statement.py:185
#, python-format
msgid "Cash difference observed during the counting (%s)"
msgstr "계수 중에 확인된 현금 차액(%s)"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:37
#, python-format
msgid "Cash: Balance"
msgstr "현금: 잔액"

#. module: account
#: model:ir.model,name:account.model_account_cashbox_line
msgid "CashBox Line"
msgstr "캐시박스 라인"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_cashbox_id
msgid "Cashbox"
msgstr "캐시박스"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_cashbox_lines_ids
msgid "Cashbox Lines"
msgstr "캐시박스 라인"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_expense_categ_id
msgid "Category of Expense Account"
msgstr "경비 계정 카테고리"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_income_categ_id
msgid "Category of Income Account"
msgstr "수입 계정 카테고리"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Change"
msgstr "변경"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_writeoff_label
msgid "Change label of the counterpart that will hold the payment difference"
msgstr "지불액 차이가 있는 반대쪽 라벨 변경"

#. module: account
#: code:addons/account/controllers/portal.py:146
#, python-format
msgid ""
"Changing VAT number is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr "계정에 청구서를 발행하고 나면 VAT 번호를 변경할 수 없습니다. 이 경우, 당사에 직접 연락해주십시오."

#. module: account
#: code:addons/account/controllers/portal.py:149
#, python-format
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr "계정에 청구서를 발행하고 나면 이름을 변경할 수 없습니다. 이 경우, 당사에 직접 연락해주십시오."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_res_company_chart_template_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_chart_template_id
msgid "Chart Template"
msgstr "일람표 양식"

#. module: account
#: model:ir.actions.act_window,name:account.open_account_charts_modules
msgid "Chart Templates"
msgstr "일람표 양식"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_coa_done
msgid "Chart of Account Checked"
msgstr "검사한 계정과목 일람표"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:278
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:13
#: model:ir.actions.act_window,name:account.action_account_form
#: model:ir.ui.menu,name:account.menu_action_account_form
#: model:ir.ui.view,arch_db:account.account_planner
#, python-format
msgid "Chart of Accounts"
msgstr "계정과목 일람표"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_form
#: model:ir.ui.view,arch_db:account.view_account_chart_template_tree
msgid "Chart of Accounts Template"
msgstr "계정과목 일람표 양식"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_chart_template_form
msgid "Chart of Accounts Templates"
msgstr "계정과목 일람표 양식"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Chart of Accounts has been\n"
"                            installed. You should review it and create any additional accounts."
msgstr ""
"계정과목 일람표가 설치되었습니다.\n"
"                            계정과목 일람표를 검토하고 추가 계정을 생성해야 합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_list
msgid "Chart of accounts"
msgstr "계정과목 일람표"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_account_bnk_stmt_check
msgid "Check Closing Balance"
msgstr "결산 잔액 확인"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_is_difference_zero
msgid "Check if difference is zero."
msgstr "차액이 0인지 확인합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_reconcile
msgid ""
"Check this box if this account allows invoices & payments matching of "
"journal items."
msgstr "계정에서 분개 항목의 청구서 및 결제 매칭을 허용할 경우 이 상자에 표시해 주십시오."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_refund_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" credit notes made from this journal"
msgstr "이 분개장에서 작성한 청구서와 신용 전표에 동일한 순서를 공유하지 않으려면 이 상자에 표시해 주십시오."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_update_posted
msgid ""
"Check this box if you want to allow the cancellation the entries related to "
"this journal or of the invoice related to this journal"
msgstr "이 분개장과 관련된 항목 또는 이 분개장과 관련된 청구서를 취소하도록 허용하려면 이 상자에 표시해 주십시오."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_price_include
#: model:ir.model.fields,help:account.field_account_tax_template_price_include
msgid ""
"Check this if the price you use on the product and invoices includes this "
"tax."
msgstr "제품과 청구서에 사용한 가격에 이 세금이 포함되면 여기에 표시해주십시오."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_reconcile
msgid ""
"Check this option if you want the user to reconcile entries in this account."
msgstr "사용자가 이 계정에서 항목을 조정하게 하려면 이 옵션에 표시하십시오."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Checks"
msgstr "검사"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_children_tax_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template_children_tax_ids
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_tax_form
msgid "Children Taxes"
msgstr "자녀세"

#. module: account
#: code:addons/account/models/chart_template.py:155
#: model:ir.actions.act_window,name:account.action_wizard_multi_chart
#, python-format
msgid "Choose Accounting Template"
msgstr "회계 템플릿 선택"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:131
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr "상대편 선택 또는 쓰기금지 생성"

#. module: account
#: model:ir.actions.act_window,help:account.action_account_bank_journal_form
msgid "Click to add a bank account."
msgstr "클릭하여 은행 계좌를 추가합니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_journal_form
msgid "Click to add a journal."
msgstr "클릭하여 분개장을 추가합니다."

#. module: account
#: model:ir.actions.act_window,help:account.account_tag_action
msgid "Click to add a new tag."
msgstr "클릭하여 새 태그를 추가합니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_form
msgid "Click to add an account."
msgstr "클릭하여 계정을 추가합니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_out_refund
msgid "Click to create a credit note."
msgstr "클릭하여 신용 전표를 생성합니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree1
msgid "Click to create a customer invoice."
msgstr "클릭하여 고객 청구서를 생성합니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_move_journal_line
msgid "Click to create a journal entry."
msgstr "클릭하여 분개 항목을 생성합니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_view_bank_statement_tree
msgid "Click to create a new cash log."
msgstr "클릭하여 새 현금 로그를 생성합니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_reconcile_model
msgid "Click to create a reconciliation model."
msgstr "클릭하여 조정 모델을 생성합니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_type_form
msgid "Click to define a new account type."
msgstr "클릭하여 새 계정 유형을 정의합니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree2
msgid "Click to record a new vendor bill."
msgstr "클릭하여 새 공급업체 청구서를 기록합니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_in_refund
msgid "Click to record a new vendor credit note."
msgstr "클릭하여 새 공급업체 신용 전표를 기록합니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_tree
msgid "Click to register a bank statement."
msgstr "클릭하여 예금거래 명세서를 등록합니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_payments
#: model:ir.actions.act_window,help:account.action_account_payments_payable
msgid "Click to register a payment"
msgstr "클릭하여 지급금을 등록합니다."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:260
#, python-format
msgid "Close"
msgstr "마감"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:60
#, python-format
msgid "Close statement"
msgstr "명세서 마감"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_date_done
msgid "Closed On"
msgstr "마감일"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_method_code
#: model:ir.model.fields,field_description:account.field_account_account_code
#: model:ir.model.fields,field_description:account.field_account_account_template_code
#: model:ir.model.fields,field_description:account.field_account_analytic_line_code
#: model:ir.model.fields,field_description:account.field_account_payment_method_code
#: model:ir.model.fields,field_description:account.field_account_payment_payment_method_code
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_method_code
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Code"
msgstr "코드"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group_code_prefix
msgid "Code Prefix"
msgstr "코드 프리픽스"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_coin_value
msgid "Coin/Bill Value"
msgstr "주화/지폐 가치"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Collect customer payments in one-click using Euro SEPA Service."
msgstr "Euro SEPA 서비스를 사용하여 클릭 한 번으로 고객 지급금을 수금합니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_color
#: model:ir.model.fields,field_description:account.field_account_journal_color
msgid "Color Index"
msgstr "컬러 인덱스"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_label_filter
msgid "Column Label"
msgstr "열 라벨"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_comment
msgid "Comment"
msgstr "코멘트"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_commercial_partner_id
#: model:ir.model.fields,help:account.field_account_invoice_report_commercial_partner_id
msgid "Commercial Entity"
msgstr "상업 항목"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_common_menu
msgid "Common Report"
msgstr "공동 보고서"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Communication"
msgstr "커뮤니케이션"

#. module: account
#: model:ir.model,name:account.model_res_company
msgid "Companies"
msgstr "회사"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_ref_company_ids
#: model:ir.model.fields,field_description:account.field_res_users_ref_company_ids
msgid "Companies that refers to partner"
msgstr "협력사"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_company_id
#: model:ir.model.fields,field_description:account.field_account_account_company_id
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_company_id
#: model:ir.model.fields,field_description:account.field_account_balance_report_company_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_company_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_company_id
#: model:ir.model.fields,field_description:account.field_account_chart_template_company_id
#: model:ir.model.fields,field_description:account.field_account_common_account_report_company_id
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_company_id
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_company_id
#: model:ir.model.fields,field_description:account.field_account_common_report_company_id
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_company_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_company_id
#: model:ir.model.fields,field_description:account.field_account_journal_company_id
#: model:ir.model.fields,field_description:account.field_account_move_company_id
#: model:ir.model.fields,field_description:account.field_account_move_line_company_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_company_id
#: model:ir.model.fields,field_description:account.field_account_opening_company_id
#: model:ir.model.fields,field_description:account.field_account_payment_company_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_company_id
#: model:ir.model.fields,field_description:account.field_account_print_journal_company_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_company_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_company_id
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_company_id
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_company_id
#: model:ir.model.fields,field_description:account.field_account_tax_company_id
#: model:ir.model.fields,field_description:account.field_account_tax_report_company_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_company_id
#: model:ir.model.fields,field_description:account.field_accounting_report_company_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_company_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Company"
msgstr "회사"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line_company_currency_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_company_currency_id
msgid "Company Currency"
msgstr "회사 통화"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:210
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:25
#, python-format
msgid "Company Data"
msgstr "회사 데이터"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_company_data_done
msgid "Company Setup Marked As Done"
msgstr "회사 설정을 완료로 표시"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_has_chart_of_accounts
msgid "Company has a chart of accounts"
msgstr "회사에 계정 차트가 있습니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_company_id
#: model:ir.model.fields,help:account.field_account_bank_statement_company_id
#: model:ir.model.fields,help:account.field_account_bank_statement_line_company_id
#: model:ir.model.fields,help:account.field_account_journal_company_id
#: model:ir.model.fields,help:account.field_account_move_company_id
#: model:ir.model.fields,help:account.field_account_payment_company_id
#: model:ir.model.fields,help:account.field_account_register_payments_company_id
msgid "Company related to this journal"
msgstr "이 분개장과 관련된 회사"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Compare actual revenues &amp; costs with budgets"
msgstr "실제 수익 및 비용과 예산 비교"

#. module: account
#: model:ir.ui.view,arch_db:account.accounting_report_view
msgid "Comparison"
msgstr "비교"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_complete_tax_set
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_complete_tax_set
msgid "Complete Set of Taxes"
msgstr "전체 세금 집합"

#. module: account
#: code:addons/account/models/account_invoice.py:575
#, python-format
msgid "Compose Email"
msgstr "이메일 작성"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Compute tax rates based on U.S. ZIP codes"
msgstr "미국 우편번호에 기초한 세율 계산"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_counterpart
msgid ""
"Compute the counter part accounts of this journal item for this journal "
"entry. This can be needed in reports."
msgstr "이 분개장의 분개 항목에 대한 반대 계정을 계산합니다. 이는 보고서에 필요할 수 있습니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_balance_end
msgid "Computed Balance"
msgstr "계산된 잔액"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_configuration
msgid "Configuration"
msgstr "구성"

#. module: account
#: code:addons/account/models/account_payment.py:643
#: code:addons/account/models/account_payment.py:645
#, python-format
msgid "Configuration Error !"
msgstr "구성 오류!"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:29
#, python-format
msgid "Configuration Steps:"
msgstr "구성 절차:"

#. module: account
#: code:addons/account/models/account_invoice.py:462
#, python-format
msgid ""
"Configuration error!\n"
"Could not find any account to create the invoice, are you sure you have a chart of account installed?"
msgstr ""
"구성 오류!\n"
"청구서를 생성할 계정을 찾을 수 없습니다. 계정 차트를 설치하였습니까?"

#. module: account
#: code:addons/account/models/account.py:443
#, python-format
msgid ""
"Configuration error!\n"
"The currency of the journal should be the same than the default credit account."
msgstr ""
"구성 오류!\n"
"분개 통화가 기본 대변 계정과 동일해야 합니다."

#. module: account
#: code:addons/account/models/account.py:445
#, python-format
msgid ""
"Configuration error!\n"
"The currency of the journal should be the same than the default debit account."
msgstr ""
"구성 오류!\n"
"분개 통화가 기본 차변 계정과 동일해야 합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Configuration menu"
msgstr "구성 메뉴"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Configure"
msgstr "구성"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
#: model:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Confirm"
msgstr "확인"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_confirm
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid "Confirm Draft Invoices"
msgstr "초안 청구서 확인"

#. module: account
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid "Confirm Invoices"
msgstr "청구서 확인"

#. module: account
#: model:ir.actions.server,name:account.action_account_confirm_payments
msgid "Confirm Payments"
msgstr "지급금 확인"

#. module: account
#: model:ir.model,name:account.model_account_invoice_confirm
msgid "Confirm the selected invoices"
msgstr "선택한 청구서 확인"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Confirmed"
msgstr "확인됨"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
msgid ""
"Confirming this will create automatically a journal entry with the "
"difference in the profit/loss account set on the cash journal."
msgstr "이를 확인하면 현금 분개에 설정된 손익 계정의 차액을 포함하여 분개장이 자동 생성됩니다."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:52
#, python-format
msgid "Congrats, you're all done!"
msgstr "축하합니다. 모두 완료하셨습니다!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Congratulations, you're done!"
msgstr "축하합니다. 모두 완료하셨습니다!"

#. module: account
#: model:ir.model,name:account.model_res_partner
msgid "Contact"
msgstr "연락처"

#. module: account
#: model:ir.model,name:account.model_account_abstract_payment
msgid ""
"Contains the logic shared between models which allows to register payments"
msgstr "지급금을 등록하기 위한 모델 사이에 공유되는 논리가 포함됩니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_contract_ids
#: model:ir.model.fields,field_description:account.field_res_partner_contracts_count
#: model:ir.model.fields,field_description:account.field_res_users_contract_ids
#: model:ir.model.fields,field_description:account.field_res_users_contracts_count
msgid "Contracts"
msgstr "계약"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Control-Access"
msgstr "관리-액세스"

#. module: account
#: model:account.account.type,name:account.data_account_type_direct_costs
msgid "Cost of Revenue"
msgstr "수익 원가"

#. module: account
#: code:addons/account/models/chart_template.py:873
#, python-format
msgid ""
"Could not install new chart of account as there are already accounting "
"entries existing"
msgstr "이미 회계분개가 존재하기 때문에 새 계정 차트를 설치할 수 었습니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_counterpart
msgid "Counterpart"
msgstr "상대"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_account_id
msgid "Counterpart Account"
msgstr "상대 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_country_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_country_id
msgid "Country"
msgstr "국가"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_country_group_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_country_group_id
msgid "Country Group"
msgstr "국가 그룹"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_country_id
msgid "Country of the Partner Company"
msgstr "협력사의 국가"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Create Customers"
msgstr "고객 생성"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Create Vendors"
msgstr "공급업체 생성"

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Create a draft credit note"
msgstr "초안 신용 전표 생성"

#. module: account
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Create and post move"
msgstr "이동 생성 및 전기"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:288
#, python-format
msgid "Create cash statement"
msgstr "현금 계산서 생성"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:274
#, python-format
msgid "Create invoice/bill"
msgstr "청구서/청구 생성"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:163
#, python-format
msgid "Create model"
msgstr "모델 생성"

#. module: account
#: model:ir.actions.act_window,help:account.rounding_list_action
msgid "Create the first cash rounding"
msgstr "첫 번째 현금 올림 생성"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Create your products"
msgstr "제품 생성"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_create_uid
#: model:ir.model.fields,field_description:account.field_account_account_tag_create_uid
#: model:ir.model.fields,field_description:account.field_account_account_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_account_type_create_uid
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_create_uid
#: model:ir.model.fields,field_description:account.field_account_balance_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_create_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_account_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_financial_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_create_uid
#: model:ir.model.fields,field_description:account.field_account_group_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_create_uid
#: model:ir.model.fields,field_description:account.field_account_journal_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal_create_uid
#: model:ir.model.fields,field_description:account.field_account_opening_create_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal_create_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_create_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_register_payments_create_uid
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_create_uid
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile_create_uid
#: model:ir.model.fields,field_description:account.field_accounting_report_create_uid
#: model:ir.model.fields,field_description:account.field_cash_box_in_create_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out_create_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_create_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move_create_uid
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_create_uid
msgid "Created by"
msgstr "작성자"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_create_date
#: model:ir.model.fields,field_description:account.field_account_account_tag_create_date
#: model:ir.model.fields,field_description:account.field_account_account_template_create_date
#: model:ir.model.fields,field_description:account.field_account_account_type_create_date
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_create_date
#: model:ir.model.fields,field_description:account.field_account_balance_report_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_create_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_create_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_create_date
#: model:ir.model.fields,field_description:account.field_account_chart_template_create_date
#: model:ir.model.fields,field_description:account.field_account_common_account_report_create_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_create_date
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_create_date
#: model:ir.model.fields,field_description:account.field_account_common_report_create_date
#: model:ir.model.fields,field_description:account.field_account_financial_report_create_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_create_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_create_date
#: model:ir.model.fields,field_description:account.field_account_group_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_line_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_create_date
#: model:ir.model.fields,field_description:account.field_account_journal_create_date
#: model:ir.model.fields,field_description:account.field_account_move_create_date
#: model:ir.model.fields,field_description:account.field_account_move_line_create_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_create_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_create_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal_create_date
#: model:ir.model.fields,field_description:account.field_account_opening_create_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_method_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_create_date
#: model:ir.model.fields,field_description:account.field_account_print_journal_create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_create_date
#: model:ir.model.fields,field_description:account.field_account_register_payments_create_date
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_create_date
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_group_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_report_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_template_create_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile_create_date
#: model:ir.model.fields,field_description:account.field_accounting_report_create_date
#: model:ir.model.fields,field_description:account.field_cash_box_in_create_date
#: model:ir.model.fields,field_description:account.field_cash_box_out_create_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_create_date
#: model:ir.model.fields,field_description:account.field_validate_account_move_create_date
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_create_date
msgid "Created on"
msgstr "작성일"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_credit
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Credit"
msgstr "대변"

#. module: account
#: model:account.account.type,name:account.data_account_type_credit_card
msgid "Credit Card"
msgstr "신용카드"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_credit_cash_basis
msgid "Credit Cash Basis"
msgstr "대변 현금주의"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_credit_move_id
msgid "Credit Move"
msgstr "대변 이동"

#. module: account
#: code:addons/account/models/account_invoice.py:441
#: code:addons/account/models/account_invoice.py:1216
#: code:addons/account/wizard/account_invoice_refund.py:111
#: model:ir.actions.act_window,name:account.action_account_invoice_refund
#: model:ir.model,name:account.model_account_invoice_refund
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#, python-format
msgid "Credit Note"
msgstr "신용 전표"

#. module: account
#: code:addons/account/models/account_invoice.py:442
#, python-format
msgid "Credit Note - %s"
msgstr "신용 전표 - %s"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Credit Note Bill"
msgstr "신용 전표 청구서"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_date_invoice
msgid "Credit Note Date"
msgstr "신용 전표 날짜"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_refund_sequence_id
msgid "Credit Note Entry Sequence"
msgstr "신용 전표 항목 순서"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Credit Notes"
msgstr "신용 전표"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_refund_sequence_number_next
msgid "Credit Notes: Next Number"
msgstr "신용 전표: 다음 번호"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_credit_account_id
msgid "Credit account"
msgstr "대변 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_credit
msgid "Credit amount"
msgstr "대변 금액"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_matched_credit_ids
msgid "Credit journal items that are matched with this journal item."
msgstr "이 분개 항목과 일치하는 대변 분개 항목입니다."

#. module: account
#: model:ir.ui.menu,name:account.menu_action_currency_form
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Currencies"
msgstr "통화"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_currency_id
#: model:ir.model.fields,field_description:account.field_account_analytic_line_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_journal_currency_id
#: model:ir.model.fields,field_description:account.field_account_chart_template_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_currency_id
#: model:ir.model.fields,field_description:account.field_account_journal_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line_currency_id
#: model:ir.model.fields,field_description:account.field_account_opening_currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_company_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_currency_id
#: model:ir.model.fields,field_description:account.field_account_payment_currency_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_currency_id
#: model:ir.model.fields,field_description:account.field_res_config_settings_currency_id
#: model:ir.model.fields,field_description:account.field_res_partner_currency_id
#: model:ir.model.fields,field_description:account.field_res_users_currency_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_currency_id
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Currency"
msgstr "통화"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_currency_rate
msgid "Currency Rate"
msgstr "환율"

#. module: account
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_currency_id
msgid "Currency as per company's country."
msgstr "회사 국가에 따른 통화입니다."

#. module: account
#: code:addons/account/models/account_move.py:1663
#: code:addons/account/models/account_move.py:1675
#, python-format
msgid "Currency exchange rate difference"
msgstr "환율차이"

#. module: account
#: code:addons/account/models/account.py:451
#, python-format
msgid ""
"Currency field should only be set if the journal's currency is different "
"from the company's. Leave the field blank to use company currency."
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_current_assets
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Current Assets"
msgstr "유동 자산"

#. module: account
#: model:account.account.type,name:account.data_account_type_current_liabilities
msgid "Current Liabilities"
msgstr "유동 부채"

#. module: account
#: model:account.account.type,name:account.data_unaffected_earnings
msgid "Current Year Earnings"
msgstr "당기 순이익"

#. module: account
#: selection:account.abstract.payment,partner_type:0
#: selection:account.payment,partner_type:0
#: selection:account.register.payments,partner_type:0
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_payment_tree
msgid "Customer"
msgstr "고객"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_payment.py:677
#, python-format
msgid "Customer Credit Note"
msgstr "고객 신용 전표"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_out_refund
#: model:ir.ui.menu,name:account.menu_action_invoice_out_refund
msgid "Customer Credit Notes"
msgstr "고객 신용 전표"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Customer Flow"
msgstr "고객 흐름"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
msgid "Customer Invoice"
msgstr "고객 청구서"

#. module: account
#: code:addons/account/models/chart_template.py:193
#: model:ir.actions.act_window,name:account.action_invoice_tree1
#: model:ir.ui.menu,name:account.menu_action_invoice_tree1
#: model:ir.ui.view,arch_db:account.account_planner
#, python-format
msgid "Customer Invoices"
msgstr "고객 청구서"

#. module: account
#: code:addons/account/models/account_payment.py:675
#, python-format
msgid "Customer Payment"
msgstr "고객 지불"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_payment_term_id
#: model:ir.model.fields,field_description:account.field_res_users_property_payment_term_id
msgid "Customer Payment Terms"
msgstr "고객 지불 조건"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Customer Payments"
msgstr "고객 지불"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_product_taxes_id
#: model:ir.model.fields,field_description:account.field_product_template_taxes_id
msgid "Customer Taxes"
msgstr "고객 세금"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Customer ref:"
msgstr "고객 참조:"

#. module: account
#: model:ir.ui.menu,name:account.menu_account_customer
msgid "Customers"
msgstr "고객"

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "DOWN"
msgstr "다운"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/account_dashboard_setup_bar.js:214
#: model:ir.ui.menu,name:account.menu_board_journal_1
#, python-format
msgid "Dashboard"
msgstr "알림"

#. module: account
#. openerp-web
#: selection:account.print.journal,sort_selection:0
#: selection:account.report.general.ledger,sortby:0
#: selection:accounting.report,filter_cmp:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:233
#: code:addons/account/static/src/xml/account_reconciliation.xml:248
#: model:ir.model.fields,field_description:account.field_account_bank_statement_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_date
#: model:ir.model.fields,field_description:account.field_account_invoice_report_date
#: model:ir.model.fields,field_description:account.field_account_move_date
#: model:ir.model.fields,field_description:account.field_account_move_line_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_date_p
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_date
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_overdue_document
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Date"
msgstr "날짜"

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_opening_date
msgid ""
"Date at which the opening entry of this company's accounting has been "
"posted."
msgstr "이 회사 회계의 개시 항목이 전기된 날짜입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op_opening_date
msgid ""
"Date from which the accounting is managed in Odoo. It is the date of the "
"opening entry."
msgstr "Odoo에서 회계를 관리하기 시작한 날짜입니다. 개시된 항목의 날짜입니다."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:56
#, python-format
msgid "Date:"
msgstr "날짜:"

#. module: account
#: model:ir.ui.view,arch_db:account.accounting_report_view
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Dates"
msgstr "날짜"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Day(s) after the end of the invoice month (Net EOM)"
msgstr "청구 월말 이후 기간(일)(순 EOM)"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Day(s) after the invoice date"
msgstr "청구 날짜 이후 기간(일)"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Deactivate setup bar on the dashboard"
msgstr "대시보드에서 설정 막대 비활성화"

#. module: account
#: code:addons/account/models/company.py:45
#, python-format
msgid ""
"Dear Sir/Madam,\n"
"\n"
"Our records indicate that some payments on your account are still due. Please find details below.\n"
"If the amount has already been paid, please disregard this notice. Otherwise, please forward us the total amount stated below.\n"
"If you have any queries regarding your account, Please contact us.\n"
"\n"
"Thank you in advance for your cooperation.\n"
"Best Regards,"
msgstr ""
"안녕하십니까.\n"
"\n"
"저희 기록에 따르면 귀하의 계정에 미수금이 존재합니다. 아래에서 자세한 사항을 확인해주십시오.\n"
"이미 대금이 지불되었다면 이 고지를 무시하십시오. 그렇지 않을 경우 아래의 금액을 저희에게 지불해주시기 바랍니다.\n"
"계정과 관련된 문의 사항이 있으면 연락해 주십시오.\n"
"\n"
"귀사의 협조에 미리 감사드립니다.\n"
"안녕히 계십시오."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_debit
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Debit"
msgstr "차변"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_debit_cash_basis
msgid "Debit Cash Basis"
msgstr "차변 현금주의"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_inbound_payment_method_ids
msgid "Debit Methods"
msgstr "차변 방식"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_debit_move_id
msgid "Debit Move"
msgstr "차변 이동"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_debit_account_id
msgid "Debit account"
msgstr "차변 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_debit
msgid "Debit amount"
msgstr "차변 금액"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_matched_debit_ids
msgid "Debit journal items that are matched with this journal item."
msgstr "이 분개 항목과 일치하는 차변 분개 항목입니다."

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "December"
msgstr "12월"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_refund_sequence
msgid "Dedicated Credit Note Sequence"
msgstr "지정된 신용 전표 순서"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_default_credit_account_id
msgid "Default Credit Account"
msgstr "기본 대변 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_default_debit_account_id
msgid "Default Debit Account"
msgstr "기본 차변 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_default_purchase_tax_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_purchase_tax_id
msgid "Default Purchase Tax"
msgstr "기본 구매세"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_default_sale_tax_id
msgid "Default Sale Tax"
msgstr "기본 판매세"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_sale_tax_id
msgid "Default Sales Tax"
msgstr "기본 판매세"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tax_ids
#: model:ir.model.fields,field_description:account.field_account_account_template_tax_ids
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_account_chart_template_form
#: model:ir.ui.view,arch_db:account.view_account_template_form
msgid "Default Taxes"
msgstr "기본 세금"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Default taxes applied to local transactions"
msgstr "지역 거래에 적용되는 기본 세금"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Deferred Revenues Management"
msgstr "이연 수익 관리"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash."
msgstr "현금 지급에 사용한 통화의 최소 단위 주화를 지정합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_cash_rounding_id
msgid ""
"Defines the smallest coinage of the currency that can be used to pay by "
"cash."
msgstr "현금 지급에 사용할 수 있는 통화의 최소 단위 주화를 지정합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_tax_form
msgid "Definition"
msgstr "정의"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_trust
#: model:ir.model.fields,field_description:account.field_res_users_trust
msgid "Degree of trust you have in this debtor"
msgstr "이 차변에 대한 신뢰도"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_deprecated
msgid "Deprecated"
msgstr "감가상각됨"

#. module: account
#: model:account.account.type,name:account.data_account_type_depreciation
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Depreciation"
msgstr "감가상각"

#. module: account
#: model:ir.ui.view,arch_db:account.cash_box_out_form
msgid "Describe why you take money from the cash register:"
msgstr "금전 등록기에서 자금을 인출한 이유 설명:"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:251
#: model:ir.model.fields,field_description:account.field_account_account_type_note
#: model:ir.model.fields,field_description:account.field_account_invoice_line_name
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_account_type_form
#, python-format
msgid "Description"
msgstr "설명"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_note
msgid "Description on the Invoice"
msgstr "청구서에 대한 설명"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_destination_account_id
msgid "Destination Account"
msgstr "대상 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_auto_apply
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_auto_apply
msgid "Detect Automatically"
msgstr "자동 탐지"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_template_type_tax_use
#: model:ir.model.fields,help:account.field_account_tax_type_tax_use
msgid ""
"Determines where the tax is selectable. Note : 'None' means a tax can't be "
"used by itself, however it can still be used in a group."
msgstr ""
"세금을 선택 가능한지 확인합니다. 참고: '없음'은 세금 자체만으로는 사용할 수 없으나 그룹에서 사용할 수 있다는 것을 의미합니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_difference
msgid "Difference"
msgstr "차액"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_writeoff_account_id
msgid "Difference Account"
msgstr "차액 계정"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_difference
msgid ""
"Difference between the computed ending balance and the specified ending "
"balance."
msgstr "계산된 결산 잔액과 지정된 결산 잔액 간의 차이입니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Direct connection to your bank"
msgstr "은행과 직접 연결"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Disc.(%)"
msgstr "할인율(%)"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_wizard_form
msgid "Discard"
msgstr "폐기"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_discount
msgid "Discount (%)"
msgstr "할인율(%)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_balance_report_display_account
#: model:ir.model.fields,field_description:account.field_account_common_account_report_display_account
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_display_account
msgid "Display Accounts"
msgstr "계정 표시"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_debit_credit
msgid "Display Debit/Credit Columns"
msgstr "차변/대변 열 표시"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_display_name
#: model:ir.model.fields,field_description:account.field_account_account_display_name
#: model:ir.model.fields,field_description:account.field_account_account_tag_display_name
#: model:ir.model.fields,field_description:account.field_account_account_template_display_name
#: model:ir.model.fields,field_description:account.field_account_account_type_display_name
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_display_name
#: model:ir.model.fields,field_description:account.field_account_balance_report_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_display_name
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_display_name
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_display_name
#: model:ir.model.fields,field_description:account.field_account_chart_template_display_name
#: model:ir.model.fields,field_description:account.field_account_common_account_report_display_name
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_display_name
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_display_name
#: model:ir.model.fields,field_description:account.field_account_common_report_display_name
#: model:ir.model.fields,field_description:account.field_account_financial_report_display_name
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_display_name
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_display_name
#: model:ir.model.fields,field_description:account.field_account_group_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_line_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_report_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_display_name
#: model:ir.model.fields,field_description:account.field_account_journal_display_name
#: model:ir.model.fields,field_description:account.field_account_move_display_name
#: model:ir.model.fields,field_description:account.field_account_move_line_display_name
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_display_name
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_display_name
#: model:ir.model.fields,field_description:account.field_account_move_reversal_display_name
#: model:ir.model.fields,field_description:account.field_account_opening_display_name
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_method_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_term_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_display_name
#: model:ir.model.fields,field_description:account.field_account_print_journal_display_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_display_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_display_name
#: model:ir.model.fields,field_description:account.field_account_register_payments_display_name
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_display_name
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_group_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_report_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_template_display_name
#: model:ir.model.fields,field_description:account.field_account_unreconcile_display_name
#: model:ir.model.fields,field_description:account.field_accounting_report_display_name
#: model:ir.model.fields,field_description:account.field_cash_box_in_display_name
#: model:ir.model.fields,field_description:account.field_cash_box_out_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_financial_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_generalledger_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_journal_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_overdue_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_partnerledger_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_tax_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_trialbalance_display_name
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_display_name
#: model:ir.model.fields,field_description:account.field_validate_account_move_display_name
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_display_name
msgid "Display Name"
msgstr "표시 이름"

#. module: account
#: selection:account.financial.report,display_detail:0
msgid "Display children flat"
msgstr "하위 요소를 평평하게 표시"

#. module: account
#: selection:account.financial.report,display_detail:0
msgid "Display children with hierarchy"
msgstr "하위 요소를 계층적으로 표시"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_display_detail
msgid "Display details"
msgstr "세부 사항 표시"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_description
msgid "Display on Invoices"
msgstr "청구서에 표시"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_print_docsaway
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Docsaway"
msgstr "Docsaway"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid ""
"Document: Customer account statement<br/>\n"
"                    Date:"
msgstr ""
"문서: 고객 계정 명세서<br/>\n"
"                    날짜:"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_payables_documents
#: model:ir.ui.menu,name:account.menu_finance_receivables_documents
msgid "Documents"
msgstr "문서"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Don't hesitate to"
msgstr "언제든"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_page
msgid "Download"
msgstr "다운로드"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Download the"
msgstr "다운로드"

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: selection:account.payment,state:0
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Draft"
msgstr "초안"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Draft Bill"
msgstr "초안 청구"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Draft Credit Note"
msgstr "초안 신용 전표"

#. module: account
#: code:addons/account/models/account_invoice.py:439
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Draft Invoice"
msgstr "초안 청구서"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Draft Invoices"
msgstr "초안 청구서"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Draft bills"
msgstr "초안 청구"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_draft_tree
msgid "Draft statements"
msgstr "초안 명세서"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Due"
msgstr "지급 금액"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_residual
msgid "Due Amount"
msgstr "지급 금액"

#. module: account
#. openerp-web
#: code:addons/account/controllers/portal.py:71
#: code:addons/account/static/src/xml/account_reconciliation.xml:234
#: model:ir.model.fields,field_description:account.field_account_invoice_date_due
#: model:ir.model.fields,field_description:account.field_account_invoice_report_date_due
#: model:ir.ui.view,arch_db:account.portal_my_invoices
#: model:ir.ui.view,arch_db:account.report_overdue_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Due Date"
msgstr "만기일"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Due Date Computation"
msgstr "만기일 계산"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Due Month"
msgstr "만기월"

#. module: account
#: model:ir.actions.report,name:account.action_report_print_overdue
msgid "Due Payments"
msgstr "만기 지급금"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_tree
msgid "Due Type"
msgstr "지불 유형"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_date_maturity
msgid "Due date"
msgstr "만기일"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Duplicate"
msgstr "복제"

#. module: account
#: code:addons/account/models/account_invoice.py:1194
#, python-format
msgid ""
"Duplicated vendor reference detected. You probably encoded twice the same "
"vendor bill/credit note."
msgstr "중복된 공급업체 참조가 발견되었습니다. 동일한 공급업체 청구/신용 전표를 두 번 인코딩한 것 같습니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_reports
msgid "Dynamic Reports"
msgstr "동적 보고서"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_l10n_eu_service
msgid "EU Digital Goods VAT"
msgstr "EU 디지털 상품 VAT"

#. module: account
#: code:addons/account/models/chart_template.py:171
#: code:addons/account/models/chart_template.py:186
#: code:addons/account/models/chart_template.py:196
#, python-format
msgid "EXCH"
msgstr "EXCH"

#. module: account
#: code:addons/account/models/account_move.py:1052
#, python-format
msgid "Either pass both debit and credit or none."
msgstr "차변/대변을 모두 통과하거나 통과하지 않습니다."

#. module: account
#: model:ir.model,name:account.model_mail_compose_message
msgid "Email composition wizard"
msgstr "이메일 작성 마법사"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_enable_filter
msgid "Enable Comparison"
msgstr "비교 활성화"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "End"
msgstr "종료"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_date_to
#: model:ir.model.fields,field_description:account.field_account_balance_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_account_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_report_date_to
#: model:ir.model.fields,field_description:account.field_account_print_journal_date_to
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_date_to
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_date_to
#: model:ir.model.fields,field_description:account.field_account_tax_report_date_to
#: model:ir.model.fields,field_description:account.field_accounting_report_date_to
#: model:ir.model.fields,field_description:account.field_accounting_report_date_to_cmp
msgid "End Date"
msgstr "종료일"

#. module: account
#: model:account.payment.term,name:account.account_payment_term
msgid "End of Following Month"
msgstr "다음 월 말"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_balance_end_real
msgid "Ending Balance"
msgstr "결산 잔액"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_end_id
msgid "Ending Cashbox"
msgstr "결산 캐시박스"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Enjoy your Odoo experience,"
msgstr "Odoo를 즐겁게 경험하세요."

#. module: account
#: model:ir.actions.act_window,name:account.action_move_line_form
msgid "Entries"
msgstr "항목"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_print_journal_sort_selection
msgid "Entries Sorted by"
msgstr "항목 정렬 기준"

#. module: account
#: code:addons/account/models/account_move.py:1019
#, python-format
msgid "Entries are not of the same account!"
msgstr "항목이 같은 계정에 속해 있지 않습니다!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Entries to Review"
msgstr "검토할 항목"

#. module: account
#: code:addons/account/models/account_analytic_line.py:58
#, python-format
msgid "Entries: "
msgstr "항목:"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "Entry Label"
msgstr "항목 라벨"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_sequence_id
msgid "Entry Sequence"
msgstr "항목 순서"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_move_line_ids
msgid "Entry lines"
msgstr "항목 라인"

#. module: account
#: model:account.account.type,name:account.data_account_type_equity
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Equity"
msgstr "자기 자본"

#. module: account
#: code:addons/account/models/res_config_settings.py:132
#, python-format
msgid "Error!"
msgstr "오류!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Excel template"
msgstr "엑셀 템플릿"

#. module: account
#: code:addons/account/models/chart_template.py:196
#, python-format
msgid "Exchange Difference"
msgstr "외환차이"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_currency_exchange_journal_id
#: model:ir.model.fields,field_description:account.field_res_config_settings_currency_exchange_journal_id
msgid "Exchange Gain or Loss Journal"
msgstr "환 이익/손실 분개"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_exchange_move_id
msgid "Exchange Move"
msgstr "환율 이동"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Exchange rates can be automatically updated once a day from <strong>Yahoo "
"Finance</strong> or the <strong>European Central Bank</strong>. You can "
"activate this feature in the bottom of the"
msgstr ""
"환율은 <strong>Yahoo Finance</strong> 또는 <strong>유럽중앙은행</strong>에서 하루 1회 자동 "
"업데이트할 수 있습니다. 하단에서도 이 기능을 활성화할 수 있습니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_expects_chart_of_accounts
msgid "Expects a Chart of Accounts"
msgstr "계정 차트 예상"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_expense0
msgid "Expense"
msgstr "경비"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_category_property_account_expense_categ_id
#: model:ir.model.fields,field_description:account.field_product_product_property_account_expense_id
#: model:ir.model.fields,field_description:account.field_product_template_property_account_expense_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Expense Account"
msgstr "경비 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_expense_id
msgid "Expense Account on Product Template"
msgstr "제품 템플릿에 대한 경비 계정"

#. module: account
#: model:account.account.type,name:account.data_account_type_expenses
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Expenses"
msgstr "경비"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_reference
msgid "External Reference"
msgstr "외부 참조"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Extra Features"
msgstr "추가 기능"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Favorites"
msgstr "즐겨찾기"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "February"
msgstr "2월"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_state_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_state_ids
msgid "Federal States"
msgstr "연방"

#. module: account
#: selection:account.journal,bank_statements_source:0
msgid "File Import"
msgstr "파일 가져오기"

#. module: account
#: model:ir.ui.view,arch_db:account.cash_box_in_form
msgid "Fill in this form if you put money in the cash register:"
msgstr "금전 등록기에 자금을 넣을 경우 이 양식 작성:"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_filter_cmp
msgid "Filter by"
msgstr "필터 기준"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:141
#, python-format
msgid "Filter..."
msgstr "필터..."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_general_account_id
#: model:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
msgid "Financial Account"
msgstr "재무 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_style_overwrite
msgid "Financial Report Style"
msgstr "재무보고서 스타일"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_financial_report_tree
#: model:ir.actions.act_window,name:account.action_account_report
#: model:ir.ui.menu,name:account.menu_account_reports
msgid "Financial Reports"
msgstr "재무보고서"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_fy_data_done
msgid "Financial Year Setup Marked As Done"
msgstr "회계연도 설정을 완료로 표시"

#. module: account
#: model:ir.actions.report,name:account.action_report_financial
msgid "Financial report"
msgstr "재무보고서"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_account_setup_fy_data_done
msgid "Financial year setup marked as done"
msgstr "회계연도 설정을 완료로 표시"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "First, register any outstanding customer invoices and vendor bills:"
msgstr "먼저 미지급 고객 청구서와 공급업체 청구를 등록합니다:"

#. module: account
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Fiscal Information"
msgstr "재무 정보"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Fiscal Localization"
msgstr "재무 현지화"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_position_id
msgid "Fiscal Mapping"
msgstr "재무 매핑"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Fiscal Periods"
msgstr "재무 기간"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_fiscal_position_template_form
#: model:ir.model,name:account.model_account_fiscal_position
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_position_id
#: model:ir.model.fields,field_description:account.field_account_invoice_fiscal_position_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_fiscal_position_id
#: model:ir.model.fields,field_description:account.field_res_partner_property_account_position_id
#: model:ir.model.fields,field_description:account.field_res_users_property_account_position_id
#: model:ir.ui.view,arch_db:account.view_account_position_form
#: model:ir.ui.view,arch_db:account.view_account_position_template_search
#: model:ir.ui.view,arch_db:account.view_account_position_template_tree
#: model:ir.ui.view,arch_db:account.view_account_position_tree
msgid "Fiscal Position"
msgstr "재정 포지션"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_name
#: model:ir.ui.view,arch_db:account.view_account_position_template_form
#: model:ir.ui.view,arch_db:account.view_account_position_template_search
msgid "Fiscal Position Template"
msgstr "재정 포지션 템플릿"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_fiscal_position_form
#: model:ir.ui.menu,name:account.menu_action_account_fiscal_position_form
msgid "Fiscal Positions"
msgstr "재정 포지션"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:251
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:17
#, python-format
msgid "Fiscal Year"
msgstr "회계연도"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_fiscalyear_last_day
#: model:ir.model.fields,field_description:account.field_res_company_fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "회계연도 마감일"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_fiscalyear_last_month
#: model:ir.model.fields,field_description:account.field_res_company_fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "회계연도 마감월"

#. module: account
#: selection:account.reconcile.model,amount_type:0
#: selection:account.reconcile.model,second_amount_type:0
#: selection:account.reconcile.model.template,amount_type:0
#: selection:account.reconcile.model.template,second_amount_type:0
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Fixed"
msgstr "고정"

#. module: account
#: selection:account.payment.term.line,value:0
msgid "Fixed Amount"
msgstr "고정 금액"

#. module: account
#: model:account.account.type,name:account.data_account_type_fixed_assets
msgid "Fixed Assets"
msgstr "고정 자산"

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_second_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template_second_amount
msgid ""
"Fixed amount will count as a debit if it is negative, as a credit if it is "
"positive."
msgstr "음수일 경우 고정 금액이 차변으로 계산되고, 양수일 경우 대변으로 계산됩니다."

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_receivables_follow_up
msgid "Follow-up"
msgstr "후속 관리"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_reports_followup
msgid "Follow-up Levels"
msgstr "후속 관리 수준"

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_report_sign
msgid ""
"For accounts that are typically more debited than credited and that you "
"would like to print as negative amounts in your reports, you should reverse "
"the sign of the balance; e.g.: Expense account. The same applies for "
"accounts that are typically more credited than debited and that you would "
"like to print as positive amounts in your reports; e.g.: Income account."
msgstr ""
"일반적으로 대변보다 차변이 크고, 보고서에서 음수값으로 나올 가능성이 큰 계정의 경우, 잔액 기호를 바꾸어야 합니다(예: 경비 계정). "
"일반적으로 차변보다 대변이 크고, 보고서에서 양수값으로 나올 가능성이 큰 계정도 동일한 원칙이 적용됩니다(예: 수입 계정)."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line_value_amount
msgid "For percent enter a ratio between 0-100."
msgstr "비율의 경우, 0~100의 비율을 입력합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"For the Odoo Team,<br/>\n"
"                            Fabien Pinckaers, Founder"
msgstr ""
"Odoo 팀의 경우,<br/>\n"
"                            Fabien Pinckaers, 창업자"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_currency_id
msgid "Forces all moves for this account to have this account currency."
msgstr "이 계정에 대한 모든 이동은 해당 계정 통화를 사용합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_currency_id
#: model:ir.model.fields,help:account.field_account_bank_accounts_wizard_currency_id
msgid "Forces all moves for this account to have this secondary currency."
msgstr "이 계정에 대한 모든 이동은 보조 통화를 사용합니다."

#. module: account
#: code:addons/account/report/account_aged_partner_balance.py:205
#: code:addons/account/report/account_balance.py:64
#: code:addons/account/report/account_general_ledger.py:114
#: code:addons/account/report/account_journal.py:100
#: code:addons/account/report/account_partner_ledger.py:74
#: code:addons/account/report/account_report_financial.py:149
#: code:addons/account/report/account_tax.py:13
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr "양식 내용이 누락되어서 이 보고서를 인쇄할 수 없습니다."

#. module: account
#: code:addons/account/models/account_invoice.py:96
#, python-format
msgid "Free Reference"
msgstr "자유 참조"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "From Payable accounts"
msgstr "지급 계정에서"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "From Receivable accounts"
msgstr "수취 계정에서"

#. module: account
#: model:ir.actions.act_window,help:account.action_account_invoice_report_all_supp
msgid ""
"From this report, you can have an overview of the amount invoiced from your "
"vendors. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"이 보고서에서는 공급업체에서 청구한 금액 개요를 확인할 수 있습니다. 또한, 검색 도구를 사용하여 청구서 보고서를 맞춤화하고 필요에 맞게"
" 분석을 매칭할 수 있습니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_invoice_report_all
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customers. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"이 보고서에서는 고객이 청구한 금액 개요를 확인할 수 있습니다. 또한, 검색 도구를 사용하여 청구서 보고서를 맞춤화하고 필요에 맞게 "
"분석을 매칭할 수 있습니다."

#. module: account
#: model:ir.model,name:account.model_account_full_reconcile
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_full_reconcile_id
msgid "Full Reconcile"
msgstr "완전 조정"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:113
#, python-format
msgid "Future"
msgstr "미래"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Future Activities"
msgstr "향후 활동"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "GROSS PROFIT"
msgstr "매출총수익"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_income_currency_exchange_account_id
#: model:ir.model.fields,field_description:account.field_res_company_income_currency_exchange_account_id
msgid "Gain Exchange Rate Account"
msgstr "이익 환율 계정"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_general_ledger_menu
#: model:ir.actions.report,name:account.action_report_general_ledger
#: model:ir.ui.menu,name:account.menu_general_ledger
msgid "General Ledger"
msgstr "총 계정 원장"

#. module: account
#: model:ir.model,name:account.model_account_report_general_ledger
msgid "General Ledger Report"
msgstr "총 계정 원장 보고서"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_generate_entries
msgid "Generate Entries"
msgstr "항목 생성"

#. module: account
#: model:ir.ui.menu,name:account.account_reports_legal_statements_menu
msgid "Generic Statements"
msgstr "종합 명세서"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Get started"
msgstr "시작하기"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Get warnings when invoicing specific customers"
msgstr "특정 고객에게 청구할 때 경고 받기"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Get your bank statements automatically imported every 4 hours, or in one-"
"click, using Yodlee and Plaid services. Once installed, set “Bank Feeds” to "
"“Bank Synchronization” in bank account settings. Then, click “Configure” on "
"the online account to enter your bank credentials."
msgstr ""
"요들리 또는 플레이드 서비스를 사용하여 4시간마다 또는 클릭 한 번으로 예금거래 명세서를 자동으로 가져오십시오. 서비스를 설치하면 은행 "
"계좌 설정에서 “은행 피드”를 “은행 동기화”로 설합니다. 그런 다음, 온라인 계정에서 “구성”을 클릭하고 은행 자격 증명을 입력합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_sequence
msgid "Gives the sequence of this line when displaying the invoice."
msgstr "청구서를 표시할 때 이 라인의 순서를 부여합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_sequence
msgid ""
"Gives the sequence order when displaying a list of bank statement lines."
msgstr "예금거래 명세서 라인 목록을 표시할 때 순서를 지정합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_tax_sequence
msgid "Gives the sequence order when displaying a list of invoice tax."
msgstr "세금 계산서 목록을 표시할 때 순서를 지정합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line_sequence
msgid ""
"Gives the sequence order when displaying a list of payment terms lines."
msgstr "결제 조건 라인 목록을 표시할 때 순서를 부여합니다."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:57
#, python-format
msgid "Go to bank statement(s)"
msgstr "예금거래 명세서로 이동하기"

#. module: account
#: code:addons/account/models/account_invoice.py:642
#, python-format
msgid "Go to the configuration panel"
msgstr "구성 패널로 이동하기"

#. module: account
#: selection:res.partner,trust:0
msgid "Good Debtor"
msgstr "우량 차변"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:46
#, python-format
msgid "Good Job!"
msgstr "잘하셨습니다!"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_group_id
#: model:ir.model.fields,field_description:account.field_account_account_template_group_id
msgid "Group"
msgstr "그룹"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_search
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#: model:ir.ui.view,arch_db:account.view_account_template_search
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Group By"
msgstr "그룹화"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_group_invoice_lines
msgid "Group Invoice Lines"
msgstr "그룹 청구서 라인"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Group of Taxes"
msgstr "세금 그룹"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Group received checks before depositing them to the bank"
msgstr "은행에 예치하기 전에 그룹이 전표를 수령했습니다."

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "HALF-UP"
msgstr "하프-업"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_has_accounting_entries
msgid "Has Accounting Entries"
msgstr "계좌 항목이 있음"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_has_invoices
msgid "Has Invoices"
msgstr "청구서가 있음"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_has_outstanding
msgid "Has Outstanding"
msgstr "미불금이 있음"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_has_unreconciled_entries
#: model:ir.model.fields,field_description:account.field_res_users_has_unreconciled_entries
msgid "Has Unreconciled Entries"
msgstr "비조정 항목이 있음"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_hide_payment_method
#: model:ir.model.fields,field_description:account.field_account_payment_hide_payment_method
#: model:ir.model.fields,field_description:account.field_account_register_payments_hide_payment_method
msgid "Hide Payment Method"
msgstr "결제 방법 숨기기"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_account_hide_setup_bar
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Hide Setup Bar"
msgstr "설정 막대 숨기기"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_hide_tax_exigibility
msgid "Hide Use Cash Basis Option"
msgstr "현금주의 옵션 사용 숨기기"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "How do account types affect your reports?"
msgstr "계정 유형이 보고서에 어떤 영향을 미칩니까?"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "How total tax amount is computed in orders and invoices"
msgstr "주문과 청구서에서 세액 합계가 계산되는 방법입니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_id
#: model:ir.model.fields,field_description:account.field_account_account_id
#: model:ir.model.fields,field_description:account.field_account_account_tag_id
#: model:ir.model.fields,field_description:account.field_account_account_template_id
#: model:ir.model.fields,field_description:account.field_account_account_type_id
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_id
#: model:ir.model.fields,field_description:account.field_account_balance_report_id
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_id
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_id
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_id
#: model:ir.model.fields,field_description:account.field_account_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_common_account_report_id
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_id
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_id
#: model:ir.model.fields,field_description:account.field_account_common_report_id
#: model:ir.model.fields,field_description:account.field_account_financial_report_id
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_id
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_id
#: model:ir.model.fields,field_description:account.field_account_group_id
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_id
#: model:ir.model.fields,field_description:account.field_account_invoice_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_id
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_id
#: model:ir.model.fields,field_description:account.field_account_journal_id
#: model:ir.model.fields,field_description:account.field_account_move_id
#: model:ir.model.fields,field_description:account.field_account_move_line_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_id
#: model:ir.model.fields,field_description:account.field_account_move_reversal_id
#: model:ir.model.fields,field_description:account.field_account_opening_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_id
#: model:ir.model.fields,field_description:account.field_account_payment_id
#: model:ir.model.fields,field_description:account.field_account_payment_method_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_id
#: model:ir.model.fields,field_description:account.field_account_print_journal_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_id
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_id
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_id
#: model:ir.model.fields,field_description:account.field_account_tax_group_id
#: model:ir.model.fields,field_description:account.field_account_tax_id
#: model:ir.model.fields,field_description:account.field_account_tax_report_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_id
#: model:ir.model.fields,field_description:account.field_account_unreconcile_id
#: model:ir.model.fields,field_description:account.field_accounting_report_id
#: model:ir.model.fields,field_description:account.field_cash_box_in_id
#: model:ir.model.fields,field_description:account.field_cash_box_out_id
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance_id
#: model:ir.model.fields,field_description:account.field_report_account_report_financial_id
#: model:ir.model.fields,field_description:account.field_report_account_report_generalledger_id
#: model:ir.model.fields,field_description:account.field_report_account_report_journal_id
#: model:ir.model.fields,field_description:account.field_report_account_report_overdue_id
#: model:ir.model.fields,field_description:account.field_report_account_report_partnerledger_id
#: model:ir.model.fields,field_description:account.field_report_account_report_tax_id
#: model:ir.model.fields,field_description:account.field_report_account_report_trialbalance_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_id
#: model:ir.model.fields,field_description:account.field_validate_account_move_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_id
msgid "ID"
msgstr "ID"

#. module: account
#: code:addons/account/models/chart_template.py:193
#, python-format
msgid "INV"
msgstr "INV"

#. module: account
#: code:addons/account/models/account_bank_statement.py:389
#, python-format
msgid "If \"Amount Currency\" is specified, then \"Amount\" must be as well."
msgstr "\"화폐 액수\"가 지정되면 \"금액\"도 지정해야 합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_nocreate
msgid ""
"If checked, the new chart of accounts will not contain this by default."
msgstr "여기에 표시하면 계정의 새 차트는 이를 기본값으로 포함하지 않습니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_reversal_journal_id
msgid "If empty, uses the journal of the journal entry to be reversed."
msgstr "비워둘 경우, 분개장의 분개를 사용하여 역전합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_include_base_amount
#: model:ir.model.fields,help:account.field_account_tax_template_include_base_amount
msgid ""
"If set, taxes which are computed after this one will be computed based on "
"the price tax included."
msgstr "이 옵션을 설정할 경우, 이 다음에 계산된 세금은 세금 포함 가격 기준으로 계산됩니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_analytic
#: model:ir.model.fields,help:account.field_account_tax_template_analytic
msgid ""
"If set, the amount computed by this tax will be assigned to the same "
"analytic account as the invoice line (if any)"
msgstr "이 옵션을 설정하면 이 세금에서 계산된 금액이 청구서 라인과 동일한 분석 계정에 할당됩니다(해당할 경우)."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_active
msgid ""
"If the active field is set to False, it will allow you to hide the payment "
"terms without removing it."
msgstr "활성 필드가 false로 설정되어 있을 경우, 결제 조건을 제거하지 않고도 숨길 수 있습니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_group_invoice_lines
msgid ""
"If this box is checked, the system will try to group the accounting lines "
"when generating them from invoices."
msgstr "이 상자에 표시하면 청구서에서 회계 라인을 생성할 때 그룹화를 시도합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you check this box, you will be able to collect payments using SEPA "
"Direct Debit mandates."
msgstr "이 상자에 표시하면 SEPA 자동 이체 지침에 따라 지급금을 수금할 수 있습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you check this box, you will be able to register your payment using SEPA."
msgstr "이 상자에 표시하면 SEPA를 사용하여 결제를 등록할 수 있습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"If you have less than 200 contacts, we recommend you\n"
"                                        create them manually."
msgstr ""
"연락처가 200개 미만이라면\n"
"                                        수동으로 생성하는 것이 좋습니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_report_general_ledger_initial_balance
msgid ""
"If you selected date, this field allow you to add a row to display the "
"amount of debit/credit/balance that precedes the filter you've set."
msgstr "날짜를 선택했을 때, 이 필드를 사용하면 설정한 필터에 선행하는 차변/대변/잔액 금액을 표시하는 행을 추가합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
msgid ""
"If you unreconcile transactions, you must also verify all the actions that "
"are linked to those transactions because they will not be disabled"
msgstr "거래를 조정하지 않으면 비활성화되지 않기 때문에 해당 거래와 연결된 모든 작업을 검증해야 합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_payment_term_id
msgid ""
"If you use payment terms, the due date will be computed automatically at the"
" generation of accounting entries. If you keep the payment terms and the due"
" date empty, it means direct payment. The payment terms may compute several "
"due dates, for example 50% now, 50% in one month."
msgstr ""
"지불 조건을 사용할 경우, 만기일은 계정 항목 생성 시 자동으로 계산됩니다. 지불 조건과 만기일을 비워 두면 직접 지불을 의미합니다. "
"지불 조건에서 여러 개 만기일이 계산될 수 있습니다(예: 현재 50%, 1개월 이내에 50%)."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_date_due
msgid ""
"If you use payment terms, the due date will be computed automatically at the"
" generation of accounting entries. The Payment terms may compute several due"
" dates, for example 50% now and 50% in one month, but if you want to force a"
" due date, make sure that the payment term is not set on the invoice. If you"
" keep the Payment terms and the due date empty, it means direct payment."
msgstr ""
"지불 조건을 사용할 경우, 만기일은 계정 항목 생성 시 자동으로 계산됩니다. 지불 조건에서 여러 만기일이 계산될 수 있습니다(예: 현재 "
"50%, 1개월 이내 50%). 그러나 만기일을 강제 집행하고 싶다면 지불 조건이 청구서에 설정되지 않도록 하십시오. 지불 조건과 "
"만기일이 비어 있으면 직접 지불을 의미합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"If you want to be able to send customer statements from Odoo, you must:"
msgstr "Odoo에서 고객 명세서를 발송하려면 다음을 수행해야 합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"If you want to be able to send your customers their statements \n"
"                        from Odoo, you first need to record all outstanding transactions \n"
"                        in the payable and receivable accounts. These would be invoices \n"
"                        that have not been paid or payments that have not been reconciled."
msgstr ""
"Odoo에서 고객에게 명세서를 발송하고 싶다면\n"
"                        먼저 지급 계정과 수취 계정에\n"
"                        모든 미불 거래를 기록해야 합니다.\n"
"                        이는 지불되지 않은 청구서 또는 조정되지 않은 지불 금액이 해당합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "If you want to do it yourself:"
msgstr "직접 실행하고 싶을 경우:"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you're selling digital goods to customers in the EU, you must charge VAT "
"based on your customers' locations. This rule applies regardless of you are "
"located. Digital goods are defined in the legislation as broadcasting, "
"telecommunications, and services that are electronically supplied instead of"
" shipped. Gift cards sent online are not included in the definition."
msgstr ""
"EU에 있는 고객에게 디지털 상품을 판매 중이라면 고객의지역에 따라 VAT를 부과해야 합니다. 이 규정은 귀하의 위치를 불문하고 "
"적용됩니다. 디지털 상품은 법률상으로 방송, 전기통신, 배송되지 않고 전자적으로 공급되는 서비스로 정의됩니다. 온라인으로 발송되는 기프트"
" 카드는 정의에 포함되지 않습니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_product_image
msgid ""
"Image of the product variant (Big-sized image of product template if false)."
" It is automatically resized as a 1024x1024px image, with aspect ratio "
"preserved."
msgstr ""
"제품 종류 이미지(false일 경우, 제품 템플릿의 대형 이미지). 가로세로비는 그대로 유지되고 1024x1024px 크기로 자동 "
"조정됩니다."

#. module: account
#: model:account.payment.term,name:account.account_payment_term_immediate
msgid "Immediate Payment"
msgstr "즉시 지불"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_qif
msgid "Import .qif files"
msgstr ".qif 파일 가져오기"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_csv
msgid "Import in .csv format"
msgstr ".csv 형식으로 가져오기"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_ofx
msgid "Import in .ofx format"
msgstr ".ofx 형식으로 가져오기"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_camt
msgid "Import in CAMT.053 format"
msgstr "CAMT.053 형식으로 가져오기"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Import using the \"Import\" button on the top left corner of"
msgstr "왼쪽 상단 모서리의 \"가져오기\" 버튼으로 가져오기"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements automatically"
msgstr "예금거래 명세서를 자동으로 가져오기"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in CAMT.053"
msgstr "CAMT.053으로 예금거래 명세서 가져오기"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in CSV"
msgstr "CSV로 예금거래 명세서 가져오기"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in OFX"
msgstr "OFX로 예금거래 명세서 가져오기"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in QIF"
msgstr "QIF로 예금거래 명세서 가져오기"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Importing your statements in via a supported file format (QIF, OFX, CODA or "
"CSV format)"
msgstr "지원 파일 형식(QIF, OFX, CODA 또는 CSV 형식)으로 명세서 가져오기"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "In Odoo,"
msgstr "Odoo에서"

#. module: account
#: code:addons/account/models/account_bank_statement.py:409
#, python-format
msgid ""
"In order to delete a bank statement line, you must first cancel it to delete"
" related journal items."
msgstr "예금거래 명세서 라인을 삭제하려면 먼저 취소한 다음에 관련 분개 항목을 삭제해야 합니다."

#. module: account
#: code:addons/account/models/account_bank_statement.py:199
#, python-format
msgid ""
"In order to delete a bank statement, you must first cancel it to delete "
"related journal items."
msgstr "예금거래 명세서를 삭제하려면 먼저 취소한 다음에 관련 분개 항목을 삭제해야 합니다."

#. module: account
#: code:addons/account/models/account_payment.py:144
#, python-format
msgid ""
"In order to pay multiple invoices at once, they must use the same currency."
msgstr "한 번에 여러 청구서를 지불하려면 동일한 통화를 사용해야 합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "In your old accounting software, print a trial balance"
msgstr "이전 회계 관리 소프트웨어에서 시산표를 인쇄합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Inactive"
msgstr "비활성"

#. module: account
#: selection:account.payment.method,payment_type:0
msgid "Inbound"
msgstr "인바운드"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_initial_balance
msgid "Include Initial Balances"
msgstr "기초 잔고 포함"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_analytic
msgid "Include in Analytic Cost"
msgstr "분석 비용에 포함"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_price_include
#: model:ir.model.fields,field_description:account.field_account_tax_template_price_include
msgid "Included in Price"
msgstr "가격에 포함됨"

#. module: account
#: model:account.account.type,name:account.data_account_type_revenue
#: model:account.financial.report,name:account.account_financial_report_income0
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Income"
msgstr "수입"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_category_property_account_income_categ_id
#: model:ir.model.fields,field_description:account.field_product_product_property_account_income_id
#: model:ir.model.fields,field_description:account.field_product_template_property_account_income_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Income Account"
msgstr "수입 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_income_id
msgid "Income Account on Product Template"
msgstr "제품 템플릿에 대한 수입 계정"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:700
#, python-format
msgid "Incorrect Operation"
msgstr "잘못된 작업"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Information"
msgstr "정보"

#. module: account
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
msgid "Information addendum"
msgstr "정보 부록"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:296
#: code:addons/account/models/company.py:311
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:9
#, python-format
msgid "Initial Balances"
msgstr "기초 잔고"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_stock_account_input_categ_id
#: model:ir.model.fields,field_description:account.field_res_company_property_stock_account_input_categ_id
msgid "Input Account for Stock Valuation"
msgstr "주식 가치 평가를 위한 투입 계정"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Install Chart of Account"
msgstr "계정과목 일람표 설치"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Install More Packages"
msgstr "더 많은 패키지 설치"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_transfer_account_id
msgid "Inter-Banks Transfer Account"
msgstr "은행간 송금 계정"

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_transfer_account_id
#: model:ir.model.fields,help:account.field_res_company_transfer_account_id
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_transfer_account_id
msgid ""
"Intermediary account used when moving money from a liquidity account to "
"another"
msgstr "유동 계정에서 다른 계정으로 자금을 이동할 때 사용하는 중개 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_narration
msgid "Internal Note"
msgstr "내부 메모"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_note
msgid "Internal Notes"
msgstr "내부 메모"

#. module: account
#: selection:account.payment,payment_type:0
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Internal Transfer"
msgstr "내부 이전"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_internal_type
msgid "Internal Type"
msgstr "내부 유형"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_template_form
msgid "Internal notes..."
msgstr "내부 메모..."

#. module: account
#: code:addons/account/models/partner.py:45
#, python-format
msgid "Invalid \"Zip Range\", please configure it properly."
msgstr "잘못된 \"우편번호 범위\"입니다. 적절히 구성해주십시오."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Invite Users"
msgstr "사용자 초대"

#. module: account
#: code:addons/account/models/account_invoice.py:1214
#: model:ir.model,name:account.model_account_invoice
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_invoice_id
#: model:ir.model.fields,field_description:account.field_account_move_line_invoice_id
#: model:ir.model.fields,field_description:account.field_res_partner_invoice_warn
#: model:ir.model.fields,field_description:account.field_res_users_invoice_warn
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:res.request.link,name:account.req_link_invoice
#, python-format
msgid "Invoice"
msgstr "청구서"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid "Invoice #"
msgstr "청구서 번호"

#. module: account
#: code:addons/account/models/account_invoice.py:440
#, python-format
msgid "Invoice - %s"
msgstr "청구서 - %s"

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_created
#: model:mail.message.subtype,name:account.mt_invoice_created
msgid "Invoice Created"
msgstr "생성된 청구서"

#. module: account
#: code:addons/account/controllers/portal.py:70
#: model:ir.model.fields,field_description:account.field_account_invoice_date_invoice
#: model:ir.ui.view,arch_db:account.portal_my_invoices
#: model:ir.ui.view,arch_db:account.report_payment_receipt
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Invoice Date"
msgstr "청구 날짜"

#. module: account
#: model:ir.model,name:account.model_account_invoice_line
#: model:ir.ui.view,arch_db:account.view_invoice_line_form
#: model:ir.ui.view,arch_db:account.view_invoice_line_tree
msgid "Invoice Line"
msgstr "청구서 라인"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_invoice_line_ids
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Invoice Lines"
msgstr "청구서 라인"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Invoice Number"
msgstr "청구서 번호"

#. module: account
#: sql_constraint:account.invoice:0
msgid "Invoice Number must be unique per Company!"
msgstr "청구서 번호는 기업마다 달라야 합니다!"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Invoice Number:"
msgstr "청구서 번호:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_invoice_id
msgid "Invoice Reference"
msgstr "청구서 참조"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_state
msgid "Invoice Status"
msgstr "청구서 상태"

#. module: account
#: model:ir.model,name:account.model_account_invoice_tax
msgid "Invoice Tax"
msgstr "세금 계산서"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_invoice_id
msgid "Invoice for which this invoice is the credit note"
msgstr "이 청구서가 신용 전표가 되는 청구서"

#. module: account
#: code:addons/account/models/account_invoice.py:753
#, python-format
msgid "Invoice must be cancelled in order to reset it to draft."
msgstr "초안으로 재설정하려면 청구서를 취소해야 합니다."

#. module: account
#: code:addons/account/models/account_invoice.py:801
#, python-format
msgid "Invoice must be in draft or open state in order to be cancelled."
msgstr "청구서를 취소하려면 초안 또는 개시 상태여야 합니다."

#. module: account
#: code:addons/account/models/account_invoice.py:775
#, python-format
msgid "Invoice must be in draft state in order to validate it."
msgstr "청구서를 검증하려면 초안 상태여야 합니다."

#. module: account
#: code:addons/account/models/account_invoice.py:795
#, python-format
msgid "Invoice must be paid in order to set it to register payment."
msgstr "청구서를 지불 등록하려면 대금을 지불해야 합니다."

#. module: account
#: code:addons/account/models/account_invoice.py:787
#, python-format
msgid "Invoice must be validated in order to set it to register payment."
msgstr "청구서를 지불 등록하려면 검증을 완료해야 합니다."

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_paid
msgid "Invoice paid"
msgstr "지불된 청구서"

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_validated
msgid "Invoice validated"
msgstr "검증된 청구서"

#. module: account
#: model:mail.template,report_name:account.email_template_edi_invoice
msgid ""
"Invoice_${(object.number or '').replace('/','_')}_${object.state == 'draft' "
"and 'draft' or ''}"
msgstr ""
"Invoice_${(object.number or '').replace('/','_')}_${object.state == 'draft' "
"and 'draft' or ''}"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Invoiced"
msgstr "청구서 발행됨"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_refund_out_tree
#: model:ir.actions.act_window,name:account.action_invoice_tree
#: model:ir.actions.report,name:account.account_invoices
#: model:ir.model.fields,field_description:account.field_account_payment_invoice_ids
#: model:ir.model.fields,field_description:account.field_account_register_payments_invoice_ids
#: model:ir.model.fields,field_description:account.field_res_partner_invoice_ids
#: model:ir.model.fields,field_description:account.field_res_users_invoice_ids
#: model:ir.ui.menu,name:account.menu_action_account_invoice_report_all
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.portal_my_home_invoice
#: model:ir.ui.view,arch_db:account.portal_my_home_menu_invoice
#: model:ir.ui.view,arch_db:account.portal_my_invoices
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_account_payment_form
#: model:ir.ui.view,arch_db:account.view_account_payment_graph
#: model:ir.ui.view,arch_db:account.view_invoice_graph
#: model:ir.ui.view,arch_db:account.view_invoice_line_calendar
#: model:ir.ui.view,arch_db:account.view_invoice_pivot
msgid "Invoices"
msgstr "청구서"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_report_all
#: model:ir.actions.act_window,name:account.action_account_invoice_report_all_supp
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_graph
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_pivot
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Invoices Analysis"
msgstr "청구서 분석"

#. module: account
#: model:ir.model,name:account.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "청구서 통계"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:186
#, python-format
msgid "Invoices owed to you"
msgstr "대금을 받아야 할 청구서"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Invoices to validate"
msgstr "검증할 청구서"

#. module: account
#: model:ir.actions.report,name:account.account_invoices_without_payment
msgid "Invoices without Payment"
msgstr "결제하지 않은 청구서"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance
#: model:ir.ui.view,arch_db:account.product_template_form_view
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Invoicing"
msgstr "청구서 발행"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_is_unaffected_earnings_line
msgid "Is Unaffected Earnings Line"
msgstr "영향을 받지 않은 순수익 라인입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_is_rounding_line
msgid "Is a rounding line in case of cash rounding."
msgstr "현금 올림의 경우, 올림 라인입니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_is_difference_zero
msgid "Is zero"
msgstr "0입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_default_credit_account_id
#: model:ir.model.fields,help:account.field_res_company_income_currency_exchange_account_id
msgid "It acts as a default account for credit amount"
msgstr "대변 금액의 기본 계정 역할을 합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_default_debit_account_id
#: model:ir.model.fields,help:account.field_res_company_expense_currency_exchange_account_id
msgid "It acts as a default account for debit amount"
msgstr "차변 금액의 기본 계정 역할을 합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_report_partner_ledger_amount_currency
msgid ""
"It adds the currency column on report if the currency differs from the "
"company currency."
msgstr "통화가 회사 통화와 다르면 보고서의 통화 열에 추가합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_reconciled
msgid ""
"It indicates that the invoice has been paid and the journal entry of the "
"invoice has been reconciled with one or several journal entries of payment."
msgstr "청구서가 지급되었고 청구서의 분개가 지불금 분개 1개 이상으로 조정되었음을 나타냅니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_sent
msgid "It indicates that the invoice has been sent."
msgstr "청구서가 발송되었음을 나타냅니다."

#. module: account
#: code:addons/account/models/account_move.py:1050
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr "반드시 계정과 분개를 지정하여 상각을 생성해야 합니다."

#. module: account
#: code:addons/account/models/account_payment.py:470
#, python-format
msgid ""
"It is not allowed to delete a payment that already created a journal entry "
"since it would create a gap in the numbering. You should create the journal "
"entry again and cancel it thanks to a regular revert."
msgstr ""
"분개 항목을 생성한 지불금은 번호 순서에 공백이 생기기 때문에 삭제할 수 없습니다. 다시 분개장을 생성하고 정기 환원하여 취소해야 "
"합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"It's common practice to change your accounting software \n"
"                            at the end of a fiscal year. This allows you to have less \n"
"                            data to import and balances to set. If you plan to do so, \n"
"                            we recommend you start using Odoo for invoicing and payments \n"
"                            now, and then move all other accounting transactions at a later time."
msgstr ""
"통상적으로 회계연도 말기에\n"
"                            회계 관리 소프트웨어를 변경합니다. 이렇게 하면\n"
"                            가져올 데이터와 설정할 잔고가 줄어듭니다. 회계 관리 소프트웨어를 변경할 계획이라면\n"
"                            지금부터 청구서 발행과 지급에 Odoo를 사용하다가\n"
"                            나중에 다른 회계 거래를 이동하는 것을 권장합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"It's recommended that you do not delete any accounts, even if they are not "
"relevant. Simply make them inactive."
msgstr "관련이 없더라도 계정은 삭제하지 않는 것이 좋습니다. 해당 계정은 비활성화하십시오."

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Italic Text (smaller)"
msgstr "이탤릭 텍스트(작은 문자)"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Items"
msgstr "항목"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
msgid "JRNL"
msgstr "JRNL"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "January"
msgstr "1월"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:185
#: code:addons/account/static/src/xml/account_reconciliation.xml:229
#: model:ir.model,name:account.model_account_journal
#: model:ir.model.fields,field_description:account.field_account_bank_statement_journal_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_journal_id
#: model:ir.model.fields,field_description:account.field_account_invoice_journal_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_journal_id
#: model:ir.model.fields,field_description:account.field_account_move_journal_id
#: model:ir.model.fields,field_description:account.field_account_move_line_journal_id
#: model:ir.model.fields,field_description:account.field_account_opening_journal_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_journal_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_journal_id
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_journal_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Journal"
msgstr "분개"

#. module: account
#: selection:account.report.general.ledger,sortby:0
msgid "Journal & Partner"
msgstr "분개 & 협력사"

#. module: account
#: code:addons/account/models/account_bank_statement.py:254
#: model:ir.actions.act_window,name:account.action_move_journal_line
#: model:ir.actions.act_window,name:account.action_move_select
#: model:ir.ui.menu,name:account.menu_action_move_journal_line_form
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#: model:ir.ui.view,arch_db:account.view_move_tree
#, python-format
msgid "Journal Entries"
msgstr "분개 기입 항목"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Journal Entries by Month"
msgstr "월별 분개 기재"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_move_id
#: model:ir.model.fields,field_description:account.field_account_move_line_move_id
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Journal Entry"
msgstr "분개 기입 항목"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_move_name
#: model:ir.model.fields,field_description:account.field_account_invoice_move_name
#: model:ir.model.fields,field_description:account.field_account_payment_move_name
msgid "Journal Entry Name"
msgstr "분개 이름"

#. module: account
#: selection:account.print.journal,sort_selection:0
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Journal Entry Number"
msgstr "분개 번호"

#. module: account
#: model:ir.model,name:account.model_account_move_line
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Journal Item"
msgstr "분개 항목"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_writeoff_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_label
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Journal Item Label"
msgstr "분개 항목 라벨"

#. module: account
#: code:addons/account/models/account_payment.py:414
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_move_line
#: model:ir.actions.act_window,name:account.act_account_move_to_account_move_line_open
#: model:ir.actions.act_window,name:account.action_account_moves_all_a
#: model:ir.actions.act_window,name:account.action_account_moves_all_tree
#: model:ir.actions.act_window,name:account.action_move_line_graph
#: model:ir.actions.act_window,name:account.action_move_line_graph_cash_basis
#: model:ir.actions.act_window,name:account.action_move_line_graph_cash_basis_posted
#: model:ir.actions.act_window,name:account.action_move_line_graph_posted
#: model:ir.actions.act_window,name:account.action_move_line_select
#: model:ir.actions.act_window,name:account.action_move_line_select_by_partner
#: model:ir.actions.act_window,name:account.action_move_line_select_by_type
#: model:ir.actions.act_window,name:account.action_move_line_select_tax_audit
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_journal_entry_ids
#: model:ir.model.fields,field_description:account.field_account_move_line_ids
#: model:ir.model.fields,field_description:account.field_res_partner_journal_item_count
#: model:ir.model.fields,field_description:account.field_res_users_journal_item_count
#: model:ir.ui.menu,name:account.menu_action_account_moves_all
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.setup_opening_move_lines_tree
#: model:ir.ui.view,arch_db:account.view_account_payment_form
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_pivot
#: model:ir.ui.view,arch_db:account.view_move_line_tree
#, python-format
msgid "Journal Items"
msgstr "분개 항목"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_action.js:323
#: model:ir.actions.client,name:account.action_manual_reconcile
#, python-format
msgid "Journal Items to Reconcile"
msgstr "조정할 분개 항목"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_name
msgid "Journal Name"
msgstr "분개장 명칭"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "Journal and Partner"
msgstr "분개 및 협력사"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Journal invoices with period in current year"
msgstr "당기가 포함된 분개 청구서"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Journal items where matching number isn't set"
msgstr "일치하는 번호가 설정되지 않은 분개 항목"

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_opening_journal_id
msgid ""
"Journal where the opening entry of this company's accounting has been "
"posted."
msgstr "이 회사 회계의 개시 항목이 전기된 분개입니다."

#. module: account
#: model:ir.actions.act_window,name:account.action_account_journal_form
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_journal_ids
#: model:ir.model.fields,field_description:account.field_account_balance_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_account_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_print_journal_journal_ids
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_journal_ids
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_journal_ids
#: model:ir.model.fields,field_description:account.field_account_tax_report_journal_ids
#: model:ir.model.fields,field_description:account.field_accounting_report_journal_ids
#: model:ir.ui.menu,name:account.menu_action_account_journal_form
msgid "Journals"
msgstr "분개장"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_print_journal_menu
#: model:ir.actions.report,name:account.action_report_journal
#: model:ir.ui.menu,name:account.menu_print_journal
msgid "Journals Audit"
msgstr "분개장 감사"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "July"
msgstr "7월"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "June"
msgstr "6월"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_reason
msgid "Justification"
msgstr "근거"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_kanban_dashboard
msgid "Kanban Dashboard"
msgstr "칸반 대시보드"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "칸반 대시보드 그래프"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Keep empty for no control"
msgstr "통제하지 않을 경우 비워두기"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_date_invoice
msgid "Keep empty to use the current date"
msgstr "오늘 날짜를 사용하려면 비워두기"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_date
msgid "Keep empty to use the invoice date."
msgstr "청구 날짜를 사용하려면 비워둡니다."

#. module: account
#: selection:account.payment,payment_difference_handling:0
msgid "Keep open"
msgstr "열어두기"

#. module: account
#: model:ir.model.fields,help:account.field_product_product_property_account_income_id
#: model:ir.model.fields,help:account.field_product_template_property_account_income_id
msgid ""
"Keep this field empty to use the default value from the product category."
msgstr "이 필드를 비워두면 제품 범주의 기본값을 사용합니다."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:522
#: code:addons/account/static/src/xml/account_reconciliation.xml:189
#: code:addons/account/static/src/xml/account_reconciliation.xml:230
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_name
#: model:ir.model.fields,field_description:account.field_account_move_line_name
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#, python-format
msgid "Label"
msgstr "라벨"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_description
msgid "Label on Invoices"
msgstr "청구서에 대한 라벨"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment___last_update
#: model:ir.model.fields,field_description:account.field_account_account___last_update
#: model:ir.model.fields,field_description:account.field_account_account_tag___last_update
#: model:ir.model.fields,field_description:account.field_account_account_template___last_update
#: model:ir.model.fields,field_description:account.field_account_account_type___last_update
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance___last_update
#: model:ir.model.fields,field_description:account.field_account_balance_report___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line___last_update
#: model:ir.model.fields,field_description:account.field_account_cash_rounding___last_update
#: model:ir.model.fields,field_description:account.field_account_cashbox_line___last_update
#: model:ir.model.fields,field_description:account.field_account_chart_template___last_update
#: model:ir.model.fields,field_description:account.field_account_common_account_report___last_update
#: model:ir.model.fields,field_description:account.field_account_common_journal_report___last_update
#: model:ir.model.fields,field_description:account.field_account_common_partner_report___last_update
#: model:ir.model.fields,field_description:account.field_account_common_report___last_update
#: model:ir.model.fields,field_description:account.field_account_financial_report___last_update
#: model:ir.model.fields,field_description:account.field_account_financial_year_op___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template___last_update
#: model:ir.model.fields,field_description:account.field_account_full_reconcile___last_update
#: model:ir.model.fields,field_description:account.field_account_group___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_line___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_refund___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_report___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_tax___last_update
#: model:ir.model.fields,field_description:account.field_account_journal___last_update
#: model:ir.model.fields,field_description:account.field_account_move___last_update
#: model:ir.model.fields,field_description:account.field_account_move_line___last_update
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile___last_update
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff___last_update
#: model:ir.model.fields,field_description:account.field_account_move_reversal___last_update
#: model:ir.model.fields,field_description:account.field_account_opening___last_update
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile___last_update
#: model:ir.model.fields,field_description:account.field_account_payment___last_update
#: model:ir.model.fields,field_description:account.field_account_payment_method___last_update
#: model:ir.model.fields,field_description:account.field_account_payment_term___last_update
#: model:ir.model.fields,field_description:account.field_account_payment_term_line___last_update
#: model:ir.model.fields,field_description:account.field_account_print_journal___last_update
#: model:ir.model.fields,field_description:account.field_account_reconcile_model___last_update
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template___last_update
#: model:ir.model.fields,field_description:account.field_account_register_payments___last_update
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger___last_update
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger___last_update
#: model:ir.model.fields,field_description:account.field_account_tax___last_update
#: model:ir.model.fields,field_description:account.field_account_tax_group___last_update
#: model:ir.model.fields,field_description:account.field_account_tax_report___last_update
#: model:ir.model.fields,field_description:account.field_account_tax_template___last_update
#: model:ir.model.fields,field_description:account.field_account_unreconcile___last_update
#: model:ir.model.fields,field_description:account.field_accounting_report___last_update
#: model:ir.model.fields,field_description:account.field_cash_box_in___last_update
#: model:ir.model.fields,field_description:account.field_cash_box_out___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_financial___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_generalledger___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_journal___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_overdue___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_partnerledger___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_tax___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_trialbalance___last_update
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard___last_update
#: model:ir.model.fields,field_description:account.field_validate_account_move___last_update
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts___last_update
msgid "Last Modified on"
msgstr "마지막 수정일"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Last Month"
msgstr "전 월"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:119
#, python-format
msgid "Last Reconciliation:"
msgstr "마지막 조정:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_write_uid
#: model:ir.model.fields,field_description:account.field_account_account_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_account_type_write_uid
#: model:ir.model.fields,field_description:account.field_account_account_write_uid
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_write_uid
#: model:ir.model.fields,field_description:account.field_account_balance_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_write_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_write_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_account_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_financial_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_write_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_write_uid
#: model:ir.model.fields,field_description:account.field_account_group_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_write_uid
#: model:ir.model.fields,field_description:account.field_account_journal_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_write_uid
#: model:ir.model.fields,field_description:account.field_account_opening_write_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_write_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal_write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_write_uid
#: model:ir.model.fields,field_description:account.field_account_register_payments_write_uid
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_write_uid
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_write_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile_write_uid
#: model:ir.model.fields,field_description:account.field_accounting_report_write_uid
#: model:ir.model.fields,field_description:account.field_cash_box_in_write_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out_write_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_write_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move_write_uid
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_write_uid
msgid "Last Updated by"
msgstr "마지막으로 업데이트한 사람"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_write_date
#: model:ir.model.fields,field_description:account.field_account_account_template_write_date
#: model:ir.model.fields,field_description:account.field_account_account_type_write_date
#: model:ir.model.fields,field_description:account.field_account_account_write_date
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_write_date
#: model:ir.model.fields,field_description:account.field_account_balance_report_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_write_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_write_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_write_date
#: model:ir.model.fields,field_description:account.field_account_chart_template_write_date
#: model:ir.model.fields,field_description:account.field_account_common_account_report_write_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_write_date
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_write_date
#: model:ir.model.fields,field_description:account.field_account_common_report_write_date
#: model:ir.model.fields,field_description:account.field_account_financial_report_write_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_write_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_write_date
#: model:ir.model.fields,field_description:account.field_account_group_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_line_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_write_date
#: model:ir.model.fields,field_description:account.field_account_journal_write_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_write_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_write_date
#: model:ir.model.fields,field_description:account.field_account_move_line_write_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal_write_date
#: model:ir.model.fields,field_description:account.field_account_move_write_date
#: model:ir.model.fields,field_description:account.field_account_opening_write_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_method_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_write_date
#: model:ir.model.fields,field_description:account.field_account_print_journal_write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_write_date
#: model:ir.model.fields,field_description:account.field_account_register_payments_write_date
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_write_date
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_group_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_report_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_template_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_write_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile_write_date
#: model:ir.model.fields,field_description:account.field_accounting_report_write_date
#: model:ir.model.fields,field_description:account.field_cash_box_in_write_date
#: model:ir.model.fields,field_description:account.field_cash_box_out_write_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_write_date
#: model:ir.model.fields,field_description:account.field_validate_account_move_write_date
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_write_date
msgid "Last Updated on"
msgstr "마지막 업데이트 날짜"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Last day of current month"
msgstr "당월 말일"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Last day of following month"
msgstr "익월 말"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_last_time_entries_checked
#: model:ir.model.fields,help:account.field_res_users_last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed for this partner. "
"It is set either if there's not at least an unreconciled debit and an "
"unreconciled credit or if you click the \"Done\" button."
msgstr ""
"이 협력사에 대해 마지막으로 청구서 및 결제 매칭이 수행되었습니다. 미조정 차변과 미조정 대변이 없거나 \"완료\" 버튼을 클릭하면 "
"설정됩니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed on this account. It"
" is set either if there's not at least an unreconciled debit and an "
"unreconciled credit Or if you click the \"Done\" button."
msgstr ""
"이 계정에 대해 마지막으로 청구서 및 결제 매칭이 수행되었습니다. 미조정 차변과 미조정 대변이 없거나 \"완료\" 버튼을 클릭하면 "
"설정됩니다."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Late Activities"
msgstr "지연된 활동"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_last_time_entries_checked
#: model:ir.model.fields,field_description:account.field_res_partner_last_time_entries_checked
#: model:ir.model.fields,field_description:account.field_res_users_last_time_entries_checked
msgid "Latest Invoices & Payments Matching Date"
msgstr "마지막 청구서 및 지불 매칭 날짜"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group_parent_left
msgid "Left Parent"
msgstr "왼쪽 상위 항목"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Legal Name"
msgstr "상호"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Legal Notes..."
msgstr "법적 고지..."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_note
msgid "Legal mentions that have to be printed on the invoices."
msgstr "청구서에 인쇄해야 하는 법적 문구입니다."

#. module: account
#: code:addons/account/models/account_invoice.py:216
#, python-format
msgid "Less Payment"
msgstr "지불금 감소"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:24
#, python-format
msgid "Let odoo try to reconcile entries for the user"
msgstr "Odoo가 사용자 대신 항목을 조정합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Let your customers pay their invoices online"
msgstr "고객이 온라인으로 청구서 대금을 지불하게 합니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_level
msgid "Level"
msgstr "수준"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_liability0
#: model:account.financial.report,name:account.account_financial_report_liabilitysum0
msgid "Liability"
msgstr "부채"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_move_id
msgid "Link to the automatically generated Journal Items."
msgstr "자동 생성된 분개 항목과 연결됩니다."

#. module: account
#: selection:account.account.type,type:0
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Liquidity"
msgstr "유동성"

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_tax_template_ids
msgid "List of all the taxes that have to be installed by the wizard"
msgstr "마법사에서 설치해야 하는 모든 세금 목록입니다."

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Litigation"
msgstr "소송"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:29
#, python-format
msgid "Load more"
msgstr "더 로드하기"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_fiscalyear_lock_date
msgid "Lock Date"
msgstr "마감일"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "자문 외 사용자의 마감일"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Logo"
msgstr "로고"

#. module: account
#: code:addons/account/models/account_bank_statement.py:173
#, python-format
msgid "Loss"
msgstr "손실"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_loss_account_id
msgid "Loss Account"
msgstr "손실 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_expense_currency_exchange_account_id
#: model:ir.model.fields,field_description:account.field_res_company_expense_currency_exchange_account_id
msgid "Loss Exchange Rate Account"
msgstr "손실 환율 계정"

#. module: account
#: code:addons/account/models/chart_template.py:195
#, python-format
msgid "MISC"
msgstr "MISC"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Mail your invoices in one-click using"
msgstr "다음을 사용하여 클릭 한 번으로 청구서 발송"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Main Currency"
msgstr "기본 통화"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Main Title 1 (bold, underlined)"
msgstr "주 제목 1(굵은 활자, 밑줄)"

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_currency_id
msgid "Main currency of the company."
msgstr "회사의 기본 통화입니다."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Main currency of your company"
msgstr "회사의 기본 통화"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Manage time &amp; material contracts or fixed-price recurring subscriptions."
msgstr "시간 및 재료 계약 또는 가격이 고정된 구독을 관리하세요."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Manage your employee expenses, from encoding, to payments and reporting."
msgstr "인코딩에서 지급, 보고까지 직원 경비를 관리하세요."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Manage your various fixed assets, such as buildings, machinery, materials, "
"cars, etc..., and calculate their associated depreciation over time."
msgstr "건물, 기계류, 재료, 차량 등의 다양한 고정 자산을 관리하고 시간에 따른 감가상각을 계산하세요."

#. module: account
#: model:ir.ui.menu,name:account.account_management_menu
#: model:ir.ui.menu,name:account.account_reports_management_menu
#: model:ir.ui.menu,name:account.menu_finance_entries_management
msgid "Management"
msgstr "관리"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Managing bank fees"
msgstr "은행 수수료 관리"

#. module: account
#: model:account.payment.method,name:account.account_payment_method_manual_in
#: model:account.payment.method,name:account.account_payment_method_manual_out
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_manual
msgid "Manual"
msgstr "수동"

#. module: account
#: model:ir.ui.view,arch_db:account.view_invoice_tax_form
#: model:ir.ui.view,arch_db:account.view_invoice_tax_tree
msgid "Manual Invoice Taxes"
msgstr "수동 세금 계산서"

#. module: account
#: model:ir.actions.client,name:account.action_manual_reconciliation
#: model:ir.ui.menu,name:account.menu_action_manual_reconciliation
msgid "Manual Reconciliation"
msgstr "수동 조정"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_inbound_payment_method_ids
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo,you are suggested to reconcile the transaction with the batch deposit. Enable this option from the settings."
msgstr ""
"수동: Odoo 외에서 현금, 수표 또는 기타 다른 방식으로 지급받습니다.\n"
"자동: 온라인에서 매입/구독할 때 고객이 저장한 카드에 대해 거래를 요청한 후 결제를 통해 자동으로 지급받습니다(결제 토큰).\n"
"일괄 예금: 은행에 예치할 일괄 예금을 생성하여 여러 고객의 전표를 한 번에 묶습니다. Odoo에서 예금거래 명세서를 인코딩할 때는 일괄 예금으로 거래를 조정하는 것이 좋습니다. 이 옵션은 설정에서 활성화합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_payment_method_id
#: model:ir.model.fields,help:account.field_account_payment_payment_method_id
#: model:ir.model.fields,help:account.field_account_register_payments_payment_method_id
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Check: Pay bill by check and print it from Odoo.\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo, you are suggested to reconcile the transaction with the batch deposit.To enable batch deposit,module account_batch_deposit must be installed.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. To enable sepa credit transfer, module account_sepa must be installed "
msgstr ""
"수동: Odoo 외에서 현금, 수표 또는 기타 다른 방식으로 지급받습니다.\n"
"자동: 온라인에서 매입/구독할 때 고객이 저장한 카드에 대해 거래를 요청한 후 결제를 통해 자동으로 지급받습니다(결제 토큰).\n"
"전표: 전표로 청구 대금을 지불하고 Odoo에서 인쇄합니다.\n"
"일괄 예금: 은행에 예치할 일괄 예금을 생성하여 여러 고객의 전표를 한 번에 묶습니다. Odoo에서 예금거래 명세서를 인코딩할 때는 일괄 예금으로 거래를 조정하는 것이 좋습니다. 일괄 예금을 활성화하려면 account_batch_deposit 모듈을 설치해야 합니다.\n"
"SEPA 신용 이전: 은행에 제출한 SEPA 신용 이전 파일 파일에서 청구 금액을 지불합니다. SEPA 신용 이전을 활성화하려면 account_sepa 모듈을 설치해야 합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_outbound_payment_method_ids
msgid ""
"Manual:Pay bill by cash or any other method outside of Odoo.\n"
"Check:Pay bill by check and print it from Odoo.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. Enable this option from the settings."
msgstr ""
"수동: Odoo 외에 현금이나 다른 방법으로 청구 금액을 지불합니다.\n"
"전표: 전표로 청구 금액을 지불하고 Odoo에서 인쇄합니다.\n"
"SEPA 신용 이전: 은행에 제출한 SEPA 신용 이전 파일에서 청구 금액을 지불합니다. 이 옵션은 설정에서 활성화합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Manually enter your transactions using our"
msgstr "다음을 사용하여 거래를 직접 입력"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "March"
msgstr "3월"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Margin Analysis"
msgstr "마진 분석"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model:ir.ui.view,arch_db:account.setup_view_company_form
msgid "Mark as done"
msgstr "완료로 표시"

#. module: account
#: selection:account.payment,payment_difference_handling:0
msgid "Mark invoice as fully paid"
msgstr "청구서를 전액 지불로 표시"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_payables_master_data
#: model:ir.ui.menu,name:account.menu_finance_receivables_master_data
msgid "Master Data"
msgstr "마스터 데이터"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_matched_credit_ids
msgid "Matched Credit"
msgstr "일치된 대변"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_matched_debit_ids
msgid "Matched Debit"
msgstr "일치된 차변"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_reconciled_line_ids
#: model:ir.ui.view,arch_db:account.view_full_reconcile_form
msgid "Matched Journal Items"
msgstr "일치된 분개 항목"

#. module: account
#: model:ir.ui.view,arch_db:account.view_full_reconcile_form
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Matching"
msgstr "매칭"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_full_reconcile_id
msgid "Matching Number"
msgstr "일치하는 번호"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_max_date
msgid "Max Date of Matched Lines"
msgstr "일치된 라인의 최대 날짜"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "May"
msgstr "5월"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_communication
#: model:ir.model.fields,field_description:account.field_account_payment_communication
#: model:ir.model.fields,field_description:account.field_account_register_payments_communication
msgid "Memo"
msgstr "메모"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:64
#, python-format
msgid "Memo:"
msgstr "메모:"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_invoice_warn_msg
#: model:ir.model.fields,field_description:account.field_res_users_invoice_warn_msg
msgid "Message for Invoice"
msgstr "청구서에 대한 메시지"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Cost of Revenue"
msgstr "수익 원가 제외"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Credit Card Accounts"
msgstr "신용카드 계정 제외"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Current Liabilities"
msgstr "유동 부채 제외"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Expenses"
msgstr "경비 제외"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Non-Current Liabilities"
msgstr "비유동 부채 제외"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Payable Accounts"
msgstr "지급 계정 제외"

#. module: account
#: selection:account.journal,type:0
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Miscellaneous"
msgstr "기타"

#. module: account
#: code:addons/account/models/chart_template.py:195
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#, python-format
msgid "Miscellaneous Operations"
msgstr "기타 작업"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:164
#, python-format
msgid "Modify models"
msgstr "모델 수정"

#. module: account
#: selection:account.cash.rounding,strategy:0
msgid "Modify tax amount"
msgstr "세액 수정"

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Modify: create credit note, reconcile and create a new draft invoice"
msgstr "수정: 신용 전표 생성, 새로운 초안 청구서를 조정하고 생성"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Monitor your product margins from invoices"
msgstr "청구서에서 제품 마진 모니터링"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_invoice_partner_relation
msgid "Monthly Turnover"
msgstr "월간 매출"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Most currencies are already created by default. If you plan\n"
"                        to use some of them, you should check their <strong>Active</strong>\n"
"                        field."
msgstr ""
"대부분 통화는 이미 기본적으로 생성되어 있습니다.\n"
"                        그중 일부를 사용할 계획이라면 <strong>활성</strong>\n"
"                        필드를 확인해야 합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Move"
msgstr "이동"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_move_id
#: model:ir.model.fields,field_description:account.field_account_payment_move_line_ids
msgid "Move Line"
msgstr "이동 라인"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_move_line_count
msgid "Move Line Count"
msgstr "이동 라인 계산"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_move_reconciled
msgid "Move Reconciled"
msgstr "조정된 이동"

#. module: account
#: code:addons/account/models/account_move.py:1362
#, python-format
msgid "Move name (id): %s (%s)"
msgstr "이동 이름(id): %s (%s)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_register_payments_multi
msgid "Multi"
msgstr "다중"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Multi Currency"
msgstr "다중 통화"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Multi-Currencies"
msgstr "다중 통화"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "My Activities"
msgstr "내 활동"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "My Invoices"
msgstr "내 청구서"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "NET ASSETS"
msgstr "순자산"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "NET PROFIT"
msgstr "순이익"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_name
#: model:ir.model.fields,field_description:account.field_account_account_tag_name
#: model:ir.model.fields,field_description:account.field_account_account_template_name
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_name
#: model:ir.model.fields,field_description:account.field_account_chart_template_name
#: model:ir.model.fields,field_description:account.field_account_group_name
#: model:ir.model.fields,field_description:account.field_account_payment_method_name
#: model:ir.model.fields,field_description:account.field_account_payment_name
#: model:ir.model.fields,field_description:account.field_account_tax_group_name
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Name"
msgstr "이름"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:52
#, python-format
msgid "Name:"
msgstr "이름:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_narration
msgid "Narration"
msgstr "내레이션"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Navigate easily through reports and see what is behind the numbers"
msgstr "보고서를 쉽게 탐색하고 숫자에 숨은 의미를 확인하세요."

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "Net"
msgstr "순"

#. module: account
#: selection:account.bank.statement,state:0
msgid "New"
msgstr "신규"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "New Statement"
msgstr "새 명세서"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "New Transactions"
msgstr "새 거래"

#. module: account
#: code:addons/account/models/account_move.py:1339
#, python-format
msgid "New expected payment date: "
msgstr "새 예상 지급일:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_sequence_number_next
#: model:ir.model.fields,field_description:account.field_account_invoice_sequence_number_next_prefix
#: model:ir.model.fields,field_description:account.field_account_journal_sequence_number_next
msgid "Next Number"
msgstr "다음 번호"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Next, register any unmatched payments:<br/>"
msgstr "다음에는 일치하지 않는 지불금을 등록합니다.<br/>"

#. module: account
#: selection:accounting.report,filter_cmp:0
msgid "No Filters"
msgstr "필터 없음"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_blocked
msgid "No Follow-up"
msgstr "후속 관리 없음"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "No Message"
msgstr "메시지 없음"

#. module: account
#: selection:account.financial.report,display_detail:0
msgid "No detail"
msgstr "세부 정보 없음"

#. module: account
#: code:addons/account/models/account.py:116
#, python-format
msgid "No opening move defined !"
msgstr "개시된 이동이 정의되지 않았습니다!"

#. module: account
#: model:ir.model.fields,help:account.field_res_company_fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr "자문을 포함하여 모든 사용자는 이 날짜 이전까지 편집할 수 없습니다. 예를 들어, 회계연도 마감에 사용합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_code_digits
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_code_digits
msgid "No. of Digits to use for account code"
msgstr "계정 코드에 사용할 자릿수"

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_code_digits
msgid "No. of digits to use for account code"
msgstr "계정 코드에 사용할 자릿수"

#. module: account
#: model:account.account.type,name:account.data_account_type_non_current_assets
msgid "Non-current Assets"
msgstr "비유동 자산"

#. module: account
#: model:account.account.type,name:account.data_account_type_non_current_liabilities
msgid "Non-current Liabilities"
msgstr "비유동 부채"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
msgid "None"
msgstr "없음"

#. module: account
#: selection:res.partner,trust:0
msgid "Normal Debtor"
msgstr "일반 차변"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Normal Text"
msgstr "일반 텍스트"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:254
#: model:ir.model.fields,field_description:account.field_account_account_template_note
#, python-format
msgid "Note"
msgstr "메모"

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_out_refund
msgid ""
"Note that the easiest way to create a credit note is to do it directly form\n"
"                the customer invoice, to refund it totally or partially."
msgstr ""
"신용 전표를 생성하는 가장 간편한 방법은\n"
"                고객 청구서에서 직접 생성한 다음, 전액 또는 부분 환불하는 것입니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_note
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_note
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_note
#: model:ir.ui.view,arch_db:account.view_account_template_form
msgid "Notes"
msgstr "메모"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Nothing to Reconcile"
msgstr "조정할 건 없음"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:34
#, python-format
msgid "Nothing to do!"
msgstr "수행할 작업이 없습니다!"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "November"
msgstr "11월"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_name
#: model:ir.model.fields,field_description:account.field_account_invoice_number
#: model:ir.model.fields,field_description:account.field_account_move_name
msgid "Number"
msgstr "번호"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Number (Move)"
msgstr "번호(이동)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_number
msgid "Number of Coins/Bills"
msgstr "주화/지폐 수"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_days
msgid "Number of Days"
msgstr "일수"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_accounts_code_digits
msgid "Number of digits in an account code"
msgstr "계정 코드 자릿수"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "OFX Import"
msgstr "OFX 가져오기"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:17
#, python-format
msgid "OK"
msgstr "확인"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "October"
msgstr "10월"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Odoo Accounting has many extra-features:"
msgstr ""

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_line
msgid ""
"Odoo allows you to reconcile a statement line directly with\n"
"                the related sale or puchase invoice(s)."
msgstr ""
"Odoo를 사용하면 관련 매출/매입 청구서로\n"
"                명세서 라인을 직접 조정할 수 있습니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_tree
msgid ""
"Odoo allows you to reconcile a statement line directly with\n"
"                the related sale or puchase invoices."
msgstr ""
"Odoo를 사용하면 관련 매출/매입 청구서로\n"
"                명세서 라인을 직접 조정할 수 있습니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_move_journal_line
msgid ""
"Odoo automatically creates one journal entry per accounting\n"
"                document: invoice, refund, vendor payment, bank statements,\n"
"                etc. So, you should record journal entries manually only/mainly\n"
"                for miscellaneous operations."
msgstr ""
"Odoo는 회계 문서 1건(청구서, 환불, 공급업체 지불,\n"
"                 예금거래 명세서)당 분개 1개를 자동 생성합니다.\n"
"                그러므로 기타 작업에만 분개를\n"
"                직접 기록하면 됩니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Odoo can manage multiple companies, but we suggest to setup everything for "
"your first company before configuring the other ones."
msgstr "Odoo는 여러 기업을 관리할 수 있으나 다른 기업을 구성하기 전에 첫 번째 기업을 완전히 구성하는 것이 좋습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Odoo has already preconfigured a few taxes according to your "
"country.<br/>Simply review them and check if you need more."
msgstr ""
"Odoo는 귀하의 국가에 맞게 일부 세금이 사전 구성되어 있습니다.<br/>세금을 검토하고 다른 세금이 필요한지 확인하기만 하면 됩니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Odoo should do most of the reconciliation work automatically, so you'll only"
" need to review a few of them when a <strong>'Reconcile Items'</strong> "
"button appears on your Vendor Bills dash."
msgstr ""
"Odoo는 대부분 조정 작업을 자동으로 수행하므로 공급업체 청구 대시에 <strong>'항목 조정'</strong> 버튼이 나타나면 "
"일부만 검토하면 됩니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree1
msgid ""
"Odoo's electronic invoicing allows to ease and fasten the\n"
"                collection of customer payments. Your customer receives the\n"
"                invoice by email and he can pay online and/or import it\n"
"                in his own system."
msgstr ""
"Odoo의 전자 청구서 발행을 사용하면 고객 지불금을\n"
"                간편하고 신속하게 수금할 수 있습니다. 고객은 이메일로\n"
"                청구서를 받고 온라인으로 지불하거나\n"
"                자체 시스템으로 가져올 수 있습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid ""
"Once draft invoices are confirmed, you will not be able\n"
"                        to modify them. The invoices will receive a unique\n"
"                        number and journal items will be created in your chart\n"
"                        of accounts."
msgstr ""
"초안 청구서가 확정되면 수정할 수 없습니다.\n"
"                        청구서에 고유 번호가 부여되고\n"
"                        분개 항목이 계정 차트에\n"
"                        생성됩니다."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Once installed, set 'Bank Feeds' to 'File Import' in bank account "
"settings.This adds a button to import from the Accounting dashboard."
msgstr ""
"Odoo를 설치하면 은행 계좌 설정에서 ‘은행 피드’를 ‘파일 가져오기’로 설정합니다. 회계 대시보드에서 가져오는 버튼이 추가됩니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Once you have created your chart of accounts, you will need to update your "
"account balances."
msgstr "계정 차트가 생성되면 계정 잔고를 업데이트해야 합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Once your bank accounts are registered, you will be able \n"
"                        to access your statements from the Accounting Dashboard. \n"
"                        The available methods for synchronization are as follows."
msgstr ""
"계좌 잔고가 등록되면 회계 \n"
"                        대시보드에서 명세서에 액세스할 수 있습니다.\n"
"                        이용가능한 동기화 방법은 다음과 같습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Once your company information is correct, you should"
msgstr "회사 정보가 정확하면 다음과 같이 해야 합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "One bank statement for each bank account you hold."
msgstr "보유한 은행 계좌별로 예금거래 명세서 1개."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_payment
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Online Payment"
msgstr "온라인 지불"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_only_one_chart_template
msgid "Only One Chart Template Available"
msgstr "차트 템플릿 1개만 이용 가능"

#. module: account
#: code:addons/account/models/account_payment.py:484
#, python-format
msgid "Only a draft payment can be posted."
msgstr "초안 지불금 1건만 전기할 수 있습니다."

#. module: account
#: code:addons/account/models/chart_template.py:866
#, python-format
msgid "Only administrators can change the settings"
msgstr "관리자만 설정을 변경할 수 있습니다."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr "'자문' 역할의 사용자만 이 날짜까지 계정을 편집할 수 있습니다. 예를 들어, 미결산 회계연도 내 마감 기간에 사용합니다."

#. module: account
#. openerp-web
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: code:addons/account/static/src/xml/account_payment.xml:82
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Open"
msgstr "개설"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:664
#: code:addons/account/static/src/xml/account_reconciliation.xml:131
#, python-format
msgid "Open balance"
msgstr "개설 잔액"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_opening_date
#: model:ir.model.fields,field_description:account.field_account_opening_date
#: model:ir.model.fields,field_description:account.field_res_company_account_opening_date
msgid "Opening Date"
msgstr "개시일"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_opening_journal_id
msgid "Opening Journal"
msgstr "개시된 분개장"

#. module: account
#: code:addons/account/models/company.py:339
#: model:ir.model.fields,field_description:account.field_account_opening_opening_move_id
#: model:ir.model.fields,field_description:account.field_res_company_account_opening_move_id
#, python-format
msgid "Opening Journal Entry"
msgstr "개시된 분개"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_opening_opening_move_line_ids
msgid "Opening Journal Items"
msgstr "개시된 분개장 항목"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_opening_move_posted
msgid "Opening Move Posted"
msgstr "전기된 개시 이동"

#. module: account
#: model:ir.model.fields,help:account.field_account_cashbox_line_number
msgid "Opening Unit Numbers"
msgstr "개시 단위 숫자"

#. module: account
#: code:addons/account/models/account.py:138
#, python-format
msgid "Opening balance"
msgstr "개시 잔액"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_opening_credit
msgid "Opening credit"
msgstr "개시 대변"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_opening_credit
msgid "Opening credit value for this account."
msgstr "이 계정의 개시 대변 값입니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_opening_debit
msgid "Opening debit"
msgstr "개시 차변"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_opening_debit
msgid "Opening debit value for this account."
msgstr "이 계정의 개시 차변 값입니다."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Operation Templates"
msgstr "작업 템플릿"

#. module: account
#: code:addons/account/models/account_bank_statement.py:1014
#, python-format
msgid ""
"Operation not allowed. Since your statement line already received a number, "
"you cannot reconcile it entirely with existing journal entries otherwise it "
"would make a gap in the numbering. You should book an entry and make a "
"regular revert of it in case you want to cancel it."
msgstr ""
"작업이 허용되지 않습니다. 명세서 라인에 이미 숫자가 입력되었기 때문에 기존 분개 항목으로는 완전히 조정할 수 없습니다. 그렇지 않으면 "
"번호 순서에 공백이 생깁니다. 항목을 기장하고, 취소를 원할 경우 이를 정기 환원해야 합니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_nocreate
msgid "Optional Create"
msgstr "선택 생성"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag_ids
#: model:ir.model.fields,help:account.field_account_account_template_tag_ids
#: model:ir.model.fields,help:account.field_account_tax_tag_ids
#: model:ir.model.fields,help:account.field_account_tax_template_tag_ids
msgid "Optional tags you may want to assign for custom reporting"
msgstr "맞춤 보고에 할당할 수 있는 선택 태그"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_option
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Options"
msgstr "옵션"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Original Amount"
msgstr "원금"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_payment_id
msgid "Originator Payment"
msgstr "자산 보유자 지불"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_line_id
msgid "Originator tax"
msgstr "자산 보유자 세금"

#. module: account
#: model:account.account.type,name:account.data_account_type_other_income
msgid "Other Income"
msgstr "기타 수입"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Other Info"
msgstr "기타 정보"

#. module: account
#: selection:account.payment.method,payment_type:0
msgid "Outbound"
msgstr "아웃바운드"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_stock_account_output_categ_id
#: model:ir.model.fields,field_description:account.field_res_company_property_stock_account_output_categ_id
msgid "Output Account for Stock Valuation"
msgstr "주식 가치 평가를 위한 인출 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_outstanding_credits_debits_widget
msgid "Outstanding Credits Debits Widget"
msgstr "미불 차변/대변 위젯"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Outstanding Transactions"
msgstr "미불 거래"

#. module: account
#: code:addons/account/models/account_invoice.py:131
#, python-format
msgid "Outstanding credits"
msgstr "미불 대변"

#. module: account
#: code:addons/account/models/account_invoice.py:134
#, python-format
msgid "Outstanding debits"
msgstr "미불 차변"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Overdue"
msgstr "연체"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_overdue_msg
msgid "Overdue Payments Message"
msgstr "연체 지불금 메시지"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Overdue invoices, maturity date passed"
msgstr "연체 청구서, 만기일 지남"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_legal_statement
msgid "PDF Reports"
msgstr "PDF 보고서"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Package"
msgstr "패키지"

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: model:ir.ui.view,arch_db:account.report_overdue_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:mail.message.subtype,name:account.mt_invoice_paid
msgid "Paid"
msgstr "지불됨"

#. module: account
#: code:addons/account/models/account_payment.py:430
#, python-format
msgid "Paid Invoices"
msgstr "지불된 청구서"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:28
#: model:ir.ui.view,arch_db:account.report_invoice_document_with_payments
#, python-format
msgid "Paid on"
msgstr "지불일"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_reconciled
msgid "Paid/Reconciled"
msgstr "지불/조정됨"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_parent_id
#: model:ir.model.fields,field_description:account.field_account_group_parent_id
msgid "Parent"
msgstr "상위"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_parent_id
msgid "Parent Chart Template"
msgstr "상위 차트 템플릿"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
msgid "Parent Report"
msgstr "상위 보고서"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_parent_state
msgid "Parent State"
msgstr "상위 상태"

#. module: account
#: model:ir.model,name:account.model_account_partial_reconcile
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Partial Reconcile"
msgstr "부분 조정"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:232
#: code:addons/account/static/src/xml/account_reconciliation.xml:249
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_partner_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_partner_id
#: model:ir.model.fields,field_description:account.field_account_move_line_partner_id
#: model:ir.model.fields,field_description:account.field_account_move_partner_id
#: model:ir.model.fields,field_description:account.field_account_payment_partner_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_partner_id
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#, python-format
msgid "Partner"
msgstr "협력사"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_commercial_partner_id
msgid "Partner Company"
msgstr "협력사"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_partner_ledger_menu
#: model:ir.actions.report,name:account.action_report_partnerledger
#: model:ir.ui.menu,name:account.menu_partner_ledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
msgid "Partner Ledger"
msgstr "협력사 원장"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_partner_name
msgid "Partner Name"
msgstr "협력사 이름"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_partner_type
#: model:ir.model.fields,field_description:account.field_account_payment_partner_type
#: model:ir.model.fields,field_description:account.field_account_register_payments_partner_type
msgid "Partner Type"
msgstr "협력사 유형"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_result_selection
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_result_selection
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_result_selection
msgid "Partner's"
msgstr "협력사"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Partners"
msgstr "협력사"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:106
#, python-format
msgid "Past"
msgstr "과거"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Pay your bills in one-click using Euro SEPA service"
msgstr "Euro SEPA 서비스를 사용하여 클릭 한 번으로 청구된 금액을 지불합니다."

#. module: account
#: model:account.account.type,name:account.data_account_type_payable
#: selection:account.account.type,type:0
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Payable"
msgstr "지급금"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_payable_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Payable Account"
msgstr "지불 계정"

#. module: account
#: selection:account.aged.trial.balance,result_selection:0
#: selection:account.common.partner.report,result_selection:0
#: selection:account.report.partner.ledger,result_selection:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.view_account_search
msgid "Payable Accounts"
msgstr "지불 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_debit_limit
#: model:ir.model.fields,field_description:account.field_res_users_debit_limit
msgid "Payable Limit"
msgstr "지불 한도"

#. module: account
#: model:ir.ui.view,arch_db:account.product_template_form_view
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Payables"
msgstr "지급금"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_amount
#: model:ir.model.fields,field_description:account.field_account_payment_amount
#: model:ir.model.fields,field_description:account.field_account_register_payments_amount
msgid "Payment Amount"
msgstr "지불 금액"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_date
#: model:ir.model.fields,field_description:account.field_account_payment_payment_date
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_date
msgid "Payment Date"
msgstr "지불일"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_payment_difference
#: model:ir.model.fields,field_description:account.field_account_payment_payment_difference_handling
msgid "Payment Difference"
msgstr "지불 차액"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_journal_id
#: model:ir.model.fields,field_description:account.field_account_payment_journal_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_journal_id
msgid "Payment Journal"
msgstr "지불 분개"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Payment Matching"
msgstr "지불 매칭"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Payment Method"
msgstr "지급 방법"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_method_id
#: model:ir.model.fields,field_description:account.field_account_payment_payment_method_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_method_id
msgid "Payment Method Type"
msgstr "지급 방법 유형"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:60
#, python-format
msgid "Payment Method:"
msgstr "지급 방법:"

#. module: account
#: model:ir.model,name:account.model_account_payment_method
#: model:ir.model.fields,field_description:account.field_account_journal_outbound_payment_method_ids
msgid "Payment Methods"
msgstr "지급 방법"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_payment_move_line_ids
msgid "Payment Move Lines"
msgstr "지불 이동 라인"

#. module: account
#: model:ir.actions.report,name:account.action_report_payment_receipt
msgid "Payment Receipt"
msgstr "지불 영수증"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Payment Receipt:"
msgstr "지불 영수증:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_reference_type
#: model:ir.model.fields,field_description:account.field_account_payment_payment_reference
msgid "Payment Reference"
msgstr "지급 조서"

#. module: account
#: model:ir.actions.act_window,name:account.action_payment_term_form
#: model:ir.model,name:account.model_account_payment_term
#: model:ir.model.fields,field_description:account.field_account_invoice_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_payment_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_name
#: model:ir.ui.menu,name:account.menu_action_payment_term_form
#: model:ir.ui.view,arch_db:account.account_planner
#: model:ir.ui.view,arch_db:account.view_payment_term_form
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
#: model:ir.ui.view,arch_db:account.view_payment_term_line_tree
#: model:ir.ui.view,arch_db:account.view_payment_term_search
#: model:ir.ui.view,arch_db:account.view_payment_term_tree
msgid "Payment Terms"
msgstr "지불 조건"

#. module: account
#: model:ir.model,name:account.model_account_payment_term_line
msgid "Payment Terms Line"
msgstr "지불 조건 라인"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_type
#: model:ir.model.fields,field_description:account.field_account_payment_method_payment_type
#: model:ir.model.fields,field_description:account.field_account_payment_payment_type
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_type
msgid "Payment Type"
msgstr "지불 유형"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_form
msgid "Payment terms explanation for the customer..."
msgstr "고객을 위한 지불 조건 설명..."

#. module: account
#: model:account.payment.term,note:account.account_payment_term_15days
msgid "Payment terms: 15 Days"
msgstr "지불 조건: 15일"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_net
msgid "Payment terms: 30 Net Days"
msgstr "지불 조건: 순기간 30일"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_advance
msgid "Payment terms: 30% Advance End of Following Month"
msgstr "지불 조건: 익월 말에 30% 선불"

#. module: account
#: model:account.payment.term,note:account.account_payment_term
msgid "Payment terms: End of Following Month"
msgstr "지불 조건: 익월 말"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_immediate
msgid "Payment terms: Immediate Payment"
msgstr "지불 조건: 즉시 지불"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_payment_id
msgid "Payment that created this entry"
msgstr "이 항목을 생성한 지불금"

#. module: account
#: code:addons/account/models/account_payment.py:229
#: model:ir.actions.act_window,name:account.action_account_payments
#: model:ir.actions.act_window,name:account.action_account_payments_payable
#: model:ir.model,name:account.model_account_payment
#: model:ir.model.fields,field_description:account.field_account_invoice_payment_ids
#: model:ir.ui.menu,name:account.menu_action_account_payments_payable
#: model:ir.ui.menu,name:account.menu_action_account_payments_receivable
#: model:ir.ui.view,arch_db:account.partner_view_buttons
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#, python-format
msgid "Payments"
msgstr "지불금"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "지불 매칭"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_payments_widget
msgid "Payments Widget"
msgstr "지불 위젯"

#. module: account
#: model:ir.actions.act_window,help:account.action_account_payments
#: model:ir.actions.act_window,help:account.action_account_payments_payable
msgid ""
"Payments are used to register liquidity movements (send, collect or transfer money).\n"
"                  You can then process those payments by your own means or by using installed facilities."
msgstr ""
"지불금은 유동성 이동을 등록하는 데 사용합니다(자금 송금, 수금 또는 이전).\n"
"                  그 후 자체적인 수단이나 설치된 시설을 사용하여 해당 지불금을 처리할 수 있습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Payments to do"
msgstr "실행할 결제"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_tree_pending_invoice
msgid "Pending Invoice"
msgstr "대기 중인 청구서"

#. module: account
#: selection:account.payment.term.line,value:0
msgid "Percent"
msgstr "비율"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_matched_percentage
msgid "Percentage Matched"
msgstr "일치 비율"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Percentage of Price"
msgstr "가격 비율"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Percentage of Price Tax Included"
msgstr "세금 포함 가격 비율"

#. module: account
#: selection:account.reconcile.model,second_amount_type:0
#: selection:account.reconcile.model.template,second_amount_type:0
msgid "Percentage of amount"
msgstr "금액 비율"

#. module: account
#: selection:account.reconcile.model,amount_type:0
#: selection:account.reconcile.model.template,amount_type:0
msgid "Percentage of balance"
msgstr "잔고 비율"

#. module: account
#: code:addons/account/models/account_invoice.py:1776
#, python-format
msgid "Percentages for Payment Terms Line must be between 0 and 100."
msgstr "지불 조건 라인 비율은 0~100이어야 합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Period"
msgstr "기간"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_period_length
msgid "Period Length (days)"
msgstr "기간(일)"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_plaid
msgid "Plaid Connector"
msgstr "플레이드 커넥터"

#. module: account
#: model:ir.model,name:account.model_web_planner
msgid "Planner"
msgstr "플래너"

#. module: account
#: code:addons/account/wizard/pos_box.py:27
#, python-format
msgid "Please check that the field 'Journal' is set on the Bank Statement"
msgstr "‘분개’ 필드가 예금거래 명세서에 설정되어 있는지 확인해 주십시오."

#. module: account
#: code:addons/account/wizard/pos_box.py:29
#, python-format
msgid "Please check that the field 'Transfer Account' is set on the company."
msgstr "회사에 ‘이전 계정’ 필드가 설정되어 있는지 확인해 주십시오."

#. module: account
#: code:addons/account/models/account_invoice.py:1070
#, python-format
msgid "Please create some invoice lines."
msgstr "일부 청구서 라인을 생성해 주십시오."

#. module: account
#: code:addons/account/models/account_move.py:157
#, python-format
msgid "Please define a sequence for the credit notes"
msgstr "신용 전표에서 순서를 정의해 주십시오."

#. module: account
#: code:addons/account/models/account_move.py:162
#, python-format
msgid "Please define a sequence on the journal."
msgstr "분개에서 순서를 정의해 주십시오."

#. module: account
#: code:addons/account/models/account_invoice.py:1068
#, python-format
msgid "Please define sequence on the journal related to this invoice."
msgstr "이 청구서와 관련된 분개에 순서를 정의해 주십시오."

#. module: account
#: code:addons/account/models/company.py:336
#, python-format
msgid ""
"Please install a chart of accounts or create a miscellaneous journal before "
"proceeding."
msgstr "계정 차트를 설치하거나 처리 전에 기타 분개를 생성해 주십시오."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Bank"
msgstr "은행 추가"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Fixed Assets"
msgstr "고정 자산 추가"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Net Profit"
msgstr "순이익 추가"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Non-Current Assets"
msgstr "비유동 자산 추가"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Other Income"
msgstr "기타 수입 추가"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_wizard_form
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Post"
msgstr "전기"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Post All Entries"
msgstr "모든 항목 전기"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Post Difference In"
msgstr "다음의 차액 전기"

#. module: account
#: model:ir.actions.act_window,name:account.action_validate_account_move
#: model:ir.ui.view,arch_db:account.validate_account_move_view
msgid "Post Journal Entries"
msgstr "분개 전기"

#. module: account
#: selection:account.move,state:0 selection:account.payment,state:0
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Posted"
msgstr "전기됨"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Posted Journal Entries"
msgstr "전기된 분개"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Posted Journal Items"
msgstr "전기된 분개 항목"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_bank_account_code_prefix
#: model:ir.model.fields,field_description:account.field_res_company_bank_account_code_prefix
msgid "Prefix of the bank accounts"
msgstr "은행 계좌 프리픽스"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_cash_account_code_prefix
msgid "Prefix of the cash accounts"
msgstr "현금 계정 프리픽스"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_cash_account_code_prefix
msgid "Prefix of the main cash accounts"
msgstr "주요 현금 계정 프리픽스"

#. module: account
#: model:account.account.type,name:account.data_account_type_prepayments
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Prepayments"
msgstr "선급금"

#. module: account
#: selection:account.financial.report,sign:0
msgid "Preserve balance sign"
msgstr "잔액 기호 유지"

#. module: account
#: model:ir.model,name:account.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr "청구서 및 지불금 매칭 시 분개를 생성하기 위한 사전 설정"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.account_common_report_view
msgid "Print"
msgstr "인쇄"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Print Invoice"
msgstr "청구서 인쇄"

#. module: account
#: model:ir.model.fields,help:account.field_account_common_journal_report_amount_currency
#: model:ir.model.fields,help:account.field_account_print_journal_amount_currency
msgid ""
"Print Report with the currency column if the currency differs from the "
"company currency."
msgstr "통화가 회사 통화와 다를 경우, 통화 열을 포함하여 보고서를 인쇄합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Print checks to pay your vendors"
msgstr "공급업체에 지불할 전표 인쇄"

#. module: account
#: model:ir.model,name:account.model_product_product
#: model:ir.model.fields,field_description:account.field_account_analytic_line_product_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_product_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_product_id
#: model:ir.model.fields,field_description:account.field_account_move_line_product_id
#: model:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
msgid "Product"
msgstr "제품"

#. module: account
#: model:ir.model,name:account.model_product_category
#: model:ir.model.fields,field_description:account.field_account_invoice_report_categ_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Product Category"
msgstr "제품 범주"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_product_image
msgid "Product Image"
msgstr "제품 이미지"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_product_qty
msgid "Product Quantity"
msgstr "상품 수량"

#. module: account
#: model:ir.model,name:account.model_product_template
msgid "Product Template"
msgstr "제품 템플릿"

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action
#: model:ir.ui.view,arch_db:account.product_product_view_tree
msgid "Products"
msgstr "제품"

#. module: account
#: code:addons/account/models/account_bank_statement.py:177
#, python-format
msgid "Profit"
msgstr "이익"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Profit &amp; Loss"
msgstr "손익"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_profitloss_toreport0
msgid "Profit (Loss) to report"
msgstr "보고할 이익(손실)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_profit_account_id
msgid "Profit Account"
msgstr "이익 계정"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_profitandloss0
#: model:ir.actions.act_window,name:account.action_account_report_pl
#: model:ir.ui.menu,name:account.menu_account_report_pl
msgid "Profit and Loss"
msgstr "손익"

#. module: account
#: code:addons/account/models/account_payment.py:135
#, python-format
msgid ""
"Programming error: wizard action executed without active_ids in context."
msgstr "프로그래밍 오류: 컨텍스트에서 active_ids 없이 마법사 작업이 실행되었습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_form
msgid "Properties"
msgstr "부동산"

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action_purchasable
#: model:ir.ui.menu,name:account.product_product_menu_purchasable
msgid "Purchasable Products"
msgstr "매입 가능한 제품"

#. module: account
#: selection:account.journal,type:0
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Purchase"
msgstr "매입"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_wizard_multi_chart
msgid "Purchase Tax"
msgstr "구매세"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_purchase_tax_rate
msgid "Purchase Tax(%)"
msgstr "구매세(%)"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:35
#, python-format
msgid "Purchase: Untaxed Total"
msgstr "매입: 제세 합계"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
#: model:ir.ui.menu,name:account.menu_finance_payables
#: model:ir.ui.view,arch_db:account.view_account_journal_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Purchases"
msgstr "매입"

#. module: account
#: model:ir.actions.act_window,name:account.action_cash_box_in
#: model:ir.ui.view,arch_db:account.cash_box_in_form
msgid "Put Money In"
msgstr "다음에 자금 투입"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Python Code"
msgstr "파이썬 코드"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "QIF Import"
msgstr "QIF 가져오기"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_quantity
#: model:ir.model.fields,field_description:account.field_account_move_line_quantity
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Quantity"
msgstr "수량"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_description
#: model:ir.model.fields,field_description:account.field_cash_box_in_name
#: model:ir.model.fields,field_description:account.field_cash_box_out_name
msgid "Reason"
msgstr "사유"

#. module: account
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Reason..."
msgstr "사유..."

#. module: account
#: model:account.account.type,name:account.data_account_type_receivable
#: selection:account.account.type,type:0
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Receivable"
msgstr "미수금"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_receivable_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Receivable Account"
msgstr "수취 계정"

#. module: account
#: selection:account.aged.trial.balance,result_selection:0
#: selection:account.common.partner.report,result_selection:0
#: selection:account.report.partner.ledger,result_selection:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.view_account_form
#: model:ir.ui.view,arch_db:account.view_account_search
msgid "Receivable Accounts"
msgstr "수취 계정"

#. module: account
#: selection:account.aged.trial.balance,result_selection:0
#: selection:account.common.partner.report,result_selection:0
#: selection:account.report.partner.ledger,result_selection:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Receivable and Payable Accounts"
msgstr "수취 및 지불 계정"

#. module: account
#: model:ir.ui.view,arch_db:account.product_template_form_view
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Receivables"
msgstr "미수금"

#. module: account
#: selection:account.abstract.payment,payment_type:0
#: selection:account.payment,payment_type:0
#: selection:account.register.payments,payment_type:0
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Receive Money"
msgstr "자금 수령"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:73
#: code:addons/account/static/src/xml/account_reconciliation.xml:105
#: code:addons/account/static/src/xml/account_reconciliation.xml:106
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#, python-format
msgid "Reconcile"
msgstr "조정"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_account_move_line_reconcile
msgid "Reconcile Entries"
msgstr "항목 조정"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Reconcile With Write-Off"
msgstr "상각으로 조정"

#. module: account
#: code:addons/account/wizard/account_reconcile.py:86
#, python-format
msgid "Reconcile Writeoff"
msgstr "상각 조정"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/tour_bank_statement_reconciliation.js:11
#, python-format
msgid "Reconcile the demo bank statement"
msgstr "데모 예금거래 명세서 조정"

#. module: account
#: selection:account.payment,state:0
#: model:ir.model.fields,field_description:account.field_account_move_line_reconciled
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Reconciled"
msgstr "조정됨"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_reconciled
msgid "Reconciled Entries"
msgstr "조정된 항목"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Reconciled entries"
msgstr "조정된 항목"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Reconciliation"
msgstr "조정"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_reconcile_model
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Reconciliation Models"
msgstr "조정 모델"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_partial_reconcile_ids
msgid "Reconciliation Parts"
msgstr "조정 부분"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Reconciliation Transactions"
msgstr "조정 거래"

#. module: account
#: model:ir.actions.client,name:account.action_bank_reconcile
#: model:ir.actions.client,name:account.action_bank_reconcile_bank_statements
msgid "Reconciliation on Bank Statements"
msgstr "예금거래 명세서에 대한 조정"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Reconciling journal entries"
msgstr "분개 조정"

#. module: account
#: selection:account.journal,bank_statements_source:0
msgid "Record Manually"
msgstr "직접 기록"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Record transactions in foreign currencies"
msgstr "외국 통화로 거래 기록"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Recording invoices"
msgstr "청구서 기록"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:231
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#, python-format
msgid "Ref"
msgstr "참조"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_ref
msgid "Ref."
msgstr "참조"

#. module: account
#: code:addons/account/controllers/portal.py:72
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_ref
#: model:ir.model.fields,field_description:account.field_account_bank_statement_name
#: model:ir.model.fields,field_description:account.field_account_move_line_ref
#: model:ir.model.fields,field_description:account.field_account_move_ref
#: model:ir.model.fields,field_description:account.field_cash_box_in_ref
#, python-format
msgid "Reference"
msgstr "참조"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_uom_name
msgid "Reference Unit of Measure"
msgstr "기준 측정 단위"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Reference number"
msgstr "기준 번호"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_origin
#: model:ir.model.fields,help:account.field_account_invoice_origin
msgid "Reference of the document that produced this invoice."
msgstr "해당 청구서 생성 문서의 참조"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_payment_reference
msgid ""
"Reference of the document used to issue this payment. Eg. check number, file"
" name, etc."
msgstr "해당 지불금 발행 시 사용한 문서의 참조 (예: 전표 번호, 파일명 등)."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_name
msgid "Reference/Description"
msgstr "참조/설명"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_invoice_ids
msgid "Refund Invoices"
msgstr "청구서 환불"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_filter_refund
msgid "Refund Method"
msgstr "환불 방법"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_refund_filter_refund
msgid ""
"Refund base on this type. You can not Modify and Cancel if the invoice is "
"already reconciled"
msgstr "이 유형에 대한 환불 기준입니다. 청구서가 이미 조정되었다면 수정, 취소할 수 없습니다."

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_payment
#: model:ir.actions.act_window,name:account.action_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.view_account_payment_form
#: model:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Register Payment"
msgstr "결제 등록"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Register Payments"
msgstr "결제 등록"

#. module: account
#: model:ir.model,name:account.model_account_register_payments
msgid "Register payments on multiple invoices"
msgstr "여러 청구서에서 결제 등록"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Registering outstanding invoices and payments can be a huge undertaking, \n"
"                        but you can start using Odoo without it by:"
msgstr ""
"미지급 청구서와 지급을 등록하는 것은 굉장히 어려운 작업입니다.\n"
"                        그러나 Odoo로  간편하게 시작하실 수 있습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Registering payments related to outstanding invoices separately in a "
"different account (e.g. Account Receivables 2014)"
msgstr "각 계정에 미불 청구서와 관련된 지불금을 별도로 등록합니다(예: 2014년 수취 계정)."

#. module: account
#: selection:account.account.type,type:0
msgid "Regular"
msgstr "정기"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_residual_company_signed
msgid "Remaining amount due in the currency of the company."
msgstr "회사 통화 기준으로 지불해야 할 잔액."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_residual_signed
msgid "Remaining amount due in the currency of the invoice."
msgstr "청구서 통화 기준으로 지불해야 할 잔액."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_residual
msgid "Remaining amount due."
msgstr "지불해야 할 잔액."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_tax_dest_id
msgid "Replacement Tax"
msgstr "대체 세금"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_financial_report_form
msgid "Report"
msgstr "보고서"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_name
msgid "Report Name"
msgstr "보고서명"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.account_common_report_view
msgid "Report Options"
msgstr "보고서 옵션"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
msgid "Report Type"
msgstr "보고서 유형"

#. module: account
#: selection:account.financial.report,type:0
#: model:ir.model.fields,field_description:account.field_account_financial_report_account_report_id
msgid "Report Value"
msgstr "보고서 값"

#. module: account
#: model:ir.ui.menu,name:account.account_report_folder
#: model:ir.ui.menu,name:account.menu_finance_reports
msgid "Reporting"
msgstr "보고"

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding_rounding
msgid "Represent the non-zero value smallest coinage (for example, 0.05)."
msgstr "0이 아닌 최소 주화 단위입니다(예: 0.05)."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Reset to Draft"
msgstr "초안으로 재설정"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:236
#, python-format
msgid "Residual"
msgstr "잔존"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_amount_residual
msgid "Residual Amount"
msgstr "잔존 가치"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_amount_residual_currency
msgid "Residual Amount in Currency"
msgstr "통화 기준 잔존 가치"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_user_id
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Responsible"
msgstr "담당자"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_deferred_revenue
msgid "Revenue Recognition"
msgstr "수익인식"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal_date
msgid "Reversal date"
msgstr "환입일"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Reverse Entry"
msgstr "환입 항목"

#. module: account
#: code:addons/account/wizard/account_move_reversal.py:20
#: model:ir.actions.act_window,name:account.action_view_account_move_reversal
#: model:ir.ui.view,arch_db:account.view_account_move_reversal
#, python-format
msgid "Reverse Moves"
msgstr "환입 이동"

#. module: account
#: selection:account.financial.report,sign:0
msgid "Reverse balance sign"
msgstr "잔액 기호 역전"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review Fiscal Positions"
msgstr "재정 포지션 검토"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review Terms"
msgstr "조건 검토"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review existing Taxes"
msgstr "기존 세금 검토"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review the Chart of Accounts"
msgstr "계정 차트 검토"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review the list of available currencies (from the"
msgstr "이용 가능한 화폐 검토"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group_parent_right
msgid "Right Parent"
msgstr "오른쪽 상위"

#. module: account
#: selection:res.company,tax_calculation_rounding_method:0
msgid "Round Globally"
msgstr "전체적으로 올림"

#. module: account
#: selection:res.company,tax_calculation_rounding_method:0
msgid "Round per Line"
msgstr "라인별 올림"

#. module: account
#: model:ir.ui.view,arch_db:account.rounding_form_view
msgid "Rounding Form"
msgstr "올림 양식"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_is_rounding_line
msgid "Rounding Line"
msgstr "올림 라인"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_rounding_method
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Rounding Method"
msgstr "올림 방법"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_rounding
msgid "Rounding Precision"
msgstr "올림 정확도"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_strategy
msgid "Rounding Strategy"
msgstr "올림 전략"

#. module: account
#: model:ir.ui.view,arch_db:account.rounding_tree_view
msgid "Rounding Tree"
msgstr "올림 트리"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_sepa
msgid "SEPA Credit Transfer (SCT)"
msgstr "SEPA 신용 이전(SCT)"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "SEPA Direct Debit (SDD)"
msgstr "SEPA 자동 이체(SDD)"

#. module: account
#: selection:account.journal,type:0
#: model:ir.ui.view,arch_db:account.report_tax
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Sale"
msgstr "매출"

#. module: account
#: model:ir.ui.view,arch_db:account.view_wizard_multi_chart
msgid "Sale Tax"
msgstr "판매세"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
#: model:ir.ui.menu,name:account.menu_finance_receivables
#: model:ir.ui.view,arch_db:account.view_account_journal_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Sales"
msgstr "매출"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Sales Tax"
msgstr "판매세"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_sale_tax_rate
msgid "Sales Tax(%)"
msgstr "판매세(%)"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:33
#, python-format
msgid "Sales: Untaxed Total"
msgstr "매출: 공급가액 합계"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_id
#: model:ir.model.fields,field_description:account.field_account_invoice_user_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Salesperson"
msgstr "영업사원"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model:ir.ui.view,arch_db:account.setup_opening_move_wizard_form
#: model:ir.ui.view,arch_db:account.setup_posted_move_form
#: model:ir.ui.view,arch_db:account.setup_view_company_form
msgid "Save"
msgstr "저장"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:199
#, python-format
msgid "Save and New"
msgstr "저장 및 신규"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "이 페이지를 저장하고 돌아와서 기능을 설정합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Search Account Journal"
msgstr "계정 분개 검색"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_template_search
msgid "Search Account Templates"
msgstr "계정 템플릿 검색"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Search Bank Statements"
msgstr "예금거래 명세서 검색"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_line_search
msgid "Search Bank Statements Line"
msgstr "예금거래 명세서 라인 검색"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Search Chart of Account Templates"
msgstr "계정 차트 템플릿 검색"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Search Invoice"
msgstr "청구서 검색"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Search Journal Items"
msgstr "분개 항목 검색"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Search Move"
msgstr "이동 검색"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Search Operations"
msgstr "작업 검색"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Search Tax Templates"
msgstr "세금 템플릿 검색"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Search Taxes"
msgstr "세금 검색"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_account_id
msgid "Second Account"
msgstr "보조 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_amount
msgid "Second Amount"
msgstr "보조 금액"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_amount_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_amount_type
msgid "Second Amount type"
msgstr "보조 금액 유형"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_analytic_account_id
msgid "Second Analytic Account"
msgstr "보조 분석 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_journal_id
msgid "Second Journal"
msgstr "보조 분개"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_label
msgid "Second Journal Item Label"
msgstr "보조 분개 항목 라벨"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_tax_id
msgid "Second Tax"
msgstr "보조 세금"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_access_token
msgid "Security Token"
msgstr "보안 토큰"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_type
msgid ""
"Select 'Sale' for customer invoices journals.\n"
"Select 'Purchase' for vendor bills journals.\n"
"Select 'Cash' or 'Bank' for journals that are used in customer or vendor payments.\n"
"Select 'General' for miscellaneous operations journals."
msgstr ""
"고객 청구서 분개장은 '매출'을 선택합니다.\n"
"공급업체 청구 분개장은 '매입'을 선택합니다.\n"
"고객 또는 공급업체 지급에 사용하는 분개장은 '현금' 또는 '은행'을 선택합니다.\n"
"기타 운영 분개장은 '일반'을 선택합니다."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:74
#, python-format
msgid "Select a partner or choose a counterpart"
msgstr "협력사를 선택하거나 상대를 선택합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line_value
msgid "Select here the kind of valuation related to this payment terms line."
msgstr "이 지불 조건 라인과 관련된 가치 평가 종류를 선택합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Select this if the taxes should use cash basis, which will create an entry "
"for such taxes on a given account during reconciliation."
msgstr "세금에 현금주의를 적용하려면 이 옵션을 선택합니다. 이렇게 하면 조정 중에 특정 계정에 대한 세금 항목이 생성됩니다."

#. module: account
#: code:addons/account/wizard/account_invoice_state.py:21
#, python-format
msgid ""
"Selected invoice(s) cannot be confirmed as they are not in 'Draft' state."
msgstr "선택한 청구서는 ‘초안’ 상태가 아니므로 확정할 수 없습니다."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_invoice_warn
#: model:ir.model.fields,help:account.field_res_users_invoice_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"\"경고\" 옵션을 선택하면 사용자들이 메시지를 받을 수 있습니다. \"메시지 차단\"을 선택하면, 예외가 발생하고 메시지 흐름이 "
"차단됩니다. 메시지는 다음 필드에 작성되어야 합니다."

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action_sellable
#: model:ir.ui.menu,name:account.product_product_menu_sellable
msgid "Sellable Products"
msgstr "판매 가능 제품"

#. module: account
#: selection:account.abstract.payment,payment_type:0
#: selection:account.payment,payment_type:0
#: selection:account.register.payments,payment_type:0
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Send Money"
msgstr "송금"

#. module: account
#: model:ir.actions.act_window,name:account.account_send_payment_receipt_by_email_action
msgid "Send Receipt By Email"
msgstr "이메일로 영수증 전송"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Send by Email"
msgstr "이메일 전송"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Sending customer statements (outstanding invoices) manually during the "
"transition period"
msgstr "이전 기간에 고객 명세서(미불 청구서)를 직접 전송"

#. module: account
#: selection:account.payment,state:0
#: model:ir.model.fields,field_description:account.field_account_invoice_sent
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Sent"
msgstr "발송됨"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "September"
msgstr "9월"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_sequence
#: model:ir.model.fields,field_description:account.field_account_financial_report_sequence
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_sequence
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_sequence
#: model:ir.model.fields,field_description:account.field_account_invoice_line_sequence
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_sequence
#: model:ir.model.fields,field_description:account.field_account_journal_sequence
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_sequence
#: model:ir.model.fields,field_description:account.field_account_payment_term_sequence
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_sequence
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_sequence
#: model:ir.model.fields,field_description:account.field_account_tax_group_sequence
#: model:ir.model.fields,field_description:account.field_account_tax_sequence
#: model:ir.model.fields,field_description:account.field_account_tax_template_sequence
msgid "Sequence"
msgstr "순서"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Set To Draft"
msgstr "초안으로 설정"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag_active
msgid "Set active to false to hide the Account Tag without removing it."
msgstr "계정 태그를 제거하지 않고 숨기려면 false로 설정합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_active
msgid "Set active to false to hide the Journal without removing it."
msgstr "분개장을 제거하지 않고 숨기려면 false로 설정합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_active
#: model:ir.model.fields,help:account.field_account_tax_template_active
msgid "Set active to false to hide the tax without removing it."
msgstr "세금을 제거하지 않고 숨기려면 false로 설정합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Set the default Sales and Purchase taxes"
msgstr "기본 판매세와 구매세 설정"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_tax_adjustment
msgid ""
"Set this field to true if this tax can be used in the tax adjustment wizard,"
" used to manually fill some data in the tax declaration"
msgstr ""
"이 세금을 세금 조정 마법사에서 사용하고, 세금 신고 시 일부 데이터를 직접 채우는 데 사용할 수 있다면 이 필드는 true로 "
"설정합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_visible
msgid ""
"Set this to False if you don't want this template to be used actively in the"
" wizard that generate Chart of Accounts from templates, this is useful when "
"you want to generate accounts of this template only when loading its child "
"template."
msgstr ""
"템플릿에서 계정 차트를 생성하는 마법사에서 이 템플릿을 능동적으로 사용하지 않을 경우, false로 설정합니다. 하위 템플릿을 로드할 때"
" 템플릿의 계정을 생성할 때 유용합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Set to Draft"
msgstr "초안으로 설정"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_config
#: model:ir.ui.menu,name:account.menu_account_config
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Settings"
msgstr "설정"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Setup"
msgstr "설정"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_bar_closed
msgid "Setup Bar Closed"
msgstr "설정 막대 닫힘"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_code
msgid "Short Code"
msgstr "단문 코드"

#. module: account
#: model:res.groups,name:account.group_account_user
msgid "Show Full Accounting Features"
msgstr "전체 회계 관리 기능 표시"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Show active taxes"
msgstr "활성 세금 표시"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Show all records which has next action date is before today"
msgstr "다음 작업 일자가 오늘 이전인 모든 기록 표시"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Show inactive taxes"
msgstr "비활성 세금 표시"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_show_on_dashboard
msgid "Show journal on dashboard"
msgstr "대시보드에 분개장 표시"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_sign
msgid "Sign on Reports"
msgstr "보고서에 서명"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Simplify your cash, checks, or credit cards deposits with an integrated "
"batch payment function."
msgstr "통합 일괄 지불 기능으로 현금, 전표 또는 신용카드 예금을 단순화하실 수 있습니다."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:107
#, python-format
msgid "Skip"
msgstr "건너뛰기"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Smallest Text"
msgstr "가장 작은 문자"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_sortby
msgid "Sort by"
msgstr "정렬 기준"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_origin
#: model:ir.model.fields,field_description:account.field_account_invoice_origin
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Source Document"
msgstr "원본 문서"

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding_strategy
msgid ""
"Specify which way will be used to round the invoice amount to the rounding "
"precision"
msgstr "올림 정확도에 맞게 청구서 금액을 올림하는 데 사용하는 방법을 지정합니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_date_from
#: model:ir.model.fields,field_description:account.field_account_balance_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_account_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_report_date_from
#: model:ir.model.fields,field_description:account.field_account_print_journal_date_from
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_date_from
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_date_from
#: model:ir.model.fields,field_description:account.field_account_tax_report_date_from
#: model:ir.model.fields,field_description:account.field_accounting_report_date_from
#: model:ir.model.fields,field_description:account.field_accounting_report_date_from_cmp
msgid "Start Date"
msgstr "시작일"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_balance_start
msgid "Starting Balance"
msgstr "개시 잔액"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_start_id
msgid "Starting Cashbox"
msgstr "시작 캐시박스"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "State"
msgstr "상태"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_parent_state
msgid "State of the parent account.move"
msgstr "상위 account.move의 상태"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_statement_id
#: model:ir.model.fields,field_description:account.field_account_move_line_statement_id
msgid "Statement"
msgstr "명세서"

#. module: account
#: code:addons/account/models/account_bank_statement.py:245
#, python-format
msgid "Statement %s confirmed, journal items were created."
msgstr "%s 명세서가 확인되었고 분개 항목이 생성되었습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_line_form
msgid "Statement Line"
msgstr "명세서 라인"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_ids
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_line_tree
msgid "Statement lines"
msgstr "명세서 라인"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_tree
msgid "Statements"
msgstr "명세서"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "States"
msgstr "상태"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_states_count
msgid "States Count"
msgstr "상태 계산"

#. module: account
#: code:addons/account/controllers/portal.py:73
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_state
#: model:ir.model.fields,field_description:account.field_account_bank_statement_state
#: model:ir.model.fields,field_description:account.field_account_invoice_state
#: model:ir.model.fields,field_description:account.field_account_move_state
#: model:ir.model.fields,field_description:account.field_account_payment_state
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Status"
msgstr "상태"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_subtotal
msgid "Subtotal"
msgstr "소계"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Supplier Payments"
msgstr "공급업체 지불"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "TOTAL ASSETS"
msgstr "총 자산"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "TOTAL EQUITY"
msgstr "총 자기 자본"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_ids
#: model:ir.model.fields,field_description:account.field_account_tax_tag_ids
#: model:ir.ui.view,arch_db:account.account_tag_view_form
msgid "Tags"
msgstr "태그"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Tags for Multidimensional Analytics"
msgstr "다차원 분석 태그"

#. module: account
#: model:ir.actions.act_window,name:account.action_cash_box_out
#: model:ir.ui.view,arch_db:account.cash_box_out_form
msgid "Take Money Out"
msgstr "자금 인출"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_target_move
#: model:ir.model.fields,field_description:account.field_account_balance_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_account_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_report_target_move
#: model:ir.model.fields,field_description:account.field_account_print_journal_target_move
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_target_move
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_target_move
#: model:ir.model.fields,field_description:account.field_account_tax_report_target_move
#: model:ir.model.fields,field_description:account.field_accounting_report_target_move
msgid "Target Moves"
msgstr "표적 이동"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:174
#: model:ir.model,name:account.model_account_tax
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_tax
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_tax_id
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_tax
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#, python-format
msgid "Tax"
msgstr "세금"

#. module: account
#: code:addons/account/models/chart_template.py:842
#: code:addons/account/models/chart_template.py:845
#, python-format
msgid "Tax %.2f%%"
msgstr "세율 %.2f%%"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_account_id
msgid "Tax Account"
msgstr "세금 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_refund_account_id
msgid "Tax Account on Credit Notes"
msgstr "신용 전표에 대한 세금 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_refund_account_id
msgid "Tax Account on Refunds"
msgstr "환불에 대한 세금 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_tax_adjustment
#: model:ir.model.fields,field_description:account.field_account_tax_template_tax_adjustment
msgid "Tax Adjustment"
msgstr "세금 조정"

#. module: account
#: model:ir.actions.act_window,name:account.tax_adjustments_form
#: model:ir.ui.menu,name:account.menu_action_tax_adjustment
msgid "Tax Adjustments"
msgstr "세금 조정"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Tax Amount"
msgstr "세액"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Tax Application"
msgstr "세금 적용"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_tax_calculation_rounding_method
msgid "Tax Calculation Rounding Method"
msgstr "세금 계산 올림법"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_tax_cash_basis_rec_id
msgid "Tax Cash Basis Entry of"
msgstr "세금 현금주의 항목"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_tax_cash_basis_journal_id
msgid "Tax Cash Basis Journal"
msgstr "세금 현금주의 분개"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_amount_type
#: model:ir.model.fields,field_description:account.field_account_tax_template_amount_type
msgid "Tax Computation"
msgstr "세금 계산"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Tax Declaration"
msgstr "세금 신고"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_name
msgid "Tax Description"
msgstr "세금 설명"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_tax_exigibility
#: model:ir.model.fields,field_description:account.field_account_tax_template_tax_exigibility
msgid "Tax Due"
msgstr "납부할 세금"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_tax_group_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_tax_group_id
msgid "Tax Group"
msgstr "세금 그룹"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Tax ID"
msgstr "세금 ID"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_line_ids
msgid "Tax Lines"
msgstr "세금 라인"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_tax_ids
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Tax Mapping"
msgstr "세금 매핑"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_name
#: model:ir.model.fields,field_description:account.field_account_tax_template_name
msgid "Tax Name"
msgstr "세금 이름"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_cash_basis_account
#: model:ir.model.fields,field_description:account.field_account_tax_template_cash_basis_account
msgid "Tax Received Account"
msgstr "세금 수취 계정"

#. module: account
#: model:ir.actions.report,name:account.action_report_account_tax
#: model:ir.model,name:account.model_account_tax_report
#: model:ir.ui.menu,name:account.menu_account_report
#: model:ir.ui.view,arch_db:account.report_tax
msgid "Tax Report"
msgstr "세금 보고서"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_tax_report
msgid "Tax Reports"
msgstr "세금 보고서"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_type_tax_use
#: model:ir.model.fields,field_description:account.field_account_tax_type_tax_use
msgid "Tax Scope"
msgstr "세금 범위"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_tax_src_id
msgid "Tax Source"
msgstr "세금 소스"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Tax Template"
msgstr "세금 템플릿"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_tax_template_ids
msgid "Tax Template List"
msgstr "세금 템플릿 목록"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_tax_template_form
msgid "Tax Templates"
msgstr "세금 템플릿"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr "세금 계산 올림법"

#. module: account
#: sql_constraint:account.tax:0 sql_constraint:account.tax.template:0
msgid "Tax names must be unique !"
msgstr "세금 이름은 중복될 수 없습니다!"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_tax_src_id
msgid "Tax on Product"
msgstr "제품에 대한 세금"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_tax_dest_id
msgid "Tax to Apply"
msgstr "적용할 세금"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "TaxCloud"
msgstr "택스클라우드"

#. module: account
#: selection:account.account.tag,applicability:0
#: model:account.tax.group,name:account.tax_group_taxes
#: model:ir.actions.act_window,name:account.action_tax_form
#: model:ir.model.fields,field_description:account.field_account_invoice_line_invoice_line_tax_ids
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_ids
#: model:ir.ui.menu,name:account.menu_action_tax_form
#: model:ir.ui.view,arch_db:account.account_planner
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Taxes"
msgstr "세금"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_tax
msgid "Taxes Fiscal Position"
msgstr "조세 재정 포지션"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_template_form
msgid "Taxes Mapping"
msgstr "세금 매핑"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Taxes used in Purchases"
msgstr "매입에 사용한 세금"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Taxes used in Sales"
msgstr "매출에 사용한 세금"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Taxes, fiscal positions, chart of accounts &amp; legal statements for your "
"country"
msgstr "세금, 재정 포지션, 계정 차트, 국가별 법적 설명문"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_balance
msgid ""
"Technical field holding the debit - credit in order to open meaningful graph"
" views from reports"
msgstr "보고서에서 의미 있는 그래프 보기를 도출하기 위해 차변-대변이 포함된 기술 필드"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_balance_cash_basis
msgid ""
"Technical field holding the debit_cash_basis - credit_cash_basis in order to"
" open meaningful graph views from reports"
msgstr ""
"보고서에서 의미 있는 그래프 보기를 도출하기 위해 debit_cash_basis - credit_cash_basis가 포함된 기술 필드"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_move_name
msgid ""
"Technical field holding the number given to the invoice, automatically set "
"when the invoice is validated then stored to set the same number again if "
"the invoice is cancelled, set to draft and re-validated."
msgstr ""
"청구서에 지정된 번호를 포함한 기술 필드입니다. 청구서를 검증한 다음, 청구서를 취소했을 때 동일한 번호로 다시 저장하거나 초안으로 "
"설정하거나 다시 검증할 때 자동으로 설정됩니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_move_name
#: model:ir.model.fields,help:account.field_account_payment_move_name
msgid ""
"Technical field holding the number given to the journal entry, automatically"
" set when the statement line is reconciled then stored to set the same "
"number again if the line is cancelled, set to draft and re-processed again."
msgstr ""
"분개 항목에 지정된 번호를 포함한 기술 필드입니다. 명세서 라인을 조정한 다음, 라인을 취소했을 때 동일한 번호로 다시 저장하거나 "
"초안으로 설정하거나 다시 처리할 때 자동으로 설정됩니다."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_bank_data_done
msgid "Technical field holding the status of the bank setup step."
msgstr "은행 설정 상태가 포함된 기술 필드입니다."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_coa_done
msgid "Technical field holding the status of the chart of account setup step."
msgstr "계정 설정 상태가 포함된 기술 필드입니다."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_company_data_done
msgid "Technical field holding the status of the company setup step."
msgstr "회사 설정 상태가 포함된 기술 필드입니다."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_fy_data_done
msgid "Technical field holding the status of the financial year setup step."
msgstr "회계연도 설정 상태가 포함된 기술 필드입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_register_payments_multi
msgid ""
"Technical field indicating if the user selected invoices from multiple "
"partners or from different types."
msgstr "사용자가 선택한 청구서가 여러 협력사의 것인지, 여러 유형으로 분리되어 있는지 나타내는 기술 필드입니다."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_bar_closed
msgid ""
"Technical field set to True when setup bar has been closed by the user."
msgstr "사용자가 설정 막대를 닫으면 ture로 설정되는 기술 필드입니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_refund_only
msgid ""
"Technical field to hide filter_refund in case invoice is partially paid"
msgstr "청구서 대금이 부분 지불되었을 때 filter_refund를 숨기는 기술 필드입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_journal_type
#: model:ir.model.fields,help:account.field_account_payment_has_invoices
msgid "Technical field used for usability purposes"
msgstr "사용성 목적으로 사용하는 기술 필드입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_matched_percentage
msgid "Technical field used in cash basis method"
msgstr "현금주의법에 사용하는 기술 필드입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_account_setup_bank_data_done
msgid "Technical field used in the special view for the setup bar step."
msgstr "설정 바를 설정하기 위해 특수 보기에서 사용하는 기술 필드입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_payment_method_code
#: model:ir.model.fields,help:account.field_account_payment_payment_method_code
#: model:ir.model.fields,help:account.field_account_register_payments_payment_method_code
msgid ""
"Technical field used to adapt the interface to the payment type selected."
msgstr "선택한 지불 유형에 맞게 인터페이스를 조정하는 기술 필드입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_partial_reconcile_max_date
msgid ""
"Technical field used to determine at which date this reconciliation needs to"
" be shown on the aged receivable/payable reports."
msgstr "미수금/미지급금 보고서에 이 조정을 표시할 날짜를 확인하기 위한 기술 필드입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_hide_payment_method
#: model:ir.model.fields,help:account.field_account_payment_hide_payment_method
#: model:ir.model.fields,help:account.field_account_register_payments_hide_payment_method
msgid ""
"Technical field used to hide the payment method if the selected journal has "
"only one available which is 'manual'"
msgstr "선택한 분개가 ‘수동’만 이용 가능할 경우 지급 방법을 숨기는 기술 필드입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_tax_cash_basis_rec_id
msgid ""
"Technical field used to keep track of the tax cash basis reconciliation. "
"This is needed when cancelling the source: it will post the inverse journal "
"entry to cancel that part too."
msgstr ""
"세금 현금주의 원칙에 따른 조정을 추적하는 기술 필드입니다. 원본을 취소할 때 필요합니다. 이 부분도 취소하려면 역 분개를 전기합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_tax_exigible
msgid ""
"Technical field used to mark a tax line as exigible in the vat report or not"
" (only exigible journal items are displayed). By default all new journal "
"items are directly exigible, but with the feature cash_basis on taxes, some "
"will become exigible only when the payment is recorded."
msgstr ""
"VAT 보고서에서 집행 가능한 것으로 세금 라인을 표시하는 기술 필드입니다(집행 가능한 분개 항목만 표시됩니다). 기본적으로 모든 분개 "
"항목은 직접 집행이 가능하지만 세금에 cash_basis 기능을 적용하면 일부는 지불이 기록될 때만 집행 가능하게 됩니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_is_unaffected_earnings_line
msgid ""
"Tells whether or not this line belongs to an unaffected earnings account"
msgstr "이 라인이 영향을 받지 않은 순수익 계정에 포함되는지 알려줍니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_chart_template_id
msgid "Template"
msgstr "서식"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_account_template
msgid "Template Account Fiscal Mapping"
msgstr "템플릿 계정 재무 매핑"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_tax_template
msgid "Template Tax Fiscal Position"
msgstr "템플릿 조세 재정 포지션"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "재정 포지션 템플릿"

#. module: account
#: model:ir.model,name:account.model_account_chart_template
msgid "Templates for Account Chart"
msgstr "계정 차트 템플릿"

#. module: account
#: model:ir.model,name:account.model_account_account_template
msgid "Templates for Accounts"
msgstr "계정 템플릿"

#. module: account
#: model:ir.model,name:account.model_account_tax_template
msgid "Templates for Taxes"
msgstr "세금 템플릿"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Term Type"
msgstr "조건 유형"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_ids
#: model:ir.ui.view,arch_db:account.view_payment_term_form
msgid "Terms"
msgstr "조건"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Terms &amp; Conditions"
msgstr "약관"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Terms and conditions..."
msgstr "약관..."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "That's on average"
msgstr "평균입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_internal_type
#: model:ir.model.fields,help:account.field_account_account_type_type
msgid ""
"The 'Internal Type' is used for features available on different types of "
"accounts: liquidity type is for cash or bank accounts, payable/receivable is"
" for vendor/customer accounts."
msgstr ""
"'내부 유형'은 다양한 계정 유형에 사용하는 기능입니다. 유동성 유형은 현금 또는 은행 계좌에 사용하고, 지급/수취 유형은 공급업체/고객"
" 계정에 사용합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The Advisors have full access to the Accounting application, \n"
"                                        plus access to miscellaneous operations such as salary and asset management."
msgstr ""
"자문은 회계 관리 애플리케이션의 모든 기능을 사용할 수 있고\n"
"                                        급여, 자산 관리 등의 기타 작업에도 액세스할 수 있습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The Deposit Ticket module can also be used to settle credit card batch made "
"of multiple transactions."
msgstr "예금 티켓 모듈은 여러 거래에서 발생한 신용 카드 대금을 일괄 정산하는 데 사용할 수 있습니다."

#. module: account
#: code:addons/account/models/account_move.py:1209
#, python-format
msgid "The account %s (%s) is deprecated !"
msgstr "계정 %s (%s)이(가) 상각되었습니다!"

#. module: account
#: code:addons/account/models/account_move.py:1021
#, python-format
msgid "The account %s (%s) is not marked as reconciliable !"
msgstr "계정 %s (%s)이(가) 조정으로 표시되지 않았습니다!"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_bank_journal_id
msgid "The accounting journal corresponding to this bank account."
msgstr "이 은행 계좌에 해당하는 회계 분개입니다."

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_currency_exchange_journal_id
msgid ""
"The accounting journal where automatic exchange differences will be "
"registered"
msgstr "자동 외환차이가 등록되는 회계 분개입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_amount_currency
#: model:ir.model.fields,help:account.field_account_move_line_amount_currency
msgid ""
"The amount expressed in an optional other currency if it is a multi-currency"
" entry."
msgstr "다중 통화 항목일 경우, 다른 선택 통화로 금액이 표시됩니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_analytic_line_amount_currency
#: model:ir.model.fields,help:account.field_account_analytic_line_analytic_amount_currency
msgid ""
"The amount expressed in the related account currency if not equal to the "
"company one."
msgstr "관련 계정 통화가 회사 통화와 동일하지 않으면 금액이 계정 통화로 표시됩니다."

#. module: account
#: code:addons/account/models/account_move.py:508
#, python-format
msgid ""
"The amount expressed in the secondary currency must be positive when account"
" is debited and negative when account is credited."
msgstr "보조 통화로 표시된 금액은 계정을 인출했을 때는 양수이고 계정에 입금되었을 때는 음수여야 합니다."

#. module: account
#: code:addons/account/models/account.py:804
#, python-format
msgid ""
"The application scope of taxes in a group must be either the same as the "
"group or \"None\"."
msgstr "그룹에 세금이 적용되는 범위는 그룹과 동일하거나 \"없음\"이어야 합니다."

#. module: account
#: code:addons/account/models/account.py:452
#, python-format
msgid ""
"The bank account of a bank journal must belong to the same company (%s)."
msgstr "은행 분개의 은행 계좌는 동일한 회사에 속해야 합니다(%s)."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_statement_id
msgid "The bank statement used for bank reconciliation"
msgstr "은행 조정에 사용된 예금거래 명세서"

#. module: account
#: code:addons/account/models/account_invoice.py:1165
#, python-format
msgid ""
"The cash rounding cannot be computed because the difference must be added on the biggest tax found and no tax are specified.\n"
"Please set up a tax or change the cash rounding method."
msgstr ""
"차액을 가장 큰 세율에 추가해야 하고 세금이 지정되지 않았기 때문에 현금 올림을 계산할 수 없습니다.\n"
"세금을 설정하거나 현금 올림법을 변경해 주십시오."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_chart_template_id
msgid "The chart template for the company (if any)"
msgstr "회사의 차트 템플릿(해당할 경우)"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
msgid "The closing balance is different than the computed one!"
msgstr "마감 잔액이 계산된 잔액과 다릅니다!"

#. module: account
#: sql_constraint:account.journal:0
msgid "The code and name of the journal must be unique per company !"
msgstr "분개 코드와 이름이 회사마다 달라야 합니다!"

#. module: account
#: sql_constraint:account.account:0
msgid "The code of the account must be unique per company !"
msgstr "계정 코드가 회사마다 달라야 합니다!"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_commercial_partner_id
msgid ""
"The commercial entity that will be used on Journal Entries for this invoice"
msgstr "이 청구서의 분개에 사용할 상업 항목입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_currency_id
msgid "The currency used to enter statement"
msgstr "명세서 입력에 사용하는 통화"

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree1
msgid ""
"The discussions with your customer are automatically displayed at\n"
"                the bottom of each invoice."
msgstr ""
"고객과 의논한 내용은 각 청구서 하단에\n"
"                자동 표시됩니다."

#. module: account
#: code:addons/account/models/account_bank_statement.py:191
#, python-format
msgid ""
"The ending balance is incorrect !\n"
"The expected balance (%s) is different from the computed one. (%s)"
msgstr ""
"결산 잔액이 잘못되었습니다!\n"
"결산 잔액(%s)이 산출 잔액과 다릅니다(%s)."

#. module: account
#: model:ir.model.fields,help:account.field_product_category_property_account_expense_categ_id
msgid ""
"The expense is accounted for when a vendor bill is validated, except in "
"anglo-saxon accounting with perpetual inventory valuation in which case the "
"expense (Cost of Goods Sold account) is recognized at the customer invoice "
"validation."
msgstr ""
"공급업체 청구서가 검증되면 경비가 적용됩니다. 단, 영구 재고 가치 평가를 적용하는 앵글로색슨 회계는 고객 청구서 검증 시 경비(상품 "
"판매 계정의 비용)를 인식합니다."

#. module: account
#: model:ir.model.fields,help:account.field_product_product_property_account_expense_id
#: model:ir.model.fields,help:account.field_product_template_property_account_expense_id
msgid ""
"The expense is accounted for when a vendor bill is validated, except in "
"anglo-saxon accounting with perpetual inventory valuation in which case the "
"expense (Cost of Goods Sold account) is recognized at the customer invoice "
"validation. If the field is empty, it uses the one defined in the product "
"category."
msgstr ""
"공급업체 청구서가 검증되면 경비가 적용됩니다. 단, 영구 재고 가치 평가를 적용하는 앵글로색슨 회계는 고객 청구서 검증 시 경비(상품 "
"판매 계정의 비용)를 인식합니다. 필드가 비어 있으면 제품 범주에 정의된 것을 사용합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The first step is to set up your company's information. This is mostly used "
"in printed business documents like orders and invoices."
msgstr "첫 단계는 회사 정보를 설정하는 것입니다. 이 정보는 대부분 주문, 청구서 등의 인쇄된 업무 문서에 사용합니다."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_account_position_id
#: model:ir.model.fields,help:account.field_res_users_property_account_position_id
msgid ""
"The fiscal position will determine taxes and accounts used for the partner."
msgstr "재정 포지션에 따라 협력사에 사용하는 세금과 계정이 결정됩니다."

#. module: account
#: code:addons/account/models/account.py:456
#, python-format
msgid "The holder of a journal's bank account must be the company (%s)."
msgstr "분개의 은행 계좌를 보유한 대상은 회사여야 합니다(%s)."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_account_id
msgid "The income or expense account related to the selected product."
msgstr "선택된 제품과 관련된 수입 또는 경비 계정입니다."

#. module: account
#: code:addons/account/models/account_payment.py:643
#, python-format
msgid "The journal %s does not have a sequence, please specify one."
msgstr "분개 %s은(는) 순서가 없습니다. 순서를 지정해 주십시오."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_code
msgid "The journal entries of this journal will be named using this prefix."
msgstr "이 분개장의 분개는 이 프리픽스를 사용하여 이름을 지정합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_opening_opening_move_id
#: model:ir.model.fields,help:account.field_res_company_account_opening_move_id
msgid ""
"The journal entry containing the initial balance of all this company's "
"accounts."
msgstr "모든 회사 계정의 기초 잔고가 포함된 분개입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op_fiscalyear_last_day
#: model:ir.model.fields,help:account.field_account_financial_year_op_fiscalyear_last_month
msgid ""
"The last day of the month will be taken if the chosen day doesn't exist."
msgstr "선택한 날짜가 없으면 당월 말일을 지정합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_form
msgid ""
"The last line's computation type should be \"Balance\" to ensure that the "
"whole amount will be allocated."
msgstr "마지막 라인의 계산 유형은 \"잔액\"이 되어야 전체 금액이 할당됩니다."

#. module: account
#: code:addons/account/models/company.py:94
#, python-format
msgid "The lock date for advisors is irreversible and can't be removed."
msgstr "조언자의 잠금 날짜는 되돌릴 수 없으며 제거할 수 없습니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_move_id
msgid "The move of this entry line."
msgstr "이 항목 라인의 이동입니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The multi-currency option allows you to send or receive invoices \n"
"                        in difference currencies, set up foreign bank accounts \n"
"                        and run reports on your foreign financial activities."
msgstr ""
"다중 통화 옵션을 사용하면 여러 통화로 청구서를\n"
"                        발송/수령할 수 있고, 해외 은행 계좌를\n"
"                        개설할 수 있으며, 해외 재무 활동에 대한 보고서를 작성할 수 있습니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_name
msgid "The name that will be used on account move lines"
msgstr "계정 이동 라인에 사용할 이름입니다."

#. module: account
#: code:addons/account/models/company.py:98
#, python-format
msgid ""
"The new lock date for advisors must be set after the previous lock date."
msgstr "새 조언자의 잠금 날짜는 이전 잠금 날짜 이후에 설정해야 합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_refund_sequence_number_next
msgid "The next sequence number will be used for the next credit note."
msgstr "다음 순번은 다음 신용 전표에 사용합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_sequence_number_next
msgid "The next sequence number will be used for the next invoice."
msgstr "다음 순번은 다음 청구서에 사용합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_currency_id
#: model:ir.model.fields,help:account.field_account_move_line_currency_id
msgid "The optional other currency if it is a multi-currency entry."
msgstr "다중 통화 항목일 경우, 다른 선택 통화로 표시됩니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_quantity
msgid ""
"The optional quantity expressed by this line, eg: number of product sold. "
"The quantity is not a legal requirement but is very useful for some reports."
msgstr ""
"이 라인에서 표시한 선택 수량(예: 판매한 제품 수량)입니다. 수량은 법적인 요구 사항은 아니지만, 일부 보고서에서는 매우 유용하게 "
"사용됩니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_account_id
msgid "The partner account used for this invoice."
msgstr "이 청구서에 사용된 협력사 계정입니다."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_has_unreconciled_entries
#: model:ir.model.fields,help:account.field_res_users_has_unreconciled_entries
msgid ""
"The partner has at least one unreconciled debit and credit since last time "
"the invoices & payments matching was performed."
msgstr "마지막으로 청구서 및 지불 매칭을 수행한 이후로 협력사에 1개 이상의 미조정 차변과 대변이 있습니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_reference
msgid "The partner reference of this invoice."
msgstr "청구서 협력사 참조"

#. module: account
#: code:addons/account/models/account.py:522
#, python-format
msgid ""
"The partners of the journal's company and the related bank account mismatch."
msgstr "회사 분개장의 협력사와 관련 은행 계정이 일치하지 않습니다."

#. module: account
#: code:addons/account/models/account_payment.py:60
#, python-format
msgid "The payment amount cannot be negative."
msgstr "지불 금액은 음수일 수 없습니다."

#. module: account
#: code:addons/account/models/account_payment.py:487
#, python-format
msgid "The payment cannot be processed because the invoice is not open!"
msgstr "청구서가 개시되지 않아서 지불을 처리할 수 없습니다!"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_amount_residual_currency
msgid ""
"The residual amount on a journal item expressed in its currency (possibly "
"not the company currency)."
msgstr "해당 통화(회사 통화가 아닐 가능성이 큼)로 표시된 분개 항목의 잔존 가치입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_amount_residual
msgid ""
"The residual amount on a journal item expressed in the company currency."
msgstr "회사 통화로 표시된 분개 항목의 잔존 가치입니다."

#. module: account
#: code:addons/account/models/account_move.py:493
#, python-format
msgid ""
"The selected account of your Journal Entry forces to provide a secondary "
"currency. You should remove the secondary currency on the account."
msgstr "분개에서 선택된 계정은 강제로 보조 통화를 표시합니다. 계정에서 보조 통화를 삭제해야 합니다."

#. module: account
#: code:addons/account/models/account_invoice.py:1612
#, python-format
msgid ""
"The selected unit of measure is not compatible with the unit of measure of "
"the product."
msgstr "선택한 측정 단위가 제품 측정 단위와 일치하지 않습니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_sequence
#: model:ir.model.fields,help:account.field_account_tax_template_sequence
msgid ""
"The sequence field is used to define order in which the tax lines are "
"applied."
msgstr "순서 필드를 사용하여 세금 라인을 적용한 순서를 지정합니다."

#. module: account
#: code:addons/account/models/account_payment.py:645
#, python-format
msgid "The sequence of journal %s is deactivated."
msgstr "분개 %s의 순서가 비활성화되었습니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding_rounding_method
msgid "The tie-breaking rule used for float rounding operations"
msgstr "부동 소수점 올림 연산에 사용하는 결정 규칙입니다."

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid "There are currently no invoices and payments for your account."
msgstr "현재 귀하의 계정에 청구서와 지불금이 없습니다."

#. module: account
#: code:addons/account/models/company.py:178
#, python-format
msgid ""
"There are still unposted entries in the period you want to lock. You should "
"either post or delete them."
msgstr "잠그고자 하는 기간에 전기되지 않은 항목이 있습니다. 전기하거나 삭제해야 합니다."

#. module: account
#: code:addons/account/models/account_bank_statement.py:179
#, python-format
msgid ""
"There is no account defined on the journal %s for %s involved in a cash "
"difference."
msgstr "현금 차액에 포함되는 %s에 대한 분개 %s에 정의된 계정이 없습니다."

#. module: account
#: code:addons/account/wizard/account_validate_account_move.py:18
#, python-format
msgid "There is no journal items in draft state to post."
msgstr "전기할 초안 상태의 분개 항목이 없습니다."

#. module: account
#: code:addons/account/models/account_move.py:1744
#, python-format
msgid ""
"There is no tax cash basis journal defined for this company: \"%s\" \n"
"Configure it in Accounting/Configuration/Settings"
msgstr ""
"이 회사에 정의된 세금 현금주의 분개가 없습니다. \"%s\\” \n"
"회계/구성/설정에서 구성합니다."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:46
#, python-format
msgid "There is nothing to reconcile."
msgstr "조정할 항목이 없습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_error
msgid "There was an error processing this page."
msgstr "이 페이지를 처리하는 도중 오류가 발생했습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "These taxes are set in any new product created."
msgstr "이 세금은 새로 생성된 제품에 설정됩니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_user_type_id
msgid ""
"These types are defined according to your country. The type contains more "
"information about the account and its specificities."
msgstr "이 유형은 국가에 따라 정의됩니다. 계정과 특수성에 관한 자세한 정보가 포함되어 있습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "These users handle billing specifically."
msgstr "이 사용자는 청구를 특수하게 처리합니다."

#. module: account
#: code:addons/account/models/account_invoice.py:1337
#, python-format
msgid ""
"This %s has been created from: <a href=# data-oe-model=account.invoice data-"
"oe-id=%d>%s</a>"
msgstr ""
"이 %s은(는) <a href=# data-oe-model=account.invoice data-oe-id=%d>%s</a>에서 "
"생성되었습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "This Month"
msgstr "당월"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:111
#, python-format
msgid "This Week"
msgstr "금주"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "This Year"
msgstr "당년"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_account_payable_id
#: model:ir.model.fields,help:account.field_res_users_property_account_payable_id
msgid ""
"This account will be used instead of the default one as the payable account "
"for the current partner"
msgstr "이 계정은 현재 협력사에 대한 지불 계정으로 기본 계정 대신 사용합니다."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_account_receivable_id
#: model:ir.model.fields,help:account.field_res_users_property_account_receivable_id
msgid ""
"This account will be used instead of the default one as the receivable "
"account for the current partner"
msgstr "이 계정은 현재 협력사의 수취 계정으로 기본 계정 대신 사용됩니다"

#. module: account
#: model:ir.model.fields,help:account.field_product_category_property_account_income_categ_id
msgid "This account will be used when validating a customer invoice."
msgstr "이 계정은 고객 청구서를 검증할 때 사용합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows accountants to manage analytic and crossovered budgets. Once the"
" master budgets and the budgets are defined, the project managers can set "
"the planned amount on each analytic account."
msgstr ""
"이를 통해 회계사는 분석 및 교차 예산을 관리할 수 있습니다. 마스터 예산과 예산이 책정되면 프로젝트 관리자는 각 분석 계정에 책정 "
"금액을 설정할 수 있습니다."

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_module_account_batch_deposit
msgid ""
"This allows you to group received checks before you deposit them to the bank.\n"
"-This installs the module account_batch_deposit."
msgstr ""
"이렇게 하면 수령한 수표를 은행에 예치하기 전에 묶을 수 있습니다.\n"
"-account_batch_deposit 모듈을 설치합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows you to manage the assets owned by a company or a person. It "
"keeps track of the depreciation occurred on those assets, and creates "
"account move for those depreciation lines."
msgstr ""
"회사 또는 개인이 소유한 자산을 관리할 수 있습니다. 자산에서 발생하는 감가상각을 추적하고 감가상각 라인에 대한 계정 이동을 생성합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows you to manage the revenue recognition on selling products. It "
"keeps track of the installments occurred on those revenue recognitions, and "
"creates account moves for those installment lines."
msgstr ""
"판매 제품에 대한 수익 인식을 관리합니다. 해당 수익 인식에서 발생한 할부를 추적하고, 해당 할부 라인에 대한 계정 이동을 생성합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_complete_tax_set
msgid ""
"This boolean helps you to choose if you want to propose to the user to "
"encode the sale and purchase rates or choose from list of taxes. This last "
"choice assumes that the set of tax defined on this template is complete"
msgstr ""
"이 부울은 사용자가 매출/매입율을 인코딩하도록 제안할지 세금 목록에서 선택하게 할지 결정하는 데 도움을 줍니다. 이 마지막 옵션에서는 이"
" 템플릿에서 정의된 세금 집합이 완전하다고 가정합니다."

#. module: account
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_complete_tax_set
msgid ""
"This boolean helps you to choose if you want to propose to the user to "
"encode the sales and purchase rates or use the usual m2o fields. This last "
"choice assumes that the set of tax defined for the chosen template is "
"complete"
msgstr ""
"이 부울은 사용자가 매출/매입율을 인코딩하도록 제안할지 일반적인 m2o 필드를 사용하게 할지 결정하는 데 도움을 줍니다. 이 마지막 "
"옵션에서는 이 템플릿에서 정의된 세금 집합이 완전하다고 가정합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "This feature is useful if you issue a high amounts of invoices."
msgstr "이 기능은 대량의 청구서를 발행할 때 유용합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_refund_sequence_id
msgid ""
"This field contains the information related to the numbering of the credit "
"note entries of this journal."
msgstr "이 필드는 이 분개장의 신용 전표 항목에 번호를 지정하는 작업과 관련된 정보가 포함되어 있습니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_sequence_id
msgid ""
"This field contains the information related to the numbering of the journal "
"entries of this journal."
msgstr "이 필드는 이 분개장의 분개에 번호를 지정하는 작업과 관련된 정보가 포함되어 있습니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model_journal_id
#: model:ir.model.fields,help:account.field_account_reconcile_model_second_journal_id
msgid "This field is ignored in a bank statement reconciliation."
msgstr "이 필드는 예금거래 명세서 조정에서는 무시합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_date_maturity
msgid ""
"This field is used for payable and receivable journal entries. You can put "
"the limit date for the payment of this line."
msgstr "이 필드는 지불 및 수취 분개에 사용합니다. 이 라인의 지불일에 제한을 걸 수 있습니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_partner_name
msgid ""
"This field is used to record the third party name when importing bank "
"statement in electronic format, when the partner doesn't exist yet in the "
"database (or cannot be found)."
msgstr ""
"이 필드는 데이터페이스에 협력사가 기록되지 않은(협력사를 찾을 수 없는) 상태에서 전자 형식으로 예금거래 명세서를 가져올 때 제삼자 "
"이름을 기록하는 데 사용합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"This guide will help you get started with Odoo Accounting.\n"
"                        Once you're done, you'll benefit from:"
msgstr ""
"이 가이드는 Odoo Accounting를 시작하는 데 도움을 줄 것입니다.\n"
"                        완료하면 다음과 같은 혜택이 있습니다."

#. module: account
#: model:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid ""
"This is the accounting dashboard. If you have not yet\n"
"                installed a chart of account, please install one first."
msgstr ""
"이는 회계 데이터베이스입니다. 아직 계정 차트를\n"
"                설치하지 않았다면 먼저 설치해 주십시오."

#. module: account
#: code:addons/account/models/account.py:494
#, python-format
msgid ""
"This journal already contains items, therefore you cannot modify its "
"company."
msgstr "이 분개에는 이미 항목이 포함되어 있으므로 회사를 수정할 수 없습니다."

#. module: account
#: code:addons/account/models/account.py:503
#, python-format
msgid ""
"This journal already contains items, therefore you cannot modify its short "
"name."
msgstr "이 분개에는 이미 항목이 포함되어 있으므로 약칭을 수정할 수 없습니다."

#. module: account
#: model:ir.model.fields,help:account.field_accounting_report_label_filter
msgid ""
"This label will be displayed on report to show the balance computed for the "
"given comparison filter."
msgstr "이 라벨은 특정 비교 필터에 대해 계산된 잔액을 표시하기 위해 보고서에 기재됩니다."

#. module: account
#: code:addons/account/models/account_payment.py:533
#, python-format
msgid ""
"This method should only be called to process a single invoice's payment."
msgstr "이 방법은 단일 청구서 지불을 처리할 때만 호출해야 합니다."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:395
#, python-format
msgid ""
"This move's amount is higher than the transaction's amount. Click to "
"register a partial payment and keep the payment balance open."
msgstr "이 이동 금액은 거래 금액보다 큽니다. 클릭하여 부분 지불을 등록하고 지불 잔액을 열어두십시오."

#. module: account
#: model:ir.model.fields,help:account.field_accounting_report_debit_credit
msgid ""
"This option allows you to get more details about the way your balances are "
"computed. Because it is space consuming, we do not allow to use it while "
"doing a comparison."
msgstr ""
"이 옵션을 사용하면 잔액 계산 방법에 대한 상세한 정보를 확인할 수 있습니다. 공간을 많이 차지하기 때문에 비교할 때는 사용하지 못하게 "
"했습니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_chart_template_id
msgid ""
"This optional field allow you to link an account template to a specific "
"chart template that may differ from the one its root parent belongs to. This"
" allow you to define chart templates that extend another and complete it "
"with few new accounts (You don't need to define the whole structure that is "
"common to both several times)."
msgstr ""
"이 옵션 필드를 사용하면 계정 템플릿을 특정 차트 템플릿과 연결할 수 있으며, 해당 차트 템플릿은 루트 상위에 속한 템플릿과 다를 수 "
"있습니다. 다른 템플릿을 확장하고 이를 새 계정으로 채우는 차트 템플릿을 지정할 수 있습니다(두 개에 공통적인 전체 구조는 여러 번 "
"정의할 필요가 없습니다)."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:35
#, python-format
msgid ""
"This page displays all the bank transactions that are to be reconciled and "
"provides with a neat interface to do so."
msgstr "이 페이지는 조정할 모든 은행 거래를 표시하고 이를 위한 간단한 인터페이스를 제공합니다."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:240
#, python-format
msgid "This payment is registered but not reconciled."
msgstr "이 지불금은 등록되었지만 조정은 거치지 않았습니다."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_supplier_payment_term_id
#: model:ir.model.fields,help:account.field_res_users_property_supplier_payment_term_id
msgid ""
"This payment term will be used instead of the default one for purchase "
"orders and vendor bills"
msgstr "이 지급 기간은 구매 주문 및 공급 업체 청구서의 기본값 대신 사용됩니다"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_payment_term_id
#: model:ir.model.fields,help:account.field_res_users_property_payment_term_id
msgid ""
"This payment term will be used instead of the default one for sales orders "
"and customer invoices"
msgstr "판매 주문, 고객 청구서에 대한 기본값 대신 이 결제 조건을 사용할 것입니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"This role is best suited for managing the day to day accounting operations:"
msgstr "이 역할은 일상적인 회계 작업을 관리하는 데 적합합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_account_id
msgid ""
"This technical field can be used at the statement line creation/import time "
"in order to avoid the reconciliation process on it later on. The statement "
"line will simply create a counterpart on this account"
msgstr ""
"이 기술 필드는 명세서 라인 생성/가져오기에 사용하여, 나중의 조정 과정을 피할 수 있습니다. 명세서 라인은 이 계정에서 대응하는 것을 "
"생성합니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_validate_account_move
msgid ""
"This wizard will validate all journal entries selected. Once journal entries"
" are validated, you can not update them anymore."
msgstr "이 마법사는 선택한 모든 분개를 검증합니다. 분개가 검증되면 더는 업데이트할 수 없습니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_reconcile_model
msgid ""
"Those can be used to quickly create a journal items when reconciling\n"
"                a bank statement or an account."
msgstr ""
"이들은 예금거래 명세서나 계정을 조정할 때\n"
"                신속히 분개 항목을 생성하는 데 사용합니다."

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_account_hide_setup_bar
msgid "Tick if you wish to hide the setup bar on the dashboard"
msgstr "대시보드에서 설정 막대를 숨기려면 표시합니다."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:31
#, python-format
msgid "Tip: Hit CTRL-Enter to reconcile all the balanced items in the sheet."
msgstr "팁: CTRL-Enter를 눌러서 시트에 모든 수지가 맞는 항목을 조정합니다."

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Title 2 (bold)"
msgstr "제목 2(굵은 활자)"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Title 3 (bold, smaller)"
msgstr "제목 3(굵은 활자, 작은 크기)"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "To Invoice"
msgstr "청구서 발행"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "To Pay"
msgstr "지불하기"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"To manage the tax applied when invoicing a Company, Odoo uses the concept of"
" Fiscal Position: they serve to automatically set the right tax and/or "
"account according to the customer country and state."
msgstr ""
"회사에 청구서를 발행할 때 적용되는 세금을 관리하기 위해 Odoo는 재정 포지션 개념을 사용합니다. 이들은 고객 국가와 상태에 따라 "
"적절한 세율 및/또는 계정을 자동 설정합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "To pay"
msgstr "지불하기"

#. module: account
#: code:addons/account/models/account_move.py:1017
#, python-format
msgid "To reconcile the entries company should be the same for all entries!"
msgstr "항목을 조정할 경우, 회사는 모든 항목에 동일하게 적용해야 합니다!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "To use the <strong>multi-currency option:</strong>"
msgstr "<strong>다중 통화 옵션 사용:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Today Activities"
msgstr "당일 활동"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_total
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
msgid "Total"
msgstr "총계"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_tree
msgid "Total Amount"
msgstr "합계 금액"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_lines_tree
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_tree
msgid "Total Credit"
msgstr "총 대변"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_lines_tree
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_tree
msgid "Total Debit"
msgstr "총 차변"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_total_invoiced
#: model:ir.model.fields,field_description:account.field_res_users_total_invoiced
msgid "Total Invoiced"
msgstr "총 청구한 금액"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_debit
#: model:ir.model.fields,field_description:account.field_res_users_debit
msgid "Total Payable"
msgstr "총 미지급금"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_credit
#: model:ir.model.fields,field_description:account.field_res_users_credit
msgid "Total Receivable"
msgstr "총 미수금"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_currency_residual
msgid "Total Residual"
msgstr "총 잔존"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_price_total
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_currency_price_total
msgid "Total Without Tax"
msgstr "세금 제외 총계"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_price_subtotal_signed
msgid "Total amount in the currency of the company, negative for credit note."
msgstr "회사 통화 기준 합계, 신용 전표에 대해 음수입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_amount_total_company_signed
msgid ""
"Total amount in the currency of the company, negative for credit notes."
msgstr "회사 통화 기준 합계, 신용 전표에 대해 음수입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_amount_total_signed
msgid ""
"Total amount in the currency of the invoice, negative for credit notes."
msgstr "청구서 통화 기준 합계, 신용 전표에 대해 음수입니다."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_credit
#: model:ir.model.fields,help:account.field_res_users_credit
msgid "Total amount this customer owes you."
msgstr "이 고객이 지불해야 할 합계 금액입니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_price_total
msgid "Total amount with taxes"
msgstr "세금을 포함한 합계"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_price_subtotal
msgid "Total amount without taxes"
msgstr "세금을 제외한 합계"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_debit
#: model:ir.model.fields,help:account.field_res_users_debit
msgid "Total amount you have to pay to this vendor."
msgstr "이 공급업체에 지불해야 하는 합계 금액입니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_total_company_signed
msgid "Total in Company Currency"
msgstr "회사 통화 합계"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_total_signed
msgid "Total in Invoice Currency"
msgstr "청구서 통화 합계"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_total_entry_encoding
msgid "Total of transaction lines."
msgstr "거래 라인 합계입니다."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Track costs &amp; revenues by project, department, etc."
msgstr "프로젝트, 부서별로 비용과 수익을 추적합니다."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:250
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#, python-format
msgid "Transaction"
msgstr "거래"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "Transactions"
msgstr "거래"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_total_entry_encoding
msgid "Transactions Subtotal"
msgstr "거래 소계"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_transfer_account_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_transfer_account_id
msgid "Transfer Account"
msgstr "이전 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_destination_journal_id
msgid "Transfer To"
msgstr "전송"

#. module: account
#: code:addons/account/models/account_payment.py:357
#, python-format
msgid "Transfer account not defined on the company."
msgstr "회사에 이전 계정이 정의되지 않았습니다."

#. module: account
#: code:addons/account/models/account_payment.py:618
#, python-format
msgid "Transfer from %s"
msgstr "%s에서 이전"

#. module: account
#: code:addons/account/models/account_payment.py:699
#, python-format
msgid "Transfer to %s"
msgstr "%s(으)로 이전"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Transfers"
msgstr "이전"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_balance_menu
#: model:ir.actions.report,name:account.action_report_trial_balance
#: model:ir.ui.menu,name:account.menu_general_Balance_report
msgid "Trial Balance"
msgstr "시산표"

#. module: account
#: model:ir.model,name:account.model_account_balance_report
msgid "Trial Balance Report"
msgstr "시산표 보고서"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_user_type_id
#: model:ir.model.fields,field_description:account.field_account_account_type_type
#: model:ir.model.fields,field_description:account.field_account_account_user_type_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_journal_type
#: model:ir.model.fields,field_description:account.field_account_financial_report_type
#: model:ir.model.fields,field_description:account.field_account_invoice_line_invoice_type
#: model:ir.model.fields,field_description:account.field_account_invoice_report_type
#: model:ir.model.fields,field_description:account.field_account_invoice_type
#: model:ir.model.fields,field_description:account.field_account_journal_type
#: model:ir.model.fields,field_description:account.field_account_move_line_user_type_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_value
msgid "Type"
msgstr "유형"

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "UP"
msgstr "위로"

#. module: account
#: selection:account.journal,bank_statements_source:0
msgid "Undefined Yet"
msgstr "아직 지정되지 않음"

#. module: account
#: code:addons/account/models/company.py:367
#, python-format
msgid "Undistributed Profits/Losses"
msgstr "미분배 손익"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:392
#, python-format
msgid "Undo the partial reconciliation."
msgstr "부분 조정을 취소합니다."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_unit
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Unit Price"
msgstr "단가"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_product_uom_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_uom_id
#: model:ir.model.fields,field_description:account.field_account_move_line_product_uom_id
msgid "Unit of Measure"
msgstr "측정 단위"

#. module: account
#: code:addons/account/report/account_aged_partner_balance.py:194
#, python-format
msgid "Unknown Partner"
msgstr "미지의 협력사"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Unless you are starting a new business, you probably have a list of "
"customers and vendors you'd like to import."
msgstr "새로운 사업을 시작하는 것이 아니라면, 가져올 고객 및 공급업체 명단이 있을 것입니다."

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model:ir.ui.view,arch_db:account.setup_view_company_form
msgid "Unmark as done"
msgstr "완료로 표시 취소"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_invoice_opened
msgid "Unpaid Invoices"
msgstr "미지급 청구서"

#. module: account
#: selection:account.move,state:0
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unposted"
msgstr "전기되지 않음"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Unposted Journal Entries"
msgstr "전기하지 않은 분개"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unposted Journal Items"
msgstr "전기하지 않은 분개 항목"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:81
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
#, python-format
msgid "Unreconcile"
msgstr "미조정"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_unreconcile
msgid "Unreconcile Entries"
msgstr "미조정된 항목"

#. module: account
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
msgid "Unreconcile Transactions"
msgstr "미조정된 거래"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unreconciled"
msgstr "미조정"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_acount_move_line_open_unreconciled
msgid "Unreconciled Entries"
msgstr "미조정 항목"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_untaxed
msgid "Untaxed Amount"
msgstr "공급가액"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_untaxed_signed
msgid "Untaxed Amount in Company Currency"
msgstr "회사 통화로 표시한 공급가액"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Update exchange rates automatically"
msgstr "환율 자동 업데이트"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Use"
msgstr "사용"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_use_anglo_saxon
msgid "Use Anglo-Saxon Accounting"
msgstr "앵글로색슨 회계 사용"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_use_anglo_saxon
msgid "Use Anglo-Saxon accounting"
msgstr "앵글로색슨 회계 사용"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_tax_exigibility
msgid "Use Cash Basis"
msgstr "현금주의 사용"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_sepa_direct_debit
msgid "Use SEPA Direct Debit"
msgstr "SEPA 자동 이체 사용"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal_journal_id
msgid "Use Specific Journal"
msgstr "특정 분개 사용"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_anglo_saxon_accounting
msgid "Use anglo-saxon accounting"
msgstr "앵글로색슨 회계 사용"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_batch_deposit
msgid "Use batch deposit"
msgstr "일괄 예금 사용"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Use depreciation boards, automate amortization entries"
msgstr "감가상각 보드 사용, 정액 상각 자동화"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Use follow-up levels and schedule actions"
msgstr "후속 관리 수준 사용 및 작업 일정 관리"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Use in conjunction with contracts to calculate your monthly revenue for "
"multi-month contracts."
msgstr "계약서와 함께 사용하여 여러 개월에 걸친 계약의 월 수익을 계산합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"Use this option if you want to cancel an invoice and create a new\n"
"                                one. The credit note will be created, validated and reconciled\n"
"                                with the current invoice. A new draft invoice will be created\n"
"                                so that you can edit it."
msgstr ""
"청구서를 취소하고 새 청구서를 생성하고 싶다면\n"
"                                이 옵션을 사용합니다. 당기 청구서로 신용 전표를 생성, 검증하고\n"
"                                조정합니다. 새 초안 청구서가 생성되어\n"
"                                이를 편집할 수 있습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"Use this option if you want to cancel an invoice you should not\n"
"                                have issued. The credit note will be created, validated and reconciled\n"
"                                with the invoice. You will not be able to modify the credit note."
msgstr ""
"발행하지 말았어야 할 청구서를 취소하려면\n"
"                                이 옵션을 사용합니다. 청구서로 신용 전표를 생성, 검증하고\n"
"                                조정합니다. 신용 전표를 수정할 수 없습니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_type_include_initial_balance
msgid ""
"Used in reports to know if we should consider journal items from the "
"beginning of time instead of from the fiscal year only. Account types that "
"should be reset to zero at each new fiscal year (like expenses, revenue..) "
"should not have this option set."
msgstr ""
"보고서에서 회계연도 시작 시가 아니라 기간 시작 시에 분개 항목을 고려해야 하는지 표시합니다. 각 회계연도에 0으로 재설정해야 하는 "
"유형(예: 경비, 수익)은 이 옵션을 설정해서는 안 됩니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_reference
msgid ""
"Used to hold the reference of the external mean that created this statement "
"(name of imported file, reference of online synchronization...)"
msgstr "외부 참조를 포함하여 이 명세서를 생성합니다(가져온 파일 이름, 온라인 동기화 참조...)."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_sequence
msgid "Used to order Journals in the dashboard view"
msgstr "대시보드 보기에서 분개장을 주문하는 데 사용합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_loss_account_id
msgid ""
"Used to register a loss when the ending balance of a cash register differs "
"from what the system computes"
msgstr "시스템이 계산한 값과 금전 등록기의 마감 잔액에 차이가 있을 경우 손실을 기록하는 데 사용합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_profit_account_id
msgid ""
"Used to register a profit when the ending balance of a cash register differs"
" from what the system computes"
msgstr "시스템이 계산한 값과 금전 등록기의 마감 잔액에 차이가 있을 경우 이익을 기록하는 데 사용합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_analytic_line_company_currency_id
#: model:ir.model.fields,help:account.field_account_bank_statement_line_journal_currency_id
#: model:ir.model.fields,help:account.field_account_move_line_company_currency_id
#: model:ir.model.fields,help:account.field_account_partial_reconcile_company_currency_id
#: model:ir.model.fields,help:account.field_res_partner_currency_id
#: model:ir.model.fields,help:account.field_res_users_currency_id
msgid "Utility field to express amount currency"
msgstr "금액 통화를 나타내는 유틸리티 필드"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_vat_required
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_vat_required
msgid "VAT required"
msgstr "VAT 필요"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
msgid "VAT:"
msgstr "VAT:"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:72
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#, python-format
msgid "Validate"
msgstr "검증"

#. module: account
#: model:ir.model,name:account.model_validate_account_move
msgid "Validate Account Move"
msgstr "계정 이동 검증"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Validate purchase orders and control vendor bills by departments."
msgstr "매입 주문을 검증하고 및 부서별 공급업체 청구서를 관리합니다."

#. module: account
#: selection:account.bank.statement,state:0
#: model:mail.message.subtype,name:account.mt_invoice_validated
msgid "Validated"
msgstr "검증됨"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_value_amount
msgid "Value"
msgstr "가치"

#. module: account
#: selection:account.abstract.payment,partner_type:0
#: selection:account.payment,partner_type:0
#: selection:account.register.payments,partner_type:0
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_supplier_payment_tree
msgid "Vendor"
msgstr "공급업체"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_invoice.py:443
#: code:addons/account/models/account_invoice.py:1215
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Vendor Bill"
msgstr "공급업체 청구서"

#. module: account
#: code:addons/account/models/account_invoice.py:444
#, python-format
msgid "Vendor Bill - %s"
msgstr "공급업체 청구서 - %s"

#. module: account
#: code:addons/account/models/chart_template.py:194
#: model:ir.actions.act_window,name:account.action_invoice_tree2
#: model:ir.ui.menu,name:account.menu_action_invoice_tree2
#: model:ir.ui.view,arch_db:account.account_planner
#, python-format
msgid "Vendor Bills"
msgstr "공급업체 청구서"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_invoice.py:445
#: code:addons/account/models/account_payment.py:680
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Vendor Credit Note"
msgstr "공급업체 신용 전표"

#. module: account
#: code:addons/account/models/account_invoice.py:446
#, python-format
msgid "Vendor Credit Note - %s"
msgstr "공급업체 신용 전표 - %s"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_in_refund
#: model:ir.ui.menu,name:account.menu_action_invoice_in_refund
msgid "Vendor Credit Notes"
msgstr "공급업체 신용 전표"

#. module: account
#: code:addons/account/models/account_invoice.py:1217
#, python-format
msgid "Vendor Credit note"
msgstr "공급업체 신용 전표"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Vendor Flow"
msgstr "공급업체 흐름"

#. module: account
#: code:addons/account/models/account_payment.py:682
#, python-format
msgid "Vendor Payment"
msgstr "공급업체 지불"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_supplier_payment_term_id
#: model:ir.model.fields,field_description:account.field_res_users_property_supplier_payment_term_id
msgid "Vendor Payment Terms"
msgstr "공급업체 지불 조건"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_reference
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Vendor Reference"
msgstr "공급업체 참조"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_product_supplier_taxes_id
#: model:ir.model.fields,field_description:account.field_product_template_supplier_taxes_id
msgid "Vendor Taxes"
msgstr "공급업체 세금"

#. module: account
#: model:ir.ui.menu,name:account.menu_account_supplier
msgid "Vendors"
msgstr "공급업체"

#. module: account
#: selection:account.financial.report,type:0
msgid "View"
msgstr "보기"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "Warning"
msgstr "경고"

#. module: account
#: code:addons/account/models/account_invoice.py:662
#, python-format
msgid "Warning for %s"
msgstr "%s에 대한 경고"

#. module: account
#: model:ir.ui.view,arch_db:account.partner_view_buttons
msgid "Warning on the Invoice"
msgstr "청구서에 대한 경고"

#. module: account
#: code:addons/account/models/account_invoice.py:1554
#: code:addons/account/models/account_invoice.py:1611
#, python-format
msgid "Warning!"
msgstr "경고!"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_group_warning_account
msgid "Warnings"
msgstr "경고"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"We can handle the whole import process\n"
"                                        for you: simply send your Odoo project\n"
"                                        manager a CSV file containing all your\n"
"                                        data."
msgstr ""
"Odoo는 사용자를 대신해서 모든 가져오기 과정을\n"
"                                        처리해 드립니다.\n"
"                                        모든 데이터가 포함된 Odoo 프로젝트\n"
"                                        CSV 파일만 전송하면 됩니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"We can handle the whole import process\n"
"                                        for you: simply send your Odoo project\n"
"                                        manager a CSV file containing all your\n"
"                                        products."
msgstr ""
"Odoo는 사용자를 대신해서 모든 가져오기 과정을\n"
"                                        처리해 드립니다.\n"
"                                        모든 제품이 포함된 Odoo 프로젝트\n"
"                                        CSV 파일만 전송하면 됩니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "We hope this tool helped you implement our accounting application."
msgstr "이 도구가 회계 관리 애플리케이션을 구현하는 데 도움이 되기를 바랍니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Welcome"
msgstr "환영합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"When inviting users, you will need to define which access rights they are allowed to have. \n"
"                        This is done by assigning a role to each user."
msgstr ""
"사용자를 초청할 때는 사용자의 액세스 권한을 지정해야 합니다. \n"
"                        이 작업은 각 사용자에게 역할을 할당하여 수행합니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_show_on_dashboard
msgid "Whether this journal should be displayed on the dashboard or not"
msgstr "이 분개장을 대시보드에 표시할지 여부"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_amount_currency
#: model:ir.model.fields,field_description:account.field_account_print_journal_amount_currency
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_amount_currency
msgid "With Currency"
msgstr "통화 포함"

#. module: account
#: selection:account.balance.report,display_account:0
#: selection:account.common.account.report,display_account:0
#: selection:account.report.general.ledger,display_account:0
msgid "With balance is not equal to 0"
msgstr "잔액이 0이 아님"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "With balance not equal to zero"
msgstr "잔액이 0이 아님"

#. module: account
#: selection:account.balance.report,display_account:0
#: selection:account.common.account.report,display_account:0
#: selection:account.report.general.ledger,display_account:0
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "With movements"
msgstr "이동 포함"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_search
msgid "With tax"
msgstr "세금 포함"

#. module: account
#: model:ir.model,name:account.model_tax_adjustments_wizard
msgid "Wizard for Tax Adjustments"
msgstr "세금 조정 마법사"

#. module: account
#: code:addons/account/models/account_move.py:1056
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
#, python-format
msgid "Write-Off"
msgstr "상각"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_journal_id
msgid "Write-Off Journal"
msgstr "상각 분개"

#. module: account
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
msgid "Write-Off Move"
msgstr "상각 이동"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_writeoff_acc_id
msgid "Write-Off account"
msgstr "상각 계정"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff
msgid "Write-Off amount"
msgstr "상각 금액"

#. module: account
#: sql_constraint:account.move.line:0
msgid "Wrong credit or debit value in accounting entry !"
msgstr "회계 항목의 차변/대변 값이 잘못되었습니다!"

#. module: account
#: code:addons/account/models/account_move.py:1015
#, python-format
msgid "You are trying to reconcile some entries that are already reconciled!"
msgstr "이미 조정된 항목을 조정하려고 합니다!"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_blocked
msgid ""
"You can check this box to mark this journal item as a litigation with the "
"associated partner"
msgstr "이 상자에 표시하면 이 분개 항목을 관련 협력사와의 소송으로 표시할 수 있습니다."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree2
msgid ""
"You can control the invoice from your vendor according to\n"
"                what you purchased or received."
msgstr ""
"매입/수령한 내용에 따라 공급업체의\n"
"                청구서를 관리할 수 있습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
msgid "You can define additional accounts here"
msgstr "여기서 추가 계정을 정의할 수 있습니다."

#. module: account
#: code:addons/account/models/account_payment.py:468
#, python-format
msgid "You can not delete a payment that is already posted"
msgstr "이미 전기된 지불금을 삭제할 수 없습니다."

#. module: account
#: code:addons/account/models/account_invoice.py:1633
#, python-format
msgid "You can only delete an invoice line if the invoice is in draft state."
msgstr "청구서가 초안 상태일 때만 청구서 라인을 삭제할 수 있습니다."

#. module: account
#: code:addons/account/models/account_payment.py:141
#, python-format
msgid "You can only register payments for open invoices"
msgstr "개시된 청구서에만 지불금을 등록할 수 있습니다."

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_report_style_overwrite
msgid ""
"You can set up here the format you want this record to be displayed. If you "
"leave the automatic formatting, it will be computed based on the financial "
"reports hierarchy (auto-computed field 'level')."
msgstr ""
"여기서 이 기록을 표시하고자 하는 형식을 설정할 수 있습니다. 자동 서식 지정을 나가면 재무보고서 계층을 기준으로 계산됩니다(자동 계산된"
" 필드 ‘수준’)."

#. module: account
#: code:addons/account/models/account_move.py:209
#, python-format
msgid ""
"You cannot add/modify entries prior to and inclusive of the lock date %s"
msgstr "마감일 %s까지는 항목을 추가/수정할 수 없습니다."

#. module: account
#: code:addons/account/models/account_move.py:211
#, python-format
msgid ""
"You cannot add/modify entries prior to and inclusive of the lock date %s. "
"Check the company settings or ask someone with the 'Adviser' role"
msgstr "마감일 %s까지 항목을 추가/수정할 수 없습니다. 회사 설정을 확인하거나 ‘자문’ 역할이 부여된 사용자에게 요청하십시오."

#. module: account
#: code:addons/account/models/account_invoice.py:1196
#, python-format
msgid ""
"You cannot cancel an invoice which is partially paid. You need to "
"unreconcile related payment entries first."
msgstr "부분 지불된 청구서 대금을 취소할 수 없습니다. 먼저 관련 지불 항목을 조정해야 합니다."

#. module: account
#: code:addons/account/models/company.py:200
#, python-format
msgid ""
"You cannot change the currency of the company since some journal items "
"already exist"
msgstr "일부 분개 항목이 이미 있으므로 회사의 통화를 변경할 수 없습니다."

#. module: account
#: code:addons/account/models/account.py:235
#, python-format
msgid ""
"You cannot change the owner company of an account that already contains "
"journal items."
msgstr "분개 항목이 이미 포함된 계정의 소유주 회사를 변경할 수 없습니다."

#. module: account
#: code:addons/account/models/account.py:242
#, python-format
msgid ""
"You cannot change the value of the reconciliation on this account as it "
"already has some moves"
msgstr "이 계정에서 조정 가치는 이미 일부 이동이 있었기 때문에 변경할 수 없습니다."

#. module: account
#: code:addons/account/models/account_move.py:500
#, python-format
msgid ""
"You cannot create journal items with a secondary currency without filling "
"both 'currency' and 'amount currency' field."
msgstr "'통화'와 '화폐 액수' 필드를 채우지 않으면 보조 통화를 사용하는 분개장을 생성할 수 없습니다."

#. module: account
#: code:addons/account/models/company.py:120
#, python-format
msgid ""
"You cannot define stricter conditions on advisors than on users. Please make"
" sure that the lock date on advisor is set before the lock date for users."
msgstr ""
"사용자보다 엄격한 자문 조건을 정의할 수 없습니다. 사용자의 잠금 날짜 이전에 조언자의 잠금 날짜가 설정되어 있는지 확인합니다."

#. module: account
#: code:addons/account/models/account_invoice.py:613
#, python-format
msgid ""
"You cannot delete an invoice after it has been validated (and received a "
"number). You can set it back to \"Draft\" state and modify its content, then"
" re-confirm it."
msgstr "검증된(번호를 받은) 이후에는 청구서를 삭제할 수 없습니다. \"초안\" 상태로 되돌린 다음 내용을 수정하고, 다시 확정하십시오."

#. module: account
#: code:addons/account/models/account_invoice.py:611
#, python-format
msgid ""
"You cannot delete an invoice which is not draft or cancelled. You should "
"create a credit note instead."
msgstr "초안 또는 취소되지 않은 청구서를 삭제할 수 없습니다. 대신 신용 전표를 생성해야 합니다."

#. module: account
#: code:addons/account/models/res_config_settings.py:133
#, python-format
msgid ""
"You cannot disable this setting because some of your taxes are cash basis. "
"Modify your taxes first before disabling this setting."
msgstr "일부 세금이 현금주의 원칙을 적용하므로 이 설정을 비활성화할 수 없습니다. 이 설정을 비활성화하기 전에 세금을 수정하십시오."

#. module: account
#: code:addons/account/models/account.py:248
#, python-format
msgid "You cannot do that on an account that contains journal items."
msgstr "분개 항목이 포함된 계정에서는 실행이 불가능합니다."

#. module: account
#: code:addons/account/models/account_move.py:1364
#, python-format
msgid ""
"You cannot do this modification on a posted journal entry, you can just change some non legal fields. You must revert the journal entry to cancel it.\n"
"%s."
msgstr ""
"전기된 분개 항목은 이렇게 수정할 수 없습니다. 법률 관련이 아닌 일부 필드만 변경할 수 있습니다. 취소하려면 분개 항목을 되돌려야 합니다.\n"
"%s."

#. module: account
#: code:addons/account/models/account_move.py:1366
#, python-format
msgid ""
"You cannot do this modification on a reconciled entry. You can just change some non legal fields or you must unreconcile first.\n"
"%s."
msgstr ""
"조정된 항목은 이렇게 수정할 수 없습니다. 법률 관련이 아닌 일부 필드만 변경하거나 조정을 해제해야 합니다.\n"
"%s."

#. module: account
#: code:addons/account/models/account.py:518
#, python-format
msgid "You cannot empty the bank account once set."
msgstr "은행 계좌는 한 번 설정하면 비울 수 없습니다."

#. module: account
#: code:addons/account/models/account.py:55
#, python-format
msgid ""
"You cannot have a receivable/payable account that is not reconcilable. "
"(account code: %s)"
msgstr "조정되지 않은 수취/지불 계정은 가질 수 없습니다(계정 코드: %s)."

#. module: account
#: code:addons/account/models/company.py:109
#, python-format
msgid ""
"You cannot lock a period that is not finished yet. Please make sure that the"
" lock date for advisors is not set after the last day of the previous month."
msgstr ""
"아직 완료되지 않은 기간은 잠글 수 없습니다. 조언자의 잠금 날짜가 전월의 마지막 날 이후로 설정되지 않았는지 확인해 주세요."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:701
#, python-format
msgid "You cannot mix items from receivable and payable accounts."
msgstr "수취 계정과 지불 계정 항목을 섞을 수 없습니다."

#. module: account
#: code:addons/account/models/account_move.py:172
#, python-format
msgid ""
"You cannot modify a posted entry of this journal.\n"
"First you should set the journal to allow cancelling entries."
msgstr ""
"이 분개의 전기된 항목을 수정할 수 없습니다.\n"
"먼저 분개를 설정하여 항목을 취소할 수 있게 해야 합니다."

#. module: account
#: code:addons/account/models/account_invoice.py:789
#, python-format
msgid ""
"You cannot pay an invoice which is partially paid. You need to reconcile "
"payment entries first."
msgstr "부분 지불된 청구서 대금을 지불할 수 없습니다. 먼저 지불 항목을 조정해야 합니다."

#. module: account
#: code:addons/account/wizard/pos_box.py:36
#, python-format
msgid "You cannot put/take money in/out for a bank statement which is closed."
msgstr "마감된 예금거래 명세서에 자금을 투입/인출할 수 없습니다."

#. module: account
#: code:addons/account/models/account.py:261
#, python-format
msgid ""
"You cannot remove/deactivate an account which is set on a customer or "
"vendor."
msgstr "고객 또는 공급업체에 설정된 계정을 제거/비활성화할 수 없습니다."

#. module: account
#: code:addons/account/models/account.py:249
#, python-format
msgid ""
"You cannot set a currency on this account as it already has some journal "
"entries having a different foreign currency."
msgstr "일부 분개 항목이 이미 다른 통화를 가지고 있기 때문에 이 계정에 통화를 설정할 수 없습니다."

#. module: account
#: code:addons/account/models/account_move.py:1368
#, python-format
msgid "You cannot use deprecated account."
msgstr "감가상각된 계정은 사용할 수 없습니다."

#. module: account
#: code:addons/account/models/account_move.py:1283
#, python-format
msgid ""
"You cannot use this general account in this journal, check the tab 'Entry "
"Controls' on the related journal."
msgstr "이 분개에서는 일반 계정을 사용할 수 없습니다. 관련 분개의 ‘항목 관리’ 탭을 확인하십시오."

#. module: account
#: code:addons/account/models/account_invoice.py:74
#: code:addons/account/models/account_invoice.py:780
#, python-format
msgid ""
"You cannot validate an invoice with a negative total amount. You should "
"create a credit note instead."
msgstr "합계가 음수인 청구서를 검증할 수 없습니다. 대신 신용 전표를 생성해야 합니다."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:158
#, python-format
msgid "You did not configure any reconcile model yet, you can do it"
msgstr "아직 조정을 구성하지 않았고, 실행할 수 있습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "You have"
msgstr "가 있습니다."

#. module: account
#: code:addons/account/models/account_payment.py:505
#, python-format
msgid "You have to define a sequence for %s in your company."
msgstr "회사에서 %s에 대한 순서를 정의해야 합니다."

#. module: account
#: code:addons/account/wizard/account_report_general_ledger.py:21
#, python-format
msgid "You must define a Start Date"
msgstr "시작일을 지정해야 합니다."

#. module: account
#: code:addons/account/models/account_invoice.py:1555
#, python-format
msgid "You must first select a partner!"
msgstr "먼저 협력사를 선택해야 합니다!"

#. module: account
#: code:addons/account/wizard/account_report_aged_partner_balance.py:26
#, python-format
msgid "You must set a period length greater than 0."
msgstr "기간을 0보다 크게 설정해야 합니다."

#. module: account
#: code:addons/account/wizard/account_report_aged_partner_balance.py:28
#, python-format
msgid "You must set a start date."
msgstr "시작일을 설정해야 합니다."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "You reconciled"
msgstr "조정했습니다."

#. module: account
#: code:addons/account/models/account_move.py:1884
#, python-format
msgid ""
"You should configure the 'Exchange Rate Journal' in the accounting settings,"
" to manage automatically the booking of accounting entries related to "
"differences between exchange rates."
msgstr "회계 설정에서 ‘환율 분개’를 구성해야 환율차이와 관련된 회계 항목 기장을 자동으로 관리할 수 있습니다."

#. module: account
#: code:addons/account/models/account_move.py:1886
#, python-format
msgid ""
"You should configure the 'Gain Exchange Rate Account' in the accounting "
"settings, to manage automatically the booking of accounting entries related "
"to differences between exchange rates."
msgstr "회계 설정에서 ‘이익 환율 계정’을 구성해야 환율차이와 관련된 회계 항목 기장을 자동으로 관리할 수 있습니다."

#. module: account
#: code:addons/account/models/account_move.py:1888
#, python-format
msgid ""
"You should configure the 'Loss Exchange Rate Account' in the accounting "
"settings, to manage automatically the booking of accounting entries related "
"to differences between exchange rates."
msgstr "회계 설정에서 ‘손실 환율 계정’을 구성해야 환율차이와 관련된 회계 항목 기장을 자동으로 관리할 수 있습니다."

#. module: account
#: code:addons/account/wizard/pos_box.py:49
#: code:addons/account/wizard/pos_box.py:67
#, python-format
msgid ""
"You should have defined an 'Internal Transfer Account' in your cash "
"register's journal!"
msgstr "금전 등록기 분개에 '내부 이전 계정'을 정의해야 합니다!"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"You will be able to edit and validate this\n"
"                                credit note directly or keep it draft,\n"
"                                waiting for the document to be issued by\n"
"                                your supplier/customer."
msgstr ""
"이 신용 전표를 직접 편집, 검증하거나\n"
"                                초안으로 보관했다가\n"
"                                공급업체/고객이 문서를 발행하기를\n"
"                                기다려야 합니다."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Bank Accounts"
msgstr "내 은행 계좌"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Company"
msgstr "내 회사"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Customers"
msgstr "내 고객"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Products"
msgstr "내 제품"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Trial Balance (list of accounts and their balances)."
msgstr "내 시산표(계정 및 잔액 목록)."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your company's legal name, tax ID, address, and logo."
msgstr "회사의 상호, 세금 ID, 주소 및 로고."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your outstanding invoices, payments, and undeposited funds."
msgstr "미지급 청구서, 지급금 및 예치되지 않은 자금."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Zip Range"
msgstr "우편번호 범위"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_zip_from
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_zip_from
msgid "Zip Range From"
msgstr "우편번호 범위 시작"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_zip_to
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_zip_to
msgid "Zip Range To"
msgstr "우편번호 범위 종료"

#. module: account
#: model:ir.model,name:account.model_account_bank_accounts_wizard
msgid "account.bank.accounts.wizard"
msgstr "account.bank.accounts.wizard"

#. module: account
#: model:ir.model,name:account.model_account_financial_year_op
msgid "account.financial.year.op"
msgstr "account.financial.year.op"

#. module: account
#: model:ir.model,name:account.model_account_group
msgid "account.group"
msgstr "account.group"

#. module: account
#: model:ir.model,name:account.model_account_opening
msgid "account.opening"
msgstr "account.opening"

#. module: account
#: model:ir.model,name:account.model_account_reconcile_model_template
msgid "account.reconcile.model.template"
msgstr "account.reconcile.model.template"

#. module: account
#: model:ir.model,name:account.model_account_tax_group
msgid "account.tax.group"
msgstr "account.tax.group"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "activate this feature"
msgstr "이 기능 활성화"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:17
#, python-format
msgid "assign to invoice"
msgstr "청구서에 할당"

#. module: account
#: model:ir.model,name:account.model_cash_box_in
msgid "cash.box.in"
msgstr "cash.box.in"

#. module: account
#: model:ir.model,name:account.model_cash_box_out
msgid "cash.box.out"
msgstr "cash.box.out"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_error
#: model:ir.ui.view,arch_db:account.portal_invoice_success
msgid "close"
msgstr "마감"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "code"
msgstr "코드"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "create a journal entry"
msgstr "분개 생성"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "days"
msgstr "일"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "e.g. Bank Fees"
msgstr "예: 은행 수수료"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "fast recording interface"
msgstr "빠른 기록 인터페이스"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "first)"
msgstr "먼저)"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "for this customer. You can allocate them to mark this invoice as paid."
msgstr "이들을 할당하여 이 청구서를 지급된 것으로 표시할 수 있습니다."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "for this supplier. You can allocate them to mark this bill as paid."
msgstr "이들을 할당하여 이 청구서를 지급된 것으로 표시할 수 있습니다."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:41
#, python-format
msgid "o_manual_statement"
msgstr "o_manual_statement"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "outstanding debits"
msgstr "미불 차변"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "outstanding payments"
msgstr "미지불 대금"

#. module: account
#: model:ir.model,name:account.model_report_account_report_agedpartnerbalance
msgid "report.account.report_agedpartnerbalance"
msgstr "report.account.report_agedpartnerbalance"

#. module: account
#: model:ir.model,name:account.model_report_account_report_financial
msgid "report.account.report_financial"
msgstr "report.account.report_financial"

#. module: account
#: model:ir.model,name:account.model_report_account_report_generalledger
msgid "report.account.report_generalledger"
msgstr "report.account.report_generalledger"

#. module: account
#: model:ir.model,name:account.model_report_account_report_journal
msgid "report.account.report_journal"
msgstr "report.account.report_journal"

#. module: account
#: model:ir.model,name:account.model_report_account_report_overdue
msgid "report.account.report_overdue"
msgstr "report.account.report_overdue"

#. module: account
#: model:ir.model,name:account.model_report_account_report_partnerledger
msgid "report.account.report_partnerledger"
msgstr "report.account.report_partnerledger"

#. module: account
#: model:ir.model,name:account.model_report_account_report_tax
msgid "report.account.report_tax"
msgstr "report.account.report_tax"

#. module: account
#: model:ir.model,name:account.model_report_account_report_trialbalance
msgid "report.account.report_trialbalance"
msgstr "report.account.report_trialbalance"

#. module: account
#: model:ir.model,name:account.model_res_config_settings
msgid "res.config.settings"
msgstr "res.config.settings"

#. module: account
#: code:addons/account/models/account_move.py:238
#, python-format
msgid "reversal of: "
msgstr "역:"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "seconds per transaction."
msgstr "거래당 시간(초)."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "send us an email"
msgstr "이메일 전송"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "set company logo"
msgstr "회사 로고 설정"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "setup your bank accounts."
msgstr "회사 은행 계좌 설정"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "the customer list"
msgstr "고객 목록"

#. module: account
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "the parent company"
msgstr "모기업"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "the product list"
msgstr "제품 목록"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:158
#, python-format
msgid "there"
msgstr "여기"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "to describe<br/> your experience or to suggest improvements !"
msgstr "<br/>경험을 설명하거나 개선 사항을 제안하십시오!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "to set the balance of all of your accounts."
msgstr "모든 계정 잔액을 설정합니다."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "transactions in"
msgstr "다음에서의 거래"

#. module: account
#: model:ir.model,name:account.model_wizard_multi_charts_accounts
msgid "wizard.multi.charts.accounts"
msgstr "wizard.multi.charts.accounts"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "→ Count"
msgstr "→ 계수"
