# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <inactive+h_man<PERSON><PERSON>_<PERSON><PERSON><PERSON>@transifex.com>, 2018
# <PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <PERSON>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-03-29 09:07+0000\n"
"PO-Revision-Date: 2017-09-20 10:13+0000\n"
"Last-Translator: cafonso <<EMAIL>>, 2019\n"
"Language-Team: Portuguese (https://www.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_state
msgid ""
" * The 'Draft' status is used when a user is encoding a new and unconfirmed Invoice.\n"
" * The 'Open' status is used when user creates invoice, an invoice number is generated. It stays in the open status till the user pays the invoice.\n"
" * The 'Paid' status is set automatically when the invoice is paid. Its related journal entries may or may not be reconciled.\n"
" * The 'Cancelled' status is used when user cancel invoice."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_code_digits
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_code_digits
msgid "# of Digits"
msgstr "# de Dígitos"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_code_digits
msgid "# of Digits *"
msgstr "# de Dígitos *"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_nbr
msgid "# of Lines"
msgstr "# de Linhas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_trans_nbr
msgid "# of Transaction"
msgstr "# de Transação"

#. module: account
#: model:mail.template,report_name:account.mail_template_data_payment_receipt
msgid "${(object.name or '').replace('/','-')}"
msgstr "${(object.name or '').replace('/','-')}"

#. module: account
#: model:mail.template,subject:account.email_template_edi_invoice
msgid "${object.company_id.name} Invoice (Ref ${object.number or 'n/a'})"
msgstr "${object.company_id.name} Fatura (Ref ${object.number or 'n/a'})"

#. module: account
#: model:mail.template,subject:account.mail_template_data_payment_receipt
msgid ""
"${object.company_id.name} Payment Receipt (Ref ${object.name or 'n/a' })"
msgstr ""
"${object.company_id.name} Recibo de Pagamento (Ref ${object.name or 'n/a' })"

#. module: account
#: model:mail.template,subject:account.mail_template_data_notification_email_account_invoice
msgid "${object.subject}"
msgstr "${object.subject}"

#. module: account
#: code:addons/account/models/account_bank_statement.py:462
#, python-format
msgid "%d transactions were automatically reconciled."
msgstr "%d transações foram reconciliadas automaticamente."

#. module: account
#: code:addons/account/models/account.py:809
#, python-format
msgid "%s (Copy)"
msgstr "%s (Cópia)"

#. module: account
#: code:addons/account/models/account.py:211
#: code:addons/account/models/account.py:484
#: code:addons/account/models/account.py:485
#, python-format
msgid "%s (copy)"
msgstr "%s (cópia)"

#. module: account
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "&amp;nbsp;<span>on</span>"
msgstr "&amp;nbsp;<span>no</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ", if accounting or purchase is installed"
msgstr ", se contabilidade ou compras estiverem instaladas"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "- First Number:"
msgstr "- Primeiro número:"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "-> Reconcile"
msgstr "-> Reconciliar"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "-> View partially reconciled entries"
msgstr "-> Ver registos parcialmente reconciliados"

#. module: account
#: code:addons/account/models/account_bank_statement.py:463
#, python-format
msgid "1 transaction was automatically reconciled."
msgstr "1 transação foi reconciliada automaticamente."

#. module: account
#: model:account.payment.term,name:account.account_payment_term_15days
msgid "15 Days"
msgstr "15 dias"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_net
msgid "30 Net Days"
msgstr "30 Dias"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_advance
msgid "30% Advance End of Following Month"
msgstr "30% adiantamento no final do mês seguintes"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "5) For setup, you will need the following information:"
msgstr "5) Para configurar, irá precisar da seguinte informação:"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid ": General ledger"
msgstr ": Registo geral"

#. module: account
#: code:addons/account/models/account.py:554
#, python-format
msgid ": Refund"
msgstr "Reembolso"

#. module: account
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid ": Trial Balance"
msgstr "Balancete Analítico"

#. module: account
#: model:mail.template,body_html:account.mail_template_data_payment_receipt
msgid ""
"<?xml version=\"1.0\"?>\n"
"<data><p>Dear ${object.partner_id.name},</p>\n"
"<p>Thank you for your payment.<br/>Here is your payment receipt <strong>${(object.name or '').replace('/','-')}</strong> amounting to <strong>${format_amount(object.amount, object.currency_id)}</strong> from ${object.company_id.name}.</p>\n"
"<p>If you have any questions, please do not hesitate to contact us.</p>\n"
"<p>Best regards,\n"
"% if user and user.signature:\n"
"${user.signature | safe}\n"
"% endif\n"
"</p>\n"
"</data>"
msgstr ""
"<?xml version=\"1.0\"?>\n"
"<data><p>Exmo. ${object.partner_id.name}</p>\n"
"<p>Obrigado pelo seu pagamento.<br/>Aqui está o seu recibo de pagamento <strong>${(object.name or '').replace('/','-')}</strong> no total de <strong>${format_amount(object.amount, object.currency_id)}</strong> de ${object.company_id.name}.</p>\n"
"<p>Se tiver alguma questão, por favor não hesite em contactar-nos.</p>\n"
"<p> Os nossos melhores cumprimentos,\n"
"% if user and user.signature:\n"
"${user.signature | safe}\n"
"% endif\n"
"</p>\n"
"</data>"

#. module: account
#: model:mail.template,body_html:account.mail_template_data_notification_email_account_invoice
msgid ""
"<div>\n"
"% set record = ctx.get('record')\n"
"% set company = record and record.company_id or ctx.get('company')\n"
"<table border=\"0\" width=\"100%\" cellpadding=\"0\" bgcolor=\"#ededed\" style=\"padding: 20px; background-color: #ededed; border-collapse:separate;\" summary=\"o_mail_notification\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\">\n"
"                        <span style=\"font-size:20px; color:white; font-weight: bold;\">\n"
"                            Invoice ${object.record_name}\n"
"                        </span>\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\">\n"
"                        <img src=\"/logo.png?company=${company.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${company.name}\"/>\n"
"                    </td>\n"
"                </tr>\n"
"          </table>\n"
"    </td>\n"
"  </tr>\n"
"  <!-- CONTENT -->\n"
"  <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"min-width: 590px; background-color: rgb(255, 255, 255); padding: 20px; border-collapse:separate;\">\n"
"                <tbody>\n"
"                    <td valign=\"top\" style=\"font-family:Arial,Helvetica,sans-serif; color: #555; font-size: 14px;\">\n"
"                        ${object.body | safe}\n"
"                    </td>\n"
"                </tbody>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\" align=\"left\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                        ${company.name}<br/>\n"
"                        ${company.phone or ''}\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                        % if company.email:\n"
"                        <a href=\"mailto:${company.email}\" style=\"text-decoration:none; color: white;\">${company.email}</a><br/>\n"
"                        % endif\n"
"                        % if company.website:\n"
"                        <a href=\"${company.website}\" style=\"text-decoration:none; color: white;\">\n"
"                        ${company.website}\n"
"                        </a>\n"
"                        % endif\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <tr>\n"
"        <td align=\"center\">\n"
"            Powered by <a target=\"_blank\" href=\"https://www.odoo.com\">Odoo</a>.\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</div>"
msgstr ""

#. module: account
#: model:mail.template,body_html:account.email_template_edi_invoice
msgid ""
"<div>\n"
"<p>Dear ${object.partner_id.name}\n"
"% set access_action = object.with_context(force_website=True).get_access_action()\n"
"% set is_online = access_action and access_action['type'] == 'ir.actions.act_url'\n"
"% set access_url = object.get_mail_url()\n"
"\n"
"% if object.partner_id.parent_id:\n"
"    (<i>${object.partner_id.parent_id.name}</i>)\n"
"% endif\n"
",</p>\n"
"<p>Here is, in attachment, your \n"
"% if object.number:\n"
"invoice <strong>${object.number}</strong>\n"
"% else:\n"
"invoice\n"
"% endif\n"
"% if object.origin:\n"
"(with reference: ${object.origin})\n"
"% endif\n"
"amounting in <strong>${format_amount(object.amount_total, object.currency_id)}</strong>\n"
"from ${object.company_id.name}.\n"
"</p>\n"
"\n"
"% if is_online:\n"
"    <br/><br/>\n"
"    <center>\n"
"      <a href=\"${access_url}\" style=\"background-color: #1abc9c; padding: 20px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 16px;\" class=\"o_default_snippet_text\">View Invoice</a>\n"
"    </center>\n"
"% endif\n"
"    <br/><br/>\n"
"\n"
"% if object.state=='paid':\n"
"    <p>This invoice is already paid.</p>\n"
"% else:\n"
"    <p>Please remit payment at your earliest convenience.</p>\n"
"% endif\n"
"\n"
"<p>Thank you,</p>\n"
"<p style=\"color:#888888\">\n"
"% if object.user_id and object.user_id.signature:\n"
"    ${object.user_id.signature | safe}\n"
"% endif\n"
"</p>\n"
"</div>"
msgstr ""
"<div>\n"
"<p>Car@ ${object.partner_id.name}\n"
"% set access_action = object.with_context(force_website=True).get_access_action()\n"
"% set is_online = access_action and access_action['type'] == 'ir.actions.act_url'\n"
"% set access_url = object.get_mail_url()\n"
"\n"
"% if object.partner_id.parent_id:\n"
"    (<i>${object.partner_id.parent_id.name}</i>)\n"
"% endif\n"
",</p>\n"
"<p>Segue, em anexo, a sua\n"
"% if object.number:\n"
"fatura <strong>${object.number}</strong>\n"
"% else:\n"
"fatura\n"
"% endif\n"
"% if object.origin:\n"
"(with reference: ${object.origin})\n"
"% endif\n"
"no total de <strong>${format_amount(object.amount_total, object.currency_id)}</strong>\n"
"de ${object.company_id.name}.\n"
"</p>\n"
"\n"
"% if is_online:\n"
"    <br/><br/>\n"
"    <center>\n"
"    <a href=\"${access_url}\" style=\"background-color: #1abc9c; padding: 20px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 16px;\" class=\"o_default_snippet_text\">View Invoice</a>\n"
"    </center>\n"
"% endif\n"
"    <br/><br/>\n"
"\n"
"% if object.state=='paid':\n"
"    <p>Esta fatura já foi paga.</p>\n"
"% else:\n"
"    <p>Por favor, emita pagamento assim que possível.\n"
"% endif</p>\n"
"\n"
"<p>Obrigado,</p>\n"
"<p style=\"color:#888888\">\n"
"% if object.user_id and object.user_id.signature:\n"
"${object.user_id.signature | safe}\n"
"% endif\n"
"</p>\n"
"</div>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<span class=\"fa fa-arrow-circle-o-down\"/> Install Now"
msgstr "<span class=\"fa fa-arrow-circle-o-down\"/> Instalar Agora"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<span class=\"fa\"/> Invite Your Users"
msgstr "<span class=\"fa\"/> Convide os Seus Utilizadores"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-check\"/><span "
"class=\"hidden-xs\"> Paid</span></span>"
msgstr ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-check\"/><span "
"class=\"hidden-xs\">Pago</span></span>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-remove\"/><span "
"class=\"hidden-xs\"> Cancelled</span></span>"
msgstr ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-remove\"/> <span "
"class=\"hidden-xs\">Cancelado</span></span>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"label label-info\"><i class=\"fa fa-fw fa-clock-o\"/><span "
"class=\"hidden-xs\"> Waiting for Payment</span></span>"
msgstr ""
"<span class=\"label label-info\"><i class=\"fa fa-fw fa-clock-o\"/><span "
"class=\"hidden-xs\">Aguardando Pagamento</span></span>"

#. module: account
#: model:ir.ui.view,arch_db:account.partner_view_buttons
msgid "<span class=\"o_stat_text\">Invoiced</span>"
msgstr "<span class=\"o_stat_text\">Faturado</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>Import a file</strong><br/>\n"
"                                        <span class=\"small\">Recommended if &gt;100 products</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"&#57408;\"/>\n"
"                                        <strong>Importe um ficheiro</strong><br/>\n"
"                                        <span class=\"small\">Recomendado para &gt;100 produtos</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>Import</strong><br/>\n"
"                                        <span class=\"small\">&gt; 200 contacts</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"&#57408;\"/>\n"
"                                        <strong>Importar para</strong><br/>\n"
"                                        <span class=\"small\">&gt; 200 contactos</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> Create manually</strong><br/>\n"
"                                        <span class=\"small\">&lt; 200 contacts</span>\n"
"                                    </span>"
msgstr ""

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> Create manually</strong><br/>\n"
"                                        <span class=\"small\">Recommended if &lt;100 products</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> Criar manualmente</strong><br/>\n"
"                                        <span class=\"small\">Recomendado se &lt;100 produtos</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Accountant</strong> (Advanced access)\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Contabilista</strong> (Acesso avançado)\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Advisor</strong> (Full access)\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Consultor</strong> (Accesso Completo)\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Billing</strong> (Limited access)\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Faturação</strong> (Acesso limitado)\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Customer follow-up</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Controlo de Cliente</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Invoice payment by Check</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Pagamento de Fatura por Cheque</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Invoice payment by Wire Transfer</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Pagamento de Fatura por Transferência Bancária</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Pay your bills</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Pague as suas faturas</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Reconcile Bank Statements</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Reconciliar Extratos Bancários</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Record your Bills</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>Registe as suas faturas</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span title=\"Balance in Odoo\">Balance in GL</span>"
msgstr "Saldo no Razão Geral"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span title=\"Latest Statement\">Latest Statement</span>"
msgstr "<span title=\"Latest Statement\">Extrato Atualizado</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "<span> From </span>"
msgstr "<span> De </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "<span> To </span>"
msgstr "<span> Para </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Journal Entries</span>"
msgstr "<span>Registos Contabilísticos em Diário</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Bill</span>"
msgstr "<span>Nova Fatura</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Invoice</span>"
msgstr "<span>Nova Fatura</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New</span>"
msgstr "<span>Novo</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<span>Not due</span>"
msgstr "<span>Não vencida</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Operations</span>"
msgstr "<span>Operações</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Reconciliation</span>"
msgstr "<span>Reconciliação</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Reporting</span>"
msgstr "<span>Relatórios</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>View</span>"
msgstr "<span>Ver</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>-The Odoo Team</strong>"
msgstr "<strong>- A Equipa Odoo</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>1. Register Outstanding Invoices</strong>"
msgstr "<strong>1. Registar Faturas por Pagar</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>2. Register Unmatched Payments</strong>"
msgstr "<strong>2. Registar Pagamentos Não Correspondidos</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_invoice_document_with_payments
msgid "<strong>Amount Due</strong>"
msgstr "<strong>Valor em Dívida</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Assets Management</strong>"
msgstr "<strong>Gestão de Ativos</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Automated documents sending:</strong> automatically send your "
"invoices by email or snail mail."
msgstr ""
"<strong>Envio automático de documentos:</strong> envie as suas faturas "
"automaticamente, por e-mail ou por correio normal"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "<strong>Balance :</strong>"
msgstr "<strong>Saldo:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Banking interface:</strong> with live bank feed synchronization and "
"bank statement import."
msgstr ""
"<strong>Ligação automática a Bancos: com sincronização de registos bancários"
" em tempo real e importação de extratos bancários.</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Cash transactions</strong><br/> (for which there is no invoice or "
"bill), should be entered directly into your Cash Registers bank account."
msgstr ""
"<strong>Transacções em dinheiro</strong><br/> (para todas as transacções sem"
" fatura ou documento de suporte), deverão dar entrada diretamente na conta "
"bancária associada aos movimentos em conta Caixa."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Check the Taxes configuration:</strong>"
msgstr "<strong>Verifique a configuração de 'Impostos':</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Check unpaid invoices</strong><br/>\n"
"                                         Run an <i>Aged Receivable report</i> and check which customers still owe you money."
msgstr ""
"<strong>Verifique as faturas por receber</strong><br/>\n"
"Execute relatório de <i>Dívidas a Receber por Antiguidade</i> e verifique que clientes ainda têm valores por pagar."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Clean customer invoices:</strong> easy to create, beautiful and full"
" featured invoices."
msgstr ""
"<strong>Faturas de cliente claras:</strong> fáceis de criar, bonitas e "
"completas."

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
msgid "<strong>Company:</strong>"
msgstr "<strong>Empresa:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Contracts &amp; Subscriptions</strong>"
msgstr "<strong>Contratos e Subscrições</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Create a Customer Invoice</strong>"
msgstr "<strong>Criar uma Fatura de Cliente</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Create a Deposit Ticket</strong><br/>\n"
"                                        This allows you to record the different payments that constitute your bank deposit. (You may need to"
msgstr ""
"<strong>Criar um Documento de Depósito</strong><br/>\n"
"Isto permite-lhe registar os diferentes pagamentos que fazem parte do seu depósito bancário. (Pode precisar de"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Create the bill in Odoo</strong><br/> with a proper due date, and "
"create the vendor if it doesnt' exist yet."
msgstr ""
"<strong>Crie uma despesa no Odoo</strong><br/> com a data de vencimento "
"adequada, e crie o fornecedor, caso não exista."

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
msgid "<strong>Customer Address</strong>"
msgstr "<span><strong>Morada do cliente:</strong></span>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Customer Code:</strong>"
msgstr "<strong>Código de cliente:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Customer: </strong>"
msgstr "<strong>Cliente: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Date from :</strong>"
msgstr "<strong>Data desde:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Date to :</strong>"
msgstr "<strong>Data até:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Deposit Tickets</strong>"
msgstr "<strong>Documentos de Depósito</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Description:</strong>"
msgstr "<strong>Descrição:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Display Account:</strong>"
msgstr "<strong>Exibir conta:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "<strong>Display Account</strong>"
msgstr "<strong>Exibir Conta</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Due Date:</strong>"
msgstr "<strong>Data de vencimento:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Entries Sorted By:</strong>"
msgstr "<strong>Movimentos ordenados por:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Expenses</strong>"
msgstr "<strong>Despesas</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
msgid "<strong>Fiscal Year End</strong>"
msgstr "<strong>Fim do Ano Fiscal</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "<strong>From</strong>"
msgstr "<strong>De</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Invoice Date:</strong>"
msgstr "<strong>Data da fatura:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Journal:</strong>"
msgstr "<strong>Diário:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "<strong>Journals:</strong>"
msgstr "<strong>Diários:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Mark the bills to pay</strong><br/>\n"
"                                        Group or filter your bills to see those due in the next week, then open each bill individually, click on <strong>'Pay'</strong> and select the payment method you prefer."
msgstr ""
"<strong>Marque as faturas a pagar</strong><br/>\n"
"Agrupe ou filtre as faturas para  ver as que se vencem na próxima semana, e depois escolha cada fatura individualmente, clique em <strong>'Pagar'</strong> e seleccione o método de pagamento que preferir."

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Memo: </strong>"
msgstr "<strong>Memorando:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Next action:</strong><br/>"
msgstr "<strong>Ação seguinte:</strong><br/>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>On-the-fly payment reconciliation:</strong> automatic suggestions of"
" outstanding payments when creating invoices."
msgstr ""
"<strong>Reconciliação rápida de pagamentos:</strong> sugestão automática de "
"pagamentos pendentes quando forem criadas faturas."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Or generate payment orders</strong><br/>\n"
"                                        Create a Payment Order and select the bills you'd like to pay as Entry lines (only the validated bills will appear)."
msgstr ""
"<strong>Ou crie ordens de pagamento</strong><br/>Crie uma Ordem de Pagamento"
" e selecione as faturas que desejaria pagar como Linhas de Movimentos (irão "
"aparecer apenas as faturas validadas)."

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<strong>Partner's:</strong>"
msgstr "<strong>Parceiro</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Payment Amount: </strong>"
msgstr "<strong>Valor do pagamento: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Payment Date: </strong>"
msgstr "<strong>Data de pagamento: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Payment Method: </strong>"
msgstr "<strong>Método de pagamento: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<strong>Period Length (days)</strong>"
msgstr "<strong>Período (dias)</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Print checks</strong><br/>\n"
"                                        From the Vendor Payments list, select those you want to pay and click on Print Check (you may first need to activate the check feature in the"
msgstr ""
"<strong>Imprimir cheques</strong><br/>\n"
"A partir da lista de pagamentos de Fornecedores, seleccione aqueles que deseja pagar e clique em Imprimir Cheque (talvez seja necessário ativar primeiro a verificação na"

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "<strong>Purchase</strong>"
msgstr "<strong>Compra</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Purchases</strong>"
msgstr "<strong>Compras</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Reconcile Bank Statement</strong>"
msgstr "<strong>Reconciliar Extrato Bancário</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Reconcile with existing transaction</strong><br/>\n"
"                                        In this case, Odoo should automatically match the bank statement with the previously recorded check transaction."
msgstr ""
"<strong>Reconciliar com transacção existente</strong><br/>\n"
"Neste caso, o Odoo deve automaticamente fazer corresponder o extracto bancário com o registo de transacção do cheque correspondente."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Reconcile your Bank Statements</strong>"
msgstr "<strong>Reconcilie os seus Extratos Bancários</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Record Bank Statement (or import file)</strong><br/>\n"
"                                        Depending on the volume of your transactions, you should be recording your bank statement every week to several times a day."
msgstr ""
"<strong>Gravar Extrato Bancário (ou importar ficheiro)</strong><br/>\n"
"                                        Dependendo do volume de transações, deve gravar o seu extrato bancário de todas as semanas a várias vezes ao dia."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Record Bank Statement</strong>"
msgstr "<strong>Registar Extrato Bancário</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Record a payment by check on the Invoice</strong><br/>\n"
"                                        Simply click on the 'Pay' button."
msgstr ""
"<strong>Registe um pagamento por cheque na Fatura</strong><br/>\n"
"Basta clicar no botão \"Pagar\" ."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Reduced data entry:</strong> no need to manually create invoices, "
"register bank statements, and send payment follow-ups."
msgstr ""
"<strong>Entrada de dados reduzida:</strong> não há necessidade de criar "
"facturas manualmente, registar extractos bancários, e enviar notas de "
"valores em dívida,"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Reference:</strong>"
msgstr "<strong>Referência:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Revenue Recognition</strong>"
msgstr "<strong>Contabilização de Receitas</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Send follow-up letters</strong>"
msgstr "<strong>Enviar cartas de seguimento</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "<strong>Sorted By:</strong>"
msgstr "<strong>Organizado Por:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Source:</strong>"
msgstr "<strong>Fonte:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<strong>Start Date:</strong>"
msgstr "<strong>Data de início:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "<strong>Subtotal :</strong>"
msgstr "<strong>Subtotal :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Subtotal</strong>"
msgstr "<strong>Subtotal</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Target Moves:</strong>"
msgstr "<strong>Movimentos de destino:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Test the following three scenarios in order to assist you in "
"familiarizing yourself with Odoo:</strong>"
msgstr ""
"<strong>Teste os três cenários seguintes para se familizarizar com o "
"Odoo:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>There are three different levels of access rights in Odoo:</strong>"
msgstr "<strong>Existem três níveis de acesso diferentes no Odoo:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "<strong>There is nothing due with this customer.</strong>"
msgstr "<strong>O cliente não tem pagamentos por fazer.</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_account_kanban
msgid "<strong>Type: </strong>"
msgstr "<strong>Tipo: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Validate the bill</strong><br/> after encoding the products and "
"taxes."
msgstr ""
"<strong>Validar a fatura</strong><br/> após codificar os artigos e impostos."

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Vendor: </strong>"
msgstr "<strong>Fornecedor: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "<strong>to </strong>"
msgstr "<strong>para </strong>"

#. module: account
#: model:ir.actions.act_window,help:account.action_view_bank_statement_tree
msgid ""
"A Cash Register allows you to manage cash entries in your cash\n"
"                journals. This feature provides an easy way to follow up cash\n"
"                payments on a daily basis. You can enter the coins that are in\n"
"                your cash box, and then post entries when money comes in or\n"
"                goes out of the cash box."
msgstr ""
"Uma Caixa Registadora permite gerir entradas de numerário nos\n"
"               diários de numerário. Esta funcionalidade proporciona\n"
"               uma maneira fácil de acompanhar pagamentos por numerário\n"
"               diariamente. Pode introduzir as moedas que estão na sua caixa\n"
"                e depois marcar registos quando existe entrada ou saída de numerário."

#. module: account
#: code:addons/account/models/account_bank_statement.py:383
#, python-format
msgid "A Cash transaction can't have a 0 amount."
msgstr "Uma transação de Numerário não pode ser 0."

#. module: account
#: code:addons/account/models/account_invoice.py:1699
#, python-format
msgid "A Payment Terms should have its last line of type Balance."
msgstr "A última linha das Condições de Pagamento deve ser do tipo Saldo."

#. module: account
#: code:addons/account/models/account_invoice.py:1702
#, python-format
msgid "A Payment Terms should have only one line of type Balance."
msgstr "As Condições de Pagamento devem ter apenas uma linha do tipo Saldo."

#. module: account
#: code:addons/account/models/account.py:722
#, python-format
msgid "A bank account can only belong to one journal."
msgstr "Uma conta bancária só pode pertencer a um diário."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_tree
msgid ""
"A bank statement is a summary of all financial transactions\n"
"                occurring over a given period of time on a bank account. You\n"
"                should receive this periodicaly from your bank."
msgstr ""
"Um extrato bancário é um sumário de todas as transações financeiras\n"
"                que ocorrem num determinado período de tempo numa conta.\n"
"                O seu banco deve enviar-lhe os seus extratos periodicamente."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_line
msgid "A bank statement line is a financial transaction on a bank account."
msgstr ""
"Cada linha de um extrato bancário é uma transação financeira numa conta "
"bancária."

#. module: account
#: model:ir.actions.act_window,help:account.action_move_journal_line
msgid ""
"A journal entry consists of several journal items, each of\n"
"                which is either a debit or a credit transaction."
msgstr ""
"Uma entrada no diário consiste de vários itens de diário, sendo\n"
"                cada um desses uma transação de débito ou de crédito."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_journal_form
msgid ""
"A journal is used to record transactions of all accounting data\n"
"                related to the day-to-day business."
msgstr ""
"Um diário serve para registar transações de toda a informação\n"
"                contabilística relacionada com o quotidiano do negócio."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "A list of common taxes and their rates."
msgstr "Uma lista dos impostos mais comuns e das suas taxas."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "A list of your customer and supplier payment terms."
msgstr "Uma lista das condições de pagamento dos clientes e fornecedores."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"A product in Odoo is something you sell or buy \n"
"                        whether or not it is goods, consumables, or services.\n"
"                        Choose how you want to create your products:"
msgstr ""
"Um artigo no Odoo é algo que compra ou vende,\n"
"                        sejam bens, consumíveis, ou serviços.\n"
"                        Escolha como deseja criar os seus artigos:"

#. module: account
#: code:addons/account/models/account_move.py:891
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr "Uma reconciliação deve envolver pelo menos 2 linhas de movimento."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"A rounding per line is advised if your prices are tax-included. That way, "
"the sum of line subtotals equals the total with taxes."
msgstr ""
"Um arredondamento por linha é aconselhável se os preços incluírem imposto. "
"Desta forma, a soma dos subtotais das linhas é igual ao total com imposto."

#. module: account
#: code:addons/account/models/account_bank_statement.py:881
#: code:addons/account/models/account_bank_statement.py:884
#, python-format
msgid "A selected move line was already reconciled."
msgstr "Uma linha de movimento selecionada já foi reconciliada."

#. module: account
#: code:addons/account/models/account_bank_statement.py:892
#, python-format
msgid "A selected statement line was already reconciled with an account move."
msgstr ""
"Uma linha de declaração selecionada já foi reconciliada com um movimento de "
"conta."

#. module: account
#: sql_constraint:account.fiscal.position.tax:0
msgid "A tax fiscal position could be defined only once time on same taxes."
msgstr ""
"Uma posição fiscal de imposto apenas pode ser definida uma vez com o mesmo "
"imposto."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_journal_form
msgid ""
"A typical company may use one journal per payment method (cash,\n"
"                bank accounts, checks), one purchase journal, one sales journal\n"
"                and one for miscellaneous information."
msgstr ""
"Uma empresa típica pode usar um diário por método de pagamento\n"
"                (numerário, contas bancárias, cheques), um diário de compras, um\n"
"                diário de vendas e um para informação diversa."

#. module: account
#: model:res.groups,name:account.group_warning_account
msgid "A warning can be set on a partner (Account)"
msgstr "Um aviso pode ser ativado num parceiro (Conta)"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:521
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:523
#: code:addons/account/static/src/xml/account_reconciliation.xml:170
#: code:addons/account/static/src/xml/account_reconciliation.xml:228
#: code:addons/account/static/src/xml/account_reconciliation.xml:253
#: model:ir.model,name:account.model_account_account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_account_id
#: model:ir.model.fields,field_description:account.field_account_move_dummy_account_id
#: model:ir.model.fields,field_description:account.field_account_move_line_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_account_id
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
#: model:ir.ui.view,arch_db:account.view_account_form
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_account_search
#, python-format
msgid "Account"
msgstr "Conta"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_accountant
msgid "Account Accountant"
msgstr "Contabilista de Conta"

#. module: account
#: model:ir.model,name:account.model_account_aged_trial_balance
msgid "Account Aged Trial balance Report"
msgstr "Relatório de Contas com Saldos Vencidos"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Account Balances"
msgstr "Saldos de Conta"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_cashbox
msgid "Account Bank Statement Cashbox Details"
msgstr "Detalhes de Numerário da Conta Bancária"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_closebalance
msgid "Account Bank Statement closing balance"
msgstr "Saldo de Fecho da Declaração da Conta Bancária"

#. module: account
#: model:ir.model,name:account.model_account_common_account_report
msgid "Account Common Account Report"
msgstr "Relatório de Contas de Conta Comum"

#. module: account
#: model:ir.model,name:account.model_account_common_journal_report
msgid "Account Common Journal Report"
msgstr "Relatório de Diário de Conta Comum"

#. module: account
#: model:ir.model,name:account.model_account_common_partner_report
msgid "Account Common Partner Report"
msgstr "Relatório de Parceiros de Conta Comum"

#. module: account
#: model:ir.model,name:account.model_account_common_report
msgid "Account Common Report"
msgstr "Relatório de Conta Comum"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_currency_id
#: model:ir.model.fields,field_description:account.field_account_account_template_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_currency_id
msgid "Account Currency"
msgstr "Moeda da Conta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_account_dest_id
msgid "Account Destination"
msgstr "Destino Contabilístico"

#. module: account
#: model:ir.model,name:account.model_account_move
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Account Entry"
msgstr "Lançamento Contabilístico"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_group_form
#: model:ir.ui.view,arch_db:account.view_account_group_tree
msgid "Account Group"
msgstr "Grupo Contabilístico"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_group_tree
msgid "Account Groups"
msgstr "Groupos Contabilísticos"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_bank_journal_id
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_tree
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#: model:ir.ui.view,arch_db:account.view_account_journal_tree
msgid "Account Journal"
msgstr "Diário Contabilístico"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_account_line_id
msgid "Account Line"
msgstr "Movimento Contabilístico"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_account_ids
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Account Mapping"
msgstr "Mapeamento Contabilístico"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_reversal
msgid "Account Move Reversal"
msgstr "Reversão de Movimento Contabilístico"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_acc_name
msgid "Account Name."
msgstr "Nome da conta."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_bank_acc_number
msgid "Account Number"
msgstr "Número da Conta"

#. module: account
#: model:ir.model,name:account.model_account_report_partner_ledger
msgid "Account Partner Ledger"
msgstr "Registos Contabilístico de Parceiros"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_account_payable_id
#: model:ir.model.fields,field_description:account.field_res_users_property_account_payable_id
msgid "Account Payable"
msgstr "Conta de Pagamento"

#. module: account
#: model:ir.model,name:account.model_account_print_journal
msgid "Account Print Journal"
msgstr "Diário de Impressão de Contas"

#. module: account
#: model:ir.ui.view,arch_db:account.view_category_property_form
msgid "Account Properties"
msgstr "Propriedades da Conta"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_account_receivable_id
#: model:ir.model.fields,field_description:account.field_res_users_property_account_receivable_id
msgid "Account Receivable"
msgstr "Conta de Recebimento"

#. module: account
#: model:ir.model,name:account.model_account_financial_report
#: model:ir.model.fields,field_description:account.field_account_financial_report_children_ids
#: model:ir.ui.view,arch_db:account.view_account_financial_report_form
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
#: model:ir.ui.view,arch_db:account.view_account_financial_report_tree
msgid "Account Report"
msgstr "Relatório Contabilístico"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_account_report_id
#: model:ir.ui.menu,name:account.menu_account_financial_reports_tree
msgid "Account Reports"
msgstr "Relatórios Contabilísticos"

#. module: account
#: model:ir.model,name:account.model_account_cash_rounding
msgid "Account Rounding"
msgstr "Arrendondamento de Conta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_account_src_id
msgid "Account Source"
msgstr "Conta de Origem"

#. module: account
#: model:ir.ui.view,arch_db:account.account_bank_statement_graph
#: model:ir.ui.view,arch_db:account.account_bank_statement_pivot
#: model:ir.ui.view,arch_db:account.account_move_line_graph_date
#: model:ir.ui.view,arch_db:account.account_move_line_graph_date_cash_basis
msgid "Account Statistics"
msgstr "Estatísticas Contabilísticas"

#. module: account
#: model:ir.model,name:account.model_account_account_tag
msgid "Account Tag"
msgstr "Etiqueta de Conta"

#. module: account
#: model:ir.actions.act_window,name:account.account_tag_action
msgid "Account Tags"
msgstr "Etiquetas de Contas"

#. module: account
#: model:ir.ui.view,arch_db:account.view_tax_form
#: model:ir.ui.view,arch_db:account.view_tax_tree
msgid "Account Tax"
msgstr "Impostos de Conta"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_account_tax_template_tree
msgid "Account Tax Template"
msgstr "Modelo de Impostos de Conta"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_taxcloud
msgid "Account TaxCloud"
msgstr "Conta TaxCloud"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
#: model:ir.ui.view,arch_db:account.view_account_template_form
#: model:ir.ui.view,arch_db:account.view_account_template_search
#: model:ir.ui.view,arch_db:account.view_account_template_tree
msgid "Account Template"
msgstr "Modelo de Conta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_stock_valuation_account_id
#: model:ir.model.fields,field_description:account.field_res_company_property_stock_valuation_account_id
msgid "Account Template for Stock Valuation"
msgstr "Modelo de Conta para Validação de Ações"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_template_form
msgid "Account Templates"
msgstr "Modelos de Conta"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Account Total"
msgstr "Total de Conta"

#. module: account
#: selection:account.financial.report,type:0
#: model:ir.model,name:account.model_account_account_type
#: model:ir.model.fields,field_description:account.field_account_account_type_name
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_account_type
#: model:ir.ui.view,arch_db:account.view_account_search
#: model:ir.ui.view,arch_db:account.view_account_template_search
#: model:ir.ui.view,arch_db:account.view_account_type_form
#: model:ir.ui.view,arch_db:account.view_account_type_search
#: model:ir.ui.view,arch_db:account.view_account_type_tree
msgid "Account Type"
msgstr "Tipo de Conta"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_user_type_id
#: model:ir.model.fields,help:account.field_account_move_line_user_type_id
msgid ""
"Account Type is used for information purpose, to generate country-specific "
"legal reports, and set the rules to close a fiscal year and generate opening"
" entries."
msgstr ""
"O Tipo de Conta é usado para fins informativos, para gerar relatórios legais"
" específicos para cada país e definir as regras para fechar um ano fiscal e "
"gerar entradas de abertura."

#. module: account
#: model:ir.actions.act_window,name:account.action_account_type_form
#: model:ir.model.fields,field_description:account.field_account_financial_report_account_type_ids
msgid "Account Types"
msgstr "Tipos de Conta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_type_control_ids
msgid "Account Types Allowed"
msgstr "Tipos de Conta Permitidos"

#. module: account
#: model:ir.model,name:account.model_account_unreconcile
msgid "Account Unreconcile"
msgstr "Retirar Reconciliação de Conta"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_group_search
msgid "Account group"
msgstr "Grupo de conta"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_group_search
msgid "Account groups"
msgstr "Grupos de conta"

#. module: account
#: model:ir.model,name:account.model_account_move_line_reconcile
msgid "Account move line reconcile"
msgstr "Reconciliar linha de movimento contabilístico"

#. module: account
#: model:ir.model,name:account.model_account_move_line_reconcile_writeoff
msgid "Account move line reconcile (writeoff)"
msgstr "Reconciliar linha de movimento contabilístico (liquidação)"

#. module: account
#: model:ir.model,name:account.model_account_move_reversal
msgid "Account move reversal"
msgstr "Reversão de movimento de conta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_account_src_id
msgid "Account on Product"
msgstr "Conta no Artigo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_tag_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template_tag_ids
msgid "Account tag"
msgstr "Etiqueta de conta"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_refund_account_id
msgid ""
"Account that will be set on invoice tax lines for credit notes. Leave empty "
"to use the expense account."
msgstr ""
"Conta utilizada nas linhas de impostos em notas de crédito. Deixar vazio "
"para usar a conta de despesas."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_account_id
#: model:ir.model.fields,help:account.field_account_tax_template_account_id
msgid ""
"Account that will be set on invoice tax lines for invoices. Leave empty to "
"use the expense account."
msgstr ""
"Conta utilizada nas linhas de impostos em faturas. Deixar vazio para usar a "
"conta de despesas."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_template_refund_account_id
msgid ""
"Account that will be set on invoice tax lines for refunds. Leave empty to "
"use the expense account."
msgstr ""
"Conta utilizada nas linhas de impostos para reembolsos. Deixar vazio para "
"usar a conta de despesas."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_account_dest_id
msgid "Account to Use Instead"
msgstr "Conta Alternativa"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_cash_basis_account
#: model:ir.model.fields,help:account.field_account_tax_template_cash_basis_account
msgid ""
"Account used as counterpart for the journal entry, for taxes eligible based "
"on payments."
msgstr ""
"Conta usada como complemento à entrada no diário relativa a pagamentos "
"tributáveis."

#. module: account
#: model:ir.ui.menu,name:account.account_account_menu
msgid "Accounting"
msgstr "Contabilidade"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Accounting App Options"
msgstr "Opções da Aplicação de Contabilidade"

#. module: account
#: model:ir.ui.view,arch_db:account.view_wizard_multi_chart
msgid "Accounting Application Configuration"
msgstr "Configuração da Aplicação de Contabilidade"

#. module: account
#: model:web.planner,tooltip_planner:account.planner_account
msgid "Accounting Configuration: a step-by-step guide."
msgstr "Configuração Contabilística: um guia passo-a-passo."

#. module: account
#: model:ir.actions.act_window,name:account.open_account_journal_dashboard_kanban
msgid "Accounting Dashboard"
msgstr "Painel de Contabilidade"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_date
msgid "Accounting Date"
msgstr "Data Contabilística"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Accounting Documents"
msgstr "Documentos Contabilísticos"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_accounting_entries
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Accounting Entries"
msgstr "Lançamentos Contabilísticos"

#. module: account
#: model:ir.model,name:account.model_accounting_report
msgid "Accounting Report"
msgstr "Relatório Contabilístico"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Accounting Settings"
msgstr "Definições da Contabilidade"

#. module: account
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Accounting-related settings are managed on"
msgstr "As definições relacionadas com a contabilidade são geridas no"

#. module: account
#: selection:account.account.tag,applicability:0
#: selection:account.financial.report,type:0
#: model:ir.model.fields,field_description:account.field_account_financial_report_account_ids
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
#: model:ir.ui.view,arch_db:account.view_account_search
msgid "Accounts"
msgstr "Contas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_account_control_ids
msgid "Accounts Allowed"
msgstr "Contas Permitidas"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_account
msgid "Accounts Fiscal Position"
msgstr "Posição Fiscal de Contas"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_template_form
msgid "Accounts Mapping"
msgstr "Mapeamento de Contas"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_actions
msgid "Actions"
msgstr "Ações"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Activate Other Currencies"
msgstr "Ativar Outras Moedas"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Activate the option in the"
msgstr "Ativar a opção em"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_active
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_active
#: model:ir.model.fields,field_description:account.field_account_journal_active
#: model:ir.model.fields,field_description:account.field_account_payment_term_active
#: model:ir.model.fields,field_description:account.field_account_tax_active
#: model:ir.model.fields,field_description:account.field_account_tax_template_active
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Active"
msgstr "Ativo"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:17
#, python-format
msgid "Add"
msgstr "Adicionar"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid "Add Credit Note"
msgstr "Adicionar Nota de Crédito"

#. module: account
#: selection:account.cash.rounding,strategy:0
msgid "Add a rounding line"
msgstr "Adicionar linha de arredondamento"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_has_second_line
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_has_second_line
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Add a second line"
msgstr "Adicionar outra linha"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Add an internal note..."
msgstr "Adicione uma nota interna..."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_comment
msgid "Additional Information"
msgstr "Informação Adicional"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Additional notes..."
msgstr "Notas adicionais..."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Address"
msgstr "Morada"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_tax_id
msgid "Adjustment Tax"
msgstr "Ajuste Fiscal"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_adjustment_type
msgid "Adjustment Type"
msgstr "Tipo de Ajuste"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_tax_form
msgid "Advanced Options"
msgstr "Opções Avançadas"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Advanced Settings"
msgstr "Configurações Avançadas"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries
msgid "Adviser"
msgstr "Consultor"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_include_base_amount
msgid "Affect Base of Subsequent Taxes"
msgstr "Alterar a Base de Impostos Posteriores"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_include_base_amount
msgid "Affect Subsequent Taxes"
msgstr "Alterar Impostos Posteriores"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_aged_balance_view
#: model:ir.actions.report,name:account.action_report_aged_partner_balance
#: model:ir.ui.menu,name:account.menu_aged_trial_balance
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Aged Partner Balance"
msgstr "Saldos Vencidos de Parceiros"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
msgid ""
"Aged Partner Balance is a more detailed report of your receivables by "
"intervals. Odoo calculates a table of credit balance by start Date. So if "
"you request an interval of 30 days Odoo generates an analysis of creditors "
"for the past month, past two months, and so on."
msgstr ""
"Saldos Vencidos de Parceiros é um relatório mais detalhado dos seus valores "
"a receber por intervalos. O Odoo calcula uma tabela de saldos de crédito "
"desde a Data inicial. Deste modo, se selecionar um intervalo de 30 dias, o "
"Odoo gera uma análise de devedores para o último mês, últimos dois meses, "
"etc."

#. module: account
#: selection:account.balance.report,display_account:0
#: selection:account.common.account.report,display_account:0
#: selection:account.report.general.ledger,display_account:0
msgid "All"
msgstr "Todos"

#. module: account
#: selection:account.aged.trial.balance,target_move:0
#: selection:account.balance.report,target_move:0
#: selection:account.common.account.report,target_move:0
#: selection:account.common.journal.report,target_move:0
#: selection:account.common.partner.report,target_move:0
#: selection:account.common.report,target_move:0
#: selection:account.print.journal,target_move:0
#: selection:account.report.general.ledger,target_move:0
#: selection:account.report.partner.ledger,target_move:0
#: selection:account.tax.report,target_move:0
#: selection:accounting.report,target_move:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "All Entries"
msgstr "Todas as Entradas"

#. module: account
#: model:ir.actions.act_window,name:account.action_all_partner_invoices
msgid "All Invoices"
msgstr "Todas as Faturas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_all_lines_reconciled
msgid "All Lines Reconciled"
msgstr "Todas as Linhas Reconciliadas"

#. module: account
#: selection:account.aged.trial.balance,target_move:0
#: selection:account.balance.report,target_move:0
#: selection:account.common.account.report,target_move:0
#: selection:account.common.journal.report,target_move:0
#: selection:account.common.partner.report,target_move:0
#: selection:account.common.report,target_move:0
#: selection:account.print.journal,target_move:0
#: selection:account.report.general.ledger,target_move:0
#: selection:account.report.partner.ledger,target_move:0
#: selection:account.tax.report,target_move:0
#: selection:accounting.report,target_move:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "All Posted Entries"
msgstr "Todas as Entradas Lançadas"

#. module: account
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "All accounts"
msgstr "Todas as contas"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "All accounts'"
msgstr "De todas as contas"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:47
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr ""
"Todas as faturas e pagamentos foram acertados, os saldos das suas contas "
"estão limpos"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_state
msgid ""
"All manually created new journal entries are usually in the status "
"'Unposted', but you can set the option to skip that status on the related "
"journal. In that case, they will behave as journal entries automatically "
"created by the system on document validation (invoices, bank statements...) "
"and will be created in 'Posted' status."
msgstr ""
"Todas as entradas no diário criadas manualmente estão usualmente no estado "
"'Não Lançada', mas pode selecionar a opção para ignorar esse estado no "
"diário correspondente. Nesse caso, as entradas de diário comportam-se como "
"sendo criadas automaticamente pelo sistema aquando da validação de "
"documentos (faturas, extratos bancários...) e terão o estado 'Lançada'."

#. module: account
#: model:ir.ui.view,arch_db:account.validate_account_move_view
msgid ""
"All selected journal entries will be validated and posted. You won't be able"
" to modify them afterwards."
msgstr ""
"Todas as entradas de diário selecionadas serão validadas e lançadas. Não as "
"vai poder alterar posteriormente."

#. module: account
#: code:addons/account/models/account_bank_statement.py:240
#, python-format
msgid ""
"All the account entries lines must be processed in order to close the "
"statement."
msgstr ""
"Todas as linhas de entrada de conta têm de ser processadas de modo a fechar "
"o extrato."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_update_posted
msgid "Allow Cancelling Entries"
msgstr "Permitir Cancelamento de Entradas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_reconcile
msgid "Allow Invoices & payments Matching"
msgstr "Permitir Acertos de Faturas e Pagamentos"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_product_margin
msgid "Allow Product Margin"
msgstr "Permitir Margem de Artigo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_reconcile
msgid "Allow Reconciliation"
msgstr "Permitir Reconciliação"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_l10n_us_check_printing
msgid "Allow check printing and deposits"
msgstr "Permitir impressão de cheques e depósitos"

#. module: account
#: model:res.groups,name:account.group_cash_rounding
msgid "Allow the cash rounding management"
msgstr "Permitir gestão de arredondamento de numerário"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allow to configure taxes using cash basis"
msgstr "Permitir configuração de impostos de utilização de fundo de caixa"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allows you to use the analytic accounting."
msgstr "Permite usar a contabilidade analítica."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:193
#: code:addons/account/static/src/xml/account_reconciliation.xml:235
#: code:addons/account/static/src/xml/account_reconciliation.xml:252
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_amount
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_subtotal
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_total
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_amount
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_amount_total
#: model:ir.model.fields,field_description:account.field_account_move_amount
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_amount
#: model:ir.model.fields,field_description:account.field_account_tax_amount
#: model:ir.model.fields,field_description:account.field_account_tax_template_amount
#: model:ir.model.fields,field_description:account.field_cash_box_in_amount
#: model:ir.model.fields,field_description:account.field_cash_box_out_amount
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_amount
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_payment_tree
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_account_supplier_payment_tree
#: model:ir.ui.view,arch_db:account.view_move_line_form
#, python-format
msgid "Amount"
msgstr "Valor"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_amount_currency
#: model:ir.model.fields,field_description:account.field_account_analytic_line_analytic_amount_currency
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_amount_currency
#: model:ir.model.fields,field_description:account.field_account_move_line_amount_currency
msgid "Amount Currency"
msgstr "Moeda do Valor"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_residual
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid "Amount Due"
msgstr "Valor Vencido"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_residual_company_signed
msgid "Amount Due in Company Currency"
msgstr "Valor Vencido na Moeda da Empresa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_residual_signed
msgid "Amount Due in Invoice Currency"
msgstr "Valor Vencido na Moeda da Fatura"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Amount Paid"
msgstr "Valor Pago"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_amount_rounding
msgid "Amount Rounding"
msgstr "Valor Arredondado"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_subtotal_signed
msgid "Amount Signed"
msgstr "Valor Com Sinal"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_amount_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_amount_type
msgid "Amount Type"
msgstr "Tipo de Valor"

#. module: account
#: model:ir.model.fields,help:account.field_account_partial_reconcile_amount
msgid "Amount concerned by this matching. Assumed to be always positive"
msgstr "Valor relativo a este acerto. Assume-se que seja sempre positivo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_amount_currency
msgid "Amount in Currency"
msgstr "Valor em Moeda"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Amount type"
msgstr "Tipo de valor"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:68
#, python-format
msgid "Amount:"
msgstr "Valor:"

#. module: account
#: sql_constraint:account.fiscal.position.account:0
msgid ""
"An account fiscal position could be defined only once time on same accounts."
msgstr ""
"A posição fiscal do imposto poderá ser definida apenas uma vez nas mesmas "
"contas."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_form
msgid ""
"An account is part of a ledger allowing your company\n"
"                to register all kinds of debit and credit transactions.\n"
"                Companies present their annual accounts in two main parts: the\n"
"                balance sheet and the income statement (profit and loss\n"
"                account). The annual accounts of a company are required by law\n"
"                to disclose a certain amount of information."
msgstr ""
"Uma conta é parte de um registo, permitindo que a sua empresa\n"
"                registe todos os tipos de transações a débito e crédito.\n"
"                As empresas apresentam as suas contas anuais em duas partes principais: \n"
"                balanço e extrato de receitas (lucros e perdas)\n"
"                As contas anuais de uma empresa são obrigatórias por lei\n"
"                para divulgar uma quantidade determinada de informação."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_type_form
msgid ""
"An account type is used to determine how an account is used in\n"
"                each journal. The deferral method of an account type determines\n"
"                the process for the annual closing. Reports such as the Balance\n"
"                Sheet and the Profit and Loss report use the category\n"
"                (profit/loss or balance sheet)."
msgstr ""
"Um tipo de conta é utilizado para determinar como cada conta é utilizada em\n"
"                cada diário. O método de diferimento de um tipo de conta determina\n"
"                o processos de encerramento anual. Os relatórios, tais como Balanço\n"
"                e o relatório de Lucros e Prejuízos utilizam a categoria\n"
"                (lucro/prejuízo ou balanço)."

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Analytic"
msgstr "Analítica"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:178
#, python-format
msgid "Analytic Acc."
msgstr "Conta Analítica"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_account_analytic_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_account_analytic_id
#: model:ir.model.fields,field_description:account.field_account_move_line_analytic_account_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_analytic_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_analytic_account_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Analytic Account"
msgstr "Conta Analítica"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_group_analytic_accounting
#: model:ir.ui.menu,name:account.menu_analytic_accounting
msgid "Analytic Accounting"
msgstr "Contabilidade Analítica"

#. module: account
#: model:ir.actions.act_window,name:account.action_open_partner_analytic_accounts
#: model:ir.ui.menu,name:account.account_analytic_def_account
#: model:ir.ui.view,arch_db:account.partner_view_button_contracts_count
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytic Accounts"
msgstr "Contas Analíticas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_analytic
msgid "Analytic Cost"
msgstr "Custo da Analítica"

#. module: account
#: model:ir.actions.act_window,name:account.analytic_line_reporting_pivot
#: model:ir.ui.menu,name:account.menu_action_analytic_lines_reporting
#: model:ir.ui.menu,name:account.menu_action_analytic_lines_tree
msgid "Analytic Entries"
msgstr "Entradas de Analítica"

#. module: account
#: model:ir.model,name:account.model_account_analytic_line
msgid "Analytic Line"
msgstr "Linha de Analítica"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Analytic Lines"
msgstr "Linhas de Analítica"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_analytic_tag_ids
#: model:ir.ui.menu,name:account.account_analytic_tag_menu
msgid "Analytic Tags"
msgstr "Etiquetas de Analítica"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_account_analytic_id
msgid "Analytic account"
msgstr "Conta de Analítica"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_analytic_line_ids
msgid "Analytic lines"
msgstr "Linhas de analítica"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_analytic_tag_ids
msgid "Analytic tags"
msgstr "Etiquetas de analítica"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytics"
msgstr "Analíticas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_exigible
msgid "Appears in VAT report"
msgstr "Aparece no relatório de IVA"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_applicability
msgid "Applicability"
msgstr "Aplicabilidade"

#. module: account
#: selection:tax.adjustments.wizard,adjustment_type:0
msgid "Applied on credit journal item"
msgstr ""

#. module: account
#: selection:tax.adjustments.wizard,adjustment_type:0
msgid "Applied on debit journal item"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_auto_apply
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_auto_apply
msgid "Apply automatically this fiscal position."
msgstr "Aplica automaticamente esta posição fiscal."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_country_group_id
msgid "Apply only if delivery or invocing country match the group."
msgstr ""
"Aplicar apenas se o país de entrega ou faturação corresponder ao grupo."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_country_group_id
msgid "Apply only if delivery or invoicing country match the group."
msgstr ""
"Aplicar apenas se o país de entrega ou faturação corresponder ao grupo."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_country_id
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_country_id
msgid "Apply only if delivery or invoicing country match."
msgstr "Aplicar apenas se o país de entrega ou faturação corresponder."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_vat_required
#: model:ir.model.fields,help:account.field_account_fiscal_position_vat_required
msgid "Apply only if partner has a VAT number."
msgstr "Aplicar apenas se o parceiro tiver NIF."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Apply right VAT rates for digital products sold in EU"
msgstr "Aplicar valores de IVA corretos para artigos digitais vendidos na UE"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "April"
msgstr "Abril"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Archived"
msgstr "Arquivado"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Ask for a Credit Note"
msgstr "Pedir uma Nota de Crédito"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_assets0
msgid "Assets"
msgstr "Ativos"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_asset
msgid "Assets Management"
msgstr "Gestão de Ativos"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_account_ids
msgid "Associated Account Templates"
msgstr "Modelos de Contas Associadas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_at_least_one_inbound
msgid "At Least One Inbound"
msgstr "Pelo Menos Uma a Receber"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_at_least_one_outbound
msgid "At Least One Outbound"
msgstr "Pelo Menos Uma a Enviar"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "August"
msgstr "Agosto"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Auto-detect"
msgstr "Detetar Automaticamente"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automate deferred revenues entries for multi-year contracts"
msgstr ""
"Automatizar entradas de receitas diferidas para contratos de vários anos"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automated Entries"
msgstr "Entradas Automatizadas"

#. module: account
#: code:addons/account/models/company.py:411
#: code:addons/account/wizard/setup_wizards.py:79
#, python-format
msgid "Automatic Balancing Line"
msgstr "Linha de Saldo Automática"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_currency_rate_live
msgid "Automatic Currency Rates"
msgstr "Taxas de Câmbio Automáticas"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automatic Import"
msgstr "Importação Automática"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Automatic formatting"
msgstr "Formatação automática"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:24
#, python-format
msgid "Automatic reconciliation"
msgstr "Reconciliação automática"

#. module: account
#: code:addons/account/models/account_bank_statement.py:468
#, python-format
msgid "Automatically reconciled items"
msgstr "Itens reconciliados automaticamente"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_price_average
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_currency_price_average
msgid "Average Price"
msgstr "Preço Médio"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Awaiting payments"
msgstr "A aguardar pagamentos"

#. module: account
#: code:addons/account/models/chart_template.py:194
#, python-format
msgid "BILL"
msgstr "FTF"

#. module: account
#: selection:res.partner,trust:0
msgid "Bad Debtor"
msgstr "Mau Devedor"

#. module: account
#: selection:account.payment.term.line,value:0
#: model:ir.model.fields,field_description:account.field_account_move_line_balance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_payment_receipt
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Balance"
msgstr "Saldo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_balance_cash_basis
msgid "Balance Cash Basis"
msgstr "Saldo de Fundo de Caixa"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_balancesheet0
#: model:ir.actions.act_window,name:account.action_account_report_bs
#: model:ir.ui.menu,name:account.menu_account_report_bs
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Balance Sheet"
msgstr "Balanço"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_balance_end
msgid "Balance as calculated based on Opening Balance and transaction lines"
msgstr "Saldo calculado com base no Balanço Inicial e linhas de transação"

#. module: account
#: selection:account.bank.accounts.wizard,account_type:0
#: selection:account.journal,type:0
#: code:addons/account/models/chart_template.py:758
#: model:ir.model.fields,field_description:account.field_account_journal_bank_id
#: model:ir.model.fields,field_description:account.field_res_partner_bank_account_count
#: model:ir.model.fields,field_description:account.field_res_users_bank_account_count
#, python-format
msgid "Bank"
msgstr "Banco"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Bank &amp; Cash"
msgstr "Banco &amp; Numerário"

#. module: account
#: code:addons/account/models/company.py:226
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_bank_account_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_bank_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_partner_bank_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_partner_bank_id
#: model:ir.model.fields,field_description:account.field_account_journal_bank_account_id
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#, python-format
msgid "Bank Account"
msgstr "Conta Bancária"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Bank Account Name"
msgstr "Nome da Conta Bancária"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_partner_bank_id
msgid ""
"Bank Account Number to which the invoice will be paid. A Company bank "
"account if this is a Customer Invoice or Vendor Credit Note, otherwise a "
"Partner bank account number."
msgstr ""
"Número da Conta Bancária à qual se vai pagar a fatura. Conta bancária da "
"Empresa se se tratar de uma Fatura de Cliente ou Nota de Crédito de "
"Fornecedor, número de conta de um Parceiro caso contrário."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:21
#: model:ir.actions.act_window,name:account.action_account_bank_journal_form
#: model:ir.model,name:account.model_res_partner_bank
#: model:ir.ui.menu,name:account.menu_action_account_bank_journal_form
#, python-format
msgid "Bank Accounts"
msgstr "Contas Bancárias"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_bank_account_code_prefix
msgid "Bank Accounts Prefix"
msgstr "Prefixo das Contas Bancárias"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_bank_statements_source
msgid "Bank Feeds"
msgstr "Feeds Bancários"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_yodlee
msgid "Bank Interface - Sync your bank feeds automatically"
msgstr ""
"Interface Bancária - Sincronizar os seus feeds bancários automaticamente"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_bank_journal_ids
msgid "Bank Journals"
msgstr "Diários Bancários"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Bank Operations"
msgstr "Operações Bancárias"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_tree
msgid "Bank Reconciliation Move Presets"
msgstr "Predefinições dos Movimentos de Reconciliação Bancários"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_search
msgid "Bank Reconciliation Move preset"
msgstr "Predefinição dos Movimentos de Reconciliação Bancários"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_bank_data_done
msgid "Bank Setup Marked As Done"
msgstr "Configuração Bancária Concluída"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Bank Statement"
msgstr "Extrato Bancário"

#. module: account
#: code:addons/account/models/account_bank_statement.py:937
#, python-format
msgid "Bank Statement %s"
msgstr "Extrato Bancário %s"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Linha de Extrato Bancário"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_line
msgid "Bank Statement Lines"
msgstr "Linhas de Extrato Bancário"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_tree
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Bank Statements"
msgstr "Extratos Bancários"

#. module: account
#: model:ir.ui.view,arch_db:account.partner_view_buttons
msgid "Bank account(s)"
msgstr "Conta(s) bancária(s)"

#. module: account
#: model:account.account.type,name:account.data_account_type_liquidity
msgid "Bank and Cash"
msgstr "Banco e Numerário"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_action.js:15
#, python-format
msgid "Bank reconciliation"
msgstr "Reconciliação bancária"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_account_setup_bank_data_done
msgid "Bank setup marked as done"
msgstr "Configuração bancária concluída"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_statement_line_id
msgid "Bank statement line reconciled with this entry"
msgstr "Linha de extrato bancário reconciliada com esta entrada"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_bank_statement
msgid "Bank statements"
msgstr "Extratos bancários"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:39
#, python-format
msgid "Bank: Balance"
msgstr "Banco: Saldo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_base
#: model:ir.ui.view,arch_db:account.portal_invoice_report
msgid "Base"
msgstr "Base"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_base_amount
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Base Amount"
msgstr "Valor de Base"

#. module: account
#: selection:account.tax,tax_exigibility:0
#: selection:account.tax.template,tax_exigibility:0
msgid "Based on Invoice"
msgstr "Com base na Fatura"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_tax_exigibility
#: model:ir.model.fields,help:account.field_account_tax_template_tax_exigibility
msgid ""
"Based on Invoice: the tax is due as soon as the invoice is validated.\n"
"Based on Payment: the tax is due as soon as the payment of the invoice is received."
msgstr ""
"Com base na Fatura: o imposto é devido assim que a fatura é validada.\n"
"Com base no Pagamento: o imposto é devido assim que o pagamento da fatura é recebido."

#. module: account
#: selection:account.tax,tax_exigibility:0
#: selection:account.tax.template,tax_exigibility:0
msgid "Based on Payment"
msgstr "Com base no Pagamento"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Batch Deposits"
msgstr "Lote de Depósitos"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Before continuing, you must install the Chart of Account related to your "
"country (or the generic one if your country is not listed)."
msgstr ""
"Antes de proceder, deve instalar o Plano de Contas relativo ao seu país (ou "
"o genérico se o seu país não estiver listado)."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_belongs_to_company
msgid "Belong to the user's current company"
msgstr "Pertence à empresa atual do utilizador"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Bill"
msgstr "Fatura de Fornecedor"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "Bill Date"
msgstr "Data da Fatura de Fornecedor"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Bill lines"
msgstr "Linhas da Fatura de Fornecedor"

#. module: account
#: model:res.groups,name:account.group_account_invoice
msgid "Billing"
msgstr "Faturação"

#. module: account
#: model:res.groups,name:account.group_account_manager
msgid "Billing Manager"
msgstr "Gestor de Faturas de Fornecedor"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills"
msgstr "Faturas de Fornecedor"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills Analysis"
msgstr "Análise de Faturas de Fornecedor"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:186
#, python-format
msgid "Bills to pay"
msgstr "Fatura de Fornecedor a pagar"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "Blocking Message"
msgstr "Mensagem de Bloqueio"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_type_include_initial_balance
msgid "Bring Accounts Balance Forward"
msgstr "Apresentar Saldo de Contas"

#. module: account
#: model:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid "Browse available countries."
msgstr "Consultar países disponíveis."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_budget
msgid "Budget Management"
msgstr "Gestão de Orçamento"

#. module: account
#: model:ir.ui.menu,name:account.account_reports_business_intelligence_menu
msgid "Business Intelligence"
msgstr "Inteligência Empresarial"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_name
msgid "Button Label"
msgstr "Rótulo do Botão"

#. module: account
#: model:ir.filters,name:account.filter_invoice_country
msgid "By Country"
msgstr "Por País"

#. module: account
#: model:ir.filters,name:account.filter_invoice_refund
msgid "By Credit Note"
msgstr "Por Nota de Crédito"

#. module: account
#: model:ir.filters,name:account.filter_invoice_product
msgid "By Product"
msgstr "Por Artigo"

#. module: account
#: model:ir.filters,name:account.filter_invoice_product_category
msgid "By Product Category"
msgstr "Por Categoria de Artigo"

#. module: account
#: model:ir.filters,name:account.filter_invoice_salespersons
msgid "By Salespersons"
msgstr "Por Vendedor"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_active
msgid ""
"By unchecking the active field, you may hide a fiscal position without "
"deleting it."
msgstr ""
"Se desmarcar o campo ativo, pode esconder uma posição fiscal sem a apagar."

#. module: account
#: code:addons/account/models/chart_template.py:173
#: code:addons/account/models/chart_template.py:197
#, python-format
msgid "CABA"
msgstr "CABA"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "CAMT Import"
msgstr "Importar CAMT"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "CSV Import"
msgstr "Importar CSV"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_visible
msgid "Can be Visible?"
msgstr "Pode ser visível?"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.account_common_report_view
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
#: model:ir.ui.view,arch_db:account.cash_box_in_form
#: model:ir.ui.view,arch_db:account.cash_box_out_form
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
#: model:ir.ui.view,arch_db:account.validate_account_move_view
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
#: model:ir.ui.view,arch_db:account.view_account_move_reversal
#: model:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Cancel"
msgstr "Cancelar"

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Cancel: create credit note and reconcile"
msgstr "Cancelar: criar nota de crédito e reconciliar"

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: selection:account.payment,state:0
msgid "Cancelled"
msgstr "Cancelada"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Cancelled Invoice"
msgstr "Fatura Cancelada"

#. module: account
#: code:addons/account/wizard/account_invoice_refund.py:56
#, python-format
msgid ""
"Cannot create a credit note for the invoice which is already reconciled, "
"invoice should be unreconciled first, then only you can add credit note for "
"this invoice."
msgstr ""
"É impossível criar uma nota de crédito para uma fatura já reconciliada. Deve"
" ser anulada a reconciliação da fatura primeiro e só depois pode adicionar a"
" nota de crédito relativa à fatura."

#. module: account
#: code:addons/account/wizard/account_invoice_refund.py:54
#, python-format
msgid "Cannot create credit note for the draft/cancelled invoice."
msgstr "Impossível criar nota de crédito para uma fatura rascunho/cancelada."

#. module: account
#: code:addons/account/models/account_move.py:197
#, python-format
msgid "Cannot create moves for different companies."
msgstr "Impossível criar movimentos para empresas diferentes."

#. module: account
#: code:addons/account/models/account_move.py:229
#, python-format
msgid "Cannot create unbalanced journal entry."
msgstr "Impossível criar entrada de diário não saldada."

#. module: account
#: code:addons/account/models/account_invoice.py:641
#, python-format
msgid ""
"Cannot find a chart of accounts for this company, You should configure it. \n"
"Please go to Account Configuration."
msgstr ""
"Impossível encontrar plano de contas desta empresa sem configurar primeiro.\n"
"Por favor, vá a Configuração de Contabilidade."

#. module: account
#: code:addons/account/models/account.py:594
#, python-format
msgid "Cannot generate an unused account code."
msgstr "Impossível gerar um código de conta inutilizado."

#. module: account
#: code:addons/account/models/account.py:624
#, python-format
msgid ""
"Cannot generate an unused journal code. Please fill the 'Shortcode' field."
msgstr ""
"Impossível gerar um código de diário inutilizado. Por favor, preencha o "
"campo 'Código Abreviado'."

#. module: account
#: selection:account.bank.accounts.wizard,account_type:0
#: selection:account.journal,type:0
#: code:addons/account/models/chart_template.py:758
#, python-format
msgid "Cash"
msgstr "Numerário"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_cash_account_code_prefix
msgid "Cash Accounts Prefix"
msgstr "Prefixo de Contas de Numerário"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_tax_exigibility
msgid "Cash Basis"
msgstr "Fundo de Caixa"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_tax_cash_basis_journal_id
msgid "Cash Basis Journal"
msgstr "Diário de Fundo de Caixa"

#. module: account
#: code:addons/account/models/chart_template.py:197
#, python-format
msgid "Cash Basis Tax Journal"
msgstr "Diário de Impostos de Fundo de Caixa"

#. module: account
#: code:addons/account/models/account_bank_statement.py:210
#: model:ir.actions.act_window,name:account.action_view_account_bnk_stmt_cashbox
#, python-format
msgid "Cash Control"
msgstr "Controlo de Numerário"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Cash Operations"
msgstr "Operações de Numerário"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_bank_statement_tree
msgid "Cash Registers"
msgstr "Registos de Numerário"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_group_cash_rounding
msgid "Cash Rounding"
msgstr "Arredondamentos de Numerário"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_cash_rounding_id
msgid "Cash Rounding Method"
msgstr "Método de Arredondamento de Numerário"

#. module: account
#: model:ir.actions.act_window,name:account.rounding_list_action
#: model:ir.ui.menu,name:account.menu_action_rounding_form_view
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Cash Roundings"
msgstr "Arredondamentos de Numerário"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Cash Statements"
msgstr "Declarações de Numerário"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_bank_account_ids
msgid "Cash and Banks"
msgstr "Caixa e Bancos"

#. module: account
#: code:addons/account/models/account_bank_statement.py:185
#, python-format
msgid "Cash difference observed during the counting (%s)"
msgstr "Diferença de numerário após a contagem (%s)"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:37
#, python-format
msgid "Cash: Balance"
msgstr "Numerário: Saldo"

#. module: account
#: model:ir.model,name:account.model_account_cashbox_line
msgid "CashBox Line"
msgstr "Linha de Caixa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_cashbox_id
msgid "Cashbox"
msgstr "Caixa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_cashbox_lines_ids
msgid "Cashbox Lines"
msgstr "Linhas de Caixa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_expense_categ_id
msgid "Category of Expense Account"
msgstr "Categoria de Conta de Despesas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_income_categ_id
msgid "Category of Income Account"
msgstr "Categoria de Conta de Rendimentos"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Change"
msgstr "Troco"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_writeoff_label
msgid "Change label of the counterpart that will hold the payment difference"
msgstr "Alterar etiqueta do complemento que contém a diferença de pagamentos"

#. module: account
#: code:addons/account/controllers/portal.py:146
#, python-format
msgid ""
"Changing VAT number is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"Alterar o NIF não é permitido assim que sejam emitidas faturas na sua conta."
" Por favor contacte-nos diretamente para esta operação."

#. module: account
#: code:addons/account/controllers/portal.py:149
#, python-format
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"Alterar o seu nome não é permitido assim que sejam emitidas faturas na sua "
"conta. Por favor contacte-nos diretamente para esta operação."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_res_company_chart_template_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_chart_template_id
msgid "Chart Template"
msgstr "Modelo de Plano de Contas"

#. module: account
#: model:ir.actions.act_window,name:account.open_account_charts_modules
msgid "Chart Templates"
msgstr "Modelos de Plano de Contas"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_coa_done
msgid "Chart of Account Checked"
msgstr "Plano de Contas Verificado"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:278
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:13
#: model:ir.actions.act_window,name:account.action_account_form
#: model:ir.ui.menu,name:account.menu_action_account_form
#: model:ir.ui.view,arch_db:account.account_planner
#, python-format
msgid "Chart of Accounts"
msgstr "Plano de Contas"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_form
#: model:ir.ui.view,arch_db:account.view_account_chart_template_tree
msgid "Chart of Accounts Template"
msgstr "Modelo de Plano de Contas"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_chart_template_form
msgid "Chart of Accounts Templates"
msgstr "Modelos de Plano de Contas"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Chart of Accounts has been\n"
"                            installed. You should review it and create any additional accounts."
msgstr ""
"Plano de Contas foi\n"
"                            instalado. Pode verificar e criar contas adicionais."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_list
msgid "Chart of accounts"
msgstr "Plano de contas"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_account_bnk_stmt_check
msgid "Check Closing Balance"
msgstr "Verificar Balanço Final"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_is_difference_zero
msgid "Check if difference is zero."
msgstr "Verificar se a diferença é zero."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_reconcile
msgid ""
"Check this box if this account allows invoices & payments matching of "
"journal items."
msgstr ""
"Marque esta caixa se esta conta permite acerto de faturas e pagamentos com "
"itens do diário."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_refund_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" credit notes made from this journal"
msgstr ""
"Marque esta caixa se não quer partilhar a mesma sequência para faturas e "
"notas de crédito feitas a partir deste diário"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_update_posted
msgid ""
"Check this box if you want to allow the cancellation the entries related to "
"this journal or of the invoice related to this journal"
msgstr ""
"Marque esta caixa se quer permitir o cancelamento de entradas relativas a "
"este diário ou a faturas relativas a este diário"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_price_include
#: model:ir.model.fields,help:account.field_account_tax_template_price_include
msgid ""
"Check this if the price you use on the product and invoices includes this "
"tax."
msgstr "Marcar se o preço que usa no artigo e faturas inclui este imposto."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_reconcile
msgid ""
"Check this option if you want the user to reconcile entries in this account."
msgstr ""
"Marque esta opção se quer que o utilizador reconcilie entradas nesta conta."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Checks"
msgstr "Cheques"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_children_tax_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template_children_tax_ids
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_tax_form
msgid "Children Taxes"
msgstr "Sub-taxa"

#. module: account
#: code:addons/account/models/chart_template.py:155
#: model:ir.actions.act_window,name:account.action_wizard_multi_chart
#, python-format
msgid "Choose Accounting Template"
msgstr "Escolher Modelo Contabilístico"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:131
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr ""

#. module: account
#: model:ir.actions.act_window,help:account.action_account_bank_journal_form
msgid "Click to add a bank account."
msgstr "Clique para adicionar uma conta bancária."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_journal_form
msgid "Click to add a journal."
msgstr "Clique para adicionar um diário."

#. module: account
#: model:ir.actions.act_window,help:account.account_tag_action
msgid "Click to add a new tag."
msgstr "Clique para adicionar uma nova etiqueta."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_form
msgid "Click to add an account."
msgstr "Clique para adicionar uma conta."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_out_refund
msgid "Click to create a credit note."
msgstr "Clique para criar uma nota de crédito."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree1
msgid "Click to create a customer invoice."
msgstr "Clique para criar uma fatura de cliente."

#. module: account
#: model:ir.actions.act_window,help:account.action_move_journal_line
msgid "Click to create a journal entry."
msgstr "Clique para criar uma entrada de diário."

#. module: account
#: model:ir.actions.act_window,help:account.action_view_bank_statement_tree
msgid "Click to create a new cash log."
msgstr "Clique para criar um novo registo de numerário."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_reconcile_model
msgid "Click to create a reconciliation model."
msgstr "Clique para criar um modelo de reconciliação."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_type_form
msgid "Click to define a new account type."
msgstr "Clique para definir um novo tipo de conta."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree2
msgid "Click to record a new vendor bill."
msgstr "Clique para registar uma nova fatura de fornecedor."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_in_refund
msgid "Click to record a new vendor credit note."
msgstr "Clique para registar uma nova nota de crédito de fornecedor."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_tree
msgid "Click to register a bank statement."
msgstr "Clique para registar um extrato de conta."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_payments
#: model:ir.actions.act_window,help:account.action_account_payments_payable
msgid "Click to register a payment"
msgstr "Clique para registar um pagamento"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:260
#, python-format
msgid "Close"
msgstr "Fechar"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:60
#, python-format
msgid "Close statement"
msgstr "Fechar declaração"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_date_done
msgid "Closed On"
msgstr "Fechado Em"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_method_code
#: model:ir.model.fields,field_description:account.field_account_account_code
#: model:ir.model.fields,field_description:account.field_account_account_template_code
#: model:ir.model.fields,field_description:account.field_account_analytic_line_code
#: model:ir.model.fields,field_description:account.field_account_payment_method_code
#: model:ir.model.fields,field_description:account.field_account_payment_payment_method_code
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_method_code
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Code"
msgstr "Código"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group_code_prefix
msgid "Code Prefix"
msgstr "Prefixo do Código"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_coin_value
msgid "Coin/Bill Value"
msgstr "Valor da Moeda/Conta"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Collect customer payments in one-click using Euro SEPA Service."
msgstr ""
"Receber pagamentos de clientes com um clique usando o serviço Euro SEPA"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_color
#: model:ir.model.fields,field_description:account.field_account_journal_color
msgid "Color Index"
msgstr "Índice de Cores"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_label_filter
msgid "Column Label"
msgstr "Rótulo da Coluna"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_comment
msgid "Comment"
msgstr "Comentário"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_commercial_partner_id
#: model:ir.model.fields,help:account.field_account_invoice_report_commercial_partner_id
msgid "Commercial Entity"
msgstr "Entidade Comercial"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_common_menu
msgid "Common Report"
msgstr "Relatório Comum"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Communication"
msgstr "Comunicação"

#. module: account
#: model:ir.model,name:account.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_ref_company_ids
#: model:ir.model.fields,field_description:account.field_res_users_ref_company_ids
msgid "Companies that refers to partner"
msgstr "Empresas relativas ao parceiro"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_company_id
#: model:ir.model.fields,field_description:account.field_account_account_company_id
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_company_id
#: model:ir.model.fields,field_description:account.field_account_balance_report_company_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_company_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_company_id
#: model:ir.model.fields,field_description:account.field_account_chart_template_company_id
#: model:ir.model.fields,field_description:account.field_account_common_account_report_company_id
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_company_id
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_company_id
#: model:ir.model.fields,field_description:account.field_account_common_report_company_id
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_company_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_company_id
#: model:ir.model.fields,field_description:account.field_account_journal_company_id
#: model:ir.model.fields,field_description:account.field_account_move_company_id
#: model:ir.model.fields,field_description:account.field_account_move_line_company_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_company_id
#: model:ir.model.fields,field_description:account.field_account_opening_company_id
#: model:ir.model.fields,field_description:account.field_account_payment_company_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_company_id
#: model:ir.model.fields,field_description:account.field_account_print_journal_company_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_company_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_company_id
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_company_id
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_company_id
#: model:ir.model.fields,field_description:account.field_account_tax_company_id
#: model:ir.model.fields,field_description:account.field_account_tax_report_company_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_company_id
#: model:ir.model.fields,field_description:account.field_accounting_report_company_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_company_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Company"
msgstr "Empresa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line_company_currency_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_company_currency_id
msgid "Company Currency"
msgstr "Moeda da Empresa"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:210
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:25
#, python-format
msgid "Company Data"
msgstr "Dados da Empresa"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_company_data_done
msgid "Company Setup Marked As Done"
msgstr "Configurações da Empresa Concluídas"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_has_chart_of_accounts
msgid "Company has a chart of accounts"
msgstr "Empresa tem um plano de contas"

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_company_id
#: model:ir.model.fields,help:account.field_account_bank_statement_company_id
#: model:ir.model.fields,help:account.field_account_bank_statement_line_company_id
#: model:ir.model.fields,help:account.field_account_journal_company_id
#: model:ir.model.fields,help:account.field_account_move_company_id
#: model:ir.model.fields,help:account.field_account_payment_company_id
#: model:ir.model.fields,help:account.field_account_register_payments_company_id
msgid "Company related to this journal"
msgstr "Empresa relativa a este diário"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Compare actual revenues &amp; costs with budgets"
msgstr "Comparar receitas reais &amp; custos com orçamentos"

#. module: account
#: model:ir.ui.view,arch_db:account.accounting_report_view
msgid "Comparison"
msgstr "Comparação"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_complete_tax_set
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_complete_tax_set
msgid "Complete Set of Taxes"
msgstr "Conjunto Completo de Impostos"

#. module: account
#: code:addons/account/models/account_invoice.py:575
#, python-format
msgid "Compose Email"
msgstr "Compor Email"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Compute tax rates based on U.S. ZIP codes"
msgstr "Calcular as taxas de imposto com base nos códigos postais dos EUA"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_counterpart
msgid ""
"Compute the counter part accounts of this journal item for this journal "
"entry. This can be needed in reports."
msgstr ""
"Calcular as contas contra-partida deste item do diário para esta entrada no "
"diário. Pode ser necessário em relatórios."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_balance_end
msgid "Computed Balance"
msgstr "Balanço Calculado"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_configuration
msgid "Configuration"
msgstr "Configuração"

#. module: account
#: code:addons/account/models/account_payment.py:643
#: code:addons/account/models/account_payment.py:645
#, python-format
msgid "Configuration Error !"
msgstr "Erro de Configuração !"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:29
#, python-format
msgid "Configuration Steps:"
msgstr "Passos para Configuração:"

#. module: account
#: code:addons/account/models/account_invoice.py:462
#, python-format
msgid ""
"Configuration error!\n"
"Could not find any account to create the invoice, are you sure you have a chart of account installed?"
msgstr ""
"Erro de configuração!\n"
"Impossível encontrar uma conta para criar a fatura, tem a certeza que tem um plano de contas instalado?"

#. module: account
#: code:addons/account/models/account.py:443
#, python-format
msgid ""
"Configuration error!\n"
"The currency of the journal should be the same than the default credit account."
msgstr ""
"Erro de configuração!\n"
"A moeda do diário deve ser a mesma que a da conta crédito predefinida."

#. module: account
#: code:addons/account/models/account.py:445
#, python-format
msgid ""
"Configuration error!\n"
"The currency of the journal should be the same than the default debit account."
msgstr ""
"Erro de configuração!\n"
"A moeda do diário deve ser a mesma que a da conta débito predefinida."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Configuration menu"
msgstr "Menu de configuração"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Configure"
msgstr "Configurar"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
#: model:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Confirm"
msgstr "Confirmar"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_confirm
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid "Confirm Draft Invoices"
msgstr "Confirmar Faturas Rascunho"

#. module: account
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid "Confirm Invoices"
msgstr "Confirmar Faturas"

#. module: account
#: model:ir.actions.server,name:account.action_account_confirm_payments
msgid "Confirm Payments"
msgstr "Confirmar Pagamentos"

#. module: account
#: model:ir.model,name:account.model_account_invoice_confirm
msgid "Confirm the selected invoices"
msgstr "Confirmar as faturas selecionadas"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Confirmed"
msgstr "Confirmado"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
msgid ""
"Confirming this will create automatically a journal entry with the "
"difference in the profit/loss account set on the cash journal."
msgstr ""
"Confirmar vai criar automaticamente uma entrada no diário com a diferença no"
" conjunto de lucro/prejuízo da conta definida no diário de numerário."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:52
#, python-format
msgid "Congrats, you're all done!"
msgstr "Parabéns, está tudo pronto!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Congratulations, you're done!"
msgstr "Parabéns, terminou!"

#. module: account
#: model:ir.model,name:account.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: account
#: model:ir.model,name:account.model_account_abstract_payment
msgid ""
"Contains the logic shared between models which allows to register payments"
msgstr ""
"Contém a lógica partilhada entre modelos que permite registar pagamentos"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_contract_ids
#: model:ir.model.fields,field_description:account.field_res_partner_contracts_count
#: model:ir.model.fields,field_description:account.field_res_users_contract_ids
#: model:ir.model.fields,field_description:account.field_res_users_contracts_count
msgid "Contracts"
msgstr "Contratos"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Control-Access"
msgstr "Controlo de Acesso"

#. module: account
#: model:account.account.type,name:account.data_account_type_direct_costs
msgid "Cost of Revenue"
msgstr "Custo de Receitas"

#. module: account
#: code:addons/account/models/chart_template.py:873
#, python-format
msgid ""
"Could not install new chart of account as there are already accounting "
"entries existing"
msgstr ""
"Impossível instalar novo plano de contas pois já existem entradas "
"contabilísticas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_counterpart
msgid "Counterpart"
msgstr "Complemento"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_account_id
msgid "Counterpart Account"
msgstr "Conta de Complementos"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_country_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_country_id
msgid "Country"
msgstr "País"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_country_group_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_country_group_id
msgid "Country Group"
msgstr "Grupo de Países"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_country_id
msgid "Country of the Partner Company"
msgstr "País da Empresa Parceira"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Create Customers"
msgstr "Criar Clientes"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Create Vendors"
msgstr "Criar Fornecedores"

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Create a draft credit note"
msgstr "Criar um rascunho de nota de crédito"

#. module: account
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Create and post move"
msgstr "Criar e remeter movimento"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:288
#, python-format
msgid "Create cash statement"
msgstr "Criar declaração de numerário"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:274
#, python-format
msgid "Create invoice/bill"
msgstr "Criar fatura/conta"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:163
#, python-format
msgid "Create model"
msgstr "Criar modelo"

#. module: account
#: model:ir.actions.act_window,help:account.rounding_list_action
msgid "Create the first cash rounding"
msgstr "Criar o primeiro arredondamento de numerário"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Create your products"
msgstr "Criar os seus artigos"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_create_uid
#: model:ir.model.fields,field_description:account.field_account_account_tag_create_uid
#: model:ir.model.fields,field_description:account.field_account_account_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_account_type_create_uid
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_create_uid
#: model:ir.model.fields,field_description:account.field_account_balance_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_create_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_account_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_financial_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_create_uid
#: model:ir.model.fields,field_description:account.field_account_group_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_create_uid
#: model:ir.model.fields,field_description:account.field_account_journal_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal_create_uid
#: model:ir.model.fields,field_description:account.field_account_opening_create_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal_create_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_create_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_register_payments_create_uid
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_create_uid
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile_create_uid
#: model:ir.model.fields,field_description:account.field_accounting_report_create_uid
#: model:ir.model.fields,field_description:account.field_cash_box_in_create_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out_create_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_create_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move_create_uid
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_create_uid
msgid "Created by"
msgstr "Criada por"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_create_date
#: model:ir.model.fields,field_description:account.field_account_account_tag_create_date
#: model:ir.model.fields,field_description:account.field_account_account_template_create_date
#: model:ir.model.fields,field_description:account.field_account_account_type_create_date
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_create_date
#: model:ir.model.fields,field_description:account.field_account_balance_report_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_create_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_create_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_create_date
#: model:ir.model.fields,field_description:account.field_account_chart_template_create_date
#: model:ir.model.fields,field_description:account.field_account_common_account_report_create_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_create_date
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_create_date
#: model:ir.model.fields,field_description:account.field_account_common_report_create_date
#: model:ir.model.fields,field_description:account.field_account_financial_report_create_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_create_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_create_date
#: model:ir.model.fields,field_description:account.field_account_group_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_line_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_create_date
#: model:ir.model.fields,field_description:account.field_account_journal_create_date
#: model:ir.model.fields,field_description:account.field_account_move_create_date
#: model:ir.model.fields,field_description:account.field_account_move_line_create_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_create_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_create_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal_create_date
#: model:ir.model.fields,field_description:account.field_account_opening_create_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_method_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_create_date
#: model:ir.model.fields,field_description:account.field_account_print_journal_create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_create_date
#: model:ir.model.fields,field_description:account.field_account_register_payments_create_date
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_create_date
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_group_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_report_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_template_create_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile_create_date
#: model:ir.model.fields,field_description:account.field_accounting_report_create_date
#: model:ir.model.fields,field_description:account.field_cash_box_in_create_date
#: model:ir.model.fields,field_description:account.field_cash_box_out_create_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_create_date
#: model:ir.model.fields,field_description:account.field_validate_account_move_create_date
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_create_date
msgid "Created on"
msgstr "Criada em"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_credit
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Credit"
msgstr "Crédito"

#. module: account
#: model:account.account.type,name:account.data_account_type_credit_card
msgid "Credit Card"
msgstr "Cartão de Crédito"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_credit_cash_basis
msgid "Credit Cash Basis"
msgstr "Crédito de Fundo de Caixa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_credit_move_id
msgid "Credit Move"
msgstr "Movimento de Crédito"

#. module: account
#: code:addons/account/models/account_invoice.py:441
#: code:addons/account/models/account_invoice.py:1216
#: code:addons/account/wizard/account_invoice_refund.py:111
#: model:ir.actions.act_window,name:account.action_account_invoice_refund
#: model:ir.model,name:account.model_account_invoice_refund
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#, python-format
msgid "Credit Note"
msgstr "Nota de Crédito"

#. module: account
#: code:addons/account/models/account_invoice.py:442
#, python-format
msgid "Credit Note - %s"
msgstr "Nota de Crédito - %s"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Credit Note Bill"
msgstr "Cobrança da Nota de Crédito"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_date_invoice
msgid "Credit Note Date"
msgstr "Data da Nota de Crédito"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_refund_sequence_id
msgid "Credit Note Entry Sequence"
msgstr "Sequência de Entrada da Nota de Crédito"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Credit Notes"
msgstr "Notas de Crédito"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_refund_sequence_number_next
msgid "Credit Notes: Next Number"
msgstr "Notas de Crédito: Próximo Número"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_credit_account_id
msgid "Credit account"
msgstr "Conta crédito"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_credit
msgid "Credit amount"
msgstr "Quantia a crédito"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_matched_credit_ids
msgid "Credit journal items that are matched with this journal item."
msgstr "Itens do diário de créditos correspondentes a este item do diário."

#. module: account
#: model:ir.ui.menu,name:account.menu_action_currency_form
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Currencies"
msgstr "Moedas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_currency_id
#: model:ir.model.fields,field_description:account.field_account_analytic_line_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_journal_currency_id
#: model:ir.model.fields,field_description:account.field_account_chart_template_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_currency_id
#: model:ir.model.fields,field_description:account.field_account_journal_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line_currency_id
#: model:ir.model.fields,field_description:account.field_account_opening_currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_company_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_currency_id
#: model:ir.model.fields,field_description:account.field_account_payment_currency_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_currency_id
#: model:ir.model.fields,field_description:account.field_res_config_settings_currency_id
#: model:ir.model.fields,field_description:account.field_res_partner_currency_id
#: model:ir.model.fields,field_description:account.field_res_users_currency_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_currency_id
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Currency"
msgstr "Moeda"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_currency_rate
msgid "Currency Rate"
msgstr "Taxa de Câmbio"

#. module: account
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_currency_id
msgid "Currency as per company's country."
msgstr "Moeda do país da empresa."

#. module: account
#: code:addons/account/models/account_move.py:1663
#: code:addons/account/models/account_move.py:1675
#, python-format
msgid "Currency exchange rate difference"
msgstr "Diferença das taxas de câmbio"

#. module: account
#: code:addons/account/models/account.py:451
#, python-format
msgid ""
"Currency field should only be set if the journal's currency is different "
"from the company's. Leave the field blank to use company currency."
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_current_assets
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Current Assets"
msgstr "Ativos Atuais"

#. module: account
#: model:account.account.type,name:account.data_account_type_current_liabilities
msgid "Current Liabilities"
msgstr "Passivos Atuais"

#. module: account
#: model:account.account.type,name:account.data_unaffected_earnings
msgid "Current Year Earnings"
msgstr "Lucros do Ano Corrente"

#. module: account
#: selection:account.abstract.payment,partner_type:0
#: selection:account.payment,partner_type:0
#: selection:account.register.payments,partner_type:0
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_payment_tree
msgid "Customer"
msgstr "Cliente"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_payment.py:677
#, python-format
msgid "Customer Credit Note"
msgstr "Nota de Crédito de Cliente"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_out_refund
#: model:ir.ui.menu,name:account.menu_action_invoice_out_refund
msgid "Customer Credit Notes"
msgstr "Notas de Crédito do Cliente"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Customer Flow"
msgstr "Fluxo de Clientes"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
msgid "Customer Invoice"
msgstr "Fatura de Cliente"

#. module: account
#: code:addons/account/models/chart_template.py:193
#: model:ir.actions.act_window,name:account.action_invoice_tree1
#: model:ir.ui.menu,name:account.menu_action_invoice_tree1
#: model:ir.ui.view,arch_db:account.account_planner
#, python-format
msgid "Customer Invoices"
msgstr "Faturas de Cliente"

#. module: account
#: code:addons/account/models/account_payment.py:675
#, python-format
msgid "Customer Payment"
msgstr "Pagamentos de Cliente"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_payment_term_id
#: model:ir.model.fields,field_description:account.field_res_users_property_payment_term_id
msgid "Customer Payment Terms"
msgstr "Condições de Pagamento de Cliente"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Customer Payments"
msgstr "Pagamentos de Cliente"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_product_taxes_id
#: model:ir.model.fields,field_description:account.field_product_template_taxes_id
msgid "Customer Taxes"
msgstr "Impostos de Cliente"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Customer ref:"
msgstr "Referência do Cliente:"

#. module: account
#: model:ir.ui.menu,name:account.menu_account_customer
msgid "Customers"
msgstr "Clientes"

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "DOWN"
msgstr "PARA BAIXO"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/account_dashboard_setup_bar.js:214
#: model:ir.ui.menu,name:account.menu_board_journal_1
#, python-format
msgid "Dashboard"
msgstr "Painel"

#. module: account
#. openerp-web
#: selection:account.print.journal,sort_selection:0
#: selection:account.report.general.ledger,sortby:0
#: selection:accounting.report,filter_cmp:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:233
#: code:addons/account/static/src/xml/account_reconciliation.xml:248
#: model:ir.model.fields,field_description:account.field_account_bank_statement_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_date
#: model:ir.model.fields,field_description:account.field_account_invoice_report_date
#: model:ir.model.fields,field_description:account.field_account_move_date
#: model:ir.model.fields,field_description:account.field_account_move_line_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_date_p
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_date
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_overdue_document
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Date"
msgstr "Data"

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_opening_date
msgid ""
"Date at which the opening entry of this company's accounting has been "
"posted."
msgstr ""
"Data na qual a primeira entrada contabilística desta empresa foi lançada."

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op_opening_date
msgid ""
"Date from which the accounting is managed in Odoo. It is the date of the "
"opening entry."
msgstr ""
"Data a partir de qual a gestão contabilística é feita no Odoo. É a data da "
"primeira entrada."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:56
#, python-format
msgid "Date:"
msgstr "Data:"

#. module: account
#: model:ir.ui.view,arch_db:account.accounting_report_view
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Dates"
msgstr "Datas"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Day(s) after the end of the invoice month (Net EOM)"
msgstr "Dia(s) após o fim do mês da fatura (30 dias)"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Day(s) after the invoice date"
msgstr "Dia(s) após a data da fatura"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Deactivate setup bar on the dashboard"
msgstr "Desativar barra de configuração no painel"

#. module: account
#: code:addons/account/models/company.py:45
#, python-format
msgid ""
"Dear Sir/Madam,\n"
"\n"
"Our records indicate that some payments on your account are still due. Please find details below.\n"
"If the amount has already been paid, please disregard this notice. Otherwise, please forward us the total amount stated below.\n"
"If you have any queries regarding your account, Please contact us.\n"
"\n"
"Thank you in advance for your cooperation.\n"
"Best Regards,"
msgstr ""
"Exmo. Senhor/Senhora,\n"
"\n"
"Os nossos registos indicam que alguns pagamentos na sua conta, ainda se encontram por liquidar. Por favor, encontre os detalhes abaixo.\n"
"Se o valor já tiver sido pago, por favor, ignore este aviso. Caso contrário, por favor, envie-nos o total indicado abaixo.\n"
"Se tiver quaisquer dúvidas em relação à sua conta, por favor, contacte-nos.\n"
"\n"
"Obrigado antecipadamente pela sua colaboração.\n"
"\n"
"Melhores Cumprimentos,"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_debit
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Debit"
msgstr "Débito"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_debit_cash_basis
msgid "Debit Cash Basis"
msgstr "Débito de Fundo de Caixa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_inbound_payment_method_ids
msgid "Debit Methods"
msgstr "Métodos de Débito"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_debit_move_id
msgid "Debit Move"
msgstr "Movimento de Débito"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_debit_account_id
msgid "Debit account"
msgstr "Conta de débito"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_debit
msgid "Debit amount"
msgstr "Quantia do débito"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_matched_debit_ids
msgid "Debit journal items that are matched with this journal item."
msgstr "Itens do diário de débitos que correspondem a este item do diário."

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "December"
msgstr "Dezembro"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_refund_sequence
msgid "Dedicated Credit Note Sequence"
msgstr "Sequência de Notas de Crédito Dedicada"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_default_credit_account_id
msgid "Default Credit Account"
msgstr "Conta Crédito Predefinida"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_default_debit_account_id
msgid "Default Debit Account"
msgstr "Conta Débito Predefinida"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_default_purchase_tax_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_purchase_tax_id
msgid "Default Purchase Tax"
msgstr "Imposto de Compra Predefinido"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_default_sale_tax_id
msgid "Default Sale Tax"
msgstr "Imposto de Venda Predefinido"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_sale_tax_id
msgid "Default Sales Tax"
msgstr "Imposto de Vendas Predefinido"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tax_ids
#: model:ir.model.fields,field_description:account.field_account_account_template_tax_ids
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_account_chart_template_form
#: model:ir.ui.view,arch_db:account.view_account_template_form
msgid "Default Taxes"
msgstr "Impostos Predefinidos"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Default taxes applied to local transactions"
msgstr "Impostos aplicados a transações locais por defeito"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Deferred Revenues Management"
msgstr "Gestão de Receitas Diferida"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash."
msgstr ""
"Defina a moeda mais pequena da divisa usada para pagamentos a numerário."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_cash_rounding_id
msgid ""
"Defines the smallest coinage of the currency that can be used to pay by "
"cash."
msgstr ""
"Define a moeda mais pequena da divisa que pode ser usada para pagamentos a "
"numerário."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_tax_form
msgid "Definition"
msgstr "Definição"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_trust
#: model:ir.model.fields,field_description:account.field_res_users_trust
msgid "Degree of trust you have in this debtor"
msgstr "Grau de confiança que tem neste devedor"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_deprecated
msgid "Deprecated"
msgstr "Descontinuado"

#. module: account
#: model:account.account.type,name:account.data_account_type_depreciation
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Depreciation"
msgstr "Desvalorização"

#. module: account
#: model:ir.ui.view,arch_db:account.cash_box_out_form
msgid "Describe why you take money from the cash register:"
msgstr "Descreva a razão pela qual tirou dinheiro da caixa:"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:251
#: model:ir.model.fields,field_description:account.field_account_account_type_note
#: model:ir.model.fields,field_description:account.field_account_invoice_line_name
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_account_type_form
#, python-format
msgid "Description"
msgstr "Descrição"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_note
msgid "Description on the Invoice"
msgstr "Descrição na Fatura"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_destination_account_id
msgid "Destination Account"
msgstr "Conta Destino"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_auto_apply
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_auto_apply
msgid "Detect Automatically"
msgstr "Detetar Automaticamente"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_template_type_tax_use
#: model:ir.model.fields,help:account.field_account_tax_type_tax_use
msgid ""
"Determines where the tax is selectable. Note : 'None' means a tax can't be "
"used by itself, however it can still be used in a group."
msgstr ""
"Determina onde o imposto é selecionável. Nota: 'Nenhum' significa que um "
"imposto não pode ser usado por si só, mas talvez possa ser usado num grupo."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_difference
msgid "Difference"
msgstr "Diferença"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_writeoff_account_id
msgid "Difference Account"
msgstr "Conta de Diferenças"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_difference
msgid ""
"Difference between the computed ending balance and the specified ending "
"balance."
msgstr "Diferença entre o saldo final calculado e o saldo final especificado."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Direct connection to your bank"
msgstr "Ligação direta ao seu banco"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Disc.(%)"
msgstr "Desc.(%)"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_wizard_form
msgid "Discard"
msgstr "Rejeitar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_discount
msgid "Discount (%)"
msgstr "Desconto (%)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_balance_report_display_account
#: model:ir.model.fields,field_description:account.field_account_common_account_report_display_account
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_display_account
msgid "Display Accounts"
msgstr "Exibir Contas"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_debit_credit
msgid "Display Debit/Credit Columns"
msgstr "Exibir Colunas de Débito/Crédito"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_display_name
#: model:ir.model.fields,field_description:account.field_account_account_display_name
#: model:ir.model.fields,field_description:account.field_account_account_tag_display_name
#: model:ir.model.fields,field_description:account.field_account_account_template_display_name
#: model:ir.model.fields,field_description:account.field_account_account_type_display_name
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_display_name
#: model:ir.model.fields,field_description:account.field_account_balance_report_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_display_name
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_display_name
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_display_name
#: model:ir.model.fields,field_description:account.field_account_chart_template_display_name
#: model:ir.model.fields,field_description:account.field_account_common_account_report_display_name
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_display_name
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_display_name
#: model:ir.model.fields,field_description:account.field_account_common_report_display_name
#: model:ir.model.fields,field_description:account.field_account_financial_report_display_name
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_display_name
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_display_name
#: model:ir.model.fields,field_description:account.field_account_group_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_line_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_report_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_display_name
#: model:ir.model.fields,field_description:account.field_account_journal_display_name
#: model:ir.model.fields,field_description:account.field_account_move_display_name
#: model:ir.model.fields,field_description:account.field_account_move_line_display_name
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_display_name
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_display_name
#: model:ir.model.fields,field_description:account.field_account_move_reversal_display_name
#: model:ir.model.fields,field_description:account.field_account_opening_display_name
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_method_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_term_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_display_name
#: model:ir.model.fields,field_description:account.field_account_print_journal_display_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_display_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_display_name
#: model:ir.model.fields,field_description:account.field_account_register_payments_display_name
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_display_name
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_group_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_report_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_template_display_name
#: model:ir.model.fields,field_description:account.field_account_unreconcile_display_name
#: model:ir.model.fields,field_description:account.field_accounting_report_display_name
#: model:ir.model.fields,field_description:account.field_cash_box_in_display_name
#: model:ir.model.fields,field_description:account.field_cash_box_out_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_financial_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_generalledger_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_journal_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_overdue_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_partnerledger_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_tax_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_trialbalance_display_name
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_display_name
#: model:ir.model.fields,field_description:account.field_validate_account_move_display_name
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_display_name
msgid "Display Name"
msgstr "Nome a Exibir"

#. module: account
#: selection:account.financial.report,display_detail:0
msgid "Display children flat"
msgstr "Exibir sub-menus lineares"

#. module: account
#: selection:account.financial.report,display_detail:0
msgid "Display children with hierarchy"
msgstr "Exibir sub-menus com hierarquia"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_display_detail
msgid "Display details"
msgstr "Exibir detalhes"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_description
msgid "Display on Invoices"
msgstr "Exibir nas Faturas"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_print_docsaway
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Docsaway"
msgstr "Docsaway"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid ""
"Document: Customer account statement<br/>\n"
"                    Date:"
msgstr ""
"Documento: Extrato de conta do cliente<br/>\n"
"                    Data:"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_payables_documents
#: model:ir.ui.menu,name:account.menu_finance_receivables_documents
msgid "Documents"
msgstr "Documentos"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Don't hesitate to"
msgstr "Não hesite em"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_page
msgid "Download"
msgstr "Transferir"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Download the"
msgstr "Transferir "

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: selection:account.payment,state:0
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Draft"
msgstr "Rascunho"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Draft Bill"
msgstr "Rascunho de Conta"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Draft Credit Note"
msgstr "Rascunho de Nota de Crédito"

#. module: account
#: code:addons/account/models/account_invoice.py:439
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Draft Invoice"
msgstr "Rascunho de Fatura"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Draft Invoices"
msgstr "Rascunho de Faturas"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Draft bills"
msgstr "Rascunho de faturas de fornecedor"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_draft_tree
msgid "Draft statements"
msgstr "Rascunho de extratos"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Due"
msgstr "Vencido"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_residual
msgid "Due Amount"
msgstr "Total Vencido"

#. module: account
#. openerp-web
#: code:addons/account/controllers/portal.py:71
#: code:addons/account/static/src/xml/account_reconciliation.xml:234
#: model:ir.model.fields,field_description:account.field_account_invoice_date_due
#: model:ir.model.fields,field_description:account.field_account_invoice_report_date_due
#: model:ir.ui.view,arch_db:account.portal_my_invoices
#: model:ir.ui.view,arch_db:account.report_overdue_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Due Date"
msgstr "Data de Vencimento"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Due Date Computation"
msgstr "Cálculo da Data de Vencimento"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Due Month"
msgstr "Mês de Vencimento"

#. module: account
#: model:ir.actions.report,name:account.action_report_print_overdue
msgid "Due Payments"
msgstr "Pagamentos Vencidos"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_tree
msgid "Due Type"
msgstr "Tipo de Vencimento"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_date_maturity
msgid "Due date"
msgstr "Data de vencimento"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Duplicate"
msgstr "Duplicar"

#. module: account
#: code:addons/account/models/account_invoice.py:1194
#, python-format
msgid ""
"Duplicated vendor reference detected. You probably encoded twice the same "
"vendor bill/credit note."
msgstr ""
"Detetada referência de fornecedor duplicada. Pode ter codificado a mesma "
"fatura ou nota de crédito de fornecedor duas vezes."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_reports
msgid "Dynamic Reports"
msgstr "Relatórios Dinâmicos"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_l10n_eu_service
msgid "EU Digital Goods VAT"
msgstr "Imposto Europeu Sobre Artigos Digitais"

#. module: account
#: code:addons/account/models/chart_template.py:171
#: code:addons/account/models/chart_template.py:186
#: code:addons/account/models/chart_template.py:196
#, python-format
msgid "EXCH"
msgstr "EXCH"

#. module: account
#: code:addons/account/models/account_move.py:1052
#, python-format
msgid "Either pass both debit and credit or none."
msgstr "Forneça ou débito e crédito ou nenhum."

#. module: account
#: model:ir.model,name:account.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Assistente de composição de e-mail"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_enable_filter
msgid "Enable Comparison"
msgstr "Permitir Comparação"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "End"
msgstr "Terminar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_date_to
#: model:ir.model.fields,field_description:account.field_account_balance_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_account_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_report_date_to
#: model:ir.model.fields,field_description:account.field_account_print_journal_date_to
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_date_to
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_date_to
#: model:ir.model.fields,field_description:account.field_account_tax_report_date_to
#: model:ir.model.fields,field_description:account.field_accounting_report_date_to
#: model:ir.model.fields,field_description:account.field_accounting_report_date_to_cmp
msgid "End Date"
msgstr "Data de Fim"

#. module: account
#: model:account.payment.term,name:account.account_payment_term
msgid "End of Following Month"
msgstr "Fim do Mês Seguinte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_balance_end_real
msgid "Ending Balance"
msgstr "Saldo Final"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_end_id
msgid "Ending Cashbox"
msgstr "Cashbox Final"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Enjoy your Odoo experience,"
msgstr "Desfrute da sua experiência Odoo,"

#. module: account
#: model:ir.actions.act_window,name:account.action_move_line_form
msgid "Entries"
msgstr "Entradas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_print_journal_sort_selection
msgid "Entries Sorted by"
msgstr "Entradas Organizadas por"

#. module: account
#: code:addons/account/models/account_move.py:1019
#, python-format
msgid "Entries are not of the same account!"
msgstr "As entradas não são da mesma conta!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Entries to Review"
msgstr "Entradas a Rever"

#. module: account
#: code:addons/account/models/account_analytic_line.py:58
#, python-format
msgid "Entries: "
msgstr "Entradas:"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "Entry Label"
msgstr "Etiqueta da Entrada"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_sequence_id
msgid "Entry Sequence"
msgstr "Sequência de Entrada"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_move_line_ids
msgid "Entry lines"
msgstr "Linhas de entrada"

#. module: account
#: model:account.account.type,name:account.data_account_type_equity
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Equity"
msgstr "Ações"

#. module: account
#: code:addons/account/models/res_config_settings.py:132
#, python-format
msgid "Error!"
msgstr "Erro!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Excel template"
msgstr "Modelo Excel"

#. module: account
#: code:addons/account/models/chart_template.py:196
#, python-format
msgid "Exchange Difference"
msgstr "Diferença de Câmbio"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_currency_exchange_journal_id
#: model:ir.model.fields,field_description:account.field_res_config_settings_currency_exchange_journal_id
msgid "Exchange Gain or Loss Journal"
msgstr "Diário de Ganho ou Perda Cambial"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_exchange_move_id
msgid "Exchange Move"
msgstr "Movimento de Câmbio"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Exchange rates can be automatically updated once a day from <strong>Yahoo "
"Finance</strong> or the <strong>European Central Bank</strong>. You can "
"activate this feature in the bottom of the"
msgstr ""
"As Taxas de Câmbio podem ser automaticamente atualizadas uma vez por dia "
"através do <strong>Yahoo Finance</strong> ou do  <strong>Banco Central "
"Europeu</strong>. Pode ativar esta função em baixo na"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_expects_chart_of_accounts
msgid "Expects a Chart of Accounts"
msgstr "Espera um Plano de Contas"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_expense0
msgid "Expense"
msgstr "Despesa"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_category_property_account_expense_categ_id
#: model:ir.model.fields,field_description:account.field_product_product_property_account_expense_id
#: model:ir.model.fields,field_description:account.field_product_template_property_account_expense_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Expense Account"
msgstr "Conta para Despesas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_expense_id
msgid "Expense Account on Product Template"
msgstr "Conta para Despesas do Modelo de Artigo"

#. module: account
#: model:account.account.type,name:account.data_account_type_expenses
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Expenses"
msgstr "Despesas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_reference
msgid "External Reference"
msgstr "Referência Externa"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Extra Features"
msgstr "Funções Extra"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Favorites"
msgstr "Favoritos"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "February"
msgstr "Fevereiro"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_state_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_state_ids
msgid "Federal States"
msgstr "Estados Federais"

#. module: account
#: selection:account.journal,bank_statements_source:0
msgid "File Import"
msgstr "Importar Ficheiro"

#. module: account
#: model:ir.ui.view,arch_db:account.cash_box_in_form
msgid "Fill in this form if you put money in the cash register:"
msgstr "Preencha este formulário se colocar dinheiro na caixa:"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_filter_cmp
msgid "Filter by"
msgstr "Filtrado por"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:141
#, python-format
msgid "Filter..."
msgstr "Filtrar..."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_general_account_id
#: model:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
msgid "Financial Account"
msgstr "Conta Financeira"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_style_overwrite
msgid "Financial Report Style"
msgstr "Estilo de Relatório Financeiro"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_financial_report_tree
#: model:ir.actions.act_window,name:account.action_account_report
#: model:ir.ui.menu,name:account.menu_account_reports
msgid "Financial Reports"
msgstr "Relatórios Financeiros"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_fy_data_done
msgid "Financial Year Setup Marked As Done"
msgstr "Configuração do Ano Fiscal Concluída"

#. module: account
#: model:ir.actions.report,name:account.action_report_financial
msgid "Financial report"
msgstr "Relatório financeiro"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_account_setup_fy_data_done
msgid "Financial year setup marked as done"
msgstr "Configuração do ano fiscal concluída"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "First, register any outstanding customer invoices and vendor bills:"
msgstr ""
"Primeiro, registe quaisquer faturas de clientes e contas de fornecedores "
"pendentes:"

#. module: account
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Fiscal Information"
msgstr "Informação Fiscal"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Fiscal Localization"
msgstr "Morada Fiscal"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_position_id
msgid "Fiscal Mapping"
msgstr "Levantamento Fiscal"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Fiscal Periods"
msgstr "Períodos Fiscais"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_fiscal_position_template_form
#: model:ir.model,name:account.model_account_fiscal_position
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_position_id
#: model:ir.model.fields,field_description:account.field_account_invoice_fiscal_position_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_fiscal_position_id
#: model:ir.model.fields,field_description:account.field_res_partner_property_account_position_id
#: model:ir.model.fields,field_description:account.field_res_users_property_account_position_id
#: model:ir.ui.view,arch_db:account.view_account_position_form
#: model:ir.ui.view,arch_db:account.view_account_position_template_search
#: model:ir.ui.view,arch_db:account.view_account_position_template_tree
#: model:ir.ui.view,arch_db:account.view_account_position_tree
msgid "Fiscal Position"
msgstr "Posição Fiscal"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_name
#: model:ir.ui.view,arch_db:account.view_account_position_template_form
#: model:ir.ui.view,arch_db:account.view_account_position_template_search
msgid "Fiscal Position Template"
msgstr "Modelo de Situação Fiscal"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_fiscal_position_form
#: model:ir.ui.menu,name:account.menu_action_account_fiscal_position_form
msgid "Fiscal Positions"
msgstr "Situações Fiscais"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:251
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:17
#, python-format
msgid "Fiscal Year"
msgstr "Ano fiscal"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_fiscalyear_last_day
#: model:ir.model.fields,field_description:account.field_res_company_fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "Último Dia do Ano Fiscal"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_fiscalyear_last_month
#: model:ir.model.fields,field_description:account.field_res_company_fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "Último Mês do Ano Fiscal"

#. module: account
#: selection:account.reconcile.model,amount_type:0
#: selection:account.reconcile.model,second_amount_type:0
#: selection:account.reconcile.model.template,amount_type:0
#: selection:account.reconcile.model.template,second_amount_type:0
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Fixed"
msgstr "Fixo"

#. module: account
#: selection:account.payment.term.line,value:0
msgid "Fixed Amount"
msgstr "Valor Fixo"

#. module: account
#: model:account.account.type,name:account.data_account_type_fixed_assets
msgid "Fixed Assets"
msgstr "Ativos Imóveis"

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_second_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template_second_amount
msgid ""
"Fixed amount will count as a debit if it is negative, as a credit if it is "
"positive."
msgstr ""
"O montante fixo conta como débito se for negativo, como crédito se for "
"positivo."

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_receivables_follow_up
msgid "Follow-up"
msgstr "Acompanhar"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_reports_followup
msgid "Follow-up Levels"
msgstr "Níveis de Acompanhamento"

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_report_sign
msgid ""
"For accounts that are typically more debited than credited and that you "
"would like to print as negative amounts in your reports, you should reverse "
"the sign of the balance; e.g.: Expense account. The same applies for "
"accounts that are typically more credited than debited and that you would "
"like to print as positive amounts in your reports; e.g.: Income account."
msgstr ""
"Para contas que têm tipicamente mais débitos que créditos e que gostaria de "
"imprimir como quantias negativas nos seus relatórios, deve inverter os "
"sinais dos saldos; Ex.: Conta de despesas. O mesmo se aplica às contas que "
"têm tipicamente mais créditos que débitos e que gostaria de imprimir como "
"quantias positivas nos seus relatórios; Ex.: Conta de rendimentos."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line_value_amount
msgid "For percent enter a ratio between 0-100."
msgstr "Em percentagem, coloque um valor entre 0-100."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"For the Odoo Team,<br/>\n"
"                            Fabien Pinckaers, Founder"
msgstr ""
"Para a Equipa Odoo,<br/>\n"
"                            Fabien Pinckaers, Fundador"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_currency_id
msgid "Forces all moves for this account to have this account currency."
msgstr ""
"Força todos os movimentos para esta conta a terem a mesma moeda da conta."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_currency_id
#: model:ir.model.fields,help:account.field_account_bank_accounts_wizard_currency_id
msgid "Forces all moves for this account to have this secondary currency."
msgstr ""
"Força todos os movimentos para esta conta a terem esta moeda secundária."

#. module: account
#: code:addons/account/report/account_aged_partner_balance.py:205
#: code:addons/account/report/account_balance.py:64
#: code:addons/account/report/account_general_ledger.py:114
#: code:addons/account/report/account_journal.py:100
#: code:addons/account/report/account_partner_ledger.py:74
#: code:addons/account/report/account_report_financial.py:149
#: code:addons/account/report/account_tax.py:13
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr "Conteúdo do formulário em falta, o relatório não pode ser impresso."

#. module: account
#: code:addons/account/models/account_invoice.py:96
#, python-format
msgid "Free Reference"
msgstr "Referência Livre"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "From Payable accounts"
msgstr "De contas a pagar"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "From Receivable accounts"
msgstr "De contas a receber"

#. module: account
#: model:ir.actions.act_window,help:account.action_account_invoice_report_all_supp
msgid ""
"From this report, you can have an overview of the amount invoiced from your "
"vendors. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"A partir deste relatório, consegue uma visão geral da quantia cobrada pelos "
"seus fornecedores. A ferramenta de pesquisa também pode ser usada para "
"personalizar os seus relatórios de Fatura e desta forma ajustar a análise às"
" suas necessidades."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_invoice_report_all
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customers. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"A partir deste relatório, consegue uma visão geral da quantia cobrada aos "
"seus clientes. A ferramenta de pesquisa também pode ser usada para "
"personalizar os seus relatórios de Fatura e desta forma ajustar a análise às"
" suas necessidades."

#. module: account
#: model:ir.model,name:account.model_account_full_reconcile
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_full_reconcile_id
msgid "Full Reconcile"
msgstr "Reconciliar Tudo"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:113
#, python-format
msgid "Future"
msgstr "Futuro"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Future Activities"
msgstr "Atividades Futuras"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "GROSS PROFIT"
msgstr "RENDIMENTO BRUTO"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_income_currency_exchange_account_id
#: model:ir.model.fields,field_description:account.field_res_company_income_currency_exchange_account_id
msgid "Gain Exchange Rate Account"
msgstr "Conta de Ganhos de Câmbio"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_general_ledger_menu
#: model:ir.actions.report,name:account.action_report_general_ledger
#: model:ir.ui.menu,name:account.menu_general_ledger
msgid "General Ledger"
msgstr "Registo Geral"

#. module: account
#: model:ir.model,name:account.model_account_report_general_ledger
msgid "General Ledger Report"
msgstr "Relatório de Registo Geral"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_generate_entries
msgid "Generate Entries"
msgstr "Gerar Entradas"

#. module: account
#: model:ir.ui.menu,name:account.account_reports_legal_statements_menu
msgid "Generic Statements"
msgstr "Declarações Genéricas"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Get started"
msgstr "Começar"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Get warnings when invoicing specific customers"
msgstr "Receba avisos quando fatura clientes específicos"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Get your bank statements automatically imported every 4 hours, or in one-"
"click, using Yodlee and Plaid services. Once installed, set “Bank Feeds” to "
"“Bank Synchronization” in bank account settings. Then, click “Configure” on "
"the online account to enter your bank credentials."
msgstr ""
"Receba os seus extratos bancários automaticamente a cada 4 horas ou num só "
"clique, usando os serviços Yodlee e Plaid. Uma vez instalados, defina os "
"\"Feeds Bancários\" para \"Sincronização Bancária\" nas configurações de "
"conta bancária. Depois clique em \"Configurar\" nas contas online para "
"introduzir as suas credenciais bancárias."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_sequence
msgid "Gives the sequence of this line when displaying the invoice."
msgstr "Devolve a sequência desta linha quando mostra a fatura."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_sequence
msgid ""
"Gives the sequence order when displaying a list of bank statement lines."
msgstr ""
"Devolva a ordem da sequência quando mostra uma lista de linhas de extrato "
"bancário."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_tax_sequence
msgid "Gives the sequence order when displaying a list of invoice tax."
msgstr ""
"Devolve a ordem da sequência quando mostra uma lista de impostos da fatura."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line_sequence
msgid ""
"Gives the sequence order when displaying a list of payment terms lines."
msgstr ""
"Devolve a ordem da sequência quando mostra uma lista de linhas de condições "
"de pagamento."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:57
#, python-format
msgid "Go to bank statement(s)"
msgstr "Ir para extrato(s) bancário(s)"

#. module: account
#: code:addons/account/models/account_invoice.py:642
#, python-format
msgid "Go to the configuration panel"
msgstr "Ir para o painel de configuração"

#. module: account
#: selection:res.partner,trust:0
msgid "Good Debtor"
msgstr "Bom Devedor"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:46
#, python-format
msgid "Good Job!"
msgstr "Bom Trabalho!"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_group_id
#: model:ir.model.fields,field_description:account.field_account_account_template_group_id
msgid "Group"
msgstr "Grupo"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_search
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#: model:ir.ui.view,arch_db:account.view_account_template_search
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Group By"
msgstr "Agrupar por"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_group_invoice_lines
msgid "Group Invoice Lines"
msgstr "Agrupar Linhas de Fatura"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Group of Taxes"
msgstr "Grupo Fiscal"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Group received checks before depositing them to the bank"
msgstr "Cheques recebidos pelo grupo antes de serem depositados no banco"

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "HALF-UP"
msgstr "MEIO"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_has_accounting_entries
msgid "Has Accounting Entries"
msgstr "Tem Entradas Contabilísticas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_has_invoices
msgid "Has Invoices"
msgstr "Tem Faturas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_has_outstanding
msgid "Has Outstanding"
msgstr "Tem Pendentes"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_has_unreconciled_entries
#: model:ir.model.fields,field_description:account.field_res_users_has_unreconciled_entries
msgid "Has Unreconciled Entries"
msgstr "Tem Entradas Não Reconciliadas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_hide_payment_method
#: model:ir.model.fields,field_description:account.field_account_payment_hide_payment_method
#: model:ir.model.fields,field_description:account.field_account_register_payments_hide_payment_method
msgid "Hide Payment Method"
msgstr "Ocultar Método de Pagamento"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_account_hide_setup_bar
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Hide Setup Bar"
msgstr "Ocultar Barra de Configuração"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_hide_tax_exigibility
msgid "Hide Use Cash Basis Option"
msgstr "Ocultar Opção de Uso de Fundo de Caixa"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "How do account types affect your reports?"
msgstr "Como é que os tipos de conta afetam os seus relatórios?"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "How total tax amount is computed in orders and invoices"
msgstr "Como o total do imposto é calculado em encomendas e faturas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_id
#: model:ir.model.fields,field_description:account.field_account_account_id
#: model:ir.model.fields,field_description:account.field_account_account_tag_id
#: model:ir.model.fields,field_description:account.field_account_account_template_id
#: model:ir.model.fields,field_description:account.field_account_account_type_id
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_id
#: model:ir.model.fields,field_description:account.field_account_balance_report_id
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_id
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_id
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_id
#: model:ir.model.fields,field_description:account.field_account_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_common_account_report_id
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_id
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_id
#: model:ir.model.fields,field_description:account.field_account_common_report_id
#: model:ir.model.fields,field_description:account.field_account_financial_report_id
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_id
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_id
#: model:ir.model.fields,field_description:account.field_account_group_id
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_id
#: model:ir.model.fields,field_description:account.field_account_invoice_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_id
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_id
#: model:ir.model.fields,field_description:account.field_account_journal_id
#: model:ir.model.fields,field_description:account.field_account_move_id
#: model:ir.model.fields,field_description:account.field_account_move_line_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_id
#: model:ir.model.fields,field_description:account.field_account_move_reversal_id
#: model:ir.model.fields,field_description:account.field_account_opening_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_id
#: model:ir.model.fields,field_description:account.field_account_payment_id
#: model:ir.model.fields,field_description:account.field_account_payment_method_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_id
#: model:ir.model.fields,field_description:account.field_account_print_journal_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_id
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_id
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_id
#: model:ir.model.fields,field_description:account.field_account_tax_group_id
#: model:ir.model.fields,field_description:account.field_account_tax_id
#: model:ir.model.fields,field_description:account.field_account_tax_report_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_id
#: model:ir.model.fields,field_description:account.field_account_unreconcile_id
#: model:ir.model.fields,field_description:account.field_accounting_report_id
#: model:ir.model.fields,field_description:account.field_cash_box_in_id
#: model:ir.model.fields,field_description:account.field_cash_box_out_id
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance_id
#: model:ir.model.fields,field_description:account.field_report_account_report_financial_id
#: model:ir.model.fields,field_description:account.field_report_account_report_generalledger_id
#: model:ir.model.fields,field_description:account.field_report_account_report_journal_id
#: model:ir.model.fields,field_description:account.field_report_account_report_overdue_id
#: model:ir.model.fields,field_description:account.field_report_account_report_partnerledger_id
#: model:ir.model.fields,field_description:account.field_report_account_report_tax_id
#: model:ir.model.fields,field_description:account.field_report_account_report_trialbalance_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_id
#: model:ir.model.fields,field_description:account.field_validate_account_move_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_id
msgid "ID"
msgstr "Id."

#. module: account
#: code:addons/account/models/chart_template.py:193
#, python-format
msgid "INV"
msgstr "FT"

#. module: account
#: code:addons/account/models/account_bank_statement.py:389
#, python-format
msgid "If \"Amount Currency\" is specified, then \"Amount\" must be as well."
msgstr ""
"Se a \"Moeda do Montante\" for especificada, então o \"Montante\" também tem"
" de ser."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_nocreate
msgid ""
"If checked, the new chart of accounts will not contain this by default."
msgstr "Se o marcar, o novo plano de contas não o vai conter por defeito."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_reversal_journal_id
msgid "If empty, uses the journal of the journal entry to be reversed."
msgstr "Se ficar vazio, usa o diário da entrada do diário a ser estornada."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_include_base_amount
#: model:ir.model.fields,help:account.field_account_tax_template_include_base_amount
msgid ""
"If set, taxes which are computed after this one will be computed based on "
"the price tax included."
msgstr ""
"Se for definido, os impostos calculados depois deste serão calculados com "
"base na tarifa fiscal incluída."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_analytic
#: model:ir.model.fields,help:account.field_account_tax_template_analytic
msgid ""
"If set, the amount computed by this tax will be assigned to the same "
"analytic account as the invoice line (if any)"
msgstr ""
"Se for definido, o montante calculado por este imposto será associado à "
"mesma conta analítica que a linha de fatura (se existir)"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_active
msgid ""
"If the active field is set to False, it will allow you to hide the payment "
"terms without removing it."
msgstr ""
"Se o campo ativo for definido como Falso, permite-lhe ocultar as condições "
"de pagamento sem as remover."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_group_invoice_lines
msgid ""
"If this box is checked, the system will try to group the accounting lines "
"when generating them from invoices."
msgstr ""
"Se esta caixa for marcada, o sistema tentará agrupar as linhas "
"contabilísticas quando as gerar nas faturas."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you check this box, you will be able to collect payments using SEPA "
"Direct Debit mandates."
msgstr ""
"Marcar esta caixa permite-lhe receber pagamentos usando autorizações de "
"débito direto da SEPA."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you check this box, you will be able to register your payment using SEPA."
msgstr "Marcar esta caixa permite-lhe efetuar pagamentos via SEPA."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"If you have less than 200 contacts, we recommend you\n"
"                                        create them manually."
msgstr ""
"Se tiver menos de 200 contactos, recomendamos que\n"
"                                        os crie manualmente."

#. module: account
#: model:ir.model.fields,help:account.field_account_report_general_ledger_initial_balance
msgid ""
"If you selected date, this field allow you to add a row to display the "
"amount of debit/credit/balance that precedes the filter you've set."
msgstr ""
"Se selecionou a data, este campo permite-lhe adicionar uma linha para exibir"
" o montante de débito/crédito/saldo que precede o filtro definido."

#. module: account
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
msgid ""
"If you unreconcile transactions, you must also verify all the actions that "
"are linked to those transactions because they will not be disabled"
msgstr ""
"Se anular a reconciliação de transações, deve também verificar todas as "
"ações que estão ligadas a essas transações, pois não vão ser desabilitadas."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_payment_term_id
msgid ""
"If you use payment terms, the due date will be computed automatically at the"
" generation of accounting entries. If you keep the payment terms and the due"
" date empty, it means direct payment. The payment terms may compute several "
"due dates, for example 50% now, 50% in one month."
msgstr ""
"Se usar condições de pagamento, a data de vencimento vai ser calculada "
"automaticamente aquando da geração de entradas contabilísticas. Se deixar as"
" condições de pagamento e a data de vencimento vazios, significa pagamento "
"direto. Os períodos de pagamento podem calcular várias datas de vencimento, "
"por exemplo 50% agora, 50% dentro de um mês."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_date_due
msgid ""
"If you use payment terms, the due date will be computed automatically at the"
" generation of accounting entries. The Payment terms may compute several due"
" dates, for example 50% now and 50% in one month, but if you want to force a"
" due date, make sure that the payment term is not set on the invoice. If you"
" keep the Payment terms and the due date empty, it means direct payment."
msgstr ""
"Se usar condições de pagamento, a data de vencimento vai ser calculada "
"automaticamente aquando da geração de entradas contabilísticas. As condições"
" de pagamento podem calcular várias datas de vencimento, por exemplo 50% "
"agora, 50% dentro de um mês, mas se quiser forçar uma data de vencimento, "
"assegure-se que o período de pagamento não está definido na fatura. Se "
"deixar as condições de pagamento e a data de vencimento vazios, significa "
"pagamento direto."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"If you want to be able to send customer statements from Odoo, you must:"
msgstr "Se quiser enviar declarações de cliente através do Odoo, deve:"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"If you want to be able to send your customers their statements \n"
"                        from Odoo, you first need to record all outstanding transactions \n"
"                        in the payable and receivable accounts. These would be invoices \n"
"                        that have not been paid or payments that have not been reconciled."
msgstr ""
"Se quiser enviar aos clientes as suas declarações através do Odoo, \n"
"                        primeiro precisa de registar todas as transações pendentes \n"
"                        nas contas a pagar e a receber. Estas podem ser faturas que \n"
"                        não foram pagas ou pagamentos ainda não reconciliados."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "If you want to do it yourself:"
msgstr "Se quiser fazer você mesmo:"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you're selling digital goods to customers in the EU, you must charge VAT "
"based on your customers' locations. This rule applies regardless of you are "
"located. Digital goods are defined in the legislation as broadcasting, "
"telecommunications, and services that are electronically supplied instead of"
" shipped. Gift cards sent online are not included in the definition."
msgstr ""
"Se estiver a vender artigos digitais a clientes na UE, deve cobrar o IVA com"
" base nas localizações dos seus clientes. Esta regra aplica-se "
"independentemente da sua localização. Os artigos digitais estão definidos na"
" legislação como transmissões, telecomunicações, e serviços que são "
"fornecidos eletronicamente em vez de enviados. Os cartões de oferta enviados"
" on-line não estão incluídos nesta definição."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_product_image
msgid ""
"Image of the product variant (Big-sized image of product template if false)."
" It is automatically resized as a 1024x1024px image, with aspect ratio "
"preserved."
msgstr ""
"Imagem da variante do artigo (Ou uma imagem grande do modelo de artigo, se "
"falso). É automaticamente redimensionada como uma imagem de 1024x1024px, "
"preservando as proporções originais."

#. module: account
#: model:account.payment.term,name:account.account_payment_term_immediate
msgid "Immediate Payment"
msgstr "Pagamento Imediato"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_qif
msgid "Import .qif files"
msgstr "Importar ficheiros .qif"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_csv
msgid "Import in .csv format"
msgstr "Importar em formato .csv"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_ofx
msgid "Import in .ofx format"
msgstr "Importar em formato .ofx"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_camt
msgid "Import in CAMT.053 format"
msgstr "Importar em formato CAMT.053"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Import using the \"Import\" button on the top left corner of"
msgstr "Importe clicando no botão \"Importar\" no canto superior esquerdo de"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements automatically"
msgstr "Importe os seus extratos bancários automaticamente"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in CAMT.053"
msgstr "Importe os seus extratos bancários em CAMT.053"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in CSV"
msgstr "Importe os seus extratos bancários em CSV"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in OFX"
msgstr "Importe os seus extratos bancários em OFX"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in QIF"
msgstr "Importe os seus extratos bancários em QIF"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Importing your statements in via a supported file format (QIF, OFX, CODA or "
"CSV format)"
msgstr ""
"Importando os seus extratos através de um formato suportado (formato QIF, "
"OFX, CODA ou CSV)"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "In Odoo,"
msgstr "No Odoo,"

#. module: account
#: code:addons/account/models/account_bank_statement.py:409
#, python-format
msgid ""
"In order to delete a bank statement line, you must first cancel it to delete"
" related journal items."
msgstr ""
"Para apagar uma linha de extrato bancário, deve primeiro cancelá-lo para "
"apagar os itens do diário relativos ao extrato."

#. module: account
#: code:addons/account/models/account_bank_statement.py:199
#, python-format
msgid ""
"In order to delete a bank statement, you must first cancel it to delete "
"related journal items."
msgstr ""
"Para apagar um extrato bancário, deve primeiro cancelá-lo para apagar os "
"itens do diário relativos ao extrato."

#. module: account
#: code:addons/account/models/account_payment.py:144
#, python-format
msgid ""
"In order to pay multiple invoices at once, they must use the same currency."
msgstr ""
"Para pagar várias faturas em simultâneo, estas têm de usar a mesma moeda."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "In your old accounting software, print a trial balance"
msgstr ""
"Imprima um relatório de contas no seu software de contabilidade antigo"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Inactive"
msgstr "Inativo"

#. module: account
#: selection:account.payment.method,payment_type:0
msgid "Inbound"
msgstr "A Receber"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_initial_balance
msgid "Include Initial Balances"
msgstr "Incluir Saldos Iniciais"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_analytic
msgid "Include in Analytic Cost"
msgstr "Incluir Custo Analítico"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_price_include
#: model:ir.model.fields,field_description:account.field_account_tax_template_price_include
msgid "Included in Price"
msgstr "Incluído no Preço"

#. module: account
#: model:account.account.type,name:account.data_account_type_revenue
#: model:account.financial.report,name:account.account_financial_report_income0
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Income"
msgstr "Receitas"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_category_property_account_income_categ_id
#: model:ir.model.fields,field_description:account.field_product_product_property_account_income_id
#: model:ir.model.fields,field_description:account.field_product_template_property_account_income_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Income Account"
msgstr "Conta de Receitas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_income_id
msgid "Income Account on Product Template"
msgstr "Conta de Receitas no Modelo de Artigo"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:700
#, python-format
msgid "Incorrect Operation"
msgstr "Operação Incorreta"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Information"
msgstr "Informação"

#. module: account
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
msgid "Information addendum"
msgstr "Adenda informativa"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:296
#: code:addons/account/models/company.py:311
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:9
#, python-format
msgid "Initial Balances"
msgstr "Saldos Iniciais"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_stock_account_input_categ_id
#: model:ir.model.fields,field_description:account.field_res_company_property_stock_account_input_categ_id
msgid "Input Account for Stock Valuation"
msgstr "Introduzir Conta para Valoração de Ações"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Install Chart of Account"
msgstr "Instalar Plano de Contas"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Install More Packages"
msgstr "Instalar Mais Packages"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_transfer_account_id
msgid "Inter-Banks Transfer Account"
msgstr "Conta de Transferências Bancárias"

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_transfer_account_id
#: model:ir.model.fields,help:account.field_res_company_transfer_account_id
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_transfer_account_id
msgid ""
"Intermediary account used when moving money from a liquidity account to "
"another"
msgstr ""
"Conta intermedia usada quando se transfere dinheiro de uma conta de liquidez"
" para outra"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_narration
msgid "Internal Note"
msgstr "Nota Interna"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_note
msgid "Internal Notes"
msgstr "Notas Internas"

#. module: account
#: selection:account.payment,payment_type:0
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Internal Transfer"
msgstr "Transferência Interna"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_internal_type
msgid "Internal Type"
msgstr "Tipo Interno"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_template_form
msgid "Internal notes..."
msgstr "Notas internas..."

#. module: account
#: code:addons/account/models/partner.py:45
#, python-format
msgid "Invalid \"Zip Range\", please configure it properly."
msgstr ""
"\"Gama de Códigos Postais\" inválida, por favor faça a configuração "
"corretamente."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Invite Users"
msgstr "Convidar Utilizadores"

#. module: account
#: code:addons/account/models/account_invoice.py:1214
#: model:ir.model,name:account.model_account_invoice
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_invoice_id
#: model:ir.model.fields,field_description:account.field_account_move_line_invoice_id
#: model:ir.model.fields,field_description:account.field_res_partner_invoice_warn
#: model:ir.model.fields,field_description:account.field_res_users_invoice_warn
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:res.request.link,name:account.req_link_invoice
#, python-format
msgid "Invoice"
msgstr "Fatura"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid "Invoice #"
msgstr "Fatura #"

#. module: account
#: code:addons/account/models/account_invoice.py:440
#, python-format
msgid "Invoice - %s"
msgstr "Fatura - %s"

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_created
#: model:mail.message.subtype,name:account.mt_invoice_created
msgid "Invoice Created"
msgstr "Fatura Criada"

#. module: account
#: code:addons/account/controllers/portal.py:70
#: model:ir.model.fields,field_description:account.field_account_invoice_date_invoice
#: model:ir.ui.view,arch_db:account.portal_my_invoices
#: model:ir.ui.view,arch_db:account.report_payment_receipt
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Invoice Date"
msgstr "Data da Fatura"

#. module: account
#: model:ir.model,name:account.model_account_invoice_line
#: model:ir.ui.view,arch_db:account.view_invoice_line_form
#: model:ir.ui.view,arch_db:account.view_invoice_line_tree
msgid "Invoice Line"
msgstr "Linha da Fatura"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_invoice_line_ids
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Invoice Lines"
msgstr "Linhas da Fatura"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Invoice Number"
msgstr "Número da Fatura"

#. module: account
#: sql_constraint:account.invoice:0
msgid "Invoice Number must be unique per Company!"
msgstr "O Número da Fatura tem que ser único por cada Empresa!"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Invoice Number:"
msgstr "Número da Fatura:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_invoice_id
msgid "Invoice Reference"
msgstr "Referência da Fatura"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_state
msgid "Invoice Status"
msgstr "Estado da Fatura"

#. module: account
#: model:ir.model,name:account.model_account_invoice_tax
msgid "Invoice Tax"
msgstr "Imposto da Fatura"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_invoice_id
msgid "Invoice for which this invoice is the credit note"
msgstr "Fatura para a qual esta fatura é a nota de crédito"

#. module: account
#: code:addons/account/models/account_invoice.py:753
#, python-format
msgid "Invoice must be cancelled in order to reset it to draft."
msgstr "Fatura deve ser cancelada de modo a voltar ao estado Rascunho."

#. module: account
#: code:addons/account/models/account_invoice.py:801
#, python-format
msgid "Invoice must be in draft or open state in order to be cancelled."
msgstr ""
"Fatura deve estar em estado Rascunho ou Aberto para poder ser cancelada."

#. module: account
#: code:addons/account/models/account_invoice.py:775
#, python-format
msgid "Invoice must be in draft state in order to validate it."
msgstr "Fatura deve estar em estado Rascunho para poder ser validada."

#. module: account
#: code:addons/account/models/account_invoice.py:795
#, python-format
msgid "Invoice must be paid in order to set it to register payment."
msgstr "Fatura deve estar no estado Pago para se poder registar o pagamento."

#. module: account
#: code:addons/account/models/account_invoice.py:787
#, python-format
msgid "Invoice must be validated in order to set it to register payment."
msgstr "Fatura deve ser validada para poder registar o pagamento."

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_paid
msgid "Invoice paid"
msgstr "Fatura paga"

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_validated
msgid "Invoice validated"
msgstr "Fatura validada"

#. module: account
#: model:mail.template,report_name:account.email_template_edi_invoice
msgid ""
"Invoice_${(object.number or '').replace('/','_')}_${object.state == 'draft' "
"and 'draft' or ''}"
msgstr ""
"Invoice_${(object.number or '').replace('/','_')}_${object.state == 'draft' "
"e 'draft' ou ''}"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Invoiced"
msgstr "Faturado"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_refund_out_tree
#: model:ir.actions.act_window,name:account.action_invoice_tree
#: model:ir.actions.report,name:account.account_invoices
#: model:ir.model.fields,field_description:account.field_account_payment_invoice_ids
#: model:ir.model.fields,field_description:account.field_account_register_payments_invoice_ids
#: model:ir.model.fields,field_description:account.field_res_partner_invoice_ids
#: model:ir.model.fields,field_description:account.field_res_users_invoice_ids
#: model:ir.ui.menu,name:account.menu_action_account_invoice_report_all
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.portal_my_home_invoice
#: model:ir.ui.view,arch_db:account.portal_my_home_menu_invoice
#: model:ir.ui.view,arch_db:account.portal_my_invoices
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_account_payment_form
#: model:ir.ui.view,arch_db:account.view_account_payment_graph
#: model:ir.ui.view,arch_db:account.view_invoice_graph
#: model:ir.ui.view,arch_db:account.view_invoice_line_calendar
#: model:ir.ui.view,arch_db:account.view_invoice_pivot
msgid "Invoices"
msgstr "Faturas"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_report_all
#: model:ir.actions.act_window,name:account.action_account_invoice_report_all_supp
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_graph
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_pivot
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Invoices Analysis"
msgstr "Análise de Faturas"

#. module: account
#: model:ir.model,name:account.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Estatísticas de Faturas"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:186
#, python-format
msgid "Invoices owed to you"
msgstr "Faturas a receber"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Invoices to validate"
msgstr "Faturas por validar"

#. module: account
#: model:ir.actions.report,name:account.account_invoices_without_payment
msgid "Invoices without Payment"
msgstr "Faturas sem Pagamento"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance
#: model:ir.ui.view,arch_db:account.product_template_form_view
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Invoicing"
msgstr "Faturação"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_is_unaffected_earnings_line
msgid "Is Unaffected Earnings Line"
msgstr "É Uma Linha de Rendimentos Inalterada"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_is_rounding_line
msgid "Is a rounding line in case of cash rounding."
msgstr ""
"É uma linha de arredondamentos no caso de arredondamento de numerário."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_is_difference_zero
msgid "Is zero"
msgstr "É zero"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_default_credit_account_id
#: model:ir.model.fields,help:account.field_res_company_income_currency_exchange_account_id
msgid "It acts as a default account for credit amount"
msgstr "Age como uma conta de crédito por defeito"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_default_debit_account_id
#: model:ir.model.fields,help:account.field_res_company_expense_currency_exchange_account_id
msgid "It acts as a default account for debit amount"
msgstr "Age como uma conta de débito por defeito"

#. module: account
#: model:ir.model.fields,help:account.field_account_report_partner_ledger_amount_currency
msgid ""
"It adds the currency column on report if the currency differs from the "
"company currency."
msgstr ""
"Acrescenta a coluna de moeda ao relatório se a moeda for diferente da moeda "
"da empresa."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_reconciled
msgid ""
"It indicates that the invoice has been paid and the journal entry of the "
"invoice has been reconciled with one or several journal entries of payment."
msgstr ""
"Indica que a fatura foi paga e que a entrada no diário da fatura foi "
"reconciliada com uma ou várias entradas de pagamento no diário."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_sent
msgid "It indicates that the invoice has been sent."
msgstr "Indica que a fatura foi enviada."

#. module: account
#: code:addons/account/models/account_move.py:1050
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr ""
"É necessário especificar uma conta e um diário para criar uma liquidação."

#. module: account
#: code:addons/account/models/account_payment.py:470
#, python-format
msgid ""
"It is not allowed to delete a payment that already created a journal entry "
"since it would create a gap in the numbering. You should create the journal "
"entry again and cancel it thanks to a regular revert."
msgstr ""
"Não é permitido apagar um pagamento que já criou uma entrada no diário pois "
"iria criar uma lacuna na numeração. Deve criar a entrada do diário de novo e"
" cancelá-la usando uma reversão regular."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"It's common practice to change your accounting software \n"
"                            at the end of a fiscal year. This allows you to have less \n"
"                            data to import and balances to set. If you plan to do so, \n"
"                            we recommend you start using Odoo for invoicing and payments \n"
"                            now, and then move all other accounting transactions at a later time."
msgstr ""
"É prática comum alterar o seu software contabilístico \n"
"                            no fim do ano fiscal. Isto permite-lhe ter menos \n"
"                            dados para importar e saldos a definir. Se está a  pensar em fazê-lo, \n"
"                           , recomendamos que comece a usar o Odoo para faturação e pagamentos primeiro\n"
"                             e mais tarde proceda às restantes transações contabilísticas."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"It's recommended that you do not delete any accounts, even if they are not "
"relevant. Simply make them inactive."
msgstr ""
"Recomendamos que não apague nenhuma conta, mesmo que já não lhe sejam "
"relevantes. Basta marcá-las como inativas."

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Italic Text (smaller)"
msgstr "Texto Itálico (mais pequeno)"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Items"
msgstr "Itens"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
msgid "JRNL"
msgstr "DIÁR"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "January"
msgstr "Janeiro"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:185
#: code:addons/account/static/src/xml/account_reconciliation.xml:229
#: model:ir.model,name:account.model_account_journal
#: model:ir.model.fields,field_description:account.field_account_bank_statement_journal_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_journal_id
#: model:ir.model.fields,field_description:account.field_account_invoice_journal_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_journal_id
#: model:ir.model.fields,field_description:account.field_account_move_journal_id
#: model:ir.model.fields,field_description:account.field_account_move_line_journal_id
#: model:ir.model.fields,field_description:account.field_account_opening_journal_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_journal_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_journal_id
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_journal_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Journal"
msgstr "Diário"

#. module: account
#: selection:account.report.general.ledger,sortby:0
msgid "Journal & Partner"
msgstr "Diário e Parceiro"

#. module: account
#: code:addons/account/models/account_bank_statement.py:254
#: model:ir.actions.act_window,name:account.action_move_journal_line
#: model:ir.actions.act_window,name:account.action_move_select
#: model:ir.ui.menu,name:account.menu_action_move_journal_line_form
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#: model:ir.ui.view,arch_db:account.view_move_tree
#, python-format
msgid "Journal Entries"
msgstr "Entradas no Diário"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Journal Entries by Month"
msgstr "Entradas no Diário por Mês"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_move_id
#: model:ir.model.fields,field_description:account.field_account_move_line_move_id
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Journal Entry"
msgstr "Entrada no Diário"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_move_name
#: model:ir.model.fields,field_description:account.field_account_invoice_move_name
#: model:ir.model.fields,field_description:account.field_account_payment_move_name
msgid "Journal Entry Name"
msgstr "Nome da Entrada no Diário"

#. module: account
#: selection:account.print.journal,sort_selection:0
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Journal Entry Number"
msgstr "Número da Entrada no Diário"

#. module: account
#: model:ir.model,name:account.model_account_move_line
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Journal Item"
msgstr "Item do Diário"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_writeoff_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_label
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Journal Item Label"
msgstr "Etiqueta do Item do Diário"

#. module: account
#: code:addons/account/models/account_payment.py:414
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_move_line
#: model:ir.actions.act_window,name:account.act_account_move_to_account_move_line_open
#: model:ir.actions.act_window,name:account.action_account_moves_all_a
#: model:ir.actions.act_window,name:account.action_account_moves_all_tree
#: model:ir.actions.act_window,name:account.action_move_line_graph
#: model:ir.actions.act_window,name:account.action_move_line_graph_cash_basis
#: model:ir.actions.act_window,name:account.action_move_line_graph_cash_basis_posted
#: model:ir.actions.act_window,name:account.action_move_line_graph_posted
#: model:ir.actions.act_window,name:account.action_move_line_select
#: model:ir.actions.act_window,name:account.action_move_line_select_by_partner
#: model:ir.actions.act_window,name:account.action_move_line_select_by_type
#: model:ir.actions.act_window,name:account.action_move_line_select_tax_audit
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_journal_entry_ids
#: model:ir.model.fields,field_description:account.field_account_move_line_ids
#: model:ir.model.fields,field_description:account.field_res_partner_journal_item_count
#: model:ir.model.fields,field_description:account.field_res_users_journal_item_count
#: model:ir.ui.menu,name:account.menu_action_account_moves_all
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.setup_opening_move_lines_tree
#: model:ir.ui.view,arch_db:account.view_account_payment_form
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_pivot
#: model:ir.ui.view,arch_db:account.view_move_line_tree
#, python-format
msgid "Journal Items"
msgstr "Itens do Diário"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_action.js:323
#: model:ir.actions.client,name:account.action_manual_reconcile
#, python-format
msgid "Journal Items to Reconcile"
msgstr "Itens do Diário a Reconciliar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_name
msgid "Journal Name"
msgstr "Nome do Diário"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "Journal and Partner"
msgstr "Diário e Parceiro"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Journal invoices with period in current year"
msgstr "Faturas do diário com data do ano corrente"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Journal items where matching number isn't set"
msgstr "Itens do diário onde o número de acerto não está definido."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_opening_journal_id
msgid ""
"Journal where the opening entry of this company's accounting has been "
"posted."
msgstr ""
"Diário onde a primeira entrada contabilística desta empresa foi lançada."

#. module: account
#: model:ir.actions.act_window,name:account.action_account_journal_form
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_journal_ids
#: model:ir.model.fields,field_description:account.field_account_balance_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_account_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_print_journal_journal_ids
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_journal_ids
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_journal_ids
#: model:ir.model.fields,field_description:account.field_account_tax_report_journal_ids
#: model:ir.model.fields,field_description:account.field_accounting_report_journal_ids
#: model:ir.ui.menu,name:account.menu_action_account_journal_form
msgid "Journals"
msgstr "Diários"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_print_journal_menu
#: model:ir.actions.report,name:account.action_report_journal
#: model:ir.ui.menu,name:account.menu_print_journal
msgid "Journals Audit"
msgstr "Auditoria de Diários"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "July"
msgstr "Julho"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "June"
msgstr "Junho"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_reason
msgid "Justification"
msgstr "Justificação"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_kanban_dashboard
msgid "Kanban Dashboard"
msgstr "Painel do Kanban"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "Gráfico do Painel do Kanban"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Keep empty for no control"
msgstr "Deixar vazio para não limitar"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_date_invoice
msgid "Keep empty to use the current date"
msgstr "Deixar vazio para usar a data corrente"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_date
msgid "Keep empty to use the invoice date."
msgstr "Deixar vazio para usar a data da fatura."

#. module: account
#: selection:account.payment,payment_difference_handling:0
msgid "Keep open"
msgstr "Deixar aberto"

#. module: account
#: model:ir.model.fields,help:account.field_product_product_property_account_income_id
#: model:ir.model.fields,help:account.field_product_template_property_account_income_id
msgid ""
"Keep this field empty to use the default value from the product category."
msgstr ""
"Deixar este campo vazio para usar o valor por defeito da categoria do "
"artigo."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:522
#: code:addons/account/static/src/xml/account_reconciliation.xml:189
#: code:addons/account/static/src/xml/account_reconciliation.xml:230
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_name
#: model:ir.model.fields,field_description:account.field_account_move_line_name
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#, python-format
msgid "Label"
msgstr "Etiqueta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_description
msgid "Label on Invoices"
msgstr "Etiqueta nas Faturas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment___last_update
#: model:ir.model.fields,field_description:account.field_account_account___last_update
#: model:ir.model.fields,field_description:account.field_account_account_tag___last_update
#: model:ir.model.fields,field_description:account.field_account_account_template___last_update
#: model:ir.model.fields,field_description:account.field_account_account_type___last_update
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance___last_update
#: model:ir.model.fields,field_description:account.field_account_balance_report___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line___last_update
#: model:ir.model.fields,field_description:account.field_account_cash_rounding___last_update
#: model:ir.model.fields,field_description:account.field_account_cashbox_line___last_update
#: model:ir.model.fields,field_description:account.field_account_chart_template___last_update
#: model:ir.model.fields,field_description:account.field_account_common_account_report___last_update
#: model:ir.model.fields,field_description:account.field_account_common_journal_report___last_update
#: model:ir.model.fields,field_description:account.field_account_common_partner_report___last_update
#: model:ir.model.fields,field_description:account.field_account_common_report___last_update
#: model:ir.model.fields,field_description:account.field_account_financial_report___last_update
#: model:ir.model.fields,field_description:account.field_account_financial_year_op___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template___last_update
#: model:ir.model.fields,field_description:account.field_account_full_reconcile___last_update
#: model:ir.model.fields,field_description:account.field_account_group___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_line___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_refund___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_report___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_tax___last_update
#: model:ir.model.fields,field_description:account.field_account_journal___last_update
#: model:ir.model.fields,field_description:account.field_account_move___last_update
#: model:ir.model.fields,field_description:account.field_account_move_line___last_update
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile___last_update
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff___last_update
#: model:ir.model.fields,field_description:account.field_account_move_reversal___last_update
#: model:ir.model.fields,field_description:account.field_account_opening___last_update
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile___last_update
#: model:ir.model.fields,field_description:account.field_account_payment___last_update
#: model:ir.model.fields,field_description:account.field_account_payment_method___last_update
#: model:ir.model.fields,field_description:account.field_account_payment_term___last_update
#: model:ir.model.fields,field_description:account.field_account_payment_term_line___last_update
#: model:ir.model.fields,field_description:account.field_account_print_journal___last_update
#: model:ir.model.fields,field_description:account.field_account_reconcile_model___last_update
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template___last_update
#: model:ir.model.fields,field_description:account.field_account_register_payments___last_update
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger___last_update
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger___last_update
#: model:ir.model.fields,field_description:account.field_account_tax___last_update
#: model:ir.model.fields,field_description:account.field_account_tax_group___last_update
#: model:ir.model.fields,field_description:account.field_account_tax_report___last_update
#: model:ir.model.fields,field_description:account.field_account_tax_template___last_update
#: model:ir.model.fields,field_description:account.field_account_unreconcile___last_update
#: model:ir.model.fields,field_description:account.field_accounting_report___last_update
#: model:ir.model.fields,field_description:account.field_cash_box_in___last_update
#: model:ir.model.fields,field_description:account.field_cash_box_out___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_financial___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_generalledger___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_journal___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_overdue___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_partnerledger___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_tax___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_trialbalance___last_update
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard___last_update
#: model:ir.model.fields,field_description:account.field_validate_account_move___last_update
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts___last_update
msgid "Last Modified on"
msgstr "Última Modificação em"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Last Month"
msgstr "Último Mês"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:119
#, python-format
msgid "Last Reconciliation:"
msgstr "Última Reconciliação:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_write_uid
#: model:ir.model.fields,field_description:account.field_account_account_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_account_type_write_uid
#: model:ir.model.fields,field_description:account.field_account_account_write_uid
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_write_uid
#: model:ir.model.fields,field_description:account.field_account_balance_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_write_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_write_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_account_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_financial_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_write_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_write_uid
#: model:ir.model.fields,field_description:account.field_account_group_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_write_uid
#: model:ir.model.fields,field_description:account.field_account_journal_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_write_uid
#: model:ir.model.fields,field_description:account.field_account_opening_write_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_write_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal_write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_write_uid
#: model:ir.model.fields,field_description:account.field_account_register_payments_write_uid
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_write_uid
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_write_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile_write_uid
#: model:ir.model.fields,field_description:account.field_accounting_report_write_uid
#: model:ir.model.fields,field_description:account.field_cash_box_in_write_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out_write_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_write_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move_write_uid
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_write_uid
msgid "Last Updated by"
msgstr "Última Atualização por"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_write_date
#: model:ir.model.fields,field_description:account.field_account_account_template_write_date
#: model:ir.model.fields,field_description:account.field_account_account_type_write_date
#: model:ir.model.fields,field_description:account.field_account_account_write_date
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_write_date
#: model:ir.model.fields,field_description:account.field_account_balance_report_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_write_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_write_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_write_date
#: model:ir.model.fields,field_description:account.field_account_chart_template_write_date
#: model:ir.model.fields,field_description:account.field_account_common_account_report_write_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_write_date
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_write_date
#: model:ir.model.fields,field_description:account.field_account_common_report_write_date
#: model:ir.model.fields,field_description:account.field_account_financial_report_write_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_write_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_write_date
#: model:ir.model.fields,field_description:account.field_account_group_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_line_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_write_date
#: model:ir.model.fields,field_description:account.field_account_journal_write_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_write_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_write_date
#: model:ir.model.fields,field_description:account.field_account_move_line_write_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal_write_date
#: model:ir.model.fields,field_description:account.field_account_move_write_date
#: model:ir.model.fields,field_description:account.field_account_opening_write_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_method_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_write_date
#: model:ir.model.fields,field_description:account.field_account_print_journal_write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_write_date
#: model:ir.model.fields,field_description:account.field_account_register_payments_write_date
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_write_date
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_group_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_report_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_template_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_write_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile_write_date
#: model:ir.model.fields,field_description:account.field_accounting_report_write_date
#: model:ir.model.fields,field_description:account.field_cash_box_in_write_date
#: model:ir.model.fields,field_description:account.field_cash_box_out_write_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_write_date
#: model:ir.model.fields,field_description:account.field_validate_account_move_write_date
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_write_date
msgid "Last Updated on"
msgstr "Última Atualização em"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Last day of current month"
msgstr "Último dia do mês corrente"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Last day of following month"
msgstr "Último dia do mês seguinte"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_last_time_entries_checked
#: model:ir.model.fields,help:account.field_res_users_last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed for this partner. "
"It is set either if there's not at least an unreconciled debit and an "
"unreconciled credit or if you click the \"Done\" button."
msgstr ""
"A última vez que foi feito o ajuste de faturas e pagamentos para este "
"parceiro. É definido se não houver pelo menos um débito e um crédito não "
"reconciliados ou se clicar no botão \"Feito\"."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed on this account. It"
" is set either if there's not at least an unreconciled debit and an "
"unreconciled credit Or if you click the \"Done\" button."
msgstr ""
"A última vez que foi feito o ajuste de faturas e pagamentos nesta esta "
"conta. É definido se não houver pelo menos um débito e um crédito não "
"reconciliados ou se clicar no botão \"Feito\"."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Late Activities"
msgstr "Atividades Atrasadas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_last_time_entries_checked
#: model:ir.model.fields,field_description:account.field_res_partner_last_time_entries_checked
#: model:ir.model.fields,field_description:account.field_res_users_last_time_entries_checked
msgid "Latest Invoices & Payments Matching Date"
msgstr "Última Data de Ajuste de Faturas e Pagamentos"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group_parent_left
msgid "Left Parent"
msgstr "Ascendente a Esquerda"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Legal Name"
msgstr "Nome Legal"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Legal Notes..."
msgstr "Notas Legais..."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_note
msgid "Legal mentions that have to be printed on the invoices."
msgstr "Menções legais que têm que ser impressas nas faturas."

#. module: account
#: code:addons/account/models/account_invoice.py:216
#, python-format
msgid "Less Payment"
msgstr "Menos Pagamento"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:24
#, python-format
msgid "Let odoo try to reconcile entries for the user"
msgstr "Permita que o Odoo tente reconciliar as entradas pelo utilizador"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Let your customers pay their invoices online"
msgstr "Permita que os seus clientes paguem as suas faturas online"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_level
msgid "Level"
msgstr "Comecemos por fazer uma nova fatura de cliente."

#. module: account
#: model:account.financial.report,name:account.account_financial_report_liability0
#: model:account.financial.report,name:account.account_financial_report_liabilitysum0
msgid "Liability"
msgstr "Nível"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_move_id
msgid "Link to the automatically generated Journal Items."
msgstr "Link para os Itens automaticamente gerados do Diário."

#. module: account
#: selection:account.account.type,type:0
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Liquidity"
msgstr "Risco"

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_tax_template_ids
msgid "List of all the taxes that have to be installed by the wizard"
msgstr "Lista de todos os impostos que devem ser instalados pelo assistente"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Litigation"
msgstr "Litígio"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:29
#, python-format
msgid "Load more"
msgstr "Carregar mais"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_fiscalyear_lock_date
msgid "Lock Date"
msgstr "Bloquear Data"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "Bloquear Data a não Consultores"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Logo"
msgstr "Logótipo"

#. module: account
#: code:addons/account/models/account_bank_statement.py:173
#, python-format
msgid "Loss"
msgstr "Prejuízo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_loss_account_id
msgid "Loss Account"
msgstr "Conta de Prejuízos"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_expense_currency_exchange_account_id
#: model:ir.model.fields,field_description:account.field_res_company_expense_currency_exchange_account_id
msgid "Loss Exchange Rate Account"
msgstr "Conta de Prejuízos de Variação Cambial"

#. module: account
#: code:addons/account/models/chart_template.py:195
#, python-format
msgid "MISC"
msgstr "MISC"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Mail your invoices in one-click using"
msgstr "Envie as suas faturas com um clique usando"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Main Currency"
msgstr "Moeda Principal"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Main Title 1 (bold, underlined)"
msgstr "Título Principal 1 (negrito, sublinhado)"

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_currency_id
msgid "Main currency of the company."
msgstr "Moeda principal da empresa."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Main currency of your company"
msgstr "Moeda principal da sua empresa"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Manage time &amp; material contracts or fixed-price recurring subscriptions."
msgstr ""
"Controle o tempo &amp; contratos de material ou subscrições recorrentes de "
"custo fixo."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Manage your employee expenses, from encoding, to payments and reporting."
msgstr ""
"Controle os custos com empregados, de codificações a pagamentos e "
"relatórios."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Manage your various fixed assets, such as buildings, machinery, materials, "
"cars, etc..., and calculate their associated depreciation over time."
msgstr ""
"Controle os seus vários ativos tangíveis, tais como edifícios, maquinaria, "
"materiais, carros, etc..., e calcule a desvalorização que lhes é associada "
"com o passar do tempo."

#. module: account
#: model:ir.ui.menu,name:account.account_management_menu
#: model:ir.ui.menu,name:account.account_reports_management_menu
#: model:ir.ui.menu,name:account.menu_finance_entries_management
msgid "Management"
msgstr "Gestão"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Managing bank fees"
msgstr "Gerir comissões bancárias"

#. module: account
#: model:account.payment.method,name:account.account_payment_method_manual_in
#: model:account.payment.method,name:account.account_payment_method_manual_out
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_manual
msgid "Manual"
msgstr "Manual"

#. module: account
#: model:ir.ui.view,arch_db:account.view_invoice_tax_form
#: model:ir.ui.view,arch_db:account.view_invoice_tax_tree
msgid "Manual Invoice Taxes"
msgstr "Impostos de Faturação Manuais"

#. module: account
#: model:ir.actions.client,name:account.action_manual_reconciliation
#: model:ir.ui.menu,name:account.menu_action_manual_reconciliation
msgid "Manual Reconciliation"
msgstr "Reconciliação Manual"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_inbound_payment_method_ids
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo,you are suggested to reconcile the transaction with the batch deposit. Enable this option from the settings."
msgstr ""
"Manual: Receba pagamentos em numerário, cheque ou qualquer outro método fora do Odoo.\n"
"Eletrónico: Receba pagamentos automaticamente através de um gestor de transações pedindo uma transação com um cartão registado pelo cliente ao comprar ou subscrevendo online (token de pagamento).\n"
"Depósito em Lote: Junte vários cheques de clientes ao mesmo tempo gerando um depósito em lote e submetendo ao seu banco. Quando codificar o extrato bancário no Odoo, é-lhe sugerido que reconcilie a transação com o depósito em lote. Ative esta opção nas definições."

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_payment_method_id
#: model:ir.model.fields,help:account.field_account_payment_payment_method_id
#: model:ir.model.fields,help:account.field_account_register_payments_payment_method_id
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Check: Pay bill by check and print it from Odoo.\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo, you are suggested to reconcile the transaction with the batch deposit.To enable batch deposit,module account_batch_deposit must be installed.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. To enable sepa credit transfer, module account_sepa must be installed "
msgstr ""
"Manual: Receba pagamentos em numerário, cheque ou qualquer outro método fora do Odoo.\n"
"Eletrónico: Receba pagamentos automaticamente através de um gestor de transações pedindo uma transação com um cartão registado pelo cliente ao comprar ou subscrevendo online (token de pagamento).\n"
"Cheque: Pague uma conta por cheque e imprima-o através do Odoo.\n"
"Depósito em Lote: Junte vários cheques de clientes ao mesmo tempo gerando um depósito em lote e submetendo ao seu banco. Quando codificar o extrato bancário no Odoo, é-lhe sugerido que reconcilie a transação com o depósito em lote. Para ativar o depósito em lote, é necessário instalar o módulo account_batch_deposit.\n"
"Transferência a Crédito SEPA: Pague uma conta através de um ficheiro de Transferência a Crédito SEPA submetido ao seu banco. Para ativar a transferência a crédito SEPA, é necessário instalar o módulo account_sepa."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_outbound_payment_method_ids
msgid ""
"Manual:Pay bill by cash or any other method outside of Odoo.\n"
"Check:Pay bill by check and print it from Odoo.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. Enable this option from the settings."
msgstr ""
"Manual: Pague uma conta por numerário ou qualquer outro método fora do Odoo.\n"
"Cheque: Pague uma conta por cheque e imprima-o através do Odoo.\n"
"Transferência a Crédito SEPA: Pague uma conta através de um ficheiro de Transferência a Crédito SEPA submetido ao seu banco. Ative esta opção nas definições."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Manually enter your transactions using our"
msgstr "Introduza manualmente as suas transações usando o nosso"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "March"
msgstr "Março"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Margin Analysis"
msgstr "Análise de Margens"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model:ir.ui.view,arch_db:account.setup_view_company_form
msgid "Mark as done"
msgstr "Marcar como feito"

#. module: account
#: selection:account.payment,payment_difference_handling:0
msgid "Mark invoice as fully paid"
msgstr "Marcar faturas como pagas"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_payables_master_data
#: model:ir.ui.menu,name:account.menu_finance_receivables_master_data
msgid "Master Data"
msgstr "Dados Principais"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_matched_credit_ids
msgid "Matched Credit"
msgstr "Crédito Acertado"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_matched_debit_ids
msgid "Matched Debit"
msgstr "Débito Acertado"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_reconciled_line_ids
#: model:ir.ui.view,arch_db:account.view_full_reconcile_form
msgid "Matched Journal Items"
msgstr "Itens do Diário Acertados"

#. module: account
#: model:ir.ui.view,arch_db:account.view_full_reconcile_form
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Matching"
msgstr "Acertos"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_full_reconcile_id
msgid "Matching Number"
msgstr "Número de Acerto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_max_date
msgid "Max Date of Matched Lines"
msgstr "Data Limite para Acerto de Linhas"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "May"
msgstr "Maio"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_communication
#: model:ir.model.fields,field_description:account.field_account_payment_communication
#: model:ir.model.fields,field_description:account.field_account_register_payments_communication
msgid "Memo"
msgstr "Memorando"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:64
#, python-format
msgid "Memo:"
msgstr "Memorando:"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_invoice_warn_msg
#: model:ir.model.fields,field_description:account.field_res_users_invoice_warn_msg
msgid "Message for Invoice"
msgstr "Mensagem da Fatura"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Cost of Revenue"
msgstr "Menos Custo de Produção"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Credit Card Accounts"
msgstr "Menos Contas de Cartão de Crédito"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Current Liabilities"
msgstr "Menos Passivos Correntes"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Expenses"
msgstr "Menos Despesas"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Non-Current Liabilities"
msgstr "Menos Passivos Não Correntes"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Payable Accounts"
msgstr "Menos Contas a Pagar"

#. module: account
#: selection:account.journal,type:0
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Miscellaneous"
msgstr "Diversos"

#. module: account
#: code:addons/account/models/chart_template.py:195
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#, python-format
msgid "Miscellaneous Operations"
msgstr "Operações Diversas"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:164
#, python-format
msgid "Modify models"
msgstr "Alterar modelos"

#. module: account
#: selection:account.cash.rounding,strategy:0
msgid "Modify tax amount"
msgstr "Alterar montante do imposto"

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Modify: create credit note, reconcile and create a new draft invoice"
msgstr ""
"Modificar: criar nota de crédito, reconciliar e criar um novo rascunho de "
"fatura"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Monitor your product margins from invoices"
msgstr "Monitorizar as margens dos seus artigos a partir das faturas"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_invoice_partner_relation
msgid "Monthly Turnover"
msgstr "Receita Mensal"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Most currencies are already created by default. If you plan\n"
"                        to use some of them, you should check their <strong>Active</strong>\n"
"                        field."
msgstr ""
"A maioria das moedas já se encontram criadas por defeito. Se planeia\n"
"                        usar algumas delas, deve marcar o campo <strong>Ativa</strong>."

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Move"
msgstr "Movimento"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_move_id
#: model:ir.model.fields,field_description:account.field_account_payment_move_line_ids
msgid "Move Line"
msgstr "Linha de Movimento"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_move_line_count
msgid "Move Line Count"
msgstr "Contagem de Linhas de Movimento"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_move_reconciled
msgid "Move Reconciled"
msgstr "Movimento Reconciliado"

#. module: account
#: code:addons/account/models/account_move.py:1362
#, python-format
msgid "Move name (id): %s (%s)"
msgstr "Nome do movimento: (Id.): %s (%s)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_register_payments_multi
msgid "Multi"
msgstr ""

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Multi Currency"
msgstr "Multi-Moeda"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Multi-Currencies"
msgstr "Multi-Moedas"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "My Activities"
msgstr "As Minhas Atividades"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "My Invoices"
msgstr "As Minhas Faturas"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "NET ASSETS"
msgstr "ATIVOS LÍQUIDOS"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "NET PROFIT"
msgstr "LUCRO LÍQUIDO"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_name
#: model:ir.model.fields,field_description:account.field_account_account_tag_name
#: model:ir.model.fields,field_description:account.field_account_account_template_name
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_name
#: model:ir.model.fields,field_description:account.field_account_chart_template_name
#: model:ir.model.fields,field_description:account.field_account_group_name
#: model:ir.model.fields,field_description:account.field_account_payment_method_name
#: model:ir.model.fields,field_description:account.field_account_payment_name
#: model:ir.model.fields,field_description:account.field_account_tax_group_name
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Name"
msgstr "Nome"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:52
#, python-format
msgid "Name:"
msgstr "Nome:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_narration
msgid "Narration"
msgstr "Narração"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Navigate easily through reports and see what is behind the numbers"
msgstr ""
"Navegue facilmente pelos relatórios e veja o que está por detrás dos números"

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "Net"
msgstr "Líquido"

#. module: account
#: selection:account.bank.statement,state:0
msgid "New"
msgstr "Novo"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "New Statement"
msgstr "Nova Declaração"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "New Transactions"
msgstr "Novas Transações"

#. module: account
#: code:addons/account/models/account_move.py:1339
#, python-format
msgid "New expected payment date: "
msgstr "Nova data de previsão de pagamento:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_sequence_number_next
#: model:ir.model.fields,field_description:account.field_account_invoice_sequence_number_next_prefix
#: model:ir.model.fields,field_description:account.field_account_journal_sequence_number_next
msgid "Next Number"
msgstr "Próximo Número"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Next, register any unmatched payments:<br/>"
msgstr "De seguida, registe quaisquer pagamentos não reconciliados:<br/>"

#. module: account
#: selection:accounting.report,filter_cmp:0
msgid "No Filters"
msgstr "Sem Filtros"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_blocked
msgid "No Follow-up"
msgstr "Sem Acompanhamento"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "No Message"
msgstr "Sem Mensagens"

#. module: account
#: selection:account.financial.report,display_detail:0
msgid "No detail"
msgstr "Sem detalhe"

#. module: account
#: code:addons/account/models/account.py:116
#, python-format
msgid "No opening move defined !"
msgstr "Nenhuma acção de início definida !"

#. module: account
#: model:ir.model.fields,help:account.field_res_company_fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""
"Os utilizadores, e Consultores não podem editar contas criadas em datas "
"iguais ou anteriores à data de hoje. Use esta propriedade para bloquear um "
"período fiscal aberto, por exemplo."

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_code_digits
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_code_digits
msgid "No. of Digits to use for account code"
msgstr "Nº. de dígitos para utilizar no código da conta"

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_code_digits
msgid "No. of digits to use for account code"
msgstr "Nº. de dígitos para utilizar no código da conta"

#. module: account
#: model:account.account.type,name:account.data_account_type_non_current_assets
msgid "Non-current Assets"
msgstr "Ativos Não Correntes"

#. module: account
#: model:account.account.type,name:account.data_account_type_non_current_liabilities
msgid "Non-current Liabilities"
msgstr "Passivos Não Correntes"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
msgid "None"
msgstr "Nenhum"

#. module: account
#: selection:res.partner,trust:0
msgid "Normal Debtor"
msgstr "Devedor normal"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Normal Text"
msgstr "Texto Normal"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:254
#: model:ir.model.fields,field_description:account.field_account_account_template_note
#, python-format
msgid "Note"
msgstr "Nota"

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_out_refund
msgid ""
"Note that the easiest way to create a credit note is to do it directly form\n"
"                the customer invoice, to refund it totally or partially."
msgstr ""
"Repare que a forma mais fácil de criar uma nota de crédito é fazendo-o diretamente\n"
"                a partir da fatura do cliente, para a reembolsar total ou parcialmente."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_note
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_note
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_note
#: model:ir.ui.view,arch_db:account.view_account_template_form
msgid "Notes"
msgstr "Notas"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Nothing to Reconcile"
msgstr "Nada a Reconciliar"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:34
#, python-format
msgid "Nothing to do!"
msgstr "Nada para fazer!"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "November"
msgstr "Novembro"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_name
#: model:ir.model.fields,field_description:account.field_account_invoice_number
#: model:ir.model.fields,field_description:account.field_account_move_name
msgid "Number"
msgstr "Número"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Number (Move)"
msgstr "Número (Movimento)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_number
msgid "Number of Coins/Bills"
msgstr "Número de Moedas/Notas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_days
msgid "Number of Days"
msgstr "Número de Dias"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_accounts_code_digits
msgid "Number of digits in an account code"
msgstr "Número de dígitos num código de conta"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "OFX Import"
msgstr "Importar OFX"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:17
#, python-format
msgid "OK"
msgstr "OK"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "October"
msgstr "Outubro"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Odoo Accounting has many extra-features:"
msgstr ""

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_line
msgid ""
"Odoo allows you to reconcile a statement line directly with\n"
"                the related sale or puchase invoice(s)."
msgstr ""
"O Odoo permite-lhe reconciliar uma linha declarativa diretamente com\n"
"                a(s) fatura(s) de venda ou compra relacionada(s)."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_tree
msgid ""
"Odoo allows you to reconcile a statement line directly with\n"
"                the related sale or puchase invoices."
msgstr ""
"O Odoo permite-lhe reconciliar uma linha declarativa diretamente com\n"
"                as faturas de venda ou compra relacionadas."

#. module: account
#: model:ir.actions.act_window,help:account.action_move_journal_line
msgid ""
"Odoo automatically creates one journal entry per accounting\n"
"                document: invoice, refund, vendor payment, bank statements,\n"
"                etc. So, you should record journal entries manually only/mainly\n"
"                for miscellaneous operations."
msgstr ""
"O Odoo cria automaticamente uma entrada no diário por documento\n"
"                contabilístico: fatura, reembolso, pagamento a fornecedor, extratos bancários,\n"
"                etc. Desta forma, apenas necessita de registar entradas manuais no diário\n"
"                para operações diferentes."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Odoo can manage multiple companies, but we suggest to setup everything for "
"your first company before configuring the other ones."
msgstr ""
"O Odoo pode gerir diversas empresas, mas sugerimos que configure tudo para a"
" primeira empresa antes de configurar as outras."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Odoo has already preconfigured a few taxes according to your "
"country.<br/>Simply review them and check if you need more."
msgstr ""
"O Odoo já pré-configurou alguns impostos relativos ao seu país.<br/> Basta "
"verificar e marcar se necessitar de mais."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Odoo should do most of the reconciliation work automatically, so you'll only"
" need to review a few of them when a <strong>'Reconcile Items'</strong> "
"button appears on your Vendor Bills dash."
msgstr ""
"O Odoo deverá fazer a maioria do trabalho de reconciliação automaticamente, "
"portanto só necessita de rever algumas quando um botão <strong>'Reconciliar "
"Itens'</strong> aparecer no seu painel de Faturas de Fornecedor."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree1
msgid ""
"Odoo's electronic invoicing allows to ease and fasten the\n"
"                collection of customer payments. Your customer receives the\n"
"                invoice by email and he can pay online and/or import it\n"
"                in his own system."
msgstr ""
"A faturação eletrónica do Odoo facilita-lhe e acelera a\n"
"                coleção de pagamentos de clientes. O seu cliente recebe a\n"
"                fatura por email e pode pagar online e/ou importá-la para\n"
"                o seu próprio sistema."

#. module: account
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid ""
"Once draft invoices are confirmed, you will not be able\n"
"                        to modify them. The invoices will receive a unique\n"
"                        number and journal items will be created in your chart\n"
"                        of accounts."
msgstr ""
"Assim que os rascunhos de fatura são confirmados, não será capaz\n"
"                        de os alterar. As faturas recebem um número único e\n"
"                        os itens do diário serão criados no seu plano de contas."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Once installed, set 'Bank Feeds' to 'File Import' in bank account "
"settings.This adds a button to import from the Accounting dashboard."
msgstr ""
"Uma vez instalado, altere a opção 'Feeds Bancários' para 'Importar Ficheiro'"
" nas configurações de conta bancária. Isto adiciona um botão para importação"
" a partir do painel de Contabilidade."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Once you have created your chart of accounts, you will need to update your "
"account balances."
msgstr ""
"Assim que criar o seu plano de contas, será necessário atualizar os seus "
"saldos de conta."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Once your bank accounts are registered, you will be able \n"
"                        to access your statements from the Accounting Dashboard. \n"
"                        The available methods for synchronization are as follows."
msgstr ""
"Assim que as suas contas bancárias estiverem registadas, será capaz \n"
"                        de aceder aos seus extratos através do Painel de Contabilidade. \n"
"                        Os métodos de sincronização disponíveis são os seguintes."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Once your company information is correct, you should"
msgstr "Assim que a informação da sua empresa estiver correta, deverá"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "One bank statement for each bank account you hold."
msgstr "Um extrato bancário por cada conta bancária que mantiver."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_payment
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Online Payment"
msgstr "Pagamento Online"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_only_one_chart_template
msgid "Only One Chart Template Available"
msgstr "Apenas Um Modelo de Plano Disponível"

#. module: account
#: code:addons/account/models/account_payment.py:484
#, python-format
msgid "Only a draft payment can be posted."
msgstr "Só é possível lançar um rascunho de pagamento."

#. module: account
#: code:addons/account/models/chart_template.py:866
#, python-format
msgid "Only administrators can change the settings"
msgstr "Só os administradores podem alterar as configurações"

#. module: account
#: model:ir.model.fields,help:account.field_res_company_period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""
"Somente os utilizadores com permissões 'Consultor' podem alterar contas "
"criadas até à data actual. Use este propriedade para bloquear um período de "
"um ano fiscal aberto, por exemplo."

#. module: account
#. openerp-web
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: code:addons/account/static/src/xml/account_payment.xml:82
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Open"
msgstr "Abrir"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:664
#: code:addons/account/static/src/xml/account_reconciliation.xml:131
#, python-format
msgid "Open balance"
msgstr "Saldo em aberto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_opening_date
#: model:ir.model.fields,field_description:account.field_account_opening_date
#: model:ir.model.fields,field_description:account.field_res_company_account_opening_date
msgid "Opening Date"
msgstr "Data de Abertura"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_opening_journal_id
msgid "Opening Journal"
msgstr "Diário de Abertura"

#. module: account
#: code:addons/account/models/company.py:339
#: model:ir.model.fields,field_description:account.field_account_opening_opening_move_id
#: model:ir.model.fields,field_description:account.field_res_company_account_opening_move_id
#, python-format
msgid "Opening Journal Entry"
msgstr "Primeira Entrada do Diário"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_opening_opening_move_line_ids
msgid "Opening Journal Items"
msgstr "Primeiros Itens do Diário"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_opening_move_posted
msgid "Opening Move Posted"
msgstr "Primeiro Movimento Lançado"

#. module: account
#: model:ir.model.fields,help:account.field_account_cashbox_line_number
msgid "Opening Unit Numbers"
msgstr "Primeiros Números Unitários"

#. module: account
#: code:addons/account/models/account.py:138
#, python-format
msgid "Opening balance"
msgstr "Saldo inicial"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_opening_credit
msgid "Opening credit"
msgstr "Crédito inicial"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_opening_credit
msgid "Opening credit value for this account."
msgstr "Valor de crédito inicial para esta conta."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_opening_debit
msgid "Opening debit"
msgstr "Débito inicial"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_opening_debit
msgid "Opening debit value for this account."
msgstr "Valor de débito inicial para esta conta."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Operation Templates"
msgstr "Modelos de Operações"

#. module: account
#: code:addons/account/models/account_bank_statement.py:1014
#, python-format
msgid ""
"Operation not allowed. Since your statement line already received a number, "
"you cannot reconcile it entirely with existing journal entries otherwise it "
"would make a gap in the numbering. You should book an entry and make a "
"regular revert of it in case you want to cancel it."
msgstr ""
"Operação não permitida. Como a linha declarativa já recebeu um número, não a"
" pode reconciliar completamente com entradas existentes no diário, ou então "
"criava uma lacuna na numeração. Deve marcar uma entrada e fazer uma reversão"
" regular no caso de a querer cancelar."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_nocreate
msgid "Optional Create"
msgstr "Criação Opcional"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag_ids
#: model:ir.model.fields,help:account.field_account_account_template_tag_ids
#: model:ir.model.fields,help:account.field_account_tax_tag_ids
#: model:ir.model.fields,help:account.field_account_tax_template_tag_ids
msgid "Optional tags you may want to assign for custom reporting"
msgstr ""
"Identificadores opcionais que pode querer atribuir para relatórios "
"personalizados"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_option
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Options"
msgstr "Opções"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Original Amount"
msgstr "Valor Original"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_payment_id
msgid "Originator Payment"
msgstr "Ordenante do Pagamento"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_line_id
msgid "Originator tax"
msgstr "Imposto do ordenante"

#. module: account
#: model:account.account.type,name:account.data_account_type_other_income
msgid "Other Income"
msgstr "Outras Receitas"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Other Info"
msgstr "Outra Informação"

#. module: account
#: selection:account.payment.method,payment_type:0
msgid "Outbound"
msgstr "Enviados"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_stock_account_output_categ_id
#: model:ir.model.fields,field_description:account.field_res_company_property_stock_account_output_categ_id
msgid "Output Account for Stock Valuation"
msgstr "Conta de Valoração de Ações"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_outstanding_credits_debits_widget
msgid "Outstanding Credits Debits Widget"
msgstr "Ferramenta de Créditos e Débitos Pendentes"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Outstanding Transactions"
msgstr "Transações Pendentes"

#. module: account
#: code:addons/account/models/account_invoice.py:131
#, python-format
msgid "Outstanding credits"
msgstr "Créditos pendentes"

#. module: account
#: code:addons/account/models/account_invoice.py:134
#, python-format
msgid "Outstanding debits"
msgstr "Débitos pendentes"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Overdue"
msgstr "Vencido"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_overdue_msg
msgid "Overdue Payments Message"
msgstr "Mensagem de Pagamentos Vencidos"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Overdue invoices, maturity date passed"
msgstr "Faturas vencidas, data de vencimento expirada"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_legal_statement
msgid "PDF Reports"
msgstr "Relatórios em PDF"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Package"
msgstr "Plano"

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: model:ir.ui.view,arch_db:account.report_overdue_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:mail.message.subtype,name:account.mt_invoice_paid
msgid "Paid"
msgstr "Pago"

#. module: account
#: code:addons/account/models/account_payment.py:430
#, python-format
msgid "Paid Invoices"
msgstr "Faturas Pagas"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:28
#: model:ir.ui.view,arch_db:account.report_invoice_document_with_payments
#, python-format
msgid "Paid on"
msgstr "Pago em"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_reconciled
msgid "Paid/Reconciled"
msgstr "Pago/Reconciliado"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_parent_id
#: model:ir.model.fields,field_description:account.field_account_group_parent_id
msgid "Parent"
msgstr "Fonte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_parent_id
msgid "Parent Chart Template"
msgstr "Modelo de Gráfico Fonte"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
msgid "Parent Report"
msgstr "Relatório Fonte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_parent_state
msgid "Parent State"
msgstr "Estado Ascendente"

#. module: account
#: model:ir.model,name:account.model_account_partial_reconcile
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Partial Reconcile"
msgstr "Reconciliação Parcial"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:232
#: code:addons/account/static/src/xml/account_reconciliation.xml:249
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_partner_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_partner_id
#: model:ir.model.fields,field_description:account.field_account_move_line_partner_id
#: model:ir.model.fields,field_description:account.field_account_move_partner_id
#: model:ir.model.fields,field_description:account.field_account_payment_partner_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_partner_id
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#, python-format
msgid "Partner"
msgstr "Parceiro"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_commercial_partner_id
msgid "Partner Company"
msgstr "Empresa Parceira"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_partner_ledger_menu
#: model:ir.actions.report,name:account.action_report_partnerledger
#: model:ir.ui.menu,name:account.menu_partner_ledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
msgid "Partner Ledger"
msgstr "Registo de Parceiro"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_partner_name
msgid "Partner Name"
msgstr "Nome do Parceiro"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_partner_type
#: model:ir.model.fields,field_description:account.field_account_payment_partner_type
#: model:ir.model.fields,field_description:account.field_account_register_payments_partner_type
msgid "Partner Type"
msgstr "Tipo de Parceiro"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_result_selection
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_result_selection
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_result_selection
msgid "Partner's"
msgstr "Do Parceiro"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Partners"
msgstr "Parceiros"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:106
#, python-format
msgid "Past"
msgstr "Transato"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Pay your bills in one-click using Euro SEPA service"
msgstr "Pague as suas contas com um clique através do serviço Euro SEPA"

#. module: account
#: model:account.account.type,name:account.data_account_type_payable
#: selection:account.account.type,type:0
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Payable"
msgstr "A Pagar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_payable_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Payable Account"
msgstr "Conta a Pagar"

#. module: account
#: selection:account.aged.trial.balance,result_selection:0
#: selection:account.common.partner.report,result_selection:0
#: selection:account.report.partner.ledger,result_selection:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.view_account_search
msgid "Payable Accounts"
msgstr "Contas a Pagar"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_debit_limit
#: model:ir.model.fields,field_description:account.field_res_users_debit_limit
msgid "Payable Limit"
msgstr "Limite a Pagar"

#. module: account
#: model:ir.ui.view,arch_db:account.product_template_form_view
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Payables"
msgstr "A Pagar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_amount
#: model:ir.model.fields,field_description:account.field_account_payment_amount
#: model:ir.model.fields,field_description:account.field_account_register_payments_amount
msgid "Payment Amount"
msgstr "Valor do Pagamento"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_date
#: model:ir.model.fields,field_description:account.field_account_payment_payment_date
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_date
msgid "Payment Date"
msgstr "Data do Pagamento"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_payment_difference
#: model:ir.model.fields,field_description:account.field_account_payment_payment_difference_handling
msgid "Payment Difference"
msgstr "Diferença de Pagamento"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_journal_id
#: model:ir.model.fields,field_description:account.field_account_payment_journal_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_journal_id
msgid "Payment Journal"
msgstr "Diário de Pagamentos"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Payment Matching"
msgstr "Acerto de Pagamentos"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Payment Method"
msgstr "Método de Pagamento"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_method_id
#: model:ir.model.fields,field_description:account.field_account_payment_payment_method_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_method_id
msgid "Payment Method Type"
msgstr "Método de Pagamento"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:60
#, python-format
msgid "Payment Method:"
msgstr "Método de Pagamento:"

#. module: account
#: model:ir.model,name:account.model_account_payment_method
#: model:ir.model.fields,field_description:account.field_account_journal_outbound_payment_method_ids
msgid "Payment Methods"
msgstr "Métodos de Pagamento"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_payment_move_line_ids
msgid "Payment Move Lines"
msgstr "Linhas de Movimento de Pagamento"

#. module: account
#: model:ir.actions.report,name:account.action_report_payment_receipt
msgid "Payment Receipt"
msgstr "Recibo de Pagamento"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Payment Receipt:"
msgstr "Recibo de Pagamento:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_reference_type
#: model:ir.model.fields,field_description:account.field_account_payment_payment_reference
msgid "Payment Reference"
msgstr "Referência de Pagamento"

#. module: account
#: model:ir.actions.act_window,name:account.action_payment_term_form
#: model:ir.model,name:account.model_account_payment_term
#: model:ir.model.fields,field_description:account.field_account_invoice_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_payment_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_name
#: model:ir.ui.menu,name:account.menu_action_payment_term_form
#: model:ir.ui.view,arch_db:account.account_planner
#: model:ir.ui.view,arch_db:account.view_payment_term_form
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
#: model:ir.ui.view,arch_db:account.view_payment_term_line_tree
#: model:ir.ui.view,arch_db:account.view_payment_term_search
#: model:ir.ui.view,arch_db:account.view_payment_term_tree
msgid "Payment Terms"
msgstr "Condições de Pagamento"

#. module: account
#: model:ir.model,name:account.model_account_payment_term_line
msgid "Payment Terms Line"
msgstr "Linha de Condições de Pagamento"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_type
#: model:ir.model.fields,field_description:account.field_account_payment_method_payment_type
#: model:ir.model.fields,field_description:account.field_account_payment_payment_type
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_type
msgid "Payment Type"
msgstr "Tipo de Pagamento"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_form
msgid "Payment terms explanation for the customer..."
msgstr "Explicação das condições de pagamento para o cliente..."

#. module: account
#: model:account.payment.term,note:account.account_payment_term_15days
msgid "Payment terms: 15 Days"
msgstr "Condições de pagamento: 15 Dias"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_net
msgid "Payment terms: 30 Net Days"
msgstr "Condições de pagamento: 30 Dias"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_advance
msgid "Payment terms: 30% Advance End of Following Month"
msgstr "Condições de pagamento: 30% Adiantamento e Final do Próximo Mês"

#. module: account
#: model:account.payment.term,note:account.account_payment_term
msgid "Payment terms: End of Following Month"
msgstr "Condições de pagamento: Final do Próximo Mês"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_immediate
msgid "Payment terms: Immediate Payment"
msgstr "Condições de pagamento: Pagamento Imediato"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_payment_id
msgid "Payment that created this entry"
msgstr "Pagamento que criou esta entrada"

#. module: account
#: code:addons/account/models/account_payment.py:229
#: model:ir.actions.act_window,name:account.action_account_payments
#: model:ir.actions.act_window,name:account.action_account_payments_payable
#: model:ir.model,name:account.model_account_payment
#: model:ir.model.fields,field_description:account.field_account_invoice_payment_ids
#: model:ir.ui.menu,name:account.menu_action_account_payments_payable
#: model:ir.ui.menu,name:account.menu_action_account_payments_receivable
#: model:ir.ui.view,arch_db:account.partner_view_buttons
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#, python-format
msgid "Payments"
msgstr "Pagamentos"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "Acerto de Pagamentos"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_payments_widget
msgid "Payments Widget"
msgstr "Widget de Pagamentos"

#. module: account
#: model:ir.actions.act_window,help:account.action_account_payments
#: model:ir.actions.act_window,help:account.action_account_payments_payable
msgid ""
"Payments are used to register liquidity movements (send, collect or transfer money).\n"
"                  You can then process those payments by your own means or by using installed facilities."
msgstr ""
"Os pagamentos são usados para registar movimentos de liquidez (enviar, coletar ou transferir dinheiro).\n"
"                  Pode depois processar esses pagamentos pelos seus próprios meios ou usando mecanismos instalados."

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Payments to do"
msgstr "Pagamentos a efetuar"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_tree_pending_invoice
msgid "Pending Invoice"
msgstr "Fatura Pendente"

#. module: account
#: selection:account.payment.term.line,value:0
msgid "Percent"
msgstr "Percentagem"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_matched_percentage
msgid "Percentage Matched"
msgstr "Percentagem Reconciliada"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Percentage of Price"
msgstr "Percentagem do Preço"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Percentage of Price Tax Included"
msgstr "Percentagem de Tarifa Fiscal Incluída"

#. module: account
#: selection:account.reconcile.model,second_amount_type:0
#: selection:account.reconcile.model.template,second_amount_type:0
msgid "Percentage of amount"
msgstr "Percentagem do montante"

#. module: account
#: selection:account.reconcile.model,amount_type:0
#: selection:account.reconcile.model.template,amount_type:0
msgid "Percentage of balance"
msgstr "Percentagem do saldo"

#. module: account
#: code:addons/account/models/account_invoice.py:1776
#, python-format
msgid "Percentages for Payment Terms Line must be between 0 and 100."
msgstr ""
"As percentagens das linhas de Condições de Pagamento devem estar "
"compreendidas entre 0 e 100."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Period"
msgstr "Período"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_period_length
msgid "Period Length (days)"
msgstr "Duração do Período (dias)"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_plaid
msgid "Plaid Connector"
msgstr "Conetor da Plaid"

#. module: account
#: model:ir.model,name:account.model_web_planner
msgid "Planner"
msgstr "Gestor de Tarefas"

#. module: account
#: code:addons/account/wizard/pos_box.py:27
#, python-format
msgid "Please check that the field 'Journal' is set on the Bank Statement"
msgstr "Verifique se o campo 'Diário' está marcado no Extrato Bancário"

#. module: account
#: code:addons/account/wizard/pos_box.py:29
#, python-format
msgid "Please check that the field 'Transfer Account' is set on the company."
msgstr ""
"Verifique se o campo 'Conta de Transferências' está marcado na empresa."

#. module: account
#: code:addons/account/models/account_invoice.py:1070
#, python-format
msgid "Please create some invoice lines."
msgstr "Insira algumas linhas na fatura."

#. module: account
#: code:addons/account/models/account_move.py:157
#, python-format
msgid "Please define a sequence for the credit notes"
msgstr "Defina uma sequência para as notas de crédito"

#. module: account
#: code:addons/account/models/account_move.py:162
#, python-format
msgid "Please define a sequence on the journal."
msgstr "Defina uma sequência no diário."

#. module: account
#: code:addons/account/models/account_invoice.py:1068
#, python-format
msgid "Please define sequence on the journal related to this invoice."
msgstr "Defina uma sequência relativa a esta fatura no diário."

#. module: account
#: code:addons/account/models/company.py:336
#, python-format
msgid ""
"Please install a chart of accounts or create a miscellaneous journal before "
"proceeding."
msgstr ""
"Instale um plano de contas ou crie um diário de diversos antes de proceder."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Bank"
msgstr "Mais Bancos"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Fixed Assets"
msgstr "Mais Ativos Tangíveis"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Net Profit"
msgstr "Mais Receita Líquida"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Non-Current Assets"
msgstr "Mais Ativos Não Correntes"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Other Income"
msgstr "Mais Outros Rendimentos"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_wizard_form
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Post"
msgstr "Lançar"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Post All Entries"
msgstr "Lançar Todas as Entradas"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Post Difference In"
msgstr "Lançar Diferença Em"

#. module: account
#: model:ir.actions.act_window,name:account.action_validate_account_move
#: model:ir.ui.view,arch_db:account.validate_account_move_view
msgid "Post Journal Entries"
msgstr "Lançar Entradas do Diário"

#. module: account
#: selection:account.move,state:0 selection:account.payment,state:0
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Posted"
msgstr "Lançado"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Posted Journal Entries"
msgstr "Entradas do Diário Lançadas"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Posted Journal Items"
msgstr "Itens do Diário Lançados"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_bank_account_code_prefix
#: model:ir.model.fields,field_description:account.field_res_company_bank_account_code_prefix
msgid "Prefix of the bank accounts"
msgstr "Prefixo das contas bancárias"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_cash_account_code_prefix
msgid "Prefix of the cash accounts"
msgstr "Prefixo das contas de numerário"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_cash_account_code_prefix
msgid "Prefix of the main cash accounts"
msgstr "Prefixo das contas de numerário principais"

#. module: account
#: model:account.account.type,name:account.data_account_type_prepayments
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Prepayments"
msgstr "Pagamento Antecipado"

#. module: account
#: selection:account.financial.report,sign:0
msgid "Preserve balance sign"
msgstr "Manter sinal do saldo"

#. module: account
#: model:ir.model,name:account.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""
"Modelo para criar entradas no diário durante uma conciliação de faturas e "
"pagamentos"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.account_common_report_view
msgid "Print"
msgstr "Imprimir"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Print Invoice"
msgstr "Imprimir Fatura"

#. module: account
#: model:ir.model.fields,help:account.field_account_common_journal_report_amount_currency
#: model:ir.model.fields,help:account.field_account_print_journal_amount_currency
msgid ""
"Print Report with the currency column if the currency differs from the "
"company currency."
msgstr ""
"Imprimir Relatório com a coluna 'Moeda' se a moeda for diferente da moeda da"
" empresa."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Print checks to pay your vendors"
msgstr "Imprima cheques para pagar aos seus fornecedores"

#. module: account
#: model:ir.model,name:account.model_product_product
#: model:ir.model.fields,field_description:account.field_account_analytic_line_product_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_product_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_product_id
#: model:ir.model.fields,field_description:account.field_account_move_line_product_id
#: model:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
msgid "Product"
msgstr "Artigo"

#. module: account
#: model:ir.model,name:account.model_product_category
#: model:ir.model.fields,field_description:account.field_account_invoice_report_categ_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Product Category"
msgstr "Categoria do Artigo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_product_image
msgid "Product Image"
msgstr "Imagem do Artigo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_product_qty
msgid "Product Quantity"
msgstr "Quantidade de Artigo"

#. module: account
#: model:ir.model,name:account.model_product_template
msgid "Product Template"
msgstr "Modelo de Artigo"

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action
#: model:ir.ui.view,arch_db:account.product_product_view_tree
msgid "Products"
msgstr "Artigos"

#. module: account
#: code:addons/account/models/account_bank_statement.py:177
#, python-format
msgid "Profit"
msgstr "Ganhos"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Profit &amp; Loss"
msgstr "Ganhos &amp; Prejuízo"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_profitloss_toreport0
msgid "Profit (Loss) to report"
msgstr "Ganhos (Prejuízo) a reportar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_profit_account_id
msgid "Profit Account"
msgstr "Conta de Ganhos"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_profitandloss0
#: model:ir.actions.act_window,name:account.action_account_report_pl
#: model:ir.ui.menu,name:account.menu_account_report_pl
msgid "Profit and Loss"
msgstr "Ganhos e Perdas"

#. module: account
#: code:addons/account/models/account_payment.py:135
#, python-format
msgid ""
"Programming error: wizard action executed without active_ids in context."
msgstr ""
"Erro de programação: ação do assistente executada sem active_ids em "
"contexto."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_form
msgid "Properties"
msgstr "Propriedades"

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action_purchasable
#: model:ir.ui.menu,name:account.product_product_menu_purchasable
msgid "Purchasable Products"
msgstr "Artigos Adquiríveis"

#. module: account
#: selection:account.journal,type:0
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Purchase"
msgstr "Compra"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_wizard_multi_chart
msgid "Purchase Tax"
msgstr "Imposto Sobre a Aquisição"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_purchase_tax_rate
msgid "Purchase Tax(%)"
msgstr "Imposto Sobre a Aquisição(%)"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:35
#, python-format
msgid "Purchase: Untaxed Total"
msgstr "Compra: Total Não Tributado"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
#: model:ir.ui.menu,name:account.menu_finance_payables
#: model:ir.ui.view,arch_db:account.view_account_journal_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Purchases"
msgstr "Compras"

#. module: account
#: model:ir.actions.act_window,name:account.action_cash_box_in
#: model:ir.ui.view,arch_db:account.cash_box_in_form
msgid "Put Money In"
msgstr "Introduzir Dinheiro Em"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Python Code"
msgstr "Código Python"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "QIF Import"
msgstr "Importar QIF"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_quantity
#: model:ir.model.fields,field_description:account.field_account_move_line_quantity
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Quantity"
msgstr "Quantidade"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_description
#: model:ir.model.fields,field_description:account.field_cash_box_in_name
#: model:ir.model.fields,field_description:account.field_cash_box_out_name
msgid "Reason"
msgstr "Quantidade"

#. module: account
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Reason..."
msgstr "Motivo..."

#. module: account
#: model:account.account.type,name:account.data_account_type_receivable
#: selection:account.account.type,type:0
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Receivable"
msgstr "A Receber"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_receivable_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Receivable Account"
msgstr "Conta a Receber"

#. module: account
#: selection:account.aged.trial.balance,result_selection:0
#: selection:account.common.partner.report,result_selection:0
#: selection:account.report.partner.ledger,result_selection:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.view_account_form
#: model:ir.ui.view,arch_db:account.view_account_search
msgid "Receivable Accounts"
msgstr "Contas a Receber"

#. module: account
#: selection:account.aged.trial.balance,result_selection:0
#: selection:account.common.partner.report,result_selection:0
#: selection:account.report.partner.ledger,result_selection:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Receivable and Payable Accounts"
msgstr "Contas a Receber e a Pagar"

#. module: account
#: model:ir.ui.view,arch_db:account.product_template_form_view
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Receivables"
msgstr "Recebíveis"

#. module: account
#: selection:account.abstract.payment,payment_type:0
#: selection:account.payment,payment_type:0
#: selection:account.register.payments,payment_type:0
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Receive Money"
msgstr "Receber Dinheiro"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:73
#: code:addons/account/static/src/xml/account_reconciliation.xml:105
#: code:addons/account/static/src/xml/account_reconciliation.xml:106
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#, python-format
msgid "Reconcile"
msgstr "Reconciliar"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_account_move_line_reconcile
msgid "Reconcile Entries"
msgstr "Reconciliar Entradas"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Reconcile With Write-Off"
msgstr "Reconciliar Com Liquidação"

#. module: account
#: code:addons/account/wizard/account_reconcile.py:86
#, python-format
msgid "Reconcile Writeoff"
msgstr "Reconciliar Liquidação"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/tour_bank_statement_reconciliation.js:11
#, python-format
msgid "Reconcile the demo bank statement"
msgstr "Reconciliar o extrato bancário de demonstração"

#. module: account
#: selection:account.payment,state:0
#: model:ir.model.fields,field_description:account.field_account_move_line_reconciled
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Reconciled"
msgstr "Reconciliado"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_reconciled
msgid "Reconciled Entries"
msgstr "Entradas Reconciliadas"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Reconciled entries"
msgstr "Entradas reconciliadas"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Reconciliation"
msgstr "Reconciliação"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_reconcile_model
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Reconciliation Models"
msgstr "Modelos de Reconciliação"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_partial_reconcile_ids
msgid "Reconciliation Parts"
msgstr "Partes de Reconciliação"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Reconciliation Transactions"
msgstr "Transações de Reconciliação"

#. module: account
#: model:ir.actions.client,name:account.action_bank_reconcile
#: model:ir.actions.client,name:account.action_bank_reconcile_bank_statements
msgid "Reconciliation on Bank Statements"
msgstr "Reconciliação em Extratos Bancários"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Reconciling journal entries"
msgstr "Reconciliação em Extratos Bancários"

#. module: account
#: selection:account.journal,bank_statements_source:0
msgid "Record Manually"
msgstr "Registo Manual"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Record transactions in foreign currencies"
msgstr "Registe transações em moeda estrangeira"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Recording invoices"
msgstr "Registando faturas"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:231
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#, python-format
msgid "Ref"
msgstr "Ref"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_ref
msgid "Ref."
msgstr "Ref."

#. module: account
#: code:addons/account/controllers/portal.py:72
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_ref
#: model:ir.model.fields,field_description:account.field_account_bank_statement_name
#: model:ir.model.fields,field_description:account.field_account_move_line_ref
#: model:ir.model.fields,field_description:account.field_account_move_ref
#: model:ir.model.fields,field_description:account.field_cash_box_in_ref
#, python-format
msgid "Reference"
msgstr "Referência"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_uom_name
msgid "Reference Unit of Measure"
msgstr "Unidade de Medida de Referência"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Reference number"
msgstr "Número de referência"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_origin
#: model:ir.model.fields,help:account.field_account_invoice_origin
msgid "Reference of the document that produced this invoice."
msgstr "Referência do documento que produziu esta fatura."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_payment_reference
msgid ""
"Reference of the document used to issue this payment. Eg. check number, file"
" name, etc."
msgstr ""
"Referência do documento que produziu esta fatura. Ex: cheque, nome do "
"ficheiro, etc."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_name
msgid "Reference/Description"
msgstr "Referência/Descrição"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_invoice_ids
msgid "Refund Invoices"
msgstr "Reembolsar Faturas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_filter_refund
msgid "Refund Method"
msgstr "Método de Reembolso"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_refund_filter_refund
msgid ""
"Refund base on this type. You can not Modify and Cancel if the invoice is "
"already reconciled"
msgstr ""
"Reembolso base deste tipo. Não pode Modificar ou Cancelar se a fatura já "
"estiver reconciliada."

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_payment
#: model:ir.actions.act_window,name:account.action_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.view_account_payment_form
#: model:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Register Payment"
msgstr "Registar Pagamento"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Register Payments"
msgstr "Registar Pagamentos"

#. module: account
#: model:ir.model,name:account.model_account_register_payments
msgid "Register payments on multiple invoices"
msgstr "Registar pagamentos em várias faturas"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Registering outstanding invoices and payments can be a huge undertaking, \n"
"                        but you can start using Odoo without it by:"
msgstr ""
"Registar faturas e pagamentos pendentes pode ser um esforço tremendo, \n"
"                        mas pode começar a usar o Odoo sem o fazer:"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Registering payments related to outstanding invoices separately in a "
"different account (e.g. Account Receivables 2014)"
msgstr ""
"Registar pagamentos relativos a faturas pendentes separadamente numa conta "
"diferente (ex. Contas a Receber 2014)"

#. module: account
#: selection:account.account.type,type:0
msgid "Regular"
msgstr "Periódicos"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_residual_company_signed
msgid "Remaining amount due in the currency of the company."
msgstr "Montante em dívida restante na moeda da empresa."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_residual_signed
msgid "Remaining amount due in the currency of the invoice."
msgstr "Montante em dívida restante na moeda da fatura."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_residual
msgid "Remaining amount due."
msgstr "Montante em dívida restante."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_tax_dest_id
msgid "Replacement Tax"
msgstr "Imposto de Substituição"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_financial_report_form
msgid "Report"
msgstr "Relatório"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_name
msgid "Report Name"
msgstr "Nome do Relatório"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.account_common_report_view
msgid "Report Options"
msgstr "Opções de Relatório"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
msgid "Report Type"
msgstr "Tipo de Relatório"

#. module: account
#: selection:account.financial.report,type:0
#: model:ir.model.fields,field_description:account.field_account_financial_report_account_report_id
msgid "Report Value"
msgstr "Valor do Relatório"

#. module: account
#: model:ir.ui.menu,name:account.account_report_folder
#: model:ir.ui.menu,name:account.menu_finance_reports
msgid "Reporting"
msgstr "Relatórios"

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding_rounding
msgid "Represent the non-zero value smallest coinage (for example, 0.05)."
msgstr ""
"Representa o valor mínimo diferente de zero da moeda mais pequena da divisa "
"(por exemplo, 0.05)"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Reset to Draft"
msgstr "Repor Rascunho"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:236
#, python-format
msgid "Residual"
msgstr "Residual"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_amount_residual
msgid "Residual Amount"
msgstr "Valor Residual"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_amount_residual_currency
msgid "Residual Amount in Currency"
msgstr "Valor Residual em Moeda"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_user_id
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Responsible"
msgstr "Responsável"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_deferred_revenue
msgid "Revenue Recognition"
msgstr "Identificação de Rendimentos"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal_date
msgid "Reversal date"
msgstr "Data da reversão"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Reverse Entry"
msgstr "Entrada de Estorno"

#. module: account
#: code:addons/account/wizard/account_move_reversal.py:20
#: model:ir.actions.act_window,name:account.action_view_account_move_reversal
#: model:ir.ui.view,arch_db:account.view_account_move_reversal
#, python-format
msgid "Reverse Moves"
msgstr "Movimentos de Estorno"

#. module: account
#: selection:account.financial.report,sign:0
msgid "Reverse balance sign"
msgstr "Inverter sinal do saldo"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review Fiscal Positions"
msgstr "Rever Posições Fiscais"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review Terms"
msgstr "Rever Condições"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review existing Taxes"
msgstr "Rever Impostos existentes"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review the Chart of Accounts"
msgstr "Rever o Plano de Contas"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review the list of available currencies (from the"
msgstr "Rever a lista de moedas disponíveis (a partir da"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group_parent_right
msgid "Right Parent"
msgstr "Ascendente á Direita"

#. module: account
#: selection:res.company,tax_calculation_rounding_method:0
msgid "Round Globally"
msgstr "Arredondar Globalmente"

#. module: account
#: selection:res.company,tax_calculation_rounding_method:0
msgid "Round per Line"
msgstr "Arredondar por Linha"

#. module: account
#: model:ir.ui.view,arch_db:account.rounding_form_view
msgid "Rounding Form"
msgstr "Formato de Arredondamento"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_is_rounding_line
msgid "Rounding Line"
msgstr "Linha de Arrendondamento"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_rounding_method
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Rounding Method"
msgstr "Método de Arredondamento"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_rounding
msgid "Rounding Precision"
msgstr "Precisão do Arredondamento"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_strategy
msgid "Rounding Strategy"
msgstr "Estratégia de Arredondamento"

#. module: account
#: model:ir.ui.view,arch_db:account.rounding_tree_view
msgid "Rounding Tree"
msgstr "Esquema de Arredondamento"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_sepa
msgid "SEPA Credit Transfer (SCT)"
msgstr "Transferência de Crédito SEPA (TCS)"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "SEPA Direct Debit (SDD)"
msgstr "Transferência de Débito SEPA (TDS)"

#. module: account
#: selection:account.journal,type:0
#: model:ir.ui.view,arch_db:account.report_tax
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Sale"
msgstr "Venda"

#. module: account
#: model:ir.ui.view,arch_db:account.view_wizard_multi_chart
msgid "Sale Tax"
msgstr "Imposto de Venda"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
#: model:ir.ui.menu,name:account.menu_finance_receivables
#: model:ir.ui.view,arch_db:account.view_account_journal_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Sales"
msgstr "Vendas"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Sales Tax"
msgstr "Imposto Sobre Vendas"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_sale_tax_rate
msgid "Sales Tax(%)"
msgstr "Imposto Sobre Vendas(%)"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:33
#, python-format
msgid "Sales: Untaxed Total"
msgstr "Vendas: Total Não Tributado"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_id
#: model:ir.model.fields,field_description:account.field_account_invoice_user_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Salesperson"
msgstr "Vendedor"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model:ir.ui.view,arch_db:account.setup_opening_move_wizard_form
#: model:ir.ui.view,arch_db:account.setup_posted_move_form
#: model:ir.ui.view,arch_db:account.setup_view_company_form
msgid "Save"
msgstr "Guardar"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:199
#, python-format
msgid "Save and New"
msgstr "Guardar e Novo"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "Guarde esta página e volte para configurar o recurso."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Search Account Journal"
msgstr "Pesquisar Diário de Contas"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_template_search
msgid "Search Account Templates"
msgstr "Pesquisar Modelos de Conta"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Search Bank Statements"
msgstr "Pesquisar Extratos de Conta"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_line_search
msgid "Search Bank Statements Line"
msgstr "Pesquisar Linhas de Extratos de Conta"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Search Chart of Account Templates"
msgstr "Pesquisar Modelos de Planos de Contas"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Search Invoice"
msgstr "Pesquisar Fatura"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Search Journal Items"
msgstr "Pesquisar Itens de Diário"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Search Move"
msgstr "Pesquisar Movimento"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Search Operations"
msgstr "Pesquisar Movimento"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Search Tax Templates"
msgstr "Pesquisar Modelos de Imposto"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Search Taxes"
msgstr "Pesquisar Impostos"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_account_id
msgid "Second Account"
msgstr "Segunda Conta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_amount
msgid "Second Amount"
msgstr "Segundo Valor"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_amount_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_amount_type
msgid "Second Amount type"
msgstr "Segundo Tipo de Valor"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_analytic_account_id
msgid "Second Analytic Account"
msgstr "Segunda Conta Analítica"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_journal_id
msgid "Second Journal"
msgstr "Segundo Diário"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_label
msgid "Second Journal Item Label"
msgstr "Segunda Etiqueta de Item de Diário"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_tax_id
msgid "Second Tax"
msgstr "Segundo Imposto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_access_token
msgid "Security Token"
msgstr "Token de Segurança"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_type
msgid ""
"Select 'Sale' for customer invoices journals.\n"
"Select 'Purchase' for vendor bills journals.\n"
"Select 'Cash' or 'Bank' for journals that are used in customer or vendor payments.\n"
"Select 'General' for miscellaneous operations journals."
msgstr ""
"Selecione 'Venda' para diários de faturas de clientes.\n"
"Selecione 'Compra' para diários de faturas de fornecedores.\n"
"\n"
"Selecione 'Numerário' ou 'Banco' para diários usados em pagamentos de clientes ou fornecedores.\n"
"Selecione 'Geral' para diários de operações diversas."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:74
#, python-format
msgid "Select a partner or choose a counterpart"
msgstr "Selecione um parceiro ou escolha um complemento"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line_value
msgid "Select here the kind of valuation related to this payment terms line."
msgstr ""
"Selecione aqui o tipo de verificação relacionada a esta linha de condições "
"de pagamento."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Select this if the taxes should use cash basis, which will create an entry "
"for such taxes on a given account during reconciliation."
msgstr ""
"Selecione se os impostos devem usar fundo de caixa, o que criará uma entrada"
" para esses impostos nas contas durante a reconciliação."

#. module: account
#: code:addons/account/wizard/account_invoice_state.py:21
#, python-format
msgid ""
"Selected invoice(s) cannot be confirmed as they are not in 'Draft' state."
msgstr ""
"A(s) fatura(s) selecionada(s) não pode(m) ser confirmada(s) pois não se "
"encontra(m) no estado 'Rascunho'."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_invoice_warn
#: model:ir.model.fields,help:account.field_res_users_invoice_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Selecionar a opção \"Aviso\" vai notificar o utilizador com a mensagem, "
"Selecionar \"Mensagem de Bloqueio\" irá lançar uma exceção com a mensagem e "
"bloquear o fluxo. A Mensagem tem que ser escrita no próximo campo."

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action_sellable
#: model:ir.ui.menu,name:account.product_product_menu_sellable
msgid "Sellable Products"
msgstr "Artigos de Venda"

#. module: account
#: selection:account.abstract.payment,payment_type:0
#: selection:account.payment,payment_type:0
#: selection:account.register.payments,payment_type:0
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Send Money"
msgstr "Enviar Dinheiro"

#. module: account
#: model:ir.actions.act_window,name:account.account_send_payment_receipt_by_email_action
msgid "Send Receipt By Email"
msgstr "Enviar Recibo Por Email"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Send by Email"
msgstr "Enviar por E-mail"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Sending customer statements (outstanding invoices) manually during the "
"transition period"
msgstr ""
"Enviar extratos de conta de cliente (faturas pendentes) manualmente durante "
"o período de transição"

#. module: account
#: selection:account.payment,state:0
#: model:ir.model.fields,field_description:account.field_account_invoice_sent
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Sent"
msgstr "Enviado"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "September"
msgstr "Setembro"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_sequence
#: model:ir.model.fields,field_description:account.field_account_financial_report_sequence
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_sequence
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_sequence
#: model:ir.model.fields,field_description:account.field_account_invoice_line_sequence
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_sequence
#: model:ir.model.fields,field_description:account.field_account_journal_sequence
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_sequence
#: model:ir.model.fields,field_description:account.field_account_payment_term_sequence
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_sequence
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_sequence
#: model:ir.model.fields,field_description:account.field_account_tax_group_sequence
#: model:ir.model.fields,field_description:account.field_account_tax_sequence
#: model:ir.model.fields,field_description:account.field_account_tax_template_sequence
msgid "Sequence"
msgstr "Sequência"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Set To Draft"
msgstr "Marcar Como Rascunho"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag_active
msgid "Set active to false to hide the Account Tag without removing it."
msgstr ""
"Marque ativo como falso para ocultar a Etiqueta de Conta sem a remover."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_active
msgid "Set active to false to hide the Journal without removing it."
msgstr "Marque ativo como falso para ocultar o Diário sem o remover."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_active
#: model:ir.model.fields,help:account.field_account_tax_template_active
msgid "Set active to false to hide the tax without removing it."
msgstr "Marque ativo como falso para ocultar o imposto sem o remover."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Set the default Sales and Purchase taxes"
msgstr "Defina os impostos de Vendas e Compras por defeito"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_tax_adjustment
msgid ""
"Set this field to true if this tax can be used in the tax adjustment wizard,"
" used to manually fill some data in the tax declaration"
msgstr ""
"Marque este campo como verdadeiro se este imposto puder ser usado no "
"assistente de ajuste fiscal, usado para inserir alguns dados manualmente na "
"declaração fiscal"

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_visible
msgid ""
"Set this to False if you don't want this template to be used actively in the"
" wizard that generate Chart of Accounts from templates, this is useful when "
"you want to generate accounts of this template only when loading its child "
"template."
msgstr ""
"Marque este campo como Falso se não quiser que este modelo seja usado "
"ativamente no assistente que gera Planos de Contas a partir de modelos, o "
"que é útil se quiser gerar contas deste modelo apenas quando carrega os "
"modelos originados pelo mesmo."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Set to Draft"
msgstr "Marcado como Rascunho"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_config
#: model:ir.ui.menu,name:account.menu_account_config
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Settings"
msgstr "Configurações"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Setup"
msgstr "Configuração"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_bar_closed
msgid "Setup Bar Closed"
msgstr "Barra de Configuração Fechada"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_code
msgid "Short Code"
msgstr "Código Abreviado"

#. module: account
#: model:res.groups,name:account.group_account_user
msgid "Show Full Accounting Features"
msgstr "Mostrar Todas as Funcionalidades de Contabilidade"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Show active taxes"
msgstr "Mostrar impostos ativos"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Show all records which has next action date is before today"
msgstr "Mostrar todos os registos cuja data de ação é anterior à data atual"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Show inactive taxes"
msgstr "Mostrar impostos inativos"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_show_on_dashboard
msgid "Show journal on dashboard"
msgstr "Mostrar diário no painel"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_sign
msgid "Sign on Reports"
msgstr "Assinar Relatórios"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Simplify your cash, checks, or credit cards deposits with an integrated "
"batch payment function."
msgstr ""
"Simplifique os seus depósitos de numerário, cheques ou cartões de crédito "
"com uma funcionalidade de pagamentos em lote integrada."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:107
#, python-format
msgid "Skip"
msgstr "Ignorar"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Smallest Text"
msgstr "Texto Mais Pequeno"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_sortby
msgid "Sort by"
msgstr "Organizar por"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_origin
#: model:ir.model.fields,field_description:account.field_account_invoice_origin
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Source Document"
msgstr "Documento Fonte"

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding_strategy
msgid ""
"Specify which way will be used to round the invoice amount to the rounding "
"precision"
msgstr ""
"Especifique que maneira vai ser usada para arredondar o montante da fatura "
"com a precisão estabelecida"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_date_from
#: model:ir.model.fields,field_description:account.field_account_balance_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_account_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_report_date_from
#: model:ir.model.fields,field_description:account.field_account_print_journal_date_from
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_date_from
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_date_from
#: model:ir.model.fields,field_description:account.field_account_tax_report_date_from
#: model:ir.model.fields,field_description:account.field_accounting_report_date_from
#: model:ir.model.fields,field_description:account.field_accounting_report_date_from_cmp
msgid "Start Date"
msgstr "Data de Início"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_balance_start
msgid "Starting Balance"
msgstr "Saldo Inicial"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_start_id
msgid "Starting Cashbox"
msgstr "Cashbox Inicial"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "State"
msgstr "Estado"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_parent_state
msgid "State of the parent account.move"
msgstr "Estado do account.move principal"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_statement_id
#: model:ir.model.fields,field_description:account.field_account_move_line_statement_id
msgid "Statement"
msgstr "Extrato"

#. module: account
#: code:addons/account/models/account_bank_statement.py:245
#, python-format
msgid "Statement %s confirmed, journal items were created."
msgstr "Extrato %s confirmado, itens do diário foram criados."

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_line_form
msgid "Statement Line"
msgstr "Linha da Extrato"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_ids
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_line_tree
msgid "Statement lines"
msgstr "Linhas de Extrato"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_tree
msgid "Statements"
msgstr "Extratos"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "States"
msgstr "Estados"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_states_count
msgid "States Count"
msgstr "Contagem de Estados"

#. module: account
#: code:addons/account/controllers/portal.py:73
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_state
#: model:ir.model.fields,field_description:account.field_account_bank_statement_state
#: model:ir.model.fields,field_description:account.field_account_invoice_state
#: model:ir.model.fields,field_description:account.field_account_move_state
#: model:ir.model.fields,field_description:account.field_account_payment_state
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Status"
msgstr "Estado"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_subtotal
msgid "Subtotal"
msgstr "Subtotal"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Supplier Payments"
msgstr "Pagamentos a Fornecedor"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "TOTAL ASSETS"
msgstr "TOTAL DE ATIVOS"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "TOTAL EQUITY"
msgstr "TOTAL DE CAPITAL"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_ids
#: model:ir.model.fields,field_description:account.field_account_tax_tag_ids
#: model:ir.ui.view,arch_db:account.account_tag_view_form
msgid "Tags"
msgstr "Etiquetas"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Tags for Multidimensional Analytics"
msgstr "Etiquetas para Analíticos Multidimensionais"

#. module: account
#: model:ir.actions.act_window,name:account.action_cash_box_out
#: model:ir.ui.view,arch_db:account.cash_box_out_form
msgid "Take Money Out"
msgstr "Retirar Dinheiro"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_target_move
#: model:ir.model.fields,field_description:account.field_account_balance_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_account_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_report_target_move
#: model:ir.model.fields,field_description:account.field_account_print_journal_target_move
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_target_move
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_target_move
#: model:ir.model.fields,field_description:account.field_account_tax_report_target_move
#: model:ir.model.fields,field_description:account.field_accounting_report_target_move
msgid "Target Moves"
msgstr "Movimentos Contabilísticos Específicos"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:174
#: model:ir.model,name:account.model_account_tax
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_tax
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_tax_id
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_tax
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#, python-format
msgid "Tax"
msgstr "Imposto"

#. module: account
#: code:addons/account/models/chart_template.py:842
#: code:addons/account/models/chart_template.py:845
#, python-format
msgid "Tax %.2f%%"
msgstr "Imposto %.2f%%"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_account_id
msgid "Tax Account"
msgstr "Conta de Imposto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_refund_account_id
msgid "Tax Account on Credit Notes"
msgstr "Conta de Imposto em Notas de Crédito"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_refund_account_id
msgid "Tax Account on Refunds"
msgstr "Conta de Imposto em Reembolsos"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_tax_adjustment
#: model:ir.model.fields,field_description:account.field_account_tax_template_tax_adjustment
msgid "Tax Adjustment"
msgstr "Ajuste Fiscal"

#. module: account
#: model:ir.actions.act_window,name:account.tax_adjustments_form
#: model:ir.ui.menu,name:account.menu_action_tax_adjustment
msgid "Tax Adjustments"
msgstr "Ajustes Fiscais"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Tax Amount"
msgstr "Montante do Imposto"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Tax Application"
msgstr "Tributação"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_tax_calculation_rounding_method
msgid "Tax Calculation Rounding Method"
msgstr "Método de Arredondamento do Cálculo de Imposto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_tax_cash_basis_rec_id
msgid "Tax Cash Basis Entry of"
msgstr "Entrada de Fundo de Caixa de"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_tax_cash_basis_journal_id
msgid "Tax Cash Basis Journal"
msgstr "Diário de Imposto de Fundo de Caixa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_amount_type
#: model:ir.model.fields,field_description:account.field_account_tax_template_amount_type
msgid "Tax Computation"
msgstr "Cálculo do Imposto"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Tax Declaration"
msgstr "Declaração de Imposto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_name
msgid "Tax Description"
msgstr "Descrição do Imposto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_tax_exigibility
#: model:ir.model.fields,field_description:account.field_account_tax_template_tax_exigibility
msgid "Tax Due"
msgstr "Imposto Vencido"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_tax_group_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_tax_group_id
msgid "Tax Group"
msgstr "Grupo Fiscal"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Tax ID"
msgstr "Id. de Imposto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_line_ids
msgid "Tax Lines"
msgstr "Linhas de Imposto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_tax_ids
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Tax Mapping"
msgstr "Mapeamento Tributário"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_name
#: model:ir.model.fields,field_description:account.field_account_tax_template_name
msgid "Tax Name"
msgstr "Nome do Imposto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_cash_basis_account
#: model:ir.model.fields,field_description:account.field_account_tax_template_cash_basis_account
msgid "Tax Received Account"
msgstr "Conta de Impostos a Recuperar"

#. module: account
#: model:ir.actions.report,name:account.action_report_account_tax
#: model:ir.model,name:account.model_account_tax_report
#: model:ir.ui.menu,name:account.menu_account_report
#: model:ir.ui.view,arch_db:account.report_tax
msgid "Tax Report"
msgstr "Relatório de Imposto"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_tax_report
msgid "Tax Reports"
msgstr "Relatórios de Imposto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_type_tax_use
#: model:ir.model.fields,field_description:account.field_account_tax_type_tax_use
msgid "Tax Scope"
msgstr "Cobertura do Imposto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_tax_src_id
msgid "Tax Source"
msgstr "Fonte do Imposto"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Tax Template"
msgstr "Modelo de Imposto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_tax_template_ids
msgid "Tax Template List"
msgstr "Lista de Modelos de Imposto"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_tax_template_form
msgid "Tax Templates"
msgstr "Modelos de Imposto"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr "Método de arredondamento do cálculo do imposto"

#. module: account
#: sql_constraint:account.tax:0 sql_constraint:account.tax.template:0
msgid "Tax names must be unique !"
msgstr "Nomes dos impostos devem ser únicos!"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_tax_src_id
msgid "Tax on Product"
msgstr "Imposto sobre Artigo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_tax_dest_id
msgid "Tax to Apply"
msgstr "Imposto a Aplicar"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "TaxCloud"
msgstr "Imposto Nuvem"

#. module: account
#: selection:account.account.tag,applicability:0
#: model:account.tax.group,name:account.tax_group_taxes
#: model:ir.actions.act_window,name:account.action_tax_form
#: model:ir.model.fields,field_description:account.field_account_invoice_line_invoice_line_tax_ids
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_ids
#: model:ir.ui.menu,name:account.menu_action_tax_form
#: model:ir.ui.view,arch_db:account.account_planner
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Taxes"
msgstr "Impostos"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_tax
msgid "Taxes Fiscal Position"
msgstr "Posição Fiscal dos Impostos"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_template_form
msgid "Taxes Mapping"
msgstr "Mapeamento Tributário"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Taxes used in Purchases"
msgstr "Impostos sobre Compras"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Taxes used in Sales"
msgstr "Impostos sobre Vendas"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Taxes, fiscal positions, chart of accounts &amp; legal statements for your "
"country"
msgstr ""
"Impostos, situações fiscais, planos de contas &amp; termos legais do seu "
"país"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_balance
msgid ""
"Technical field holding the debit - credit in order to open meaningful graph"
" views from reports"
msgstr ""
"Campo técnico que armazena o débito - crédito de modo a criar vistas de "
"gráficos relevantes a partir dos relatórios"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_balance_cash_basis
msgid ""
"Technical field holding the debit_cash_basis - credit_cash_basis in order to"
" open meaningful graph views from reports"
msgstr ""
"Campo técnico que armazena o debit_cash_basis - credit_cash_basis de modo a "
"criar vistas de gráficos relevantes a partir dos relatórios"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_move_name
msgid ""
"Technical field holding the number given to the invoice, automatically set "
"when the invoice is validated then stored to set the same number again if "
"the invoice is cancelled, set to draft and re-validated."
msgstr ""
"Campo técnico que armazena o número dado à fatura, atribuído automaticamente"
" quando a fatura é validada e depois armazenada de modo a atribuir o mesmo "
"número de novo se a fatura for cancelada, marcada como rascunho e validada "
"de novo."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_move_name
#: model:ir.model.fields,help:account.field_account_payment_move_name
msgid ""
"Technical field holding the number given to the journal entry, automatically"
" set when the statement line is reconciled then stored to set the same "
"number again if the line is cancelled, set to draft and re-processed again."
msgstr ""
"Campo técnico que armazena o número dado à entrada no diário, atribuído "
"automaticamente quando a linha declarativa é reconciliada e depois "
"armazenada para atribuir o mesmo número de novo se a linha for cancelada, "
"marcada como rascunho e processada de novo."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_bank_data_done
msgid "Technical field holding the status of the bank setup step."
msgstr ""
"Campo técnico que armazena o estado do passo atual da configuração do banco."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_coa_done
msgid "Technical field holding the status of the chart of account setup step."
msgstr ""
"Campo técnico que armazena o estado do passo atual da configuração do plano "
"de contas."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_company_data_done
msgid "Technical field holding the status of the company setup step."
msgstr ""
"Campo técnico que armazena o estado do passo atual da configuração da "
"empresa."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_fy_data_done
msgid "Technical field holding the status of the financial year setup step."
msgstr ""
"Campo técnico que armazena o estado do passo atual da configuração do ano "
"fiscal."

#. module: account
#: model:ir.model.fields,help:account.field_account_register_payments_multi
msgid ""
"Technical field indicating if the user selected invoices from multiple "
"partners or from different types."
msgstr ""
"Campo técnico que indica se o utilizador selecionou faturas de vários "
"parceiros ou de tipos diferentes."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_bar_closed
msgid ""
"Technical field set to True when setup bar has been closed by the user."
msgstr ""
"Campo técnico que passa a Verdadeiro quando a barra de configuração é "
"fechada pelo utilizador."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_refund_only
msgid ""
"Technical field to hide filter_refund in case invoice is partially paid"
msgstr ""
"Campo técnico usado para ocultar filter_refund no caso da fatura ser paga "
"parcialmente."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_journal_type
#: model:ir.model.fields,help:account.field_account_payment_has_invoices
msgid "Technical field used for usability purposes"
msgstr "Campo técnico usado com finalidades de funcionalidade"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_matched_percentage
msgid "Technical field used in cash basis method"
msgstr "Campo técnico usado para o método de fundo de caixa"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_account_setup_bank_data_done
msgid "Technical field used in the special view for the setup bar step."
msgstr ""
"Campo técnico usado na vista especial da barra dos passos de configuração."

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_payment_method_code
#: model:ir.model.fields,help:account.field_account_payment_payment_method_code
#: model:ir.model.fields,help:account.field_account_register_payments_payment_method_code
msgid ""
"Technical field used to adapt the interface to the payment type selected."
msgstr ""
"Campo técnico usado para adaptar a interface ao tipo de pagamento "
"selecionado."

#. module: account
#: model:ir.model.fields,help:account.field_account_partial_reconcile_max_date
msgid ""
"Technical field used to determine at which date this reconciliation needs to"
" be shown on the aged receivable/payable reports."
msgstr ""
"Campo técnico usado para determinar em que data se deve mostrar esta "
"reconciliação nos relatórios de cobranças/pagamentos em atraso."

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_hide_payment_method
#: model:ir.model.fields,help:account.field_account_payment_hide_payment_method
#: model:ir.model.fields,help:account.field_account_register_payments_hide_payment_method
msgid ""
"Technical field used to hide the payment method if the selected journal has "
"only one available which is 'manual'"
msgstr ""
"Campo técnico usado para ocultar o método de pagamento se o diário "
"selecionado só tiver o método 'manual' disponível"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_tax_cash_basis_rec_id
msgid ""
"Technical field used to keep track of the tax cash basis reconciliation. "
"This is needed when cancelling the source: it will post the inverse journal "
"entry to cancel that part too."
msgstr ""
"Campo técnico utilizado para acompanhar a reconciliação base de caixa dos "
"impostos. Isto é necessário quando cancelar a origem: isto irá publicar a "
"entrada do diário inverso para também cancelar essa parte."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_tax_exigible
msgid ""
"Technical field used to mark a tax line as exigible in the vat report or not"
" (only exigible journal items are displayed). By default all new journal "
"items are directly exigible, but with the feature cash_basis on taxes, some "
"will become exigible only when the payment is recorded."
msgstr ""
"Campo técnico usado para marcar uma linha de imposto como exigível no "
"relatório do IVA ou não (só apresenta itens exigíveis do diário). Por "
"defeito, todos os novos itens do diário são exigíveis, mas com a "
"funcionalidade cash_basis nos impostos, alguns só se tornam exigíveis quando"
" o pagamento é registado."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_is_unaffected_earnings_line
msgid ""
"Tells whether or not this line belongs to an unaffected earnings account"
msgstr "Indica se esta linha pertence a uma conta de lucros inalterada ou não"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_chart_template_id
msgid "Template"
msgstr "Modelo"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_account_template
msgid "Template Account Fiscal Mapping"
msgstr "Modelo de Mapeamento Fiscal de Conta"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_tax_template
msgid "Template Tax Fiscal Position"
msgstr "Modelo de Imposto de Posição Fiscal"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "Modelo para Posição Fiscal"

#. module: account
#: model:ir.model,name:account.model_account_chart_template
msgid "Templates for Account Chart"
msgstr "Modelos para Plano de Contas"

#. module: account
#: model:ir.model,name:account.model_account_account_template
msgid "Templates for Accounts"
msgstr "Modelos para Contas"

#. module: account
#: model:ir.model,name:account.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Modelos para Impostos"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Term Type"
msgstr "Tipo de Prazo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_ids
#: model:ir.ui.view,arch_db:account.view_payment_term_form
msgid "Terms"
msgstr "Prazos"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Terms &amp; Conditions"
msgstr "Termos &amp; Condições"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Terms and conditions..."
msgstr "Termos e condições..."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "That's on average"
msgstr "É em média"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_internal_type
#: model:ir.model.fields,help:account.field_account_account_type_type
msgid ""
"The 'Internal Type' is used for features available on different types of "
"accounts: liquidity type is for cash or bank accounts, payable/receivable is"
" for vendor/customer accounts."
msgstr ""
"O 'Tipo Interno' é usado para funcionalidades disponíveis em diversos tipos "
"de contas: liquidez é usado para contas bancárias ou de numerário, "
"pagável/cobrável é usado para contas de fornecedor/cliente."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The Advisors have full access to the Accounting application, \n"
"                                        plus access to miscellaneous operations such as salary and asset management."
msgstr ""
"Os Consultores têm acesso total à aplicação de Contabilidade, \n"
"                                        e também acesso a operações diversas como gestão de salários e ativos."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The Deposit Ticket module can also be used to settle credit card batch made "
"of multiple transactions."
msgstr ""
"O módulo Deposit Ticket também pode ser usado para liquidar lotes de cartão "
"de crédito compostos por múltiplas transações."

#. module: account
#: code:addons/account/models/account_move.py:1209
#, python-format
msgid "The account %s (%s) is deprecated !"
msgstr "A conta %s (%s) foi descontinuada!"

#. module: account
#: code:addons/account/models/account_move.py:1021
#, python-format
msgid "The account %s (%s) is not marked as reconciliable !"
msgstr "A conta %s (%s) não está marcada como reconciliável!"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_bank_journal_id
msgid "The accounting journal corresponding to this bank account."
msgstr "O diário contabilístico relativo a esta conta bancária."

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_currency_exchange_journal_id
msgid ""
"The accounting journal where automatic exchange differences will be "
"registered"
msgstr ""
"O diário contabilístico onde diferenças cambiais automáticas vão ser "
"registadas"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_amount_currency
#: model:ir.model.fields,help:account.field_account_move_line_amount_currency
msgid ""
"The amount expressed in an optional other currency if it is a multi-currency"
" entry."
msgstr ""
"O valor expresso numa outra moeda opcional se se tratar de uma entrada "
"multi-moeda."

#. module: account
#: model:ir.model.fields,help:account.field_account_analytic_line_amount_currency
#: model:ir.model.fields,help:account.field_account_analytic_line_analytic_amount_currency
msgid ""
"The amount expressed in the related account currency if not equal to the "
"company one."
msgstr ""
"O valor expresso na moeda da conta correspondente se for diferente da moeda "
"da empresa."

#. module: account
#: code:addons/account/models/account_move.py:508
#, python-format
msgid ""
"The amount expressed in the secondary currency must be positive when account"
" is debited and negative when account is credited."
msgstr ""
"O valor expresso na moeda secundária deve ser positivo quando a conta é "
"debitada e negativo quando a conta é creditada."

#. module: account
#: code:addons/account/models/account.py:804
#, python-format
msgid ""
"The application scope of taxes in a group must be either the same as the "
"group or \"None\"."
msgstr ""
"A aplicação da cobertura de imposto num grupo deve ser a mesma do grupo ou "
"\"Nenhuma\"."

#. module: account
#: code:addons/account/models/account.py:452
#, python-format
msgid ""
"The bank account of a bank journal must belong to the same company (%s)."
msgstr ""
"A conta bancária de um diário bancário deve pertencer à mesma empresa (%s)."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_statement_id
msgid "The bank statement used for bank reconciliation"
msgstr "O extrato bancário usado para reconciliação bancária"

#. module: account
#: code:addons/account/models/account_invoice.py:1165
#, python-format
msgid ""
"The cash rounding cannot be computed because the difference must be added on the biggest tax found and no tax are specified.\n"
"Please set up a tax or change the cash rounding method."
msgstr ""
"Impossível calcular o arredondamento de numerário pois a diferença deve ser adicionada ao maior imposto disponível e não foi especificado nenhum imposto.\n"
"Configure um imposto ou altere o método de arredondamento de numerário."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_chart_template_id
msgid "The chart template for the company (if any)"
msgstr "O modelo de Plano de Contas da empresa (se existir)"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
msgid "The closing balance is different than the computed one!"
msgstr "O saldo de fecho é diferente do que foi calculado!"

#. module: account
#: sql_constraint:account.journal:0
msgid "The code and name of the journal must be unique per company !"
msgstr "O código e nome do diário devem ser únicos por empresa!"

#. module: account
#: sql_constraint:account.account:0
msgid "The code of the account must be unique per company !"
msgstr "O código da conta deve ser único por empresa!"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_commercial_partner_id
msgid ""
"The commercial entity that will be used on Journal Entries for this invoice"
msgstr ""
"A entidade comercial que será usada nas entradas do diário para esta fatura"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_currency_id
msgid "The currency used to enter statement"
msgstr "A moeda usada para a introdução da linha declarativa"

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree1
msgid ""
"The discussions with your customer are automatically displayed at\n"
"                the bottom of each invoice."
msgstr ""
"O que for discutido com o seu cliente é automaticamente apresentado\n"
"                ao fundo de cada fatura."

#. module: account
#: code:addons/account/models/account_bank_statement.py:191
#, python-format
msgid ""
"The ending balance is incorrect !\n"
"The expected balance (%s) is different from the computed one. (%s)"
msgstr ""
"O saldo de fecho está incorreto!\n"
"O saldo esperado (%s) é diferente do saldo calculado. (%s)"

#. module: account
#: model:ir.model.fields,help:account.field_product_category_property_account_expense_categ_id
msgid ""
"The expense is accounted for when a vendor bill is validated, except in "
"anglo-saxon accounting with perpetual inventory valuation in which case the "
"expense (Cost of Goods Sold account) is recognized at the customer invoice "
"validation."
msgstr ""
"A despesa é incluída quando uma fatura de fornecedor for validada, exceto em"
" contabilidade anglo-saxónica com estimativas de inventório perpétuas, em "
"cujo caso a despesa (conta de Custo de Mercadorias Vendidas) é incluída na "
"validação da fatura do cliente."

#. module: account
#: model:ir.model.fields,help:account.field_product_product_property_account_expense_id
#: model:ir.model.fields,help:account.field_product_template_property_account_expense_id
msgid ""
"The expense is accounted for when a vendor bill is validated, except in "
"anglo-saxon accounting with perpetual inventory valuation in which case the "
"expense (Cost of Goods Sold account) is recognized at the customer invoice "
"validation. If the field is empty, it uses the one defined in the product "
"category."
msgstr ""
"A despesa é incluída quando uma fatura de fornecedor for validada, exceto em"
" contabilidade anglo-saxónica com estimativas de inventório perpétuas, em "
"cujo caso a despesa (conta de Custo de Mercadorias Vendidas) é incluída na "
"validação da fatura do cliente. Se o campo estiver vazio, vai utilizar "
"aquela definida na categoria do artigo."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The first step is to set up your company's information. This is mostly used "
"in printed business documents like orders and invoices."
msgstr ""
"O primeiro passo para configurar a informação da sua empresa. Esta é "
"utilizada principalmente nos documentos de negócio impressos, tais como "
"encomendas e faturas."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_account_position_id
#: model:ir.model.fields,help:account.field_res_users_property_account_position_id
msgid ""
"The fiscal position will determine taxes and accounts used for the partner."
msgstr ""
"A situação fiscal vai determinar os impostos e contas usados pelo parceiro."

#. module: account
#: code:addons/account/models/account.py:456
#, python-format
msgid "The holder of a journal's bank account must be the company (%s)."
msgstr ""
"O titular de uma conta bancária de um diário tem que ser a empresa (%s)."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_account_id
msgid "The income or expense account related to the selected product."
msgstr "A conta de rendimentos ou despesas relativa ao artigo selecionado."

#. module: account
#: code:addons/account/models/account_payment.py:643
#, python-format
msgid "The journal %s does not have a sequence, please specify one."
msgstr "O diário %s não contém uma sequência, defina uma a utilizar."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_code
msgid "The journal entries of this journal will be named using this prefix."
msgstr ""
"As entradas de diário deste diário serão identificadas utilizando este "
"prefixo."

#. module: account
#: model:ir.model.fields,help:account.field_account_opening_opening_move_id
#: model:ir.model.fields,help:account.field_res_company_account_opening_move_id
msgid ""
"The journal entry containing the initial balance of all this company's "
"accounts."
msgstr ""
"A entrada do diário que contém o saldo inicial de todas as contas desta "
"empresa."

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op_fiscalyear_last_day
#: model:ir.model.fields,help:account.field_account_financial_year_op_fiscalyear_last_month
msgid ""
"The last day of the month will be taken if the chosen day doesn't exist."
msgstr ""
"O último dia do mês será retirado se o mesmo não existir no calendário real."

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_form
msgid ""
"The last line's computation type should be \"Balance\" to ensure that the "
"whole amount will be allocated."
msgstr ""
"O tipo de cálculo da última linha deve ser Saldo para garantir que será "
"alocado o montante por inteiro."

#. module: account
#: code:addons/account/models/company.py:94
#, python-format
msgid "The lock date for advisors is irreversible and can't be removed."
msgstr ""
"A data de fecho para consultores é irreversível e não pode ser alterada."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_move_id
msgid "The move of this entry line."
msgstr "O movimento desta linha de entrada."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The multi-currency option allows you to send or receive invoices \n"
"                        in difference currencies, set up foreign bank accounts \n"
"                        and run reports on your foreign financial activities."
msgstr ""
"A opção de multi-moeda permite-lhe enviar ou receber faturas \n"
"                        em moedas diferentes, configurar contas bancárias estrangeiras \n"
"                        e conduzir relatórios nas suas atividades financeiras no estrangeiro."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_name
msgid "The name that will be used on account move lines"
msgstr "O nome que vai ser usado nas linhas de movimentos de conta."

#. module: account
#: code:addons/account/models/company.py:98
#, python-format
msgid ""
"The new lock date for advisors must be set after the previous lock date."
msgstr ""
"A nova data de fecho para consultores deve ser estabecida posteriormente à "
"data de fecho anterior."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_refund_sequence_number_next
msgid "The next sequence number will be used for the next credit note."
msgstr ""
"O próximo número da sequência irá ser usado na próxima nota de crédito."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_sequence_number_next
msgid "The next sequence number will be used for the next invoice."
msgstr "O próximo número da sequência irá ser usado na próxima fatura."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_currency_id
#: model:ir.model.fields,help:account.field_account_move_line_currency_id
msgid "The optional other currency if it is a multi-currency entry."
msgstr "A outra moeda opcional se se tratar de uma entrada multi-moeda."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_quantity
msgid ""
"The optional quantity expressed by this line, eg: number of product sold. "
"The quantity is not a legal requirement but is very useful for some reports."
msgstr ""
"A quantidade opcional expressa por esta linha, ex: número de artigos "
"vendidos. A quantidade não é um requerimento legal, mas é muito útil para "
"alguns relatórios."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_account_id
msgid "The partner account used for this invoice."
msgstr "A conta de parceiro usada para esta fatura."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_has_unreconciled_entries
#: model:ir.model.fields,help:account.field_res_users_has_unreconciled_entries
msgid ""
"The partner has at least one unreconciled debit and credit since last time "
"the invoices & payments matching was performed."
msgstr ""
"O parceiro tem pelo menos um débito e crédito não reconciliado desde a "
"última vez que foi feito o acerto de faturas e pagamentos."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_reference
msgid "The partner reference of this invoice."
msgstr "A referência do parceiro desta fatura."

#. module: account
#: code:addons/account/models/account.py:522
#, python-format
msgid ""
"The partners of the journal's company and the related bank account mismatch."
msgstr ""
"Os parceiros da empresa deste diário e a respetiva conta de banco não "
"coincidem."

#. module: account
#: code:addons/account/models/account_payment.py:60
#, python-format
msgid "The payment amount cannot be negative."
msgstr "O montante de pagamento não pode ser negativo."

#. module: account
#: code:addons/account/models/account_payment.py:487
#, python-format
msgid "The payment cannot be processed because the invoice is not open!"
msgstr "O pagamento não pode ser processado porque a fatura não está aberta!"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_amount_residual_currency
msgid ""
"The residual amount on a journal item expressed in its currency (possibly "
"not the company currency)."
msgstr ""
"O montante residual num item do diário expresso na sua moeda (possivelmente "
"diferente da moeda da empresa)."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_amount_residual
msgid ""
"The residual amount on a journal item expressed in the company currency."
msgstr "O montante residual num item do diário expresso na moeda da empresa."

#. module: account
#: code:addons/account/models/account_move.py:493
#, python-format
msgid ""
"The selected account of your Journal Entry forces to provide a secondary "
"currency. You should remove the secondary currency on the account."
msgstr ""
"A conta selecionada na entrada do diário implica que forneça uma moeda "
"secundária. Remova a moeda secundária da conta."

#. module: account
#: code:addons/account/models/account_invoice.py:1612
#, python-format
msgid ""
"The selected unit of measure is not compatible with the unit of measure of "
"the product."
msgstr ""
"A unidade de medida selecionada não é compatível com a unidade de medida do "
"artigo."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_sequence
#: model:ir.model.fields,help:account.field_account_tax_template_sequence
msgid ""
"The sequence field is used to define order in which the tax lines are "
"applied."
msgstr ""
"O campo de sequência é usado para definir a ordem pela qual as linhas de "
"imposto são aplicadas."

#. module: account
#: code:addons/account/models/account_payment.py:645
#, python-format
msgid "The sequence of journal %s is deactivated."
msgstr "A sequência do diário %s está desativada."

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding_rounding_method
msgid "The tie-breaking rule used for float rounding operations"
msgstr "A regra de desempate usada para operações de arredondamento de floats"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid "There are currently no invoices and payments for your account."
msgstr "Não existem quaisquer faturas ou pagamentos na sua conta de momento."

#. module: account
#: code:addons/account/models/company.py:178
#, python-format
msgid ""
"There are still unposted entries in the period you want to lock. You should "
"either post or delete them."
msgstr ""
"Ainda existem entradas não lançadas no período que pretende bloquear. Deve "
"primeiro lançar ou apagar as entradas."

#. module: account
#: code:addons/account/models/account_bank_statement.py:179
#, python-format
msgid ""
"There is no account defined on the journal %s for %s involved in a cash "
"difference."
msgstr ""
"Não existe nenhuma conta definida no diário %s para %s envolvidas em "
"diferenças de numerário."

#. module: account
#: code:addons/account/wizard/account_validate_account_move.py:18
#, python-format
msgid "There is no journal items in draft state to post."
msgstr "Não existem itens do diário no estado 'Rascunho' para lançar."

#. module: account
#: code:addons/account/models/account_move.py:1744
#, python-format
msgid ""
"There is no tax cash basis journal defined for this company: \"%s\" \n"
"Configure it in Accounting/Configuration/Settings"
msgstr ""
"Não existe diário de imposto de fundo de caixa definido para esta empresa: \"%s\"\n"
"Configure-o em Contabilidade/Configuração/Definições"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:46
#, python-format
msgid "There is nothing to reconcile."
msgstr "Não há nada a reconciliar."

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_error
msgid "There was an error processing this page."
msgstr "Houve um erro ao processar esta página."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "These taxes are set in any new product created."
msgstr "Estes impostos são associados a qualquer novo artigo criado."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_user_type_id
msgid ""
"These types are defined according to your country. The type contains more "
"information about the account and its specificities."
msgstr ""
"Estes tipos são definidos de acordo com o seu país. O tipo contém mais "
"informação acerca da conta e das suas especificidades."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "These users handle billing specifically."
msgstr "Estes utilizadores tratam da faturação distintamente."

#. module: account
#: code:addons/account/models/account_invoice.py:1337
#, python-format
msgid ""
"This %s has been created from: <a href=# data-oe-model=account.invoice data-"
"oe-id=%d>%s</a>"
msgstr ""
"Esta %s foi criada a partir de: <a href=# data-oe-model=account.invoice "
"data-oe-id=%d>%s</a>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "This Month"
msgstr "Este Mês"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:111
#, python-format
msgid "This Week"
msgstr "Esta Semana"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "This Year"
msgstr "Este Ano"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_account_payable_id
#: model:ir.model.fields,help:account.field_res_users_property_account_payable_id
msgid ""
"This account will be used instead of the default one as the payable account "
"for the current partner"
msgstr ""
"Esta conta vai ser usada em vez da conta por defeito como conta a pagar para"
" o parceiro corrente"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_account_receivable_id
#: model:ir.model.fields,help:account.field_res_users_property_account_receivable_id
msgid ""
"This account will be used instead of the default one as the receivable "
"account for the current partner"
msgstr ""
"Esta conta vai ser usada em vez da conta por defeito como conta a receber "
"para o parceiro corrente"

#. module: account
#: model:ir.model.fields,help:account.field_product_category_property_account_income_categ_id
msgid "This account will be used when validating a customer invoice."
msgstr "Esta conta vai ser usada ao validar uma fatura de cliente."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows accountants to manage analytic and crossovered budgets. Once the"
" master budgets and the budgets are defined, the project managers can set "
"the planned amount on each analytic account."
msgstr ""
"Isto permite aos contabilistas gerir orçamentos analíticos e cruzados. Assim"
" que os orçamentos forem definidos, os gestores de projeto podem indicar o "
"montante planeado em cada conta analítica."

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_module_account_batch_deposit
msgid ""
"This allows you to group received checks before you deposit them to the bank.\n"
"-This installs the module account_batch_deposit."
msgstr ""
"Isto permite-lhe agrupar os cheques recebidos antes de os depositar no banco.\n"
"- Isto instala o módulo account_batch_deposit."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows you to manage the assets owned by a company or a person. It "
"keeps track of the depreciation occurred on those assets, and creates "
"account move for those depreciation lines."
msgstr ""
"Isto permite gerir os ativos de uma empresa ou de uma pessoa. Permite "
"monitorizar a desvalorização associada a esses ativos e cria movimentos de "
"conta para essas linhas de desvalorização."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows you to manage the revenue recognition on selling products. It "
"keeps track of the installments occurred on those revenue recognitions, and "
"creates account moves for those installment lines."
msgstr ""
"Isto permite gerir a realização de receitas de artigos vendidos. Permite "
"monitorizar as prestações que ocorrem nessas realizações e cria movimentos "
"de conta para essas linhas de prestação."

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_complete_tax_set
msgid ""
"This boolean helps you to choose if you want to propose to the user to "
"encode the sale and purchase rates or choose from list of taxes. This last "
"choice assumes that the set of tax defined on this template is complete"
msgstr ""
"Este booliano ajuda-o a escolher se deseja propor ao utilizador para "
"codificar as taxas de vendas e de compras ou escolher da lista de impostos. "
"Esta última opção assume que o conjunto de impostos definido neste modelo "
"está completo"

#. module: account
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_complete_tax_set
msgid ""
"This boolean helps you to choose if you want to propose to the user to "
"encode the sales and purchase rates or use the usual m2o fields. This last "
"choice assumes that the set of tax defined for the chosen template is "
"complete"
msgstr ""
"Este booliano ajuda-o a escolher se deseja propor ao utilizador para "
"codificar as tarifas de vendas e de compras ou utilizar os campos m2o "
"normais. Esta última opção assume que o conjunto de impostos definido para o"
" modelo escolhido está completo"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "This feature is useful if you issue a high amounts of invoices."
msgstr "Esta funcionalidade é útil se emitir um grande volume de faturas."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_refund_sequence_id
msgid ""
"This field contains the information related to the numbering of the credit "
"note entries of this journal."
msgstr ""
"Este campo contém a informação relativa à numeração das entradas de notas de"
" crédito deste diário."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_sequence_id
msgid ""
"This field contains the information related to the numbering of the journal "
"entries of this journal."
msgstr ""
"Este campo contém a informação relativa à numeração das entradas de diário "
"deste diário."

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model_journal_id
#: model:ir.model.fields,help:account.field_account_reconcile_model_second_journal_id
msgid "This field is ignored in a bank statement reconciliation."
msgstr "Este campo é ignorado numa reconciliação de extrato bancário."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_date_maturity
msgid ""
"This field is used for payable and receivable journal entries. You can put "
"the limit date for the payment of this line."
msgstr ""
"Este campo é usado para entradas no diário a pagar e a receber. Pode inserir"
" a data limite para o pagamento desta linha."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_partner_name
msgid ""
"This field is used to record the third party name when importing bank "
"statement in electronic format, when the partner doesn't exist yet in the "
"database (or cannot be found)."
msgstr ""
"Este campo é usado para registar o nome da entidade interveniente quando "
"importa extratos bancários em formato eletrónico, quando o parceiro ainda "
"não existe na base de dados (ou não foi encontrado)."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"This guide will help you get started with Odoo Accounting.\n"
"                        Once you're done, you'll benefit from:"
msgstr ""
"Este guia vai ajudá-lo a começar a utilizar a Contabilidade do Odoo.\n"
"                        Quando o concluir, beneficiará de:"

#. module: account
#: model:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid ""
"This is the accounting dashboard. If you have not yet\n"
"                installed a chart of account, please install one first."
msgstr ""
"Este é o painel de contabilidade. Se ainda não\n"
"                instalou um plano de contas, instale um primeiro."

#. module: account
#: code:addons/account/models/account.py:494
#, python-format
msgid ""
"This journal already contains items, therefore you cannot modify its "
"company."
msgstr ""
"Este diário já contém itens, portanto não pode modificar a empresa a que diz"
" respeito."

#. module: account
#: code:addons/account/models/account.py:503
#, python-format
msgid ""
"This journal already contains items, therefore you cannot modify its short "
"name."
msgstr ""
"Este diário já contém itens, portanto não pode modificar o seu nome "
"abreviado."

#. module: account
#: model:ir.model.fields,help:account.field_accounting_report_label_filter
msgid ""
"This label will be displayed on report to show the balance computed for the "
"given comparison filter."
msgstr ""
"Esta etiqueta será exibida em relatórios para mostrar o saldo calculado para"
" o filtro de comparação indicado."

#. module: account
#: code:addons/account/models/account_payment.py:533
#, python-format
msgid ""
"This method should only be called to process a single invoice's payment."
msgstr ""
"Este método só deve ser chamado para processar um pagamento de uma única "
"fatura."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:395
#, python-format
msgid ""
"This move's amount is higher than the transaction's amount. Click to "
"register a partial payment and keep the payment balance open."
msgstr ""
"O valor deste movimento é mais elevado que o valor da transação. Clique para"
" registar um pagamento parcial e manter o saldo de pagamentos aberto."

#. module: account
#: model:ir.model.fields,help:account.field_accounting_report_debit_credit
msgid ""
"This option allows you to get more details about the way your balances are "
"computed. Because it is space consuming, we do not allow to use it while "
"doing a comparison."
msgstr ""
"Esta opção permite-lhe obter mais detalhes acerca da forma como os seus "
"saldos são calculados. Como consome muito espaço, não é permitida quando é "
"feita uma comparação."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_chart_template_id
msgid ""
"This optional field allow you to link an account template to a specific "
"chart template that may differ from the one its root parent belongs to. This"
" allow you to define chart templates that extend another and complete it "
"with few new accounts (You don't need to define the whole structure that is "
"common to both several times)."
msgstr ""
"Este campo opcional permite-lhe vincular um modelo de conta de um modelo do "
"plano de contas específico que pode diferir do seu original a que pertence. "
"Isto permite-lhe definir modelos para o plano de contas que se estendem "
"noutro e concluí-lo com algumas contas novas (não precisa de definir toda a "
"estrutura que é comum a ambos, várias vezes)."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:35
#, python-format
msgid ""
"This page displays all the bank transactions that are to be reconciled and "
"provides with a neat interface to do so."
msgstr ""
"Esta página apresenta todas as transações bancárias a reconciliar e "
"proporciona-lhe uma interface simples para o fazer."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:240
#, python-format
msgid "This payment is registered but not reconciled."
msgstr "Este pagamento foi registado mas não reconciliado."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_supplier_payment_term_id
#: model:ir.model.fields,help:account.field_res_users_property_supplier_payment_term_id
msgid ""
"This payment term will be used instead of the default one for purchase "
"orders and vendor bills"
msgstr ""
"Esta condição de pagamento vai ser usada em vez da condição por defeito para"
" notas de encomenda e faturas de fornecedor"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_payment_term_id
#: model:ir.model.fields,help:account.field_res_users_property_payment_term_id
msgid ""
"This payment term will be used instead of the default one for sales orders "
"and customer invoices"
msgstr ""
"Esta condição de pagamento vai ser usada em vez da condição por defeito para"
" ordens de venda e faturas de cliente"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"This role is best suited for managing the day to day accounting operations:"
msgstr ""
"Esta função é mais indicada para gerir as operações contabilísticas do "
"dia-a-dia:"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_account_id
msgid ""
"This technical field can be used at the statement line creation/import time "
"in order to avoid the reconciliation process on it later on. The statement "
"line will simply create a counterpart on this account"
msgstr ""
"Este campo técnico pode ser usado aquando da criação/importação da linha "
"declarativa para evitar o processo de reconciliação da mesma mais tarde. A "
"linha declarativa criará simplesmente um complemento para esta conta"

#. module: account
#: model:ir.actions.act_window,help:account.action_validate_account_move
msgid ""
"This wizard will validate all journal entries selected. Once journal entries"
" are validated, you can not update them anymore."
msgstr ""
"Este assistente irá validar todas as entradas do diário selecionadas. Assim "
"que as entradas do diário estiverem validadas, já não as poderá alterar."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_reconcile_model
msgid ""
"Those can be used to quickly create a journal items when reconciling\n"
"                a bank statement or an account."
msgstr ""
"Esses podem ser usados para rapidamente criar itens de diário quando\n"
"                reconciliar um extrato de conta ou uma conta."

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_account_hide_setup_bar
msgid "Tick if you wish to hide the setup bar on the dashboard"
msgstr "Marque se deseja ocultar a barra de configuração no painel"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:31
#, python-format
msgid "Tip: Hit CTRL-Enter to reconcile all the balanced items in the sheet."
msgstr ""
"Dica: pressione CTRL-Enter para reconciliar todos os itens saldados na "
"folha."

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Title 2 (bold)"
msgstr "Título 2 (negrito)"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Title 3 (bold, smaller)"
msgstr "Título 3 (negrito, mais pequeno)"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "To Invoice"
msgstr "Para Faturar"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "To Pay"
msgstr "A Pagamento"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"To manage the tax applied when invoicing a Company, Odoo uses the concept of"
" Fiscal Position: they serve to automatically set the right tax and/or "
"account according to the customer country and state."
msgstr ""
"De modo a gerir o imposto aplicado quando fatura uma Empresa, o Odoo usa o "
"conceito de Posição Fiscal: serve para automaticamente configurar o imposto "
"correto e/ou conta relativamente ao país e estado do cliente."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "To pay"
msgstr "A pagamento"

#. module: account
#: code:addons/account/models/account_move.py:1017
#, python-format
msgid "To reconcile the entries company should be the same for all entries!"
msgstr ""
"Para reconciliar as entradas, a empresa deve ser a mesma para todas as "
"entradas!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "To use the <strong>multi-currency option:</strong>"
msgstr "Para usar a opção <strong>multi-moeda: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Today Activities"
msgstr "Atividades do Dia"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_total
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
msgid "Total"
msgstr "Total"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_tree
msgid "Total Amount"
msgstr "Valor Total"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_lines_tree
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_tree
msgid "Total Credit"
msgstr "Crédito Total"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_lines_tree
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_tree
msgid "Total Debit"
msgstr "Débito Total"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_total_invoiced
#: model:ir.model.fields,field_description:account.field_res_users_total_invoiced
msgid "Total Invoiced"
msgstr "Total Faturado"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_debit
#: model:ir.model.fields,field_description:account.field_res_users_debit
msgid "Total Payable"
msgstr "Total Por Saldar"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_credit
#: model:ir.model.fields,field_description:account.field_res_users_credit
msgid "Total Receivable"
msgstr "Total Cobrável"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_currency_residual
msgid "Total Residual"
msgstr "Total Residual"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_price_total
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_currency_price_total
msgid "Total Without Tax"
msgstr "Total Sem Imposto"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_price_subtotal_signed
msgid "Total amount in the currency of the company, negative for credit note."
msgstr "Valor total na moeda da empresa, negativo se for nota de crédito."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_amount_total_company_signed
msgid ""
"Total amount in the currency of the company, negative for credit notes."
msgstr "Valor total na moeda da empresa, negativo se forem notas de crédito."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_amount_total_signed
msgid ""
"Total amount in the currency of the invoice, negative for credit notes."
msgstr "Valor total na moeda da fatura, negativo se for nota de crédito."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_credit
#: model:ir.model.fields,help:account.field_res_users_credit
msgid "Total amount this customer owes you."
msgstr "Valor total devido por este cliente."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_price_total
msgid "Total amount with taxes"
msgstr "Valor total com impostos"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_price_subtotal
msgid "Total amount without taxes"
msgstr "Valor total sem impostos"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_debit
#: model:ir.model.fields,help:account.field_res_users_debit
msgid "Total amount you have to pay to this vendor."
msgstr "Valor total a pagar a este fornecedor."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_total_company_signed
msgid "Total in Company Currency"
msgstr "Total na moeda da Empresa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_total_signed
msgid "Total in Invoice Currency"
msgstr "Total na moeda da Fatura"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_total_entry_encoding
msgid "Total of transaction lines."
msgstr "Total das linhas de transação."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Track costs &amp; revenues by project, department, etc."
msgstr "Acompanhe custos &amp; receitas por projeto, departamento, etc."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:250
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#, python-format
msgid "Transaction"
msgstr "Transação"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "Transactions"
msgstr "Transações"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_total_entry_encoding
msgid "Transactions Subtotal"
msgstr "Subtotal das Transações"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_transfer_account_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_transfer_account_id
msgid "Transfer Account"
msgstr "Conta de Transferência"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_destination_journal_id
msgid "Transfer To"
msgstr "Transferir Para"

#. module: account
#: code:addons/account/models/account_payment.py:357
#, python-format
msgid "Transfer account not defined on the company."
msgstr "Conta de transferências não definida na empresa."

#. module: account
#: code:addons/account/models/account_payment.py:618
#, python-format
msgid "Transfer from %s"
msgstr "Transferência de %s"

#. module: account
#: code:addons/account/models/account_payment.py:699
#, python-format
msgid "Transfer to %s"
msgstr "Transferência para %s"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Transfers"
msgstr "Transferências"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_balance_menu
#: model:ir.actions.report,name:account.action_report_trial_balance
#: model:ir.ui.menu,name:account.menu_general_Balance_report
msgid "Trial Balance"
msgstr "Balancete Geral"

#. module: account
#: model:ir.model,name:account.model_account_balance_report
msgid "Trial Balance Report"
msgstr "Relatório do Balancete Geral"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_user_type_id
#: model:ir.model.fields,field_description:account.field_account_account_type_type
#: model:ir.model.fields,field_description:account.field_account_account_user_type_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_journal_type
#: model:ir.model.fields,field_description:account.field_account_financial_report_type
#: model:ir.model.fields,field_description:account.field_account_invoice_line_invoice_type
#: model:ir.model.fields,field_description:account.field_account_invoice_report_type
#: model:ir.model.fields,field_description:account.field_account_invoice_type
#: model:ir.model.fields,field_description:account.field_account_journal_type
#: model:ir.model.fields,field_description:account.field_account_move_line_user_type_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_value
msgid "Type"
msgstr "Tipo"

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "UP"
msgstr "PARA CIMA"

#. module: account
#: selection:account.journal,bank_statements_source:0
msgid "Undefined Yet"
msgstr "Ainda Não Definido"

#. module: account
#: code:addons/account/models/company.py:367
#, python-format
msgid "Undistributed Profits/Losses"
msgstr "Lucros/Prejuízos Não Distribuídos"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:392
#, python-format
msgid "Undo the partial reconciliation."
msgstr "Desfazer reconciliação parcial."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_unit
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Unit Price"
msgstr "Preço Unitário"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_product_uom_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_uom_id
#: model:ir.model.fields,field_description:account.field_account_move_line_product_uom_id
msgid "Unit of Measure"
msgstr "Unidade de Medida"

#. module: account
#: code:addons/account/report/account_aged_partner_balance.py:194
#, python-format
msgid "Unknown Partner"
msgstr "Parceiro Desconhecido"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Unless you are starting a new business, you probably have a list of "
"customers and vendors you'd like to import."
msgstr ""
"A menos que esteja a criar um novo negócio, provavelmente já tem uma lista "
"de clientes e fornecedores que gostaria de importar."

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model:ir.ui.view,arch_db:account.setup_view_company_form
msgid "Unmark as done"
msgstr "Desmarcar como feito"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_invoice_opened
msgid "Unpaid Invoices"
msgstr "Faturas Vencidas"

#. module: account
#: selection:account.move,state:0
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unposted"
msgstr "Não Lançados"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Unposted Journal Entries"
msgstr "Entradas do Diário Não Lançadas"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unposted Journal Items"
msgstr "Itens do Diário Não Lançados"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:81
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
#, python-format
msgid "Unreconcile"
msgstr "Anular Reconciliação"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_unreconcile
msgid "Unreconcile Entries"
msgstr "Anular Reconciliação de Entradas"

#. module: account
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
msgid "Unreconcile Transactions"
msgstr "Anular Reconciliação de Transações"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unreconciled"
msgstr "Não Reconciliadas"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_acount_move_line_open_unreconciled
msgid "Unreconciled Entries"
msgstr "Entradas Não Reconciliadas"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_untaxed
msgid "Untaxed Amount"
msgstr "Montante sem imposto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_untaxed_signed
msgid "Untaxed Amount in Company Currency"
msgstr "Montante Não Tributado na Moeda da Empresa"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Update exchange rates automatically"
msgstr "Atualizar automaticamente as taxas de câmbio"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Use"
msgstr "Usar"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_use_anglo_saxon
msgid "Use Anglo-Saxon Accounting"
msgstr "Usar Contabilidade Anglo-Saxónica"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_use_anglo_saxon
msgid "Use Anglo-Saxon accounting"
msgstr "Usar contabilidade Anglo-Saxónica"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_tax_exigibility
msgid "Use Cash Basis"
msgstr "Usar Fundo de Caixa"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_sepa_direct_debit
msgid "Use SEPA Direct Debit"
msgstr "Usar Débito Direto SEPA"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal_journal_id
msgid "Use Specific Journal"
msgstr "Usar Diário Específico"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_anglo_saxon_accounting
msgid "Use anglo-saxon accounting"
msgstr "Usar contabilidade anglo-saxónica"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_batch_deposit
msgid "Use batch deposit"
msgstr "Usar depósito em lote"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Use depreciation boards, automate amortization entries"
msgstr "Use quadros de desvalorização, automatize entradas de amortização"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Use follow-up levels and schedule actions"
msgstr "Use níveis de seguimento e agende ações"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Use in conjunction with contracts to calculate your monthly revenue for "
"multi-month contracts."
msgstr ""
"Use em conjunção com contratos para calcular a sua receita mensal para "
"contratos de vários meses."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"Use this option if you want to cancel an invoice and create a new\n"
"                                one. The credit note will be created, validated and reconciled\n"
"                                with the current invoice. A new draft invoice will be created\n"
"                                so that you can edit it."
msgstr ""
"Use esta opção se deseja cancelar uma fatura e criar outra nova.\n"
"                                A nota de crédito será criada, validada e reconciliada\n"
"                                com a fatura corrente. Um novo rascunho de fatura será\n"
"                                criado para que o possa editar."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"Use this option if you want to cancel an invoice you should not\n"
"                                have issued. The credit note will be created, validated and reconciled\n"
"                                with the invoice. You will not be able to modify the credit note."
msgstr ""
"Use esta opção se deseja cancelar uma fatura que não devia \n"
"                                ter emitido. A nota de crédito será criada, \n"
"validada e reconciliada\n"
"                                com a fatura. Não poderá modificar a nota de crédito."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_type_include_initial_balance
msgid ""
"Used in reports to know if we should consider journal items from the "
"beginning of time instead of from the fiscal year only. Account types that "
"should be reset to zero at each new fiscal year (like expenses, revenue..) "
"should not have this option set."
msgstr ""
"Usado em relatórios para saber se devemos considerar itens do diário desde o"
" início de tudo em vez de apenas do início do ano fiscal. Tipos de conta que"
" que devem ser zerados a cada novo ano fiscal (como despesas, receitas...) "
"não devem ter esta opção ativa."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_reference
msgid ""
"Used to hold the reference of the external mean that created this statement "
"(name of imported file, reference of online synchronization...)"
msgstr ""
"Usado para armazenar a referência ao meio externo que criou esta declaração "
"(nome do ficheiro importado, referência de sincronização online...)"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_sequence
msgid "Used to order Journals in the dashboard view"
msgstr "Utilizado para ordenar os Diários na visualização do painel"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_loss_account_id
msgid ""
"Used to register a loss when the ending balance of a cash register differs "
"from what the system computes"
msgstr "Usado para ordenar Diários na vista do painel"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_profit_account_id
msgid ""
"Used to register a profit when the ending balance of a cash register differs"
" from what the system computes"
msgstr ""
"Usado para registar um lucro quando o saldo de fecho de uma caixa difere "
"daquilo que o sistema calcular"

#. module: account
#: model:ir.model.fields,help:account.field_account_analytic_line_company_currency_id
#: model:ir.model.fields,help:account.field_account_bank_statement_line_journal_currency_id
#: model:ir.model.fields,help:account.field_account_move_line_company_currency_id
#: model:ir.model.fields,help:account.field_account_partial_reconcile_company_currency_id
#: model:ir.model.fields,help:account.field_res_partner_currency_id
#: model:ir.model.fields,help:account.field_res_users_currency_id
msgid "Utility field to express amount currency"
msgstr "Campo utilitário que exprime a moeda do montante"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_vat_required
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_vat_required
msgid "VAT required"
msgstr "Necessário NIF"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
msgid "VAT:"
msgstr "NIF:"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:72
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#, python-format
msgid "Validate"
msgstr "Validar"

#. module: account
#: model:ir.model,name:account.model_validate_account_move
msgid "Validate Account Move"
msgstr "Validar Movimento de Conta"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Validate purchase orders and control vendor bills by departments."
msgstr ""
"Valide notas de encomenda e controle faturas de fornecedor por departamento."

#. module: account
#: selection:account.bank.statement,state:0
#: model:mail.message.subtype,name:account.mt_invoice_validated
msgid "Validated"
msgstr "Validado"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_value_amount
msgid "Value"
msgstr "Valor"

#. module: account
#: selection:account.abstract.payment,partner_type:0
#: selection:account.payment,partner_type:0
#: selection:account.register.payments,partner_type:0
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_supplier_payment_tree
msgid "Vendor"
msgstr "Fornecedor"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_invoice.py:443
#: code:addons/account/models/account_invoice.py:1215
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Vendor Bill"
msgstr "Fatura de Fornecedor"

#. module: account
#: code:addons/account/models/account_invoice.py:444
#, python-format
msgid "Vendor Bill - %s"
msgstr "Fatura de Fornecedor - %s"

#. module: account
#: code:addons/account/models/chart_template.py:194
#: model:ir.actions.act_window,name:account.action_invoice_tree2
#: model:ir.ui.menu,name:account.menu_action_invoice_tree2
#: model:ir.ui.view,arch_db:account.account_planner
#, python-format
msgid "Vendor Bills"
msgstr "Faturas de Fornecedor"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_invoice.py:445
#: code:addons/account/models/account_payment.py:680
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Vendor Credit Note"
msgstr "Nota de Crédito de Fornecedor"

#. module: account
#: code:addons/account/models/account_invoice.py:446
#, python-format
msgid "Vendor Credit Note - %s"
msgstr "Nota de Crédito de Fornecedor - %s"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_in_refund
#: model:ir.ui.menu,name:account.menu_action_invoice_in_refund
msgid "Vendor Credit Notes"
msgstr "Notas de Crédito de Fornecedor"

#. module: account
#: code:addons/account/models/account_invoice.py:1217
#, python-format
msgid "Vendor Credit note"
msgstr "Nota de Crédito de Fornecedor"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Vendor Flow"
msgstr "Fluxo de Fornecedores"

#. module: account
#: code:addons/account/models/account_payment.py:682
#, python-format
msgid "Vendor Payment"
msgstr "Pagamento a Fornecedor"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_supplier_payment_term_id
#: model:ir.model.fields,field_description:account.field_res_users_property_supplier_payment_term_id
msgid "Vendor Payment Terms"
msgstr "Condições de Pagamento a Fornecedor"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_reference
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Vendor Reference"
msgstr "Referência de Fornecedor"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_product_supplier_taxes_id
#: model:ir.model.fields,field_description:account.field_product_template_supplier_taxes_id
msgid "Vendor Taxes"
msgstr "Impostos de Fornecedor"

#. module: account
#: model:ir.ui.menu,name:account.menu_account_supplier
msgid "Vendors"
msgstr "Fornecedores"

#. module: account
#: selection:account.financial.report,type:0
msgid "View"
msgstr "Ver"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "Warning"
msgstr "Aviso"

#. module: account
#: code:addons/account/models/account_invoice.py:662
#, python-format
msgid "Warning for %s"
msgstr "Aviso de %s"

#. module: account
#: model:ir.ui.view,arch_db:account.partner_view_buttons
msgid "Warning on the Invoice"
msgstr "Aviso na Fatura"

#. module: account
#: code:addons/account/models/account_invoice.py:1554
#: code:addons/account/models/account_invoice.py:1611
#, python-format
msgid "Warning!"
msgstr "Aviso!"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_group_warning_account
msgid "Warnings"
msgstr "Avisos"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"We can handle the whole import process\n"
"                                        for you: simply send your Odoo project\n"
"                                        manager a CSV file containing all your\n"
"                                        data."
msgstr ""
"Podemos lidar com todo o processo de importação\n"
"                                        por si: basta enviar ao seu gestor\n"
"                                        de projetos Odoo um ficheiro CSV\n"
"                                        contendo todos os seus dados."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"We can handle the whole import process\n"
"                                        for you: simply send your Odoo project\n"
"                                        manager a CSV file containing all your\n"
"                                        products."
msgstr ""
"Podemos lidar com todo o processo de importação\n"
"                                        por si: basta enviar ao seu gestor\n"
"                                        de projetos Odoo um ficheiro CSV\n"
"                                        contendo todos os seus artigos."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "We hope this tool helped you implement our accounting application."
msgstr ""
"Esperamos que esta ferramenta tenha ajudado a implementar a sua aplicação de"
" contabilidade."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Welcome"
msgstr "Bem-vindo"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"When inviting users, you will need to define which access rights they are allowed to have. \n"
"                        This is done by assigning a role to each user."
msgstr ""
"Quando convidar utilizadores, terá de definir quais os direitos de acesso que eles podem ter. \n"
"                        Isto é efetuado, associando uma função a cada utilizador."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_show_on_dashboard
msgid "Whether this journal should be displayed on the dashboard or not"
msgstr "Se este diário deve ser apresentado no painel ou não"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_amount_currency
#: model:ir.model.fields,field_description:account.field_account_print_journal_amount_currency
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_amount_currency
msgid "With Currency"
msgstr "Com Moeda"

#. module: account
#: selection:account.balance.report,display_account:0
#: selection:account.common.account.report,display_account:0
#: selection:account.report.general.ledger,display_account:0
msgid "With balance is not equal to 0"
msgstr "Com saldo não igual a 0"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "With balance not equal to zero"
msgstr "Com saldo não igual a zero"

#. module: account
#: selection:account.balance.report,display_account:0
#: selection:account.common.account.report,display_account:0
#: selection:account.report.general.ledger,display_account:0
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "With movements"
msgstr "Com movimentos"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_search
msgid "With tax"
msgstr "Com imposto"

#. module: account
#: model:ir.model,name:account.model_tax_adjustments_wizard
msgid "Wizard for Tax Adjustments"
msgstr "Assistente de Ajuste Fiscal"

#. module: account
#: code:addons/account/models/account_move.py:1056
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
#, python-format
msgid "Write-Off"
msgstr "Liquidação"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_journal_id
msgid "Write-Off Journal"
msgstr "Diário de Liquidações"

#. module: account
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
msgid "Write-Off Move"
msgstr "Movimento de Liquidação"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_writeoff_acc_id
msgid "Write-Off account"
msgstr "Conta de liquidações"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff
msgid "Write-Off amount"
msgstr "Montante de liquidação"

#. module: account
#: sql_constraint:account.move.line:0
msgid "Wrong credit or debit value in accounting entry !"
msgstr "Valor de crédito ou débito errado na entrada contabilística!"

#. module: account
#: code:addons/account/models/account_move.py:1015
#, python-format
msgid "You are trying to reconcile some entries that are already reconciled!"
msgstr "Está a tentar reconciliar algumas entradas já reconciliadas!"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_blocked
msgid ""
"You can check this box to mark this journal item as a litigation with the "
"associated partner"
msgstr ""
"Pode marcar esta caixa para marcar este item do diário como um litígio com o"
" parceiro associado"

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree2
msgid ""
"You can control the invoice from your vendor according to\n"
"                what you purchased or received."
msgstr ""
"Pode controlar a fatura do seu fornecedor de acordo com\n"
"                o que comprou ou recebeu."

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
msgid "You can define additional accounts here"
msgstr "Pode definir contas adicionais aqui"

#. module: account
#: code:addons/account/models/account_payment.py:468
#, python-format
msgid "You can not delete a payment that is already posted"
msgstr "Não pode apagar um pagamento que já foi lançado"

#. module: account
#: code:addons/account/models/account_invoice.py:1633
#, python-format
msgid "You can only delete an invoice line if the invoice is in draft state."
msgstr ""
"Só pode apagar uma linha da fatura se a fatura estiver no estado 'Rascunho'"

#. module: account
#: code:addons/account/models/account_payment.py:141
#, python-format
msgid "You can only register payments for open invoices"
msgstr "Só pode registar pagamentos em faturas abertas"

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_report_style_overwrite
msgid ""
"You can set up here the format you want this record to be displayed. If you "
"leave the automatic formatting, it will be computed based on the financial "
"reports hierarchy (auto-computed field 'level')."
msgstr ""
"Pode definir aqui o formato no qual quer que este registo seja apresentado. "
"Se deixar a formatação automática, será calculado com base na hierarquia de "
"relatórios financeiros (campo 'nível' auto-calculado)."

#. module: account
#: code:addons/account/models/account_move.py:209
#, python-format
msgid ""
"You cannot add/modify entries prior to and inclusive of the lock date %s"
msgstr ""
"Não pode adicionar/modificar entradas antes de e na data de bloqueio %s"

#. module: account
#: code:addons/account/models/account_move.py:211
#, python-format
msgid ""
"You cannot add/modify entries prior to and inclusive of the lock date %s. "
"Check the company settings or ask someone with the 'Adviser' role"
msgstr ""
"Não pode adicionar/modificar entradas antes de e na data de bloqueio %s. "
"Verifique as configurações da empresa ou inquira com alguém que detenha a "
"função de 'Consultor'."

#. module: account
#: code:addons/account/models/account_invoice.py:1196
#, python-format
msgid ""
"You cannot cancel an invoice which is partially paid. You need to "
"unreconcile related payment entries first."
msgstr ""
"Não pode cancelar uma fatura que foi paga parcialmente. É necessário "
"primeiro anular a reconciliação das entradas de pagamento respetivas."

#. module: account
#: code:addons/account/models/company.py:200
#, python-format
msgid ""
"You cannot change the currency of the company since some journal items "
"already exist"
msgstr "Não pode alterar a moeda da empresa que já contém itens de diário."

#. module: account
#: code:addons/account/models/account.py:235
#, python-format
msgid ""
"You cannot change the owner company of an account that already contains "
"journal items."
msgstr ""
"Não pode alterar a empresa dona de uma conta que já contém itens de diário."

#. module: account
#: code:addons/account/models/account.py:242
#, python-format
msgid ""
"You cannot change the value of the reconciliation on this account as it "
"already has some moves"
msgstr ""
"Não pode alterar o valor da reconciliação desta conta pois já tem alguns "
"itens de diário."

#. module: account
#: code:addons/account/models/account_move.py:500
#, python-format
msgid ""
"You cannot create journal items with a secondary currency without filling "
"both 'currency' and 'amount currency' field."
msgstr ""
"Não pode criar itens de diário com uma moeda secundária sem preencher ambos "
"os campos 'moeda' e 'moeda do montante'."

#. module: account
#: code:addons/account/models/company.py:120
#, python-format
msgid ""
"You cannot define stricter conditions on advisors than on users. Please make"
" sure that the lock date on advisor is set before the lock date for users."
msgstr ""
"Não pode definir condições mais estritas para consultores do que para "
"utilizadores. Assegure-se de que a data de fecho de consultores fica "
"estabelecida antes da data de fecho para utilizadores."

#. module: account
#: code:addons/account/models/account_invoice.py:613
#, python-format
msgid ""
"You cannot delete an invoice after it has been validated (and received a "
"number). You can set it back to \"Draft\" state and modify its content, then"
" re-confirm it."
msgstr ""
"Não pode apagar uma fatura após ter sido validada (e numerada). Pode alterar"
" o estado para \"Rascunho\" e modificar o seu conteúdo, e depois validar de "
"novo."

#. module: account
#: code:addons/account/models/account_invoice.py:611
#, python-format
msgid ""
"You cannot delete an invoice which is not draft or cancelled. You should "
"create a credit note instead."
msgstr ""
"Não pode apagar uma fatura que não é rascunho ou cancelada. Deve, em vez "
"disso, criar uma nota de crédito."

#. module: account
#: code:addons/account/models/res_config_settings.py:133
#, python-format
msgid ""
"You cannot disable this setting because some of your taxes are cash basis. "
"Modify your taxes first before disabling this setting."
msgstr ""
"Não pode desativar esta definição porque alguns dos seus impostos são de "
"fundos de caixa. Modifique os seus impostos primeiro antes de desativar a "
"definição."

#. module: account
#: code:addons/account/models/account.py:248
#, python-format
msgid "You cannot do that on an account that contains journal items."
msgstr "Não é possível fazer isto numa conta que contém itens no diário."

#. module: account
#: code:addons/account/models/account_move.py:1364
#, python-format
msgid ""
"You cannot do this modification on a posted journal entry, you can just change some non legal fields. You must revert the journal entry to cancel it.\n"
"%s."
msgstr ""
"Não pode fazer esta modificação numa entrada de diário lançada, só pode mudar alguns campos não oficiais. Deve reverter a entrada no diário para a cancelar.\n"
"%s."

#. module: account
#: code:addons/account/models/account_move.py:1366
#, python-format
msgid ""
"You cannot do this modification on a reconciled entry. You can just change some non legal fields or you must unreconcile first.\n"
"%s."
msgstr ""
"Não pode fazer esta modificação numa entrada reconciliada. Só pode alterar alguns campos não oficiais, ou anular a reconciliação primeiro.\n"
"%s."

#. module: account
#: code:addons/account/models/account.py:518
#, python-format
msgid "You cannot empty the bank account once set."
msgstr "Não pode esvaziar a conta bancária uma vez que esteja estabelecida."

#. module: account
#: code:addons/account/models/account.py:55
#, python-format
msgid ""
"You cannot have a receivable/payable account that is not reconcilable. "
"(account code: %s)"
msgstr ""
"Não pode ter uma conta de cobranças/pagamentos que não seja reconciliável. "
"(código da conta: %s)"

#. module: account
#: code:addons/account/models/company.py:109
#, python-format
msgid ""
"You cannot lock a period that is not finished yet. Please make sure that the"
" lock date for advisors is not set after the last day of the previous month."
msgstr ""
"Não pode fechar um período que ainda não tenha terminado, Assegure-se de que"
" a data de fecho para consultores seja definida não ultrapassando o último "
"dia do mês anterior."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:701
#, python-format
msgid "You cannot mix items from receivable and payable accounts."
msgstr ""
"Não pode misturar itens de contas de cobranças com itens de contas de "
"pagamentos."

#. module: account
#: code:addons/account/models/account_move.py:172
#, python-format
msgid ""
"You cannot modify a posted entry of this journal.\n"
"First you should set the journal to allow cancelling entries."
msgstr ""
"Não pode modificar uma entrada lançada deste diário.\n"
"Primeiro devia indicar que o diário pode permitir cancelamento de entradas."

#. module: account
#: code:addons/account/models/account_invoice.py:789
#, python-format
msgid ""
"You cannot pay an invoice which is partially paid. You need to reconcile "
"payment entries first."
msgstr ""
"Não pode pagar uma fatura que está parcialmente paga. Primeiro precisa de "
"reconciliar as entradas de pagamento."

#. module: account
#: code:addons/account/wizard/pos_box.py:36
#, python-format
msgid "You cannot put/take money in/out for a bank statement which is closed."
msgstr ""
"Não pode colocar/retirar dinheiro num/de um extrato bancário que se encontra"
" fechado."

#. module: account
#: code:addons/account/models/account.py:261
#, python-format
msgid ""
"You cannot remove/deactivate an account which is set on a customer or "
"vendor."
msgstr ""
"Não pode remover/desativar uma conta que está atribuída a um cliente ou "
"fornecedor."

#. module: account
#: code:addons/account/models/account.py:249
#, python-format
msgid ""
"You cannot set a currency on this account as it already has some journal "
"entries having a different foreign currency."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:1368
#, python-format
msgid "You cannot use deprecated account."
msgstr "Não pode usar uma conta descontinuada."

#. module: account
#: code:addons/account/models/account_move.py:1283
#, python-format
msgid ""
"You cannot use this general account in this journal, check the tab 'Entry "
"Controls' on the related journal."
msgstr ""
"Não pode usar esta conta genérica neste diário, verifique a aba 'Controlos "
"de Entrada' no diário respetivo."

#. module: account
#: code:addons/account/models/account_invoice.py:74
#: code:addons/account/models/account_invoice.py:780
#, python-format
msgid ""
"You cannot validate an invoice with a negative total amount. You should "
"create a credit note instead."
msgstr ""
"Não pode validar uma fatura com um montante total negativo. Em vez disso, "
"deve criar uma nota de crédito."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:158
#, python-format
msgid "You did not configure any reconcile model yet, you can do it"
msgstr "Ainda não configurou nenhum modelo de reconciliação, pode fazê-lo"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "You have"
msgstr "Tem"

#. module: account
#: code:addons/account/models/account_payment.py:505
#, python-format
msgid "You have to define a sequence for %s in your company."
msgstr "Tem que definir uma sequência para %s na sua empresa."

#. module: account
#: code:addons/account/wizard/account_report_general_ledger.py:21
#, python-format
msgid "You must define a Start Date"
msgstr "Tem que definir uma Data de Início"

#. module: account
#: code:addons/account/models/account_invoice.py:1555
#, python-format
msgid "You must first select a partner!"
msgstr "Primeiro tem que selecionar um parceiro!"

#. module: account
#: code:addons/account/wizard/account_report_aged_partner_balance.py:26
#, python-format
msgid "You must set a period length greater than 0."
msgstr "Tem que definir uma duração maior que 0."

#. module: account
#: code:addons/account/wizard/account_report_aged_partner_balance.py:28
#, python-format
msgid "You must set a start date."
msgstr "Tem que definir uma data de início."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "You reconciled"
msgstr "Reconciliou"

#. module: account
#: code:addons/account/models/account_move.py:1884
#, python-format
msgid ""
"You should configure the 'Exchange Rate Journal' in the accounting settings,"
" to manage automatically the booking of accounting entries related to "
"differences between exchange rates."
msgstr ""
"Deve configurar o 'Diário de Taxas de Câmbio' nas definições "
"contabilísticas, para gerir automaticamente o agendamento de entradas "
"contabilísticas relativas às diferenças cambiais."

#. module: account
#: code:addons/account/models/account_move.py:1886
#, python-format
msgid ""
"You should configure the 'Gain Exchange Rate Account' in the accounting "
"settings, to manage automatically the booking of accounting entries related "
"to differences between exchange rates."
msgstr ""
"Deve configurar a 'Conta de Ganhos de Taxas de Câmbio' nas definições "
"contabilísticas, para gerir automaticamente o agendamento de entradas "
"contabilísticas relativas às diferenças cambiais."

#. module: account
#: code:addons/account/models/account_move.py:1888
#, python-format
msgid ""
"You should configure the 'Loss Exchange Rate Account' in the accounting "
"settings, to manage automatically the booking of accounting entries related "
"to differences between exchange rates."
msgstr ""
"Deve configurar a 'Conta de Prejuízos de Taxas de Câmbio' nas definições "
"contabilísticas, para gerir automaticamente o agendamento de entradas "
"contabilísticas relativas às diferenças cambiais."

#. module: account
#: code:addons/account/wizard/pos_box.py:49
#: code:addons/account/wizard/pos_box.py:67
#, python-format
msgid ""
"You should have defined an 'Internal Transfer Account' in your cash "
"register's journal!"
msgstr ""
"Deve definir uma 'Conta de Transferências Internas' no seu diário de caixas!"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"You will be able to edit and validate this\n"
"                                credit note directly or keep it draft,\n"
"                                waiting for the document to be issued by\n"
"                                your supplier/customer."
msgstr ""
"Será capaz de editar e validar esta\n"
"                                nota de crédito diretamente ou mantê-la\n"
"                                como rascunho, aguardando que o documento\n"
"                                seja emitido pelo seu fornecedor/cliente."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Bank Accounts"
msgstr "As Suas Contas Bancárias"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Company"
msgstr "A Sua Empresa"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Customers"
msgstr "Os Seus Clientes"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Products"
msgstr "Os Seus Artigos"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Trial Balance (list of accounts and their balances)."
msgstr "O Seu Balancete Geral (lista de contas e dos seus saldos)."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your company's legal name, tax ID, address, and logo."
msgstr "O nome legal, Id. de imposto, morada e logótipo da sua empresa."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your outstanding invoices, payments, and undeposited funds."
msgstr "As suas faturas vencidas, pagamentos e fundos não depositados."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Zip Range"
msgstr "Gama de Códigos Postais"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_zip_from
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_zip_from
msgid "Zip Range From"
msgstr "Início da Gama de Códigos Postais"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_zip_to
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_zip_to
msgid "Zip Range To"
msgstr "Fim da Gama de Códigos Postais"

#. module: account
#: model:ir.model,name:account.model_account_bank_accounts_wizard
msgid "account.bank.accounts.wizard"
msgstr "account.bank.accounts.wizard"

#. module: account
#: model:ir.model,name:account.model_account_financial_year_op
msgid "account.financial.year.op"
msgstr "account.financial.year.op"

#. module: account
#: model:ir.model,name:account.model_account_group
msgid "account.group"
msgstr "account.group"

#. module: account
#: model:ir.model,name:account.model_account_opening
msgid "account.opening"
msgstr "account.opening"

#. module: account
#: model:ir.model,name:account.model_account_reconcile_model_template
msgid "account.reconcile.model.template"
msgstr "account.reconcile.model.template"

#. module: account
#: model:ir.model,name:account.model_account_tax_group
msgid "account.tax.group"
msgstr "account.tax.group"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "activate this feature"
msgstr "ativar esta funcionalidade"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:17
#, python-format
msgid "assign to invoice"
msgstr "associar a fatura"

#. module: account
#: model:ir.model,name:account.model_cash_box_in
msgid "cash.box.in"
msgstr "cash.box.in"

#. module: account
#: model:ir.model,name:account.model_cash_box_out
msgid "cash.box.out"
msgstr "cash.box.out"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_error
#: model:ir.ui.view,arch_db:account.portal_invoice_success
msgid "close"
msgstr "fechar"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "code"
msgstr "código"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "create a journal entry"
msgstr "criar uma entrada no diário"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "days"
msgstr "dias"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "e.g. Bank Fees"
msgstr "ex.: Comissões Bancárias"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "fast recording interface"
msgstr "interface de gravação rápida"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "first)"
msgstr "primeiro)"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "for this customer. You can allocate them to mark this invoice as paid."
msgstr ""
"para este cliente. Pode-os atribuir para marcar esta fatura como paga."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "for this supplier. You can allocate them to mark this bill as paid."
msgstr ""
"para este fornecedor. Pode alocá-los para marcar esta fatura como paga."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:41
#, python-format
msgid "o_manual_statement"
msgstr ""

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "outstanding debits"
msgstr "débitos por liquidar"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "outstanding payments"
msgstr "pagamentos a fazer"

#. module: account
#: model:ir.model,name:account.model_report_account_report_agedpartnerbalance
msgid "report.account.report_agedpartnerbalance"
msgstr "report.account.report_agedpartnerbalance"

#. module: account
#: model:ir.model,name:account.model_report_account_report_financial
msgid "report.account.report_financial"
msgstr "report.account.report_financial"

#. module: account
#: model:ir.model,name:account.model_report_account_report_generalledger
msgid "report.account.report_generalledger"
msgstr "report.account.report_generalledger"

#. module: account
#: model:ir.model,name:account.model_report_account_report_journal
msgid "report.account.report_journal"
msgstr "report.account.report_journal"

#. module: account
#: model:ir.model,name:account.model_report_account_report_overdue
msgid "report.account.report_overdue"
msgstr "report.account.report_overdue"

#. module: account
#: model:ir.model,name:account.model_report_account_report_partnerledger
msgid "report.account.report_partnerledger"
msgstr "report.account.report_partnerledger"

#. module: account
#: model:ir.model,name:account.model_report_account_report_tax
msgid "report.account.report_tax"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_report_account_report_trialbalance
msgid "report.account.report_trialbalance"
msgstr "report.account.report_trialbalance"

#. module: account
#: model:ir.model,name:account.model_res_config_settings
msgid "res.config.settings"
msgstr "res.config.settings"

#. module: account
#: code:addons/account/models/account_move.py:238
#, python-format
msgid "reversal of: "
msgstr "reversão de:"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "seconds per transaction."
msgstr "segundos por transacção."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "send us an email"
msgstr "envie-nos um e-mail"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "set company logo"
msgstr "definir logótipo da empresa"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "setup your bank accounts."
msgstr "configure as suas contas bancárias."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "the customer list"
msgstr "a lista de clientes"

#. module: account
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "the parent company"
msgstr "a empresa mãe"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "the product list"
msgstr "lista de artigos"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:158
#, python-format
msgid "there"
msgstr "aí"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "to describe<br/> your experience or to suggest improvements !"
msgstr "para descrever <br/> a sua experiência ou sugerir melhorias!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "to set the balance of all of your accounts."
msgstr "para definir o saldo de todas as suas contas."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "transactions in"
msgstr "transações em"

#. module: account
#: model:ir.model,name:account.model_wizard_multi_charts_accounts
msgid "wizard.multi.charts.accounts"
msgstr "wizard.multi.charts.accounts"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "→ Count"
msgstr "→ Contagem"
