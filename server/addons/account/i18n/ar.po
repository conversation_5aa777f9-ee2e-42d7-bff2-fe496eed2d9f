# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <PERSON><PERSON>hali <<EMAIL>>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <AUTHOR> <EMAIL>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON>ldawood <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# Martin Trigaux, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-03-29 09:07+0000\n"
"PO-Revision-Date: 2017-09-20 10:13+0000\n"
"Last-Translator: Martin Trigaux, 2019\n"
"Language-Team: Arabic (https://www.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_state
msgid ""
" * The 'Draft' status is used when a user is encoding a new and unconfirmed Invoice.\n"
" * The 'Open' status is used when user creates invoice, an invoice number is generated. It stays in the open status till the user pays the invoice.\n"
" * The 'Paid' status is set automatically when the invoice is paid. Its related journal entries may or may not be reconciled.\n"
" * The 'Cancelled' status is used when user cancel invoice."
msgstr ""
"*تستخدم حالة ' مسودة ' عند ادخال المستخدم لفاتورة جديده وغير مؤكده.\n"
" * حالة ال 'مفتوحة ' تستخدم عندما يصدر المستخدم فاتورة ورقم للفاتورة، تبقى على تلك الحالة حتى يتم تسجيل السداد.\n"
" * يتم تغيير الحالة إلى 'مدفوعة ' تلقائيا عند سداد الفاتورة. وقد تكون القيود اليومية للفاتورة مسددة أو غير مسددة. \n"
" * الحالة ' ملغي ' تستخدم عند إلغاء المستخدم للفاتورة."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_code_digits
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_code_digits
msgid "# of Digits"
msgstr "عدد خانات الأرقام"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_code_digits
msgid "# of Digits *"
msgstr "عدد خانات الأرقام *"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_nbr
msgid "# of Lines"
msgstr "عدد البنود"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_trans_nbr
msgid "# of Transaction"
msgstr "عدد المعاملات"

#. module: account
#: model:mail.template,report_name:account.mail_template_data_payment_receipt
msgid "${(object.name or '').replace('/','-')}"
msgstr "${(object.name or '').replace('/','-')}"

#. module: account
#: model:mail.template,subject:account.email_template_edi_invoice
msgid "${object.company_id.name} Invoice (Ref ${object.number or 'n/a'})"
msgstr "${object.company_id.name} فاتورة (رقم ${object.number or 'n/a'})"

#. module: account
#: model:mail.template,subject:account.mail_template_data_payment_receipt
msgid ""
"${object.company_id.name} Payment Receipt (Ref ${object.name or 'n/a' })"
msgstr "${object.company_id.name} إيصال سداد (رقم ${object.name or 'n/a' })"

#. module: account
#: model:mail.template,subject:account.mail_template_data_notification_email_account_invoice
msgid "${object.subject}"
msgstr "${object.subject}"

#. module: account
#: code:addons/account/models/account_bank_statement.py:462
#, python-format
msgid "%d transactions were automatically reconciled."
msgstr "تم تسوية %d معاملة آلياً."

#. module: account
#: code:addons/account/models/account.py:809
#, python-format
msgid "%s (Copy)"
msgstr "%s (نسخة)"

#. module: account
#: code:addons/account/models/account.py:211
#: code:addons/account/models/account.py:484
#: code:addons/account/models/account.py:485
#, python-format
msgid "%s (copy)"
msgstr "%s (نسخة)"

#. module: account
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "&amp;nbsp;<span>on</span>"
msgstr "&amp;nbsp;<span>في</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ", if accounting or purchase is installed"
msgstr "، إذا تم تثبيت الحسابات أو المشتريات"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "- First Number:"
msgstr "- الرقم الأول:"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "-> Reconcile"
msgstr "-> تسوية"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "-> View partially reconciled entries"
msgstr "-> عرض القيود المسواة جزئياً"

#. module: account
#: code:addons/account/models/account_bank_statement.py:463
#, python-format
msgid "1 transaction was automatically reconciled."
msgstr "تم تسوية معاملة 1 واحدة آلياً."

#. module: account
#: model:account.payment.term,name:account.account_payment_term_15days
msgid "15 Days"
msgstr "15 يوم"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_net
msgid "30 Net Days"
msgstr "صافي 30 يوم"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_advance
msgid "30% Advance End of Following Month"
msgstr "30% مقدم والباقي نهاية الشهر التالي"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "5) For setup, you will need the following information:"
msgstr "5) لأغراض الإعدادات، ستحتاج المعلومات التالية:"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid ": General ledger"
msgstr ": الأستاذ العام"

#. module: account
#: code:addons/account/models/account.py:554
#, python-format
msgid ": Refund"
msgstr ": المردود"

#. module: account
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid ": Trial Balance"
msgstr ": ميزان المراجعة"

#. module: account
#: model:mail.template,body_html:account.mail_template_data_payment_receipt
msgid ""
"<?xml version=\"1.0\"?>\n"
"<data><p>Dear ${object.partner_id.name},</p>\n"
"<p>Thank you for your payment.<br/>Here is your payment receipt <strong>${(object.name or '').replace('/','-')}</strong> amounting to <strong>${format_amount(object.amount, object.currency_id)}</strong> from ${object.company_id.name}.</p>\n"
"<p>If you have any questions, please do not hesitate to contact us.</p>\n"
"<p>Best regards,\n"
"% if user and user.signature:\n"
"${user.signature | safe}\n"
"% endif\n"
"</p>\n"
"</data>"
msgstr ""

#. module: account
#: model:mail.template,body_html:account.mail_template_data_notification_email_account_invoice
msgid ""
"<div>\n"
"% set record = ctx.get('record')\n"
"% set company = record and record.company_id or ctx.get('company')\n"
"<table border=\"0\" width=\"100%\" cellpadding=\"0\" bgcolor=\"#ededed\" style=\"padding: 20px; background-color: #ededed; border-collapse:separate;\" summary=\"o_mail_notification\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\">\n"
"                        <span style=\"font-size:20px; color:white; font-weight: bold;\">\n"
"                            Invoice ${object.record_name}\n"
"                        </span>\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\">\n"
"                        <img src=\"/logo.png?company=${company.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${company.name}\"/>\n"
"                    </td>\n"
"                </tr>\n"
"          </table>\n"
"    </td>\n"
"  </tr>\n"
"  <!-- CONTENT -->\n"
"  <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"min-width: 590px; background-color: rgb(255, 255, 255); padding: 20px; border-collapse:separate;\">\n"
"                <tbody>\n"
"                    <td valign=\"top\" style=\"font-family:Arial,Helvetica,sans-serif; color: #555; font-size: 14px;\">\n"
"                        ${object.body | safe}\n"
"                    </td>\n"
"                </tbody>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\" align=\"left\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                        ${company.name}<br/>\n"
"                        ${company.phone or ''}\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                        % if company.email:\n"
"                        <a href=\"mailto:${company.email}\" style=\"text-decoration:none; color: white;\">${company.email}</a><br/>\n"
"                        % endif\n"
"                        % if company.website:\n"
"                        <a href=\"${company.website}\" style=\"text-decoration:none; color: white;\">\n"
"                        ${company.website}\n"
"                        </a>\n"
"                        % endif\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <tr>\n"
"        <td align=\"center\">\n"
"            Powered by <a target=\"_blank\" href=\"https://www.odoo.com\">Odoo</a>.\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</div>"
msgstr ""
"<html>\n"
"                <head></head>\n"
"                <body style=\"margin: 0; padding: 0;\">\n"
"                <table border=\"0\" width=\"100%\" cellpadding=\"0\" bgcolor=\"#ededed\" style=\"padding: 20px; background-color: #ededed\" summary=\"o_mail_notification\">\n"
"                    <tbody>\n"
"\n"
"                      <!-- HEADER -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px;\">\n"
"                            <tr>\n"
"                              <td valign=\"middle\">\n"
"                                  <span style=\"font-size:20px; color:white; font-weight: bold;\">\n"
"                                      فاتورة ${object.record_name}\n"
"                                  </span>\n"
"                              </td>\n"
"                              <td valign=\"middle\" align=\"right\">\n"
"                                  <img src=\"/logo.png\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${user.company_id.name}\">\n"
"                              </td>\n"
"                            </tr>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"\n"
"                      <!-- CONTENT -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"min-width: 590px; background-color: rgb(255, 255, 255); padding: 20px;\">\n"
"                            <tbody>\n"
"                              <td valign=\"top\" style=\"font-family:Arial,Helvetica,sans-serif; color: #555; font-size: 14px;\">\n"
"                                ${object.body | safe}\n"
"                              </td>\n"
"                            </tbody>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"\n"
"                      <!-- FOOTER -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px;\">\n"
"                            <tr>\n"
"                              <td valign=\"middle\" align=\"left\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                                ${user.company_id.name}<br/>\n"
"                                ${user.company_id.phone or ''}\n"
"                              </td>\n"
"                              <td valign=\"middle\" align=\"right\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                                % if user.company_id.email:\n"
"                                <a href=\"mailto:${user.company_id.email}\" style=\"text-decoration:none; color: white;\">${user.company_id.email}</a><br/>\n"
"                                % endif\n"
"                                % if user.company_id.website:\n"
"                                    <a href=\"${user.company_id.website}\" style=\"text-decoration:none; color: white;\">\n"
"                                        ${user.company_id.website}\n"
"                                    </a>\n"
"                                % endif\n"
"                              </td>\n"
"                            </tr>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"                      <tr>\n"
"                        <td align=\"center\">\n"
"                            بالاعتماد على نظام أودو.\n"
"                        </td>\n"
"                      </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                </body>\n"
"                </html>\n"
"            "

#. module: account
#: model:mail.template,body_html:account.email_template_edi_invoice
msgid ""
"<div>\n"
"<p>Dear ${object.partner_id.name}\n"
"% set access_action = object.with_context(force_website=True).get_access_action()\n"
"% set is_online = access_action and access_action['type'] == 'ir.actions.act_url'\n"
"% set access_url = object.get_mail_url()\n"
"\n"
"% if object.partner_id.parent_id:\n"
"    (<i>${object.partner_id.parent_id.name}</i>)\n"
"% endif\n"
",</p>\n"
"<p>Here is, in attachment, your \n"
"% if object.number:\n"
"invoice <strong>${object.number}</strong>\n"
"% else:\n"
"invoice\n"
"% endif\n"
"% if object.origin:\n"
"(with reference: ${object.origin})\n"
"% endif\n"
"amounting in <strong>${format_amount(object.amount_total, object.currency_id)}</strong>\n"
"from ${object.company_id.name}.\n"
"</p>\n"
"\n"
"% if is_online:\n"
"    <br/><br/>\n"
"    <center>\n"
"      <a href=\"${access_url}\" style=\"background-color: #1abc9c; padding: 20px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 16px;\" class=\"o_default_snippet_text\">View Invoice</a>\n"
"    </center>\n"
"% endif\n"
"    <br/><br/>\n"
"\n"
"% if object.state=='paid':\n"
"    <p>This invoice is already paid.</p>\n"
"% else:\n"
"    <p>Please remit payment at your earliest convenience.</p>\n"
"% endif\n"
"\n"
"<p>Thank you,</p>\n"
"<p style=\"color:#888888\">\n"
"% if object.user_id and object.user_id.signature:\n"
"    ${object.user_id.signature | safe}\n"
"% endif\n"
"</p>\n"
"</div>"
msgstr ""

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<span class=\"fa fa-arrow-circle-o-down\"/> Install Now"
msgstr "<span class=\"fa fa-arrow-circle-o-down\"/> تثبيت الآن"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<span class=\"fa\"/> Invite Your Users"
msgstr "<span class=\"fa\"/> دعوة المستخدمين"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-check\"/><span "
"class=\"hidden-xs\"> Paid</span></span>"
msgstr ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-check\"/><span "
"class=\"hidden-xs\"> مدفوعة</span></span>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-remove\"/><span "
"class=\"hidden-xs\"> Cancelled</span></span>"
msgstr ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-remove\"/><span "
"class=\"hidden-xs\"> ملغاة</span></span>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"label label-info\"><i class=\"fa fa-fw fa-clock-o\"/><span "
"class=\"hidden-xs\"> Waiting for Payment</span></span>"
msgstr ""
"<span class=\"label label-info\"><i class=\"fa fa-fw fa-clock-o\"/><span "
"class=\"hidden-xs\"> بانتظار السداد</span></span>"

#. module: account
#: model:ir.ui.view,arch_db:account.partner_view_buttons
msgid "<span class=\"o_stat_text\">Invoiced</span>"
msgstr "<span class=\"o_stat_text\">صدرت فاتورته</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>Import a file</strong><br/>\n"
"                                        <span class=\"small\">Recommended if &gt;100 products</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>استيراد ملف</strong><br/>\n"
"                                        <span class=\"small\">موصى به إذا كان أقل من 100 منتج</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>Import</strong><br/>\n"
"                                        <span class=\"small\">&gt; 200 contacts</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>استيراد</strong><br/>\n"
"                                        <span class=\"small\">أكبر من 200 جهة اتصال</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> Create manually</strong><br/>\n"
"                                        <span class=\"small\">&lt; 200 contacts</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> إنشاء يدوي</strong><br/>\n"
"                                        <span class=\"small\">أقل من 200 جهة اتصال</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> Create manually</strong><br/>\n"
"                                        <span class=\"small\">Recommended if &lt;100 products</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> إنشاء يدوي</strong><br/>\n"
"                                        <span class=\"small\">موصى به لأقل من 100 منتج</span>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Accountant</strong> (Advanced access)\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>محاسب</strong> (صلاحيات متقدمة)\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Advisor</strong> (Full access)\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>مراقب مالي</strong> (صلاحية شاملة)\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Billing</strong> (Limited access)\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>إصدار فواتير</strong> (صلاحية محدودة)\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Customer follow-up</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>متابعة التحصيلات من العملاء</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Invoice payment by Check</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>سداد الفواتير بشيكات بنكية</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Invoice payment by Wire Transfer</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>سداد الفواتير بتحويلات بنكية</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Pay your bills</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>سداد الفواتير</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Reconcile Bank Statements</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>تسوية كشوفات الحسابات البنكية</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Record your Bills</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <strong>تسجيل فواتيرك</strong>\n"
"                                    </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span title=\"Balance in Odoo\">Balance in GL</span>"
msgstr "<span title=\"الرصيد في أودو\">الرصيد الدفتري</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span title=\"Latest Statement\">Latest Statement</span>"
msgstr "<span title=\"Latest Statement\">أحدث كشف</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "<span> From </span>"
msgstr "<span> من </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "<span> To </span>"
msgstr "<span> إلى </span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Journal Entries</span>"
msgstr "<span>قيود اليومية</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Bill</span>"
msgstr "<span>فاتورة جديدة</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Invoice</span>"
msgstr "<span>فاتورة جديدة</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New</span>"
msgstr "<span>جديد</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<span>Not due</span>"
msgstr "<span>لم تُستحق بعد</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Operations</span>"
msgstr "<span>العمليات</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Reconciliation</span>"
msgstr "<span>التسوية</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Reporting</span>"
msgstr "<span>التقارير</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>View</span>"
msgstr "<span>عرض</span>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>-The Odoo Team</strong>"
msgstr "<strong>- فريق أودو</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>1. Register Outstanding Invoices</strong>"
msgstr "<strong>. 1سجل الفواتير الحالية</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>2. Register Unmatched Payments</strong>"
msgstr "<strong>2.سجل المدفوعات غير المطابقة</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_invoice_document_with_payments
msgid "<strong>Amount Due</strong>"
msgstr "<strong>المبلغ المستحق</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Assets Management</strong>"
msgstr "<strong>إدارة الأصول</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Automated documents sending:</strong> automatically send your "
"invoices by email or snail mail."
msgstr ""
"<strong>إرسال المستندات آلياً:</strong> أرسل الفواتير عبر البريد الإلكتروني "
"أو البريد العادي."

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "<strong>Balance :</strong>"
msgstr "<strong>الرصيد :</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Banking interface:</strong> with live bank feed synchronization and "
"bank statement import."
msgstr ""
"<strong>واجهة مزامنة مع البنوك:</strong> مزامنة آنية للحركة على الحساب "
"البنكي وإمكانية إستيراد كشوفات الحسابات البنكية."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Cash transactions</strong><br/> (for which there is no invoice or "
"bill), should be entered directly into your Cash Registers bank account."
msgstr ""
"<strong>المعاملات النقدية</strong><br/> (في حالة لا توجد فاتورة مبيعات أو "
"مشتريات)، يجب إدخالها مباشرة في سجلات الخزينة أو الإيداعات التقدية."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Check the Taxes configuration:</strong>"
msgstr "<strong>راجع إعدادات الضرائب:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Check unpaid invoices</strong><br/>\n"
"                                         Run an <i>Aged Receivable report</i> and check which customers still owe you money."
msgstr ""
"<strong>راجع الفواتير غير المدفوعة</strong><br/>\n"
"                                         قم بتحيين <i>تقرير الديون المطلوب تحصيلها</i> و تعرف على الزبائن الذين يدينون لك بالمال."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Clean customer invoices:</strong> easy to create, beautiful and full"
" featured invoices."
msgstr ""
"<strong>فاتورة مبيعات متميزة:</strong> سهلة الانشاء, جميلة وكاملة المزايا."

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
msgid "<strong>Company:</strong>"
msgstr "<strong>الشركة:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Contracts &amp; Subscriptions</strong>"
msgstr "<strong>العقود والإشتراكات</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Create a Customer Invoice</strong>"
msgstr "<strong>إنشاء فاتورة عميل</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Create a Deposit Ticket</strong><br/>\n"
"                                        This allows you to record the different payments that constitute your bank deposit. (You may need to"
msgstr ""
"<strong>إنشاء بطاقة الإيداع</strong><br/>\n"
"تسمح لك بطاقة الإيداع بتسجيل مدفوعاتك المختلفة والتي تشكل إيداعاتك البنكية. (قد تحتاج إلى"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Create the bill in Odoo</strong><br/> with a proper due date, and "
"create the vendor if it doesnt' exist yet."
msgstr ""
"<strong>إنشاء الفاتورة في أودو</strong><br/> بتاريخ استحقاق صحيح, وإنشاء "
"المورد إذا لم يسبق إنشاؤه من قبل."

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
msgid "<strong>Customer Address</strong>"
msgstr "<strong>عنوان العميل</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Customer Code:</strong>"
msgstr "<strong>كود العميل:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Customer: </strong>"
msgstr "<strong>العميل: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Date from :</strong>"
msgstr "<strong>من تاريخ: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Date to :</strong>"
msgstr "<strong>إلى تاريخ: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Deposit Tickets</strong>"
msgstr "<strong>بطاقة الإيداع</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Description:</strong>"
msgstr "<strong>الوصف:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Display Account:</strong>"
msgstr "</strong>عرض الحساب:<strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "<strong>Display Account</strong>"
msgstr "</strong>عرض الحساب<strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Due Date:</strong>"
msgstr "<strong>تاريخ الاستحقاق:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Entries Sorted By:</strong>"
msgstr "<strong>ترتيب القيود حسب:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Expenses</strong>"
msgstr "<strong>المصروفات</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
msgid "<strong>Fiscal Year End</strong>"
msgstr "<strong>إقفال السنة المالية</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "<strong>From</strong>"
msgstr "من"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Invoice Date:</strong>"
msgstr "<strong>تاريخ الفاتورة:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Journal:</strong>"
msgstr "<strong>اليومية:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "<strong>Journals:</strong>"
msgstr "<strong>اليوميات:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Mark the bills to pay</strong><br/>\n"
"                                        Group or filter your bills to see those due in the next week, then open each bill individually, click on <strong>'Pay'</strong> and select the payment method you prefer."
msgstr ""
"<strong>حدد الفواتير المرغوب سدادها</strong><br/>\n"
"قم بتجميع أو فرز الفواتير الخاصة بك لترى الفواتير المستحقة في الأسبوع القادم, ثم افتح كل فاتورة على حدة، وانقر على <strong>'دفع'</strong> واختر طريقة السداد المناسبة لك."

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Memo: </strong>"
msgstr "ملاحظة:"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Next action:</strong><br/>"
msgstr "<strong>الإجراء التالي</strong><br/>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>On-the-fly payment reconciliation:</strong> automatic suggestions of"
" outstanding payments when creating invoices."
msgstr ""
"<strong>تسوية المدفوعات على الفور:</strong> اقتراحات تلقائية للمدفوعات "
"المستحقة عند إنشاء الفواتير."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Or generate payment orders</strong><br/>\n"
"                                        Create a Payment Order and select the bills you'd like to pay as Entry lines (only the validated bills will appear)."
msgstr ""
"<strong>أو إنشاء أوامر الدفع</strong><br/> \n"
"انشئ امر الدفع واختر الفاتورة التي ترغب في دفعها كبنود قيد (ستظهر الفواتير المعتمدة فقط)."

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<strong>Partner's:</strong>"
msgstr "<strong>خاص بالشريك:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Payment Amount: </strong>"
msgstr "مبلغ الدفع:"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Payment Date: </strong>"
msgstr "تاريخ الدفع:"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Payment Method: </strong>"
msgstr "طريقة الدفع:"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<strong>Period Length (days)</strong>"
msgstr "<strong>طول الفترة (أيام)</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Print checks</strong><br/>\n"
"                                        From the Vendor Payments list, select those you want to pay and click on Print Check (you may first need to activate the check feature in the"
msgstr ""
"<strong>طباعة الشيكات</strong><br/>\n"
" من قائمة مدفوعات  المورد, أختر تلك التي تريد ان تدفع وانقر على طباعة الشيك (قد تحتاج أولا لتفعيل خاصية الشيك في"

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "<strong>Purchase</strong>"
msgstr "شراء"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Purchases</strong>"
msgstr "<strong>المشتريات</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Reconcile Bank Statement</strong>"
msgstr "</strong>تسوية البيان البنكي<strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Reconcile with existing transaction</strong><br/>\n"
"                                        In this case, Odoo should automatically match the bank statement with the previously recorded check transaction."
msgstr ""
"<strong>التسوية مع المعاملات الحالية</strong><br/>\n"
"                                        في هذه الحالة، سيقوم أودو آلياً بمطابقة بنود كشف الحساب البنكي مع معاملاتك البنكية والشيكات المسجلة لديه."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Reconcile your Bank Statements</strong>"
msgstr "</strong>تسوية البيان البنكي الخاص بك<strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Record Bank Statement (or import file)</strong><br/>\n"
"                                        Depending on the volume of your transactions, you should be recording your bank statement every week to several times a day."
msgstr ""
"<strong>تسجيل كشف الحساب (أو استيراد ملف)</strong><br/>\n"
"                                        وفقاً لحجم معاملاتك، يجب عليك تسجيل كشف الحساب مرة في أسبوع، أو عدة مرات في اليوم."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Record Bank Statement</strong>"
msgstr "</strong>تسجيل البيان البنكي<strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Record a payment by check on the Invoice</strong><br/>\n"
"                                        Simply click on the 'Pay' button."
msgstr ""
"<strong>تسجيل مدفوعات على الفاتورة باستخدام شيك</strong><br/>\n"
"                                        ببساطة، انقر زر 'سداد'."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Reduced data entry:</strong> no need to manually create invoices, "
"register bank statements, and send payment follow-ups."
msgstr ""
"<strong>تقليل إدخال البيانات:</strong> لا داعي لإنشاء الفواتير، تسجيل كشوفات"
" الحسابات البنكية ومتابعة التحصيلات يدوياً."

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Reference:</strong>"
msgstr "<strong>رقم الإشارة:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Revenue Recognition</strong>"
msgstr "<strong>إثبات الإيراد</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "<strong>Send follow-up letters</strong>"
msgstr "<strong>ارسال رسائل متابعة</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "<strong>Sorted By:</strong>"
msgstr "<strong>مرتب حسب:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Source:</strong>"
msgstr "<strong>المصدر:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "<strong>Start Date:</strong>"
msgstr "<strong>تاريخ البدء:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "<strong>Subtotal :</strong>"
msgstr "<strong>الإجمالي الفرعي</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Subtotal</strong>"
msgstr "<strong>الإجمالي الفرعي</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "<strong>Target Moves:</strong>"
msgstr "<strong>الحركات المستهدفة: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Test the following three scenarios in order to assist you in "
"familiarizing yourself with Odoo:</strong>"
msgstr ""
"<strong>جرب السيناريوهات الثلاث التالية لتساعدك على الإلمام بأودو:</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>There are three different levels of access rights in Odoo:</strong>"
msgstr "<strong>هناك ثلاثة مستويات مختلفة لحقوق الدخول في أودو </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "<strong>There is nothing due with this customer.</strong>"
msgstr "<strong>لا شيء مستحق على هذا العميل.</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Total</strong>"
msgstr "<strong>الإجمالي</strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_account_kanban
msgid "<strong>Type: </strong>"
msgstr "النوع:"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"<strong>Validate the bill</strong><br/> after encoding the products and "
"taxes."
msgstr "<strong>إعتماد الفاتورة</strong><br/> بعد إدراج المنتجات والضرائب."

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "<strong>Vendor: </strong>"
msgstr "المورد:"

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "<strong>to </strong>"
msgstr "إلى"

#. module: account
#: model:ir.actions.act_window,help:account.action_view_bank_statement_tree
msgid ""
"A Cash Register allows you to manage cash entries in your cash\n"
"                journals. This feature provides an easy way to follow up cash\n"
"                payments on a daily basis. You can enter the coins that are in\n"
"                your cash box, and then post entries when money comes in or\n"
"                goes out of the cash box."
msgstr ""
"يسمح لك صندوق الكاشير بإدارة القيود النقدية في النقدية الخاصة بك\n"
"دفاتر اليومية. تقدم هذه الخاصية طريقة سهلة لمتابعة النقدية\n"
" المدفوعات على أساس يومي. يمكنك من إدخال العملات الموجودة في\n"
"صندوق الكاشير الخاص بك, و ترحيل القيود بعد ذلك عندما تدخل أموال أو\n"
"تخرج من صندوق الكاشير"

#. module: account
#: code:addons/account/models/account_bank_statement.py:383
#, python-format
msgid "A Cash transaction can't have a 0 amount."
msgstr "لا يمكن لمعاملة نقدية أن يكون مبلغها 0."

#. module: account
#: code:addons/account/models/account_invoice.py:1699
#, python-format
msgid "A Payment Terms should have its last line of type Balance."
msgstr "يجب أن تحتوي شروط السداد بنداً واحداً على الأقل من نوع رصيد."

#. module: account
#: code:addons/account/models/account_invoice.py:1702
#, python-format
msgid "A Payment Terms should have only one line of type Balance."
msgstr "يجب أن تحتوي شروط السداد بنداً واحداً على الأقل من نوع رصيد."

#. module: account
#: code:addons/account/models/account.py:722
#, python-format
msgid "A bank account can only belong to one journal."
msgstr "يمكن ربط الحساب البنكي بدفتر يومية واحد فقط."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_tree
msgid ""
"A bank statement is a summary of all financial transactions\n"
"                occurring over a given period of time on a bank account. You\n"
"                should receive this periodicaly from your bank."
msgstr ""
"كشف الحساب البنكي هم ملخص لكل المعاملات المالية\n"
"التي تمت على الحساب البنكي خلال فترة زمنية معينة. أنت\n"
"تحصل على كشف الحساب بشكل دوري من البنك الخاص بك."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_line
msgid "A bank statement line is a financial transaction on a bank account."
msgstr "بند كشف الحساب هو معاملة مالية على الحساب البنكي."

#. module: account
#: model:ir.actions.act_window,help:account.action_move_journal_line
msgid ""
"A journal entry consists of several journal items, each of\n"
"                which is either a debit or a credit transaction."
msgstr ""
"قيد اليومية يتكون من عناصر متعددة لدفتر اليومية. كل\n"
"واحد منها اما معاملة دائن أو مدين"

#. module: account
#: model:ir.actions.act_window,help:account.action_account_journal_form
msgid ""
"A journal is used to record transactions of all accounting data\n"
"                related to the day-to-day business."
msgstr ""
"يستخدم دفتر اليومية لتسجيل المعاملات على كل البيانات المحاسبية\n"
"المتعلقة بالأعمال اليومية "

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "A list of common taxes and their rates."
msgstr "قائمة الضرائب المشتركة ومعدلاتها."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "A list of your customer and supplier payment terms."
msgstr "قائمة بعملائك و مرونة شروط الدفع الخاصة بك"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"A product in Odoo is something you sell or buy \n"
"                        whether or not it is goods, consumables, or services.\n"
"                        Choose how you want to create your products:"
msgstr ""
"المنتج في أودو هو شيء تبيعه أو تشتريه\n"
"أما أن يكون بضائع أو سلع أو خدمات\n"
"أختر كيف تريد إنشاء منتجاتك:"

#. module: account
#: code:addons/account/models/account_move.py:891
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr "التسوية يجب أن تشمل على بندى حركة على الأقل."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"A rounding per line is advised if your prices are tax-included. That way, "
"the sum of line subtotals equals the total with taxes."
msgstr ""
"يوصى باستخدام التقريب لكل بند إذا كانت الأسعار شاملة الضرائب. بهذا الأسلوب، "
"فإن مجموع الإجماليات الفرعية لكل بند سيساوي الإجمالي شامل الضرائب."

#. module: account
#: code:addons/account/models/account_bank_statement.py:881
#: code:addons/account/models/account_bank_statement.py:884
#, python-format
msgid "A selected move line was already reconciled."
msgstr "أحد بنود حركة المخزون تم تسويته بالفعل."

#. module: account
#: code:addons/account/models/account_bank_statement.py:892
#, python-format
msgid "A selected statement line was already reconciled with an account move."
msgstr "سطر كشف الحساب المختار تم تسويته بالفعل مع الحركة المحاسبية."

#. module: account
#: sql_constraint:account.fiscal.position.tax:0
msgid "A tax fiscal position could be defined only once time on same taxes."
msgstr "يمكن تعريف الوضع المالي للضريبة مرة واحدة فقط لنفس الضرائب."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_journal_form
msgid ""
"A typical company may use one journal per payment method (cash,\n"
"                bank accounts, checks), one purchase journal, one sales journal\n"
"                and one for miscellaneous information."
msgstr ""
"الشركات في العادة قد تستخدم دفتر يومية واحد لكل طريقة سداد (نقدي،\n"
"حسابات بنكية، أو الشيكات)، ودفتر يومية واحد للمشتريات، وآخر للمبيعات\n"
"وواحد للعمليات المتفرقة والنثريات."

#. module: account
#: model:res.groups,name:account.group_warning_account
msgid "A warning can be set on a partner (Account)"
msgstr "يمكن ضبط تحذير خاص (بحساب) الشريك"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:521
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:523
#: code:addons/account/static/src/xml/account_reconciliation.xml:170
#: code:addons/account/static/src/xml/account_reconciliation.xml:228
#: code:addons/account/static/src/xml/account_reconciliation.xml:253
#: model:ir.model,name:account.model_account_account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_account_id
#: model:ir.model.fields,field_description:account.field_account_move_dummy_account_id
#: model:ir.model.fields,field_description:account.field_account_move_line_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_account_id
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
#: model:ir.ui.view,arch_db:account.view_account_form
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_account_search
#, python-format
msgid "Account"
msgstr "حساب"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_accountant
msgid "Account Accountant"
msgstr "المحاسب"

#. module: account
#: model:ir.model,name:account.model_account_aged_trial_balance
msgid "Account Aged Trial balance Report"
msgstr "تقرير ميزان المراجعة لعمر الحساب"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Account Balances"
msgstr "أرصدة الحساب"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_cashbox
msgid "Account Bank Statement Cashbox Details"
msgstr "تفاصيل كشف الحساب البنكي لصندوق الكاشير"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_closebalance
msgid "Account Bank Statement closing balance"
msgstr "رصيد الإقفال لكشف الحساب البنكي "

#. module: account
#: model:ir.model,name:account.model_account_common_account_report
msgid "Account Common Account Report"
msgstr "تقرير الحساب/الحساب المشترك"

#. module: account
#: model:ir.model,name:account.model_account_common_journal_report
msgid "Account Common Journal Report"
msgstr "تقرير حساب اليومية المشترك"

#. module: account
#: model:ir.model,name:account.model_account_common_partner_report
msgid "Account Common Partner Report"
msgstr "تقرير الحساب المشترك للشريك"

#. module: account
#: model:ir.model,name:account.model_account_common_report
msgid "Account Common Report"
msgstr "تقرير مشترك للحساب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_currency_id
#: model:ir.model.fields,field_description:account.field_account_account_template_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_currency_id
msgid "Account Currency"
msgstr "العملة للحساب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_account_dest_id
msgid "Account Destination"
msgstr "وجهة الحساب"

#. module: account
#: model:ir.model,name:account.model_account_move
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Account Entry"
msgstr "القيد"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_group_form
#: model:ir.ui.view,arch_db:account.view_account_group_tree
msgid "Account Group"
msgstr "مجموعة الحساب"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_group_tree
msgid "Account Groups"
msgstr "مجموعات الحساب"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_bank_journal_id
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_tree
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#: model:ir.ui.view,arch_db:account.view_account_journal_tree
msgid "Account Journal"
msgstr "حساب اليومية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_account_line_id
msgid "Account Line"
msgstr "بند الحساب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_account_ids
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Account Mapping"
msgstr "تفاصيل الحساب"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_reversal
msgid "Account Move Reversal"
msgstr "الحركة العكسية للحساب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_acc_name
msgid "Account Name."
msgstr "اسم الحساب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_bank_acc_number
msgid "Account Number"
msgstr "رقم الحساب"

#. module: account
#: model:ir.model,name:account.model_account_report_partner_ledger
msgid "Account Partner Ledger"
msgstr "حساب أستاذ الشريك"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_account_payable_id
#: model:ir.model.fields,field_description:account.field_res_users_property_account_payable_id
msgid "Account Payable"
msgstr "الحسابات الدائنة"

#. module: account
#: model:ir.model,name:account.model_account_print_journal
msgid "Account Print Journal"
msgstr "يومية طباعة حساب"

#. module: account
#: model:ir.ui.view,arch_db:account.view_category_property_form
msgid "Account Properties"
msgstr "خصائص الحساب"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_account_receivable_id
#: model:ir.model.fields,field_description:account.field_res_users_property_account_receivable_id
msgid "Account Receivable"
msgstr "الحسابات المدينة"

#. module: account
#: model:ir.model,name:account.model_account_financial_report
#: model:ir.model.fields,field_description:account.field_account_financial_report_children_ids
#: model:ir.ui.view,arch_db:account.view_account_financial_report_form
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
#: model:ir.ui.view,arch_db:account.view_account_financial_report_tree
msgid "Account Report"
msgstr "تقرير الحساب"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_account_report_id
#: model:ir.ui.menu,name:account.menu_account_financial_reports_tree
msgid "Account Reports"
msgstr "تقارير الحساب"

#. module: account
#: model:ir.model,name:account.model_account_cash_rounding
msgid "Account Rounding"
msgstr "تقريب الحساب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_account_src_id
msgid "Account Source"
msgstr "مصدر الحساب"

#. module: account
#: model:ir.ui.view,arch_db:account.account_bank_statement_graph
#: model:ir.ui.view,arch_db:account.account_bank_statement_pivot
#: model:ir.ui.view,arch_db:account.account_move_line_graph_date
#: model:ir.ui.view,arch_db:account.account_move_line_graph_date_cash_basis
msgid "Account Statistics"
msgstr "إحصائيات الحساب"

#. module: account
#: model:ir.model,name:account.model_account_account_tag
msgid "Account Tag"
msgstr "وسم الحساب"

#. module: account
#: model:ir.actions.act_window,name:account.account_tag_action
msgid "Account Tags"
msgstr "أوسمة الحساب"

#. module: account
#: model:ir.ui.view,arch_db:account.view_tax_form
#: model:ir.ui.view,arch_db:account.view_tax_tree
msgid "Account Tax"
msgstr "ضريبة الحساب"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_account_tax_template_tree
msgid "Account Tax Template"
msgstr "القالب الضريبي للحساب"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_taxcloud
msgid "Account TaxCloud"
msgstr "حساب خدمة TaxCloud"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
#: model:ir.ui.view,arch_db:account.view_account_template_form
#: model:ir.ui.view,arch_db:account.view_account_template_search
#: model:ir.ui.view,arch_db:account.view_account_template_tree
msgid "Account Template"
msgstr "قالب الحساب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_stock_valuation_account_id
#: model:ir.model.fields,field_description:account.field_res_company_property_stock_valuation_account_id
msgid "Account Template for Stock Valuation"
msgstr "قالب الحساب لتقييم المخزون"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_template_form
msgid "Account Templates"
msgstr "قوالب الحساب"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Account Total"
msgstr "إجمالي الحساب"

#. module: account
#: selection:account.financial.report,type:0
#: model:ir.model,name:account.model_account_account_type
#: model:ir.model.fields,field_description:account.field_account_account_type_name
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_account_type
#: model:ir.ui.view,arch_db:account.view_account_search
#: model:ir.ui.view,arch_db:account.view_account_template_search
#: model:ir.ui.view,arch_db:account.view_account_type_form
#: model:ir.ui.view,arch_db:account.view_account_type_search
#: model:ir.ui.view,arch_db:account.view_account_type_tree
msgid "Account Type"
msgstr "نوع الحساب"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_user_type_id
#: model:ir.model.fields,help:account.field_account_move_line_user_type_id
msgid ""
"Account Type is used for information purpose, to generate country-specific "
"legal reports, and set the rules to close a fiscal year and generate opening"
" entries."
msgstr ""
"يستخدم نوع الحساب لغرض الاستعلام، لتوليد تقارير قانونية خاصة ببلد معين, "
"ولتحديد قواعد إنهاء عام مالي و توليد إدخالات افتتاحية."

#. module: account
#: model:ir.actions.act_window,name:account.action_account_type_form
#: model:ir.model.fields,field_description:account.field_account_financial_report_account_type_ids
msgid "Account Types"
msgstr "أنواع الحساب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_type_control_ids
msgid "Account Types Allowed"
msgstr "انواع الحسابات المسموح بها "

#. module: account
#: model:ir.model,name:account.model_account_unreconcile
msgid "Account Unreconcile"
msgstr "إلغاء تسوية الحساب"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_group_search
msgid "Account group"
msgstr "مجموعة الحساب"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_group_search
msgid "Account groups"
msgstr "مجموعات الحساب"

#. module: account
#: model:ir.model,name:account.model_account_move_line_reconcile
msgid "Account move line reconcile"
msgstr "تسوية بند التحرك للحساب"

#. module: account
#: model:ir.model,name:account.model_account_move_line_reconcile_writeoff
msgid "Account move line reconcile (writeoff)"
msgstr "تسوية بند التحرك للحساب (إلغاء)"

#. module: account
#: model:ir.model,name:account.model_account_move_reversal
msgid "Account move reversal"
msgstr "الحركة العكسية للحساب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_account_src_id
msgid "Account on Product"
msgstr "حساب خاص بالمنتج"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_tag_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template_tag_ids
msgid "Account tag"
msgstr "وسم الحساب"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_refund_account_id
msgid ""
"Account that will be set on invoice tax lines for credit notes. Leave empty "
"to use the expense account."
msgstr ""
"الحساب الذي سيستخدم في بنود ضرائب الفاتورة للإشعارات الدائنة. اتركه فارغاً "
"لاستخدام حساب المصروف."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_account_id
#: model:ir.model.fields,help:account.field_account_tax_template_account_id
msgid ""
"Account that will be set on invoice tax lines for invoices. Leave empty to "
"use the expense account."
msgstr ""
"الحساب الذي سيتم تعيينه على بنود الفاتورة الضريبية للفواتير. اتركه فارغاً "
"لاستخدام حساب المصروفات"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_template_refund_account_id
msgid ""
"Account that will be set on invoice tax lines for refunds. Leave empty to "
"use the expense account."
msgstr ""
"الحساب الذي سيتم تعيينه على بنود الفاتورة الضريبية للمرتجعات. اتركه فارغاً "
"لاستخدام حساب المصروفات"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_account_dest_id
msgid "Account to Use Instead"
msgstr "حساب يستخدم بديلاً"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_cash_basis_account
#: model:ir.model.fields,help:account.field_account_tax_template_cash_basis_account
msgid ""
"Account used as counterpart for the journal entry, for taxes eligible based "
"on payments."
msgstr "الحساب المستخدم لطرف القيد الآخر، للضرائب المحسوبة على أساس التحصيل."

#. module: account
#: model:ir.ui.menu,name:account.account_account_menu
msgid "Accounting"
msgstr "الحسابات"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Accounting App Options"
msgstr "خيارات تطبيق الحسابات"

#. module: account
#: model:ir.ui.view,arch_db:account.view_wizard_multi_chart
msgid "Accounting Application Configuration"
msgstr "إعداد برنامج المحاسبة"

#. module: account
#: model:web.planner,tooltip_planner:account.planner_account
msgid "Accounting Configuration: a step-by-step guide."
msgstr "إعداد الحسابات: خطوة بخطوة"

#. module: account
#: model:ir.actions.act_window,name:account.open_account_journal_dashboard_kanban
msgid "Accounting Dashboard"
msgstr "لوحة معلومات الحسابات"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_date
msgid "Accounting Date"
msgstr "تاريخ المحاسبة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Accounting Documents"
msgstr "المستندات المحاسبية"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_accounting_entries
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Accounting Entries"
msgstr "القيود المحاسبية"

#. module: account
#: model:ir.model,name:account.model_accounting_report
msgid "Accounting Report"
msgstr "التقرير المحاسبي"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Accounting Settings"
msgstr "إعدادات الحسابات"

#. module: account
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Accounting-related settings are managed on"
msgstr "تدار الإعدادات المتعلقة بالمحاسبة على"

#. module: account
#: selection:account.account.tag,applicability:0
#: selection:account.financial.report,type:0
#: model:ir.model.fields,field_description:account.field_account_financial_report_account_ids
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
#: model:ir.ui.view,arch_db:account.view_account_search
msgid "Accounts"
msgstr "الحسابات"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_account_control_ids
msgid "Accounts Allowed"
msgstr "الحسابات المسموح بها"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_account
msgid "Accounts Fiscal Position"
msgstr "الوضع المالي للحسابات"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_template_form
msgid "Accounts Mapping"
msgstr "تفاصيل الحسابات"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_actions
msgid "Actions"
msgstr "الإجراءات"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Activate Other Currencies"
msgstr "تفعيل عملات أخرى"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Activate the option in the"
msgstr "تفعيل الخيار في"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_active
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_active
#: model:ir.model.fields,field_description:account.field_account_journal_active
#: model:ir.model.fields,field_description:account.field_account_payment_term_active
#: model:ir.model.fields,field_description:account.field_account_tax_active
#: model:ir.model.fields,field_description:account.field_account_tax_template_active
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Active"
msgstr "نشط"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:17
#, python-format
msgid "Add"
msgstr "إضافة"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid "Add Credit Note"
msgstr "إضافة إشعار دائن"

#. module: account
#: selection:account.cash.rounding,strategy:0
msgid "Add a rounding line"
msgstr "أضف بند تقريب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_has_second_line
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_has_second_line
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Add a second line"
msgstr "اضافة بند اخر"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Add an internal note..."
msgstr "إضافة ملاحظة داخلية..."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_comment
msgid "Additional Information"
msgstr "معلومات إضافية"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Additional notes..."
msgstr "ملاحظات إضافية..."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Address"
msgstr "العنوان"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_tax_id
msgid "Adjustment Tax"
msgstr "تسوية الضرائب"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_adjustment_type
msgid "Adjustment Type"
msgstr "نوع التسوية "

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_tax_form
msgid "Advanced Options"
msgstr "خيارات متقدمة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Advanced Settings"
msgstr "إعدادات متقدّمة"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries
msgid "Adviser"
msgstr "المحاسب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_include_base_amount
msgid "Affect Base of Subsequent Taxes"
msgstr "تؤثر على قاعدة الضرائب اللاحقة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_include_base_amount
msgid "Affect Subsequent Taxes"
msgstr "تؤثر على الضرائب اللاحقة"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_aged_balance_view
#: model:ir.actions.report,name:account.action_report_aged_partner_balance
#: model:ir.ui.menu,name:account.menu_aged_trial_balance
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Aged Partner Balance"
msgstr "متأخرات أرصدة الشركاء"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
msgid ""
"Aged Partner Balance is a more detailed report of your receivables by "
"intervals. Odoo calculates a table of credit balance by start Date. So if "
"you request an interval of 30 days Odoo generates an analysis of creditors "
"for the past month, past two months, and so on."
msgstr ""
"قائمة الديون غير المحصلة هي تقرير أكثر تفصيلا عن الدينون حسب الفترات. أودو "
"يحتسب جدولا بقائمة الديون بتاريخ بدئها. إذن إذا كنت تود مجالات من 30 يوم "
"أودو ينشئ تحليلا للمدينين للشهر أو الأشهر الماضية و هكذا."

#. module: account
#: selection:account.balance.report,display_account:0
#: selection:account.common.account.report,display_account:0
#: selection:account.report.general.ledger,display_account:0
msgid "All"
msgstr "الكل"

#. module: account
#: selection:account.aged.trial.balance,target_move:0
#: selection:account.balance.report,target_move:0
#: selection:account.common.account.report,target_move:0
#: selection:account.common.journal.report,target_move:0
#: selection:account.common.partner.report,target_move:0
#: selection:account.common.report,target_move:0
#: selection:account.print.journal,target_move:0
#: selection:account.report.general.ledger,target_move:0
#: selection:account.report.partner.ledger,target_move:0
#: selection:account.tax.report,target_move:0
#: selection:accounting.report,target_move:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "All Entries"
msgstr "كل القيود"

#. module: account
#: model:ir.actions.act_window,name:account.action_all_partner_invoices
msgid "All Invoices"
msgstr "كافة الفواتير"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_all_lines_reconciled
msgid "All Lines Reconciled"
msgstr "كافة البنود المسواة"

#. module: account
#: selection:account.aged.trial.balance,target_move:0
#: selection:account.balance.report,target_move:0
#: selection:account.common.account.report,target_move:0
#: selection:account.common.journal.report,target_move:0
#: selection:account.common.partner.report,target_move:0
#: selection:account.common.report,target_move:0
#: selection:account.print.journal,target_move:0
#: selection:account.report.general.ledger,target_move:0
#: selection:account.report.partner.ledger,target_move:0
#: selection:account.tax.report,target_move:0
#: selection:accounting.report,target_move:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "All Posted Entries"
msgstr "كل القيود المرحلة"

#. module: account
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "All accounts"
msgstr "كل الحسابات"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "All accounts'"
msgstr "الخاصة بكل الحسابات"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:47
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr "تم مطابقة جميع الفواتير والمدفوعات, الأرصدة الخاصة بحساباتك نظيفة."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_state
msgid ""
"All manually created new journal entries are usually in the status "
"'Unposted', but you can set the option to skip that status on the related "
"journal. In that case, they will behave as journal entries automatically "
"created by the system on document validation (invoices, bank statements...) "
"and will be created in 'Posted' status."
msgstr ""
"كافة القيود اليومية الجديدة التى تم إنشائها يدوياً عادتاً ما تكون فى وضع "
"\"عدم النشر \" , و لكن يمكنك تحديد الخيار لتخطى هذا الوضع إلى اليومية ذات "
"الصلة . في هذه الحالة، فإنها تتصرف كما لو أنها قيود اليومية التي تم إنشاؤها "
"تلقائيا من قبل النظام و تم التحقق من صحة الوثيقة (الفواتير، والبيانات "
"البنكية ...) وسيتم إنشاؤها في وضع 'نشر'."

#. module: account
#: model:ir.ui.view,arch_db:account.validate_account_move_view
msgid ""
"All selected journal entries will be validated and posted. You won't be able"
" to modify them afterwards."
msgstr ""
"كل القيود اليومية سيتم التحقق من صحتها و نشرها. و لن تتمكن من التعديل عليها "
"بعد ذلك."

#. module: account
#: code:addons/account/models/account_bank_statement.py:240
#, python-format
msgid ""
"All the account entries lines must be processed in order to close the "
"statement."
msgstr "يجب أن تتم معالجة جميع بنود القيود من أجل إقفال القائمة."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_update_posted
msgid "Allow Cancelling Entries"
msgstr "السماح بإلغاء القيود"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_reconcile
msgid "Allow Invoices & payments Matching"
msgstr "السماح بمطابقة الفواتير و المدفوعات "

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_product_margin
msgid "Allow Product Margin"
msgstr "السماح بهوامش الربح في المنتج"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_reconcile
msgid "Allow Reconciliation"
msgstr "السماح بالتسوية"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_l10n_us_check_printing
msgid "Allow check printing and deposits"
msgstr "السماح بطباعة الشيكات و دفعها كمقدمات "

#. module: account
#: model:res.groups,name:account.group_cash_rounding
msgid "Allow the cash rounding management"
msgstr "السماح بإدارة التقريب في المعاملات المالية"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allow to configure taxes using cash basis"
msgstr "السماح بضبط ضرائب على أساس التحصيل"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allows you to use the analytic accounting."
msgstr "يسمح لك باستخدام المحاسبة التحليلية"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:193
#: code:addons/account/static/src/xml/account_reconciliation.xml:235
#: code:addons/account/static/src/xml/account_reconciliation.xml:252
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_amount
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_subtotal
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_total
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_amount
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_amount_total
#: model:ir.model.fields,field_description:account.field_account_move_amount
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_amount
#: model:ir.model.fields,field_description:account.field_account_tax_amount
#: model:ir.model.fields,field_description:account.field_account_tax_template_amount
#: model:ir.model.fields,field_description:account.field_cash_box_in_amount
#: model:ir.model.fields,field_description:account.field_cash_box_out_amount
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_amount
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_payment_tree
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_account_supplier_payment_tree
#: model:ir.ui.view,arch_db:account.view_move_line_form
#, python-format
msgid "Amount"
msgstr "المبلغ"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_amount_currency
#: model:ir.model.fields,field_description:account.field_account_analytic_line_analytic_amount_currency
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_amount_currency
#: model:ir.model.fields,field_description:account.field_account_move_line_amount_currency
msgid "Amount Currency"
msgstr "المبلغ بالعملة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_residual
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid "Amount Due"
msgstr "المبلغ المستحق"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_residual_company_signed
msgid "Amount Due in Company Currency"
msgstr "المبلغ المستحق بعملة الشركة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_residual_signed
msgid "Amount Due in Invoice Currency"
msgstr "المبلغ المستحق بعملة الفاتورة"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Amount Paid"
msgstr "المبلغ المدفوع"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_amount_rounding
msgid "Amount Rounding"
msgstr "تقريب المبلغ"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_subtotal_signed
msgid "Amount Signed"
msgstr "المبلغ الموقع "

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_amount_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_amount_type
msgid "Amount Type"
msgstr "نوع الكمية"

#. module: account
#: model:ir.model.fields,help:account.field_account_partial_reconcile_amount
msgid "Amount concerned by this matching. Assumed to be always positive"
msgstr "المبلغ المعنى بهذا التطابق. يفترض أن يكون موجب دائما."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_amount_currency
msgid "Amount in Currency"
msgstr "المبلغ بالعملة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Amount type"
msgstr "نوع المبلغ"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:68
#, python-format
msgid "Amount:"
msgstr "المبلغ:"

#. module: account
#: sql_constraint:account.fiscal.position.account:0
msgid ""
"An account fiscal position could be defined only once time on same accounts."
msgstr "يمكن  تعريف وضع الحساب المالي مرة واحدة فقط في نفس الحسابات."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_form
msgid ""
"An account is part of a ledger allowing your company\n"
"                to register all kinds of debit and credit transactions.\n"
"                Companies present their annual accounts in two main parts: the\n"
"                balance sheet and the income statement (profit and loss\n"
"                account). The annual accounts of a company are required by law\n"
"                to disclose a certain amount of information."
msgstr ""
"الحساب هو جزء من دفتر الحسابات يتيح لشركتك\n"
"تسجيل جميع المعاملات الدائنة و المدينة.\n"
"تقدم الشركات حساباتها السنوية في جزئيين أساسيين: \n"
"الميزانية العمومية و كشف الإيراد (حساب الربح و الخسارة)\n"
"الحسابات السنوية للشركة مطالبه من قبل القانون\n"
"بالكشف عن قدر معين من المعلومات."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_type_form
msgid ""
"An account type is used to determine how an account is used in\n"
"                each journal. The deferral method of an account type determines\n"
"                the process for the annual closing. Reports such as the Balance\n"
"                Sheet and the Profit and Loss report use the category\n"
"                (profit/loss or balance sheet)."
msgstr ""
"يستخدم نوع الحساب في تحديد كيفية استخدام الحساب في\n"
"كل دفتر يومية. طريقة تأجيل نوع الحساب تحدد\n"
" عملية الإغلاق السنوي. التقارير مثل الميزانية\n"
" العمومية و تقرير الربح و الخسارة يستخدم فئة \n"
" (ربح/خسارة أو سجل الرصيد)"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Analytic"
msgstr "تحليلي"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:178
#, python-format
msgid "Analytic Acc."
msgstr "حساب تحليلي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_account_analytic_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_account_analytic_id
#: model:ir.model.fields,field_description:account.field_account_move_line_analytic_account_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_analytic_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_analytic_account_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Analytic Account"
msgstr "الحساب التحليلي"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_group_analytic_accounting
#: model:ir.ui.menu,name:account.menu_analytic_accounting
msgid "Analytic Accounting"
msgstr "محاسبة تحليلية"

#. module: account
#: model:ir.actions.act_window,name:account.action_open_partner_analytic_accounts
#: model:ir.ui.menu,name:account.account_analytic_def_account
#: model:ir.ui.view,arch_db:account.partner_view_button_contracts_count
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytic Accounts"
msgstr "حسابات تحليلية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_analytic
msgid "Analytic Cost"
msgstr "تكلفة تحليلية"

#. module: account
#: model:ir.actions.act_window,name:account.analytic_line_reporting_pivot
#: model:ir.ui.menu,name:account.menu_action_analytic_lines_reporting
#: model:ir.ui.menu,name:account.menu_action_analytic_lines_tree
msgid "Analytic Entries"
msgstr "قيود تحليلية"

#. module: account
#: model:ir.model,name:account.model_account_analytic_line
msgid "Analytic Line"
msgstr "البنود التحليلية"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Analytic Lines"
msgstr "البنود التحليلية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_analytic_tag_ids
#: model:ir.ui.menu,name:account.account_analytic_tag_menu
msgid "Analytic Tags"
msgstr "الوسوم التحليلية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_account_analytic_id
msgid "Analytic account"
msgstr "الحساب التحليلي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_analytic_line_ids
msgid "Analytic lines"
msgstr "البنود التحليلية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_analytic_tag_ids
msgid "Analytic tags"
msgstr "وسوم التحليل"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytics"
msgstr "التحليلات"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_exigible
msgid "Appears in VAT report"
msgstr "يظهر في تقرير ضريبة القيمة المضافة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_applicability
msgid "Applicability"
msgstr "المطابقة"

#. module: account
#: selection:tax.adjustments.wizard,adjustment_type:0
msgid "Applied on credit journal item"
msgstr "تم التطبيق علي عنصر دفتر يوميه الائتمان"

#. module: account
#: selection:tax.adjustments.wizard,adjustment_type:0
msgid "Applied on debit journal item"
msgstr "تم التطبيق علي عنصر دفتر يوميه الخصم"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_auto_apply
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_auto_apply
msgid "Apply automatically this fiscal position."
msgstr "تطبيق هذا الوضع المالي تلقائيا."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_country_group_id
msgid "Apply only if delivery or invocing country match the group."
msgstr "تطبق فقط في حال كان التسليم أو فاتورة البلد تتطابق مع المجموعة"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_country_group_id
msgid "Apply only if delivery or invoicing country match the group."
msgstr ""
"التطبيق فقط إذا كان الدولة في عنوان الشحن أو عنوان الفاتورة تنتمي للمجموعة."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_country_id
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_country_id
msgid "Apply only if delivery or invoicing country match."
msgstr "تطبق فقط في حال كان التسليم أو فاتورة البلد تتطابق"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_template_vat_required
#: model:ir.model.fields,help:account.field_account_fiscal_position_vat_required
msgid "Apply only if partner has a VAT number."
msgstr "تطبق فقط  في حال كان الشريك حاصل على رقم ضريبة القيمة المضافة (VAT)"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Apply right VAT rates for digital products sold in EU"
msgstr ""
"تطبيق أسعار القيمة المضافة الصحيحة للمنتجات الرقمية في الاتحاد الأوروبي"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "April"
msgstr "أبريل"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Archived"
msgstr "مؤرشف"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Ask for a Credit Note"
msgstr "طلب إشعار دائن"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_assets0
msgid "Assets"
msgstr "الأصول"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_asset
msgid "Assets Management"
msgstr "إدارة الأصول"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_account_ids
msgid "Associated Account Templates"
msgstr "نماذج الحساب المرتبطة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_at_least_one_inbound
msgid "At Least One Inbound"
msgstr "وارد واحد على الأقل"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_at_least_one_outbound
msgid "At Least One Outbound"
msgstr "صادر واحد على الاقل"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "August"
msgstr "أغسطس"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Auto-detect"
msgstr "كشف تلقائي"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automate deferred revenues entries for multi-year contracts"
msgstr "تطبيق آلي لقيود الأرباح المُرحلة للعقود متعددة السنوات"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automated Entries"
msgstr "تطبيق آلي للقيود"

#. module: account
#: code:addons/account/models/company.py:411
#: code:addons/account/wizard/setup_wizards.py:79
#, python-format
msgid "Automatic Balancing Line"
msgstr "بند الترصيد الآلي"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_currency_rate_live
msgid "Automatic Currency Rates"
msgstr "أسعار الصرف الآلية"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automatic Import"
msgstr "استيراد آلي"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Automatic formatting"
msgstr "تنسيق تلقائي"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:24
#, python-format
msgid "Automatic reconciliation"
msgstr "التسوية تلقائيا"

#. module: account
#: code:addons/account/models/account_bank_statement.py:468
#, python-format
msgid "Automatically reconciled items"
msgstr "تسوية العناصر تلقائيا"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_price_average
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_currency_price_average
msgid "Average Price"
msgstr "متوسط السعر"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Awaiting payments"
msgstr "في انتظار المدفوعات"

#. module: account
#: code:addons/account/models/chart_template.py:194
#, python-format
msgid "BILL"
msgstr "فاتورة"

#. module: account
#: selection:res.partner,trust:0
msgid "Bad Debtor"
msgstr "مدين سيئ"

#. module: account
#: selection:account.payment.term.line,value:0
#: model:ir.model.fields,field_description:account.field_account_move_line_balance
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_payment_receipt
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Balance"
msgstr "الرصيد"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_balance_cash_basis
msgid "Balance Cash Basis"
msgstr "الرصد الإجمالي - أساس نقدي"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_balancesheet0
#: model:ir.actions.act_window,name:account.action_account_report_bs
#: model:ir.ui.menu,name:account.menu_account_report_bs
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Balance Sheet"
msgstr "الميزانية العمومية"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_balance_end
msgid "Balance as calculated based on Opening Balance and transaction lines"
msgstr "يتم احتساب الرصيد على أساس الرصيد الافتتاحى و بنود المعاملة. "

#. module: account
#: selection:account.bank.accounts.wizard,account_type:0
#: selection:account.journal,type:0
#: code:addons/account/models/chart_template.py:758
#: model:ir.model.fields,field_description:account.field_account_journal_bank_id
#: model:ir.model.fields,field_description:account.field_res_partner_bank_account_count
#: model:ir.model.fields,field_description:account.field_res_users_bank_account_count
#, python-format
msgid "Bank"
msgstr "البنك"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Bank &amp; Cash"
msgstr "النقدية والبنوك"

#. module: account
#: code:addons/account/models/company.py:226
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_bank_account_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_bank_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_partner_bank_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_partner_bank_id
#: model:ir.model.fields,field_description:account.field_account_journal_bank_account_id
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#, python-format
msgid "Bank Account"
msgstr "الحساب البنكي"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Bank Account Name"
msgstr "اسم الحساب البنكي"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_partner_bank_id
msgid ""
"Bank Account Number to which the invoice will be paid. A Company bank "
"account if this is a Customer Invoice or Vendor Credit Note, otherwise a "
"Partner bank account number."
msgstr ""
"رقم الحساب البنكي الذي ستدفع الفاتورة فيه. حساب المؤسسة البنكي إذا كانت "
"فاتورة العميل أو إشعار المورد الدائن، أو سيتم استخدام رقم الحساب البنكي "
"للشريك."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:21
#: model:ir.actions.act_window,name:account.action_account_bank_journal_form
#: model:ir.model,name:account.model_res_partner_bank
#: model:ir.ui.menu,name:account.menu_action_account_bank_journal_form
#, python-format
msgid "Bank Accounts"
msgstr "الحسابات البنكية"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_bank_account_code_prefix
msgid "Bank Accounts Prefix"
msgstr "بادئة الحسابات البنكية "

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_bank_statements_source
msgid "Bank Feeds"
msgstr "تغذية البنك"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_yodlee
msgid "Bank Interface - Sync your bank feeds automatically"
msgstr "الواجهة البنكية - مزامنة تغذية البنك تلقائيا"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_bank_journal_ids
msgid "Bank Journals"
msgstr "يوميات البنك"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Bank Operations"
msgstr "عمليات البنك"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_tree
msgid "Bank Reconciliation Move Presets"
msgstr "حركة مسبقة للتسوية البنكية"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_search
msgid "Bank Reconciliation Move preset"
msgstr "حركة مسبقة للتسوية البنكية"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_bank_data_done
msgid "Bank Setup Marked As Done"
msgstr "تم تحديد إعدادات البنوك كمهمة منتهية"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Bank Statement"
msgstr "كشف حساب المصرف"

#. module: account
#: code:addons/account/models/account_bank_statement.py:937
#, python-format
msgid "Bank Statement %s"
msgstr "كشف حساب بنكي %s"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "بند كشف حساب المصرف"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_line
msgid "Bank Statement Lines"
msgstr "بنود كشف الحساب"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_tree
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Bank Statements"
msgstr "كشوف الحساب"

#. module: account
#: model:ir.ui.view,arch_db:account.partner_view_buttons
msgid "Bank account(s)"
msgstr "الحسابات البنكية"

#. module: account
#: model:account.account.type,name:account.data_account_type_liquidity
msgid "Bank and Cash"
msgstr "النقدية و البنك"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_action.js:15
#, python-format
msgid "Bank reconciliation"
msgstr "التسوية البنكية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_account_setup_bank_data_done
msgid "Bank setup marked as done"
msgstr "تم تحديد إعداد البنوك كمهمة منتهية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_statement_line_id
msgid "Bank statement line reconciled with this entry"
msgstr "تسوية بند كشف الحساب بهذا القيد"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_bank_statement
msgid "Bank statements"
msgstr "كشوف الحساب"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:39
#, python-format
msgid "Bank: Balance"
msgstr "البنك: الرصيد"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_base
#: model:ir.ui.view,arch_db:account.portal_invoice_report
msgid "Base"
msgstr "الأساس"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_base_amount
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Base Amount"
msgstr "المبلغ الأساسي"

#. module: account
#: selection:account.tax,tax_exigibility:0
#: selection:account.tax.template,tax_exigibility:0
msgid "Based on Invoice"
msgstr "بناء على الفواتير"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_tax_exigibility
#: model:ir.model.fields,help:account.field_account_tax_template_tax_exigibility
msgid ""
"Based on Invoice: the tax is due as soon as the invoice is validated.\n"
"Based on Payment: the tax is due as soon as the payment of the invoice is received."
msgstr ""
"بناء على الفواتير: تستحق الضريبة ما أن يتم اعتماد وتحقيق الفاتورة.\n"
"بناء على التحصيل: تستحق الضريبة ما أن يتم تسجيل سداد على الفاتورة."

#. module: account
#: selection:account.tax,tax_exigibility:0
#: selection:account.tax.template,tax_exigibility:0
msgid "Based on Payment"
msgstr "بناء على التحصيل"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Batch Deposits"
msgstr "دفعات  الايداع"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Before continuing, you must install the Chart of Account related to your "
"country (or the generic one if your country is not listed)."
msgstr ""
"قبل المتابعة, يجب عليك تثبيت الدليل المحاسبي الخاص ببلدك ( أو الدليل العام "
"اذا لم تكن بلدك في القائمة)."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_belongs_to_company
msgid "Belong to the user's current company"
msgstr "يعود لمستخدمي الشركة الحالية"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Bill"
msgstr "الفاتورة"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "Bill Date"
msgstr "تاريخ الفاتورة"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Bill lines"
msgstr "بنود الفاتورة"

#. module: account
#: model:res.groups,name:account.group_account_invoice
msgid "Billing"
msgstr "عمليات الفواتير"

#. module: account
#: model:res.groups,name:account.group_account_manager
msgid "Billing Manager"
msgstr "المدير المالي"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills"
msgstr "الفواتير"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills Analysis"
msgstr "تحليل الفواتير"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:186
#, python-format
msgid "Bills to pay"
msgstr "فواتير للدفع"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "Blocking Message"
msgstr "رسالة حظر"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_type_include_initial_balance
msgid "Bring Accounts Balance Forward"
msgstr "جلب حسابات الرصيد إلى الأمام"

#. module: account
#: model:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid "Browse available countries."
msgstr "تصفح البلدان المتاحة."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_budget
msgid "Budget Management"
msgstr "إدارة الموازنات"

#. module: account
#: model:ir.ui.menu,name:account.account_reports_business_intelligence_menu
msgid "Business Intelligence"
msgstr "ذكاء الأعمال"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_name
msgid "Button Label"
msgstr "تسمية الزر"

#. module: account
#: model:ir.filters,name:account.filter_invoice_country
msgid "By Country"
msgstr "بالدولة"

#. module: account
#: model:ir.filters,name:account.filter_invoice_refund
msgid "By Credit Note"
msgstr "حسب الإشعار الدائن"

#. module: account
#: model:ir.filters,name:account.filter_invoice_product
msgid "By Product"
msgstr "حسب المنتج"

#. module: account
#: model:ir.filters,name:account.filter_invoice_product_category
msgid "By Product Category"
msgstr "بفئة المنتج"

#. module: account
#: model:ir.filters,name:account.filter_invoice_salespersons
msgid "By Salespersons"
msgstr "حسب موظف المبيعات"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_active
msgid ""
"By unchecking the active field, you may hide a fiscal position without "
"deleting it."
msgstr "عند إلغاء اختيار حقل \"نشط\"، يمكنك إخفاء الوضع المالي دون حذفه."

#. module: account
#: code:addons/account/models/chart_template.py:173
#: code:addons/account/models/chart_template.py:197
#, python-format
msgid "CABA"
msgstr "CABA"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "CAMT Import"
msgstr "استيراد CAMT"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "CSV Import"
msgstr "استيراد CSV"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_visible
msgid "Can be Visible?"
msgstr "هل تكون واضحة؟"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.account_common_report_view
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
#: model:ir.ui.view,arch_db:account.cash_box_in_form
#: model:ir.ui.view,arch_db:account.cash_box_out_form
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
#: model:ir.ui.view,arch_db:account.validate_account_move_view
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
#: model:ir.ui.view,arch_db:account.view_account_move_reversal
#: model:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Cancel"
msgstr "إلغاء"

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Cancel: create credit note and reconcile"
msgstr "إلغاء: إنشاء إشعار دائن والتسوية"

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: selection:account.payment,state:0
msgid "Cancelled"
msgstr "ملغى "

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Cancelled Invoice"
msgstr "فاتورة ملغاة"

#. module: account
#: code:addons/account/wizard/account_invoice_refund.py:56
#, python-format
msgid ""
"Cannot create a credit note for the invoice which is already reconciled, "
"invoice should be unreconciled first, then only you can add credit note for "
"this invoice."
msgstr ""
"لم يمكن إنشاء إشعار دائن للفاتورة حيث تم تسويتها بالفعل، يجب إلغاء تسوية "
"الفاتورة أولاً وبعدها فقط يمكن إضافة إشعار دائن على هذه الفاتورة."

#. module: account
#: code:addons/account/wizard/account_invoice_refund.py:54
#, python-format
msgid "Cannot create credit note for the draft/cancelled invoice."
msgstr "لم يمكن إنشاء إشعار دائن لفاتورة في حالة مسودة/ملغية."

#. module: account
#: code:addons/account/models/account_move.py:197
#, python-format
msgid "Cannot create moves for different companies."
msgstr "لا تستطيع إنشاء حركات لمختلف الشركات."

#. module: account
#: code:addons/account/models/account_move.py:229
#, python-format
msgid "Cannot create unbalanced journal entry."
msgstr "لا يمكن انشاء دفتر يومية غير متوازن."

#. module: account
#: code:addons/account/models/account_invoice.py:641
#, python-format
msgid ""
"Cannot find a chart of accounts for this company, You should configure it. \n"
"Please go to Account Configuration."
msgstr ""
"لم يتم العثور على أي رسم بياني لحسابات هذه المؤسسة. يجب عليك اعدادها.\n"
" الرجاء الذهاب الى اعدادات الحساب"

#. module: account
#: code:addons/account/models/account.py:594
#, python-format
msgid "Cannot generate an unused account code."
msgstr "لا يمكن انشاء كود حساب غير مستخدم."

#. module: account
#: code:addons/account/models/account.py:624
#, python-format
msgid ""
"Cannot generate an unused journal code. Please fill the 'Shortcode' field."
msgstr ""
"لا يمكن انشاء كود دفتر يومية غير مستخدم. من فضلك املئ حقل \"الكود المختصر\""

#. module: account
#: selection:account.bank.accounts.wizard,account_type:0
#: selection:account.journal,type:0
#: code:addons/account/models/chart_template.py:758
#, python-format
msgid "Cash"
msgstr "نقدي"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_cash_account_code_prefix
msgid "Cash Accounts Prefix"
msgstr "بادئة الحسابات النقدية"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_tax_exigibility
msgid "Cash Basis"
msgstr "الأساس النقدي"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_tax_cash_basis_journal_id
msgid "Cash Basis Journal"
msgstr "تم التطبيق علي عنصر دفتر يوميه النقد"

#. module: account
#: code:addons/account/models/chart_template.py:197
#, python-format
msgid "Cash Basis Tax Journal"
msgstr "يوميه ضريبة الأساس النقدي"

#. module: account
#: code:addons/account/models/account_bank_statement.py:210
#: model:ir.actions.act_window,name:account.action_view_account_bnk_stmt_cashbox
#, python-format
msgid "Cash Control"
msgstr "التحكم بالنقدية"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Cash Operations"
msgstr "العمليات النقدية"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_bank_statement_tree
msgid "Cash Registers"
msgstr "سجلات النقدية"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_group_cash_rounding
msgid "Cash Rounding"
msgstr "تقريب النقدية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_cash_rounding_id
msgid "Cash Rounding Method"
msgstr "طريقة تقريب النقدية"

#. module: account
#: model:ir.actions.act_window,name:account.rounding_list_action
#: model:ir.ui.menu,name:account.menu_action_rounding_form_view
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Cash Roundings"
msgstr "تقريب النقدية"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Cash Statements"
msgstr "الكشوفات النقدية"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_bank_account_ids
msgid "Cash and Banks"
msgstr "النقدية و البنوك"

#. module: account
#: code:addons/account/models/account_bank_statement.py:185
#, python-format
msgid "Cash difference observed during the counting (%s)"
msgstr "الفرق النقدي لوحظ أثناء العد (%s)"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:37
#, python-format
msgid "Cash: Balance"
msgstr "النقدية: الرصيد"

#. module: account
#: model:ir.model,name:account.model_account_cashbox_line
msgid "CashBox Line"
msgstr "بند صندوق الكاشير"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_cashbox_id
msgid "Cashbox"
msgstr "صندوق الكاشير"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_cashbox_lines_ids
msgid "Cashbox Lines"
msgstr "بنود صندوق الكاشير"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_expense_categ_id
msgid "Category of Expense Account"
msgstr "فئة حساب المصروفات"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_income_categ_id
msgid "Category of Income Account"
msgstr "فئة حساب الإيرادات"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Change"
msgstr "تغيير"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_writeoff_label
msgid "Change label of the counterpart that will hold the payment difference"
msgstr "تغيير تسميه الحساب  الذي يحتوي فرق الدفع"

#. module: account
#: code:addons/account/controllers/portal.py:146
#, python-format
msgid ""
"Changing VAT number is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"لا يسمح بتغيير رقم ضريبة القيمة المضافة بعد إصدار الفواتير لحسابك. يرجى "
"الاتصال بنا مباشرة لهذه العملية."

#. module: account
#: code:addons/account/controllers/portal.py:149
#, python-format
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"لا يسمح بتغيير إسمك بعد إصدار الفواتير لحسابك. يرجى الاتصال بنا مباشرة لهذه "
"العملية."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_chart_template_id
#: model:ir.model.fields,field_description:account.field_res_company_chart_template_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_chart_template_id
msgid "Chart Template"
msgstr "قالب الرسم البيانى"

#. module: account
#: model:ir.actions.act_window,name:account.open_account_charts_modules
msgid "Chart Templates"
msgstr "قوالب الرسم البيانى"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_coa_done
msgid "Chart of Account Checked"
msgstr "تثبيت شجرة الحسابات"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:278
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:13
#: model:ir.actions.act_window,name:account.action_account_form
#: model:ir.ui.menu,name:account.menu_action_account_form
#: model:ir.ui.view,arch_db:account.account_planner
#, python-format
msgid "Chart of Accounts"
msgstr "الدليل المحاسبي"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_form
#: model:ir.ui.view,arch_db:account.view_account_chart_template_tree
msgid "Chart of Accounts Template"
msgstr "قالب الدليل المحاسبي"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_chart_template_form
msgid "Chart of Accounts Templates"
msgstr "قوالب الدليل المحاسبي"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Chart of Accounts has been\n"
"                            installed. You should review it and create any additional accounts."
msgstr ""
"الدليل المحاسبي قد تم\n"
"تنصيبه. يجدر بك مراجعته و إنشاء الحسابات الإضافية."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_list
msgid "Chart of accounts"
msgstr "الدليل المحاسبي"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_account_bnk_stmt_check
msgid "Check Closing Balance"
msgstr "التحقق من رصيد الإقفال"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_is_difference_zero
msgid "Check if difference is zero."
msgstr "التحقق إذا كان الفرق صفر."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_reconcile
msgid ""
"Check this box if this account allows invoices & payments matching of "
"journal items."
msgstr ""
"اختر هذا المربع إذا كان الحساب يسمح بمطابقة الفواتير و المدفوعات بعناصر دفتر"
" اليومية."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_refund_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" credit notes made from this journal"
msgstr ""
"حدد هذا المربع إذا كنت لا تريد مشاركه نفس التسلسل للفواتير والإشعارات "
"الدائنة المصنوعة من دفتر اليومية هذا"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_update_posted
msgid ""
"Check this box if you want to allow the cancellation the entries related to "
"this journal or of the invoice related to this journal"
msgstr ""
"اختر هذا المربع إذا كنت ترغب في السماح بإلغاء البنود المتعلقة بهذه اليومية "
"أو من الفاتورة ذات الصلة لهذه اليومية"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_price_include
#: model:ir.model.fields,help:account.field_account_tax_template_price_include
msgid ""
"Check this if the price you use on the product and invoices includes this "
"tax."
msgstr ""
"تحقق من هذا اذا كان السعر الذي تستخدمه على المنتج والفواتير يحتوي على هذه "
"الضريبة."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_reconcile
msgid ""
"Check this option if you want the user to reconcile entries in this account."
msgstr "قم بالتعليم علي الخيار إذا أردت للمستخدم أن يسوي القيود لهذا الحساب."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Checks"
msgstr "الضوابط"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_children_tax_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template_children_tax_ids
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_tax_form
msgid "Children Taxes"
msgstr "الضرائب التابعون"

#. module: account
#: code:addons/account/models/chart_template.py:155
#: model:ir.actions.act_window,name:account.action_wizard_multi_chart
#, python-format
msgid "Choose Accounting Template"
msgstr "اختيار قالب المحاسبة"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:131
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr "اختيار حساب  أو الشطب"

#. module: account
#: model:ir.actions.act_window,help:account.action_account_bank_journal_form
msgid "Click to add a bank account."
msgstr "انقر لإضافة حساب بنكي."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_journal_form
msgid "Click to add a journal."
msgstr "انقر هنا لإضافة دفتر يومية."

#. module: account
#: model:ir.actions.act_window,help:account.account_tag_action
msgid "Click to add a new tag."
msgstr "انقر لاضافة وسم جديد."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_form
msgid "Click to add an account."
msgstr "انقر هنا لإضافة حساب."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_out_refund
msgid "Click to create a credit note."
msgstr "انقر لإنشاء إشعار دائن."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree1
msgid "Click to create a customer invoice."
msgstr "انقر لإنشاء فاتورة العميل."

#. module: account
#: model:ir.actions.act_window,help:account.action_move_journal_line
msgid "Click to create a journal entry."
msgstr "انقر لإنشاء قيد اليومية."

#. module: account
#: model:ir.actions.act_window,help:account.action_view_bank_statement_tree
msgid "Click to create a new cash log."
msgstr "انقر لإنشاء سجل نقدي جديد."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_reconcile_model
msgid "Click to create a reconciliation model."
msgstr "انقر لإنشاء كائن (نموذج) التسوية ."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_type_form
msgid "Click to define a new account type."
msgstr "انقر لتعريف نوع حساب جديد."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree2
msgid "Click to record a new vendor bill."
msgstr "انقر لتسجيل فاتورة مورد جديدة."

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_in_refund
msgid "Click to record a new vendor credit note."
msgstr "انقر لإنشاء إشعار دائن جديد لمورد."

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_tree
msgid "Click to register a bank statement."
msgstr "انقر لتسجيل كشف حساب."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_payments
#: model:ir.actions.act_window,help:account.action_account_payments_payable
msgid "Click to register a payment"
msgstr "انقر لتسجيل دفع"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:260
#, python-format
msgid "Close"
msgstr "إقفال"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:60
#, python-format
msgid "Close statement"
msgstr "إقفال الكشف"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_date_done
msgid "Closed On"
msgstr "إقفال يوم"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_method_code
#: model:ir.model.fields,field_description:account.field_account_account_code
#: model:ir.model.fields,field_description:account.field_account_account_template_code
#: model:ir.model.fields,field_description:account.field_account_analytic_line_code
#: model:ir.model.fields,field_description:account.field_account_payment_method_code
#: model:ir.model.fields,field_description:account.field_account_payment_payment_method_code
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_method_code
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Code"
msgstr "الكود"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group_code_prefix
msgid "Code Prefix"
msgstr "بادئة الكود"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_coin_value
msgid "Coin/Bill Value"
msgstr "قيمة عملة/فاتورة"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Collect customer payments in one-click using Euro SEPA Service."
msgstr "جمع مدفوعات العملاء بنقره واحده باستخدام خدمه اليورو sepa."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_color
#: model:ir.model.fields,field_description:account.field_account_journal_color
msgid "Color Index"
msgstr "لون الرقم التسلسلى"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_label_filter
msgid "Column Label"
msgstr "اسم العمود"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_comment
msgid "Comment"
msgstr "تعليق"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_commercial_partner_id
#: model:ir.model.fields,help:account.field_account_invoice_report_commercial_partner_id
msgid "Commercial Entity"
msgstr "الكيان التجارى"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_common_menu
msgid "Common Report"
msgstr "تقرير مشترك"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Communication"
msgstr "إتصالات"

#. module: account
#: model:ir.model,name:account.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_ref_company_ids
#: model:ir.model.fields,field_description:account.field_res_users_ref_company_ids
msgid "Companies that refers to partner"
msgstr "شركات تشير إلي شركاء"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_company_id
#: model:ir.model.fields,field_description:account.field_account_account_company_id
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_company_id
#: model:ir.model.fields,field_description:account.field_account_balance_report_company_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_company_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_company_id
#: model:ir.model.fields,field_description:account.field_account_chart_template_company_id
#: model:ir.model.fields,field_description:account.field_account_common_account_report_company_id
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_company_id
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_company_id
#: model:ir.model.fields,field_description:account.field_account_common_report_company_id
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_company_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_company_id
#: model:ir.model.fields,field_description:account.field_account_journal_company_id
#: model:ir.model.fields,field_description:account.field_account_move_company_id
#: model:ir.model.fields,field_description:account.field_account_move_line_company_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_company_id
#: model:ir.model.fields,field_description:account.field_account_opening_company_id
#: model:ir.model.fields,field_description:account.field_account_payment_company_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_company_id
#: model:ir.model.fields,field_description:account.field_account_print_journal_company_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_company_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_company_id
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_company_id
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_company_id
#: model:ir.model.fields,field_description:account.field_account_tax_company_id
#: model:ir.model.fields,field_description:account.field_account_tax_report_company_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_company_id
#: model:ir.model.fields,field_description:account.field_accounting_report_company_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_company_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Company"
msgstr "الشركة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line_company_currency_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_company_currency_id
msgid "Company Currency"
msgstr "عملة المؤسسة"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:210
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:25
#, python-format
msgid "Company Data"
msgstr "بيانات الشركة"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_company_data_done
msgid "Company Setup Marked As Done"
msgstr "تم الانتهاء من  اعداد الشركة"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_has_chart_of_accounts
msgid "Company has a chart of accounts"
msgstr "الشركة لديها دليل محاسبي"

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_company_id
#: model:ir.model.fields,help:account.field_account_bank_statement_company_id
#: model:ir.model.fields,help:account.field_account_bank_statement_line_company_id
#: model:ir.model.fields,help:account.field_account_journal_company_id
#: model:ir.model.fields,help:account.field_account_move_company_id
#: model:ir.model.fields,help:account.field_account_payment_company_id
#: model:ir.model.fields,help:account.field_account_register_payments_company_id
msgid "Company related to this journal"
msgstr "الشركات المرتبطة بهذه اليومية"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Compare actual revenues &amp; costs with budgets"
msgstr "مقارنة الأرباح والتكاليف الفعلية باستخدام الموازنات"

#. module: account
#: model:ir.ui.view,arch_db:account.accounting_report_view
msgid "Comparison"
msgstr "مقارنة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_complete_tax_set
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_complete_tax_set
msgid "Complete Set of Taxes"
msgstr "مجموعة كاملة من الضرائب"

#. module: account
#: code:addons/account/models/account_invoice.py:575
#, python-format
msgid "Compose Email"
msgstr "رسالة جديدة"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Compute tax rates based on U.S. ZIP codes"
msgstr "حساب معدلات الضرائب علي أساس الرموز البريدية الامريكيه"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_counterpart
msgid ""
"Compute the counter part accounts of this journal item for this journal "
"entry. This can be needed in reports."
msgstr ""
"حساب القيد المناظر للحسابات لعنصر دفتر اليومية لهذا القيد اليومي. يمكن ان "
"يكون مطلوب في التقارير. "

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_balance_end
msgid "Computed Balance"
msgstr "الرصيد المتوقع"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_configuration
msgid "Configuration"
msgstr "الإعدادات"

#. module: account
#: code:addons/account/models/account_payment.py:643
#: code:addons/account/models/account_payment.py:645
#, python-format
msgid "Configuration Error !"
msgstr "خطأ في الإعدادات!"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:29
#, python-format
msgid "Configuration Steps:"
msgstr "خطوات الإعداد:"

#. module: account
#: code:addons/account/models/account_invoice.py:462
#, python-format
msgid ""
"Configuration error!\n"
"Could not find any account to create the invoice, are you sure you have a chart of account installed?"
msgstr ""
"خطأ في الإعدادات!\n"
"لا يمكن إيجاد أي حساب لإنشاء الفاتورة, هل أنت متأكد من وجود دليل محاسبي مثبت؟"

#. module: account
#: code:addons/account/models/account.py:443
#, python-format
msgid ""
"Configuration error!\n"
"The currency of the journal should be the same than the default credit account."
msgstr ""
"خطأ في الإعدادات\n"
"عملة دفتر اليومية يجب أن تكون مثل العملة الافتراضية للحساب."

#. module: account
#: code:addons/account/models/account.py:445
#, python-format
msgid ""
"Configuration error!\n"
"The currency of the journal should be the same than the default debit account."
msgstr ""
"خطأ في الإعدادات\n"
"عملة دفتر اليومية يجب أن تكون مثل العملة الافتراضية للحساب."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Configuration menu"
msgstr "قائمة الإعدادات"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Configure"
msgstr "تهيئة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
#: model:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Confirm"
msgstr "تأكيد"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_confirm
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid "Confirm Draft Invoices"
msgstr "تأكيد فواتير المسودة"

#. module: account
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid "Confirm Invoices"
msgstr "تأكيد الفواتير"

#. module: account
#: model:ir.actions.server,name:account.action_account_confirm_payments
msgid "Confirm Payments"
msgstr "تأكيد المدفوعات"

#. module: account
#: model:ir.model,name:account.model_account_invoice_confirm
msgid "Confirm the selected invoices"
msgstr "تأكيد الفوراتير المختارة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Confirmed"
msgstr "مؤكد"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
msgid ""
"Confirming this will create automatically a journal entry with the "
"difference in the profit/loss account set on the cash journal."
msgstr ""
"تأكيد هذا سوف ينشئ قيد يومية تلقائيا بالفرق في حساب الربح/الخسارة الموجود "
"بدفتر اليومية النقدي."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:52
#, python-format
msgid "Congrats, you're all done!"
msgstr "مبارك! لقد انتهيت من كل شيء!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Congratulations, you're done!"
msgstr "تهانينا, لقد انهيت!"

#. module: account
#: model:ir.model,name:account.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: account
#: model:ir.model,name:account.model_account_abstract_payment
msgid ""
"Contains the logic shared between models which allows to register payments"
msgstr "يحتوي علي المنطق المشترك بين الموديلات مما يسمح بتسجيل الدفع"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_contract_ids
#: model:ir.model.fields,field_description:account.field_res_partner_contracts_count
#: model:ir.model.fields,field_description:account.field_res_users_contract_ids
#: model:ir.model.fields,field_description:account.field_res_users_contracts_count
msgid "Contracts"
msgstr "العقود"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Control-Access"
msgstr "ضبط الوصول"

#. module: account
#: model:account.account.type,name:account.data_account_type_direct_costs
msgid "Cost of Revenue"
msgstr "تكاليف الدخل"

#. module: account
#: code:addons/account/models/chart_template.py:873
#, python-format
msgid ""
"Could not install new chart of account as there are already accounting "
"entries existing"
msgstr "لم يمكن تثبيت دليل محاسبي جديد إذ توجد قيود محاسبية بالفعل."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_counterpart
msgid "Counterpart"
msgstr "القيد المناظر"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_account_id
msgid "Counterpart Account"
msgstr "حساب القيد المناظر"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_country_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_country_id
msgid "Country"
msgstr "دولة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_country_group_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_country_group_id
msgid "Country Group"
msgstr "مجموعة الدولة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_country_id
msgid "Country of the Partner Company"
msgstr "دولة مؤسسة الشريك"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Create Customers"
msgstr "إنشاء العملاء"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Create Vendors"
msgstr "إنشاء الموردين"

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Create a draft credit note"
msgstr "إنشاء مسود إشعار دائن"

#. module: account
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Create and post move"
msgstr "إنشاء و ترحيل القيود"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:288
#, python-format
msgid "Create cash statement"
msgstr "إنشاء كشف حساب نقدي"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:274
#, python-format
msgid "Create invoice/bill"
msgstr "إنشاء فاتورة / سند"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:163
#, python-format
msgid "Create model"
msgstr "إنشاء قالب"

#. module: account
#: model:ir.actions.act_window,help:account.rounding_list_action
msgid "Create the first cash rounding"
msgstr "أنشئ أو تقريب للنقدية"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Create your products"
msgstr "انشاء منتجاتك"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_create_uid
#: model:ir.model.fields,field_description:account.field_account_account_tag_create_uid
#: model:ir.model.fields,field_description:account.field_account_account_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_account_type_create_uid
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_create_uid
#: model:ir.model.fields,field_description:account.field_account_balance_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_create_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_account_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_common_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_financial_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_create_uid
#: model:ir.model.fields,field_description:account.field_account_group_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_create_uid
#: model:ir.model.fields,field_description:account.field_account_journal_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_create_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal_create_uid
#: model:ir.model.fields,field_description:account.field_account_opening_create_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_create_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal_create_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_create_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_register_payments_create_uid
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_create_uid
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_report_create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template_create_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile_create_uid
#: model:ir.model.fields,field_description:account.field_accounting_report_create_uid
#: model:ir.model.fields,field_description:account.field_cash_box_in_create_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out_create_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_create_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move_create_uid
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_create_date
#: model:ir.model.fields,field_description:account.field_account_account_tag_create_date
#: model:ir.model.fields,field_description:account.field_account_account_template_create_date
#: model:ir.model.fields,field_description:account.field_account_account_type_create_date
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_create_date
#: model:ir.model.fields,field_description:account.field_account_balance_report_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_create_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_create_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_create_date
#: model:ir.model.fields,field_description:account.field_account_chart_template_create_date
#: model:ir.model.fields,field_description:account.field_account_common_account_report_create_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_create_date
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_create_date
#: model:ir.model.fields,field_description:account.field_account_common_report_create_date
#: model:ir.model.fields,field_description:account.field_account_financial_report_create_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_create_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_create_date
#: model:ir.model.fields,field_description:account.field_account_group_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_line_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_create_date
#: model:ir.model.fields,field_description:account.field_account_journal_create_date
#: model:ir.model.fields,field_description:account.field_account_move_create_date
#: model:ir.model.fields,field_description:account.field_account_move_line_create_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_create_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_create_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal_create_date
#: model:ir.model.fields,field_description:account.field_account_opening_create_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_method_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_create_date
#: model:ir.model.fields,field_description:account.field_account_print_journal_create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_create_date
#: model:ir.model.fields,field_description:account.field_account_register_payments_create_date
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_create_date
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_group_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_report_create_date
#: model:ir.model.fields,field_description:account.field_account_tax_template_create_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile_create_date
#: model:ir.model.fields,field_description:account.field_accounting_report_create_date
#: model:ir.model.fields,field_description:account.field_cash_box_in_create_date
#: model:ir.model.fields,field_description:account.field_cash_box_out_create_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_create_date
#: model:ir.model.fields,field_description:account.field_validate_account_move_create_date
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_credit
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Credit"
msgstr "الدائن"

#. module: account
#: model:account.account.type,name:account.data_account_type_credit_card
msgid "Credit Card"
msgstr "البطاقة الإئتمانية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_credit_cash_basis
msgid "Credit Cash Basis"
msgstr "دائن بصفة نقدية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_credit_move_id
msgid "Credit Move"
msgstr "الحركة الدائنة"

#. module: account
#: code:addons/account/models/account_invoice.py:441
#: code:addons/account/models/account_invoice.py:1216
#: code:addons/account/wizard/account_invoice_refund.py:111
#: model:ir.actions.act_window,name:account.action_account_invoice_refund
#: model:ir.model,name:account.model_account_invoice_refund
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#, python-format
msgid "Credit Note"
msgstr "اخطار بالرصيد"

#. module: account
#: code:addons/account/models/account_invoice.py:442
#, python-format
msgid "Credit Note - %s"
msgstr "الإشعار الدائن - %s"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Credit Note Bill"
msgstr "فاتورة الاشعار الدائن"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_date_invoice
msgid "Credit Note Date"
msgstr "تاريخ الإشعار الدائن"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_refund_sequence_id
msgid "Credit Note Entry Sequence"
msgstr "مسلسل قيود الإشعارات الدائنة"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Credit Notes"
msgstr "الإشعارات الدائنة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_refund_sequence_number_next
msgid "Credit Notes: Next Number"
msgstr "الإشعارات الدائنة: الرقم التالي"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_credit_account_id
msgid "Credit account"
msgstr "حساب دائن"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_credit
msgid "Credit amount"
msgstr "مبلغ الدائنون"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_matched_credit_ids
msgid "Credit journal items that are matched with this journal item."
msgstr "عناصر اليومية الدائنة المتطابقة مع عنصر اليومية هذا."

#. module: account
#: model:ir.ui.menu,name:account.menu_action_currency_form
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Currencies"
msgstr "العملات"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_currency_id
#: model:ir.model.fields,field_description:account.field_account_analytic_line_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_journal_currency_id
#: model:ir.model.fields,field_description:account.field_account_chart_template_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_currency_id
#: model:ir.model.fields,field_description:account.field_account_journal_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line_currency_id
#: model:ir.model.fields,field_description:account.field_account_opening_currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_company_currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_company_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_currency_id
#: model:ir.model.fields,field_description:account.field_account_payment_currency_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_currency_id
#: model:ir.model.fields,field_description:account.field_res_config_settings_currency_id
#: model:ir.model.fields,field_description:account.field_res_partner_currency_id
#: model:ir.model.fields,field_description:account.field_res_users_currency_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_currency_id
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Currency"
msgstr "العملة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_currency_rate
msgid "Currency Rate"
msgstr "سعر العملة"

#. module: account
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_currency_id
msgid "Currency as per company's country."
msgstr "العملة وفقا لبلد الشركة"

#. module: account
#: code:addons/account/models/account_move.py:1663
#: code:addons/account/models/account_move.py:1675
#, python-format
msgid "Currency exchange rate difference"
msgstr "الفرق في تغير سعر العملة"

#. module: account
#: code:addons/account/models/account.py:451
#, python-format
msgid ""
"Currency field should only be set if the journal's currency is different "
"from the company's. Leave the field blank to use company currency."
msgstr ""
"حقل العملة يجب وضعه فقط اذا كانت عملة دفتر اليومية مختلفة عن عملة الشركة. دع"
" الحقل فارغاً لاستخدام عملة الشركة."

#. module: account
#: model:account.account.type,name:account.data_account_type_current_assets
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Current Assets"
msgstr "الأصول المتداولة"

#. module: account
#: model:account.account.type,name:account.data_account_type_current_liabilities
msgid "Current Liabilities"
msgstr "الخصوم المتداولة"

#. module: account
#: model:account.account.type,name:account.data_unaffected_earnings
msgid "Current Year Earnings"
msgstr "أرباح السنة الحالية"

#. module: account
#: selection:account.abstract.payment,partner_type:0
#: selection:account.payment,partner_type:0
#: selection:account.register.payments,partner_type:0
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_payment_tree
msgid "Customer"
msgstr "العميل"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_payment.py:677
#, python-format
msgid "Customer Credit Note"
msgstr "إشعار دائن لعميل"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_out_refund
#: model:ir.ui.menu,name:account.menu_action_invoice_out_refund
msgid "Customer Credit Notes"
msgstr "إشعارات العملاء الدائنة"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Customer Flow"
msgstr "تدفق العملاء"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
msgid "Customer Invoice"
msgstr "فاتورة العميل"

#. module: account
#: code:addons/account/models/chart_template.py:193
#: model:ir.actions.act_window,name:account.action_invoice_tree1
#: model:ir.ui.menu,name:account.menu_action_invoice_tree1
#: model:ir.ui.view,arch_db:account.account_planner
#, python-format
msgid "Customer Invoices"
msgstr "فواتير العميل"

#. module: account
#: code:addons/account/models/account_payment.py:675
#, python-format
msgid "Customer Payment"
msgstr "دفع العميل"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_payment_term_id
#: model:ir.model.fields,field_description:account.field_res_users_property_payment_term_id
msgid "Customer Payment Terms"
msgstr "شروط السداد للزبائن"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Customer Payments"
msgstr "دفعات العملاء"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_product_taxes_id
#: model:ir.model.fields,field_description:account.field_product_template_taxes_id
msgid "Customer Taxes"
msgstr "ضرائب العميل"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Customer ref:"
msgstr "إشارة العميل:"

#. module: account
#: model:ir.ui.menu,name:account.menu_account_customer
msgid "Customers"
msgstr "العملاء"

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "DOWN"
msgstr "أسفل"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/account_dashboard_setup_bar.js:214
#: model:ir.ui.menu,name:account.menu_board_journal_1
#, python-format
msgid "Dashboard"
msgstr "لوحات المعلومات"

#. module: account
#. openerp-web
#: selection:account.print.journal,sort_selection:0
#: selection:account.report.general.ledger,sortby:0
#: selection:accounting.report,filter_cmp:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:233
#: code:addons/account/static/src/xml/account_reconciliation.xml:248
#: model:ir.model.fields,field_description:account.field_account_bank_statement_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_date
#: model:ir.model.fields,field_description:account.field_account_invoice_report_date
#: model:ir.model.fields,field_description:account.field_account_move_date
#: model:ir.model.fields,field_description:account.field_account_move_line_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_date_p
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_date
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_overdue_document
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Date"
msgstr "التاريخ"

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_opening_date
msgid ""
"Date at which the opening entry of this company's accounting has been "
"posted."
msgstr "التاريخ الذي تم فيه ترحيل الإدخال الافتتاحي لحساب هذه الشركة."

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op_opening_date
msgid ""
"Date from which the accounting is managed in Odoo. It is the date of the "
"opening entry."
msgstr ""
"التاريخ الذي تتم منه أداره الحسابات في odoo. وهو تاريخ الإدخال الافتتاحي."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:56
#, python-format
msgid "Date:"
msgstr "التاريخ:"

#. module: account
#: model:ir.ui.view,arch_db:account.accounting_report_view
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Dates"
msgstr "التواريخ"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Day(s) after the end of the invoice month (Net EOM)"
msgstr "يوم(أيام) بعد شهر الفاتورة (صافى نهاية الشهر)"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Day(s) after the invoice date"
msgstr "يوم(أيام) بعد تاريخ الفاتورة"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Deactivate setup bar on the dashboard"
msgstr "تعطيل شريط الإعداد في لوحة المعلومات"

#. module: account
#: code:addons/account/models/company.py:45
#, python-format
msgid ""
"Dear Sir/Madam,\n"
"\n"
"Our records indicate that some payments on your account are still due. Please find details below.\n"
"If the amount has already been paid, please disregard this notice. Otherwise, please forward us the total amount stated below.\n"
"If you have any queries regarding your account, Please contact us.\n"
"\n"
"Thank you in advance for your cooperation.\n"
"Best Regards,"
msgstr ""
"Dear Sir/Madam,\n"
"\n"
"Our records indicate that some payments on your account are still due. Please find details below.\n"
"If the amount has already been paid, please disregard this notice. Otherwise, please forward us the total amount stated below.\n"
"If you have any queries regarding your account, Please contact us.\n"
"\n"
"Thank you in advance for your cooperation.\n"
"Best Regards,"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_debit
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.report_partnerledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "Debit"
msgstr "المدين"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_debit_cash_basis
msgid "Debit Cash Basis"
msgstr "الخصم النقدي الأساسي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_inbound_payment_method_ids
msgid "Debit Methods"
msgstr "طرق الخصم"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_debit_move_id
msgid "Debit Move"
msgstr "الحركة المدينة"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_debit_account_id
msgid "Debit account"
msgstr "حساب مدين"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_debit
msgid "Debit amount"
msgstr "المبلغ المدين"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_matched_debit_ids
msgid "Debit journal items that are matched with this journal item."
msgstr "عناصر اليومية المدينة المتطابقة مع عنصر اليومية هذا."

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "December"
msgstr "ديسمبر"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_refund_sequence
msgid "Dedicated Credit Note Sequence"
msgstr "تسلسل الملاحظات الدائنة المخصصة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_default_credit_account_id
msgid "Default Credit Account"
msgstr "حساب الدائنون الإفتراضي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_default_debit_account_id
msgid "Default Debit Account"
msgstr "الحساب المدين الافتراضي"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_default_purchase_tax_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_purchase_tax_id
msgid "Default Purchase Tax"
msgstr "ضريبة المشتريات الإفتراضية"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_default_sale_tax_id
msgid "Default Sale Tax"
msgstr "ضريبة البيع الإفتراضية"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_sale_tax_id
msgid "Default Sales Tax"
msgstr "ضريبة البيع الإفتراضية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tax_ids
#: model:ir.model.fields,field_description:account.field_account_account_template_tax_ids
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_account_chart_template_form
#: model:ir.ui.view,arch_db:account.view_account_template_form
msgid "Default Taxes"
msgstr "ضرائب إفتراضية"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Default taxes applied to local transactions"
msgstr "الضرائب الافتراضية المطبقة علي المعاملات المحلية"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Deferred Revenues Management"
msgstr "إدارة الأرباح المُرحلة"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash."
msgstr "حدد أصغر فئة عملات تستخدم في السداد النقدي."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_cash_rounding_id
msgid ""
"Defines the smallest coinage of the currency that can be used to pay by "
"cash."
msgstr "حدد أصغر فئة للعملة يمكن استخدامها في السداد النقدي."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model:ir.ui.view,arch_db:account.view_tax_form
msgid "Definition"
msgstr "تعريف"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_trust
#: model:ir.model.fields,field_description:account.field_res_users_trust
msgid "Degree of trust you have in this debtor"
msgstr "درجة الثقة التي لديكم في هذا المدين."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_deprecated
msgid "Deprecated"
msgstr "مهمل"

#. module: account
#: model:account.account.type,name:account.data_account_type_depreciation
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Depreciation"
msgstr "إستهلاك"

#. module: account
#: model:ir.ui.view,arch_db:account.cash_box_out_form
msgid "Describe why you take money from the cash register:"
msgstr "اشرح لماذا قمت بأخد نقود من الماكينة:"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:251
#: model:ir.model.fields,field_description:account.field_account_account_type_note
#: model:ir.model.fields,field_description:account.field_account_invoice_line_name
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_account_type_form
#, python-format
msgid "Description"
msgstr "الوصف"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_note
msgid "Description on the Invoice"
msgstr "الوصف على الفاتورة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_destination_account_id
msgid "Destination Account"
msgstr "حساب الوجهة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_auto_apply
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_auto_apply
msgid "Detect Automatically"
msgstr "كشف تلقائي"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_template_type_tax_use
#: model:ir.model.fields,help:account.field_account_tax_type_tax_use
msgid ""
"Determines where the tax is selectable. Note : 'None' means a tax can't be "
"used by itself, however it can still be used in a group."
msgstr ""
"تحديد أين تكون الضريبة قابلة للاختيار. ملحوظة : \"لا شيء\" تعنى أن الضريبة "
"لا يمكن ان تستخدم بنفسها, و مع ذلك مازال يمكن استخدامها في مجموعة. "

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_difference
msgid "Difference"
msgstr "الفرق"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_writeoff_account_id
msgid "Difference Account"
msgstr "حساب الفرق"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_difference
msgid ""
"Difference between the computed ending balance and the specified ending "
"balance."
msgstr "الفرق بين الرصيد الختامي المتوقع و الرصيد الختامي المحدد"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Direct connection to your bank"
msgstr "ربط مباشر مع البنك الخاص بك"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Disc.(%)"
msgstr "نسبة الخصم (%)"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_wizard_form
msgid "Discard"
msgstr "تجاهل"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_discount
msgid "Discount (%)"
msgstr "الخصم (%)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_balance_report_display_account
#: model:ir.model.fields,field_description:account.field_account_common_account_report_display_account
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_display_account
msgid "Display Accounts"
msgstr "عرض الحسابات"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_debit_credit
msgid "Display Debit/Credit Columns"
msgstr "عرض خانة الدائن/المدين"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_display_name
#: model:ir.model.fields,field_description:account.field_account_account_display_name
#: model:ir.model.fields,field_description:account.field_account_account_tag_display_name
#: model:ir.model.fields,field_description:account.field_account_account_template_display_name
#: model:ir.model.fields,field_description:account.field_account_account_type_display_name
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_display_name
#: model:ir.model.fields,field_description:account.field_account_balance_report_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_display_name
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_display_name
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_display_name
#: model:ir.model.fields,field_description:account.field_account_chart_template_display_name
#: model:ir.model.fields,field_description:account.field_account_common_account_report_display_name
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_display_name
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_display_name
#: model:ir.model.fields,field_description:account.field_account_common_report_display_name
#: model:ir.model.fields,field_description:account.field_account_financial_report_display_name
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_display_name
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_display_name
#: model:ir.model.fields,field_description:account.field_account_group_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_line_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_report_display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_display_name
#: model:ir.model.fields,field_description:account.field_account_journal_display_name
#: model:ir.model.fields,field_description:account.field_account_move_display_name
#: model:ir.model.fields,field_description:account.field_account_move_line_display_name
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_display_name
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_display_name
#: model:ir.model.fields,field_description:account.field_account_move_reversal_display_name
#: model:ir.model.fields,field_description:account.field_account_opening_display_name
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_method_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_term_display_name
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_display_name
#: model:ir.model.fields,field_description:account.field_account_print_journal_display_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_display_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_display_name
#: model:ir.model.fields,field_description:account.field_account_register_payments_display_name
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_display_name
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_group_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_report_display_name
#: model:ir.model.fields,field_description:account.field_account_tax_template_display_name
#: model:ir.model.fields,field_description:account.field_account_unreconcile_display_name
#: model:ir.model.fields,field_description:account.field_accounting_report_display_name
#: model:ir.model.fields,field_description:account.field_cash_box_in_display_name
#: model:ir.model.fields,field_description:account.field_cash_box_out_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_financial_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_generalledger_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_journal_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_overdue_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_partnerledger_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_tax_display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_trialbalance_display_name
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_display_name
#: model:ir.model.fields,field_description:account.field_validate_account_move_display_name
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: account
#: selection:account.financial.report,display_detail:0
msgid "Display children flat"
msgstr "عرض الفروع"

#. module: account
#: selection:account.financial.report,display_detail:0
msgid "Display children with hierarchy"
msgstr "عرض الفروع و الشجرة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_display_detail
msgid "Display details"
msgstr "عرض التقاصيل"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_description
msgid "Display on Invoices"
msgstr "عرض على الفواتير"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_print_docsaway
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Docsaway"
msgstr "حساب خدمة Docsaway"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid ""
"Document: Customer account statement<br/>\n"
"                    Date:"
msgstr ""
"المستند: كشف حساب العميل<br/> \n"
"التاريخ:"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_payables_documents
#: model:ir.ui.menu,name:account.menu_finance_receivables_documents
msgid "Documents"
msgstr "الوثائق"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Don't hesitate to"
msgstr "لا تتردد في"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_page
msgid "Download"
msgstr "تحميل"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Download the"
msgstr "تحميل ال"

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: selection:account.payment,state:0
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Draft"
msgstr "مسودة"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Draft Bill"
msgstr "مسودة للفاتورة "

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Draft Credit Note"
msgstr "مسودة إشعار دائن"

#. module: account
#: code:addons/account/models/account_invoice.py:439
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Draft Invoice"
msgstr "مسودة للفاتورة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Draft Invoices"
msgstr "مسودة للفواتير "

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Draft bills"
msgstr "مسودة للفواتير"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_draft_tree
msgid "Draft statements"
msgstr "مسودة لكشوف الحسابات"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Due"
msgstr "مستحق"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_residual
msgid "Due Amount"
msgstr "المبلغ المستحق"

#. module: account
#. openerp-web
#: code:addons/account/controllers/portal.py:71
#: code:addons/account/static/src/xml/account_reconciliation.xml:234
#: model:ir.model.fields,field_description:account.field_account_invoice_date_due
#: model:ir.model.fields,field_description:account.field_account_invoice_report_date_due
#: model:ir.ui.view,arch_db:account.portal_my_invoices
#: model:ir.ui.view,arch_db:account.report_overdue_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Due Date"
msgstr "تاريخ الاستحقاق"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Due Date Computation"
msgstr "حساب تاريخ المستحق"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Due Month"
msgstr "شهر الاستحقاق"

#. module: account
#: model:ir.actions.report,name:account.action_report_print_overdue
msgid "Due Payments"
msgstr "المدفوعات المستحقة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_tree
msgid "Due Type"
msgstr "نوع الاستحقاق"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_date_maturity
msgid "Due date"
msgstr "تاريخ الاستحقاق"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Duplicate"
msgstr "استنساخ"

#. module: account
#: code:addons/account/models/account_invoice.py:1194
#, python-format
msgid ""
"Duplicated vendor reference detected. You probably encoded twice the same "
"vendor bill/credit note."
msgstr ""
"تم الكشف عن مرجع المورد المكرر. من المحتمل انك قمت بترميز مرتين بنفس فاتورة "
"البائع/اشعار الائتمان."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_reports
msgid "Dynamic Reports"
msgstr "تقارير ديناميكية"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_l10n_eu_service
msgid "EU Digital Goods VAT"
msgstr "ضريبة القيمة المضافة للسلع الرقمية"

#. module: account
#: code:addons/account/models/chart_template.py:171
#: code:addons/account/models/chart_template.py:186
#: code:addons/account/models/chart_template.py:196
#, python-format
msgid "EXCH"
msgstr "سعر الصرف"

#. module: account
#: code:addons/account/models/account_move.py:1052
#, python-format
msgid "Either pass both debit and credit or none."
msgstr "اما تمرير الدائن و المدين معا أو لا شيء "

#. module: account
#: model:ir.model,name:account.model_mail_compose_message
msgid "Email composition wizard"
msgstr "معالج تكوين رسالة بريد الالكتروني"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_enable_filter
msgid "Enable Comparison"
msgstr "إتاحة المقارنة"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "End"
msgstr "النهاية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_date_to
#: model:ir.model.fields,field_description:account.field_account_balance_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_account_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_date_to
#: model:ir.model.fields,field_description:account.field_account_common_report_date_to
#: model:ir.model.fields,field_description:account.field_account_print_journal_date_to
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_date_to
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_date_to
#: model:ir.model.fields,field_description:account.field_account_tax_report_date_to
#: model:ir.model.fields,field_description:account.field_accounting_report_date_to
#: model:ir.model.fields,field_description:account.field_accounting_report_date_to_cmp
msgid "End Date"
msgstr "تاريخ الإنتهاء"

#. module: account
#: model:account.payment.term,name:account.account_payment_term
msgid "End of Following Month"
msgstr "نهاية شهر المتابعة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_balance_end_real
msgid "Ending Balance"
msgstr "رصيد ختامي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_end_id
msgid "Ending Cashbox"
msgstr "انهاء صندوق الكاشير"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Enjoy your Odoo experience,"
msgstr "استمتع بتجربتك مع أودو"

#. module: account
#: model:ir.actions.act_window,name:account.action_move_line_form
msgid "Entries"
msgstr "القيود"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_print_journal_sort_selection
msgid "Entries Sorted by"
msgstr "قيود يوميه مصنفه حسب"

#. module: account
#: code:addons/account/models/account_move.py:1019
#, python-format
msgid "Entries are not of the same account!"
msgstr "القيود ليست لنفس الحساب!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Entries to Review"
msgstr "قيود للعرض"

#. module: account
#: code:addons/account/models/account_analytic_line.py:58
#, python-format
msgid "Entries: "
msgstr "القيود: "

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "Entry Label"
msgstr "تسمية القيد"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_sequence_id
msgid "Entry Sequence"
msgstr "مسلسل القيد"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_move_line_ids
msgid "Entry lines"
msgstr "بنود القيد"

#. module: account
#: model:account.account.type,name:account.data_account_type_equity
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Equity"
msgstr "الأسهم"

#. module: account
#: code:addons/account/models/res_config_settings.py:132
#, python-format
msgid "Error!"
msgstr "خطأ!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Excel template"
msgstr "قالب Excel"

#. module: account
#: code:addons/account/models/chart_template.py:196
#, python-format
msgid "Exchange Difference"
msgstr "الفرق في سعر الصرف"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_currency_exchange_journal_id
#: model:ir.model.fields,field_description:account.field_res_config_settings_currency_exchange_journal_id
msgid "Exchange Gain or Loss Journal"
msgstr "المكسب أو الخسارة في سعر صرف دفتر اليومية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_exchange_move_id
msgid "Exchange Move"
msgstr "تبادل النقل"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Exchange rates can be automatically updated once a day from <strong>Yahoo "
"Finance</strong> or the <strong>European Central Bank</strong>. You can "
"activate this feature in the bottom of the"
msgstr ""
"سعر الصرف يمكن أن يعدل مرة واحدة في اليوم من <strong> ياهو اقتصاد و "
"أعمال</strong> أو ال <strong> بنك المركزي الأوروبى</strong>. يمكنك تفعيل هذه"
" الخاصية من أسفل ال "

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_expects_chart_of_accounts
msgid "Expects a Chart of Accounts"
msgstr "يتوقع مخطط الحسابات"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_expense0
msgid "Expense"
msgstr "المصروفات"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_category_property_account_expense_categ_id
#: model:ir.model.fields,field_description:account.field_product_product_property_account_expense_id
#: model:ir.model.fields,field_description:account.field_product_template_property_account_expense_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Expense Account"
msgstr "حساب المصروف"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_expense_id
msgid "Expense Account on Product Template"
msgstr "حساب المصروفات من قالب المنتجات"

#. module: account
#: model:account.account.type,name:account.data_account_type_expenses
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Expenses"
msgstr "المصروفات"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_reference
msgid "External Reference"
msgstr "المرجع الخارجي"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Extra Features"
msgstr "ميزات اضافية"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Favorites"
msgstr "المفضلات"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "February"
msgstr "فبراير"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_state_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_state_ids
msgid "Federal States"
msgstr "الولايات الاتحادية"

#. module: account
#: selection:account.journal,bank_statements_source:0
msgid "File Import"
msgstr "إستيراد ملف"

#. module: account
#: model:ir.ui.view,arch_db:account.cash_box_in_form
msgid "Fill in this form if you put money in the cash register:"
msgstr "إملأ هذا النموذج إذا وضعت مال في صندوق النقدية"

#. module: account
#: model:ir.model.fields,field_description:account.field_accounting_report_filter_cmp
msgid "Filter by"
msgstr "فلتر حسب ـ"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:141
#, python-format
msgid "Filter..."
msgstr "فلتر....."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_general_account_id
#: model:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
msgid "Financial Account"
msgstr "الحساب المالي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_style_overwrite
msgid "Financial Report Style"
msgstr "هيئة التقرير المالي"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_financial_report_tree
#: model:ir.actions.act_window,name:account.action_account_report
#: model:ir.ui.menu,name:account.menu_account_reports
msgid "Financial Reports"
msgstr "التقارير المالية"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_fy_data_done
msgid "Financial Year Setup Marked As Done"
msgstr "تم الانتهاء من اعداد السنه المالية"

#. module: account
#: model:ir.actions.report,name:account.action_report_financial
msgid "Financial report"
msgstr "التقرير المالي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_account_setup_fy_data_done
msgid "Financial year setup marked as done"
msgstr "تم الانتهاء من اعداد السنه المالية"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "First, register any outstanding customer invoices and vendor bills:"
msgstr "أولا, سجل أي فواتير للعميل أو فواتير للمورد مازالت معلقة:"

#. module: account
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Fiscal Information"
msgstr "معلومات مالية"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Fiscal Localization"
msgstr "التوطين المالي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_position_id
msgid "Fiscal Mapping"
msgstr "مخطط السنة المالية"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Fiscal Periods"
msgstr "الفترات المالية"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_fiscal_position_template_form
#: model:ir.model,name:account.model_account_fiscal_position
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_position_id
#: model:ir.model.fields,field_description:account.field_account_invoice_fiscal_position_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_fiscal_position_id
#: model:ir.model.fields,field_description:account.field_res_partner_property_account_position_id
#: model:ir.model.fields,field_description:account.field_res_users_property_account_position_id
#: model:ir.ui.view,arch_db:account.view_account_position_form
#: model:ir.ui.view,arch_db:account.view_account_position_template_search
#: model:ir.ui.view,arch_db:account.view_account_position_template_tree
#: model:ir.ui.view,arch_db:account.view_account_position_tree
msgid "Fiscal Position"
msgstr "الوظيفة المالية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_name
#: model:ir.ui.view,arch_db:account.view_account_position_template_form
#: model:ir.ui.view,arch_db:account.view_account_position_template_search
msgid "Fiscal Position Template"
msgstr "قالب الوضع المالي"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_fiscal_position_form
#: model:ir.ui.menu,name:account.menu_action_account_fiscal_position_form
msgid "Fiscal Positions"
msgstr "الأوضاع المالية"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:251
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:17
#, python-format
msgid "Fiscal Year"
msgstr "سنة مالية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_fiscalyear_last_day
#: model:ir.model.fields,field_description:account.field_res_company_fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "آخر يوم في السنة المالية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_fiscalyear_last_month
#: model:ir.model.fields,field_description:account.field_res_company_fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "آخر شهر في السنة المالية"

#. module: account
#: selection:account.reconcile.model,amount_type:0
#: selection:account.reconcile.model,second_amount_type:0
#: selection:account.reconcile.model.template,amount_type:0
#: selection:account.reconcile.model.template,second_amount_type:0
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Fixed"
msgstr "ثابت"

#. module: account
#: selection:account.payment.term.line,value:0
msgid "Fixed Amount"
msgstr "مبلغ ثابت"

#. module: account
#: model:account.account.type,name:account.data_account_type_fixed_assets
msgid "Fixed Assets"
msgstr "الأصول الثابتة"

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_second_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template_second_amount
msgid ""
"Fixed amount will count as a debit if it is negative, as a credit if it is "
"positive."
msgstr "المبلغ الثابت سيعامل كدين إذا كان بالسالب, و كرصيد إذا كان بالموجب."

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_receivables_follow_up
msgid "Follow-up"
msgstr "متابعة"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_reports_followup
msgid "Follow-up Levels"
msgstr "مستويات المتابعة"

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_report_sign
msgid ""
"For accounts that are typically more debited than credited and that you "
"would like to print as negative amounts in your reports, you should reverse "
"the sign of the balance; e.g.: Expense account. The same applies for "
"accounts that are typically more credited than debited and that you would "
"like to print as positive amounts in your reports; e.g.: Income account."
msgstr ""
"للحسابات التي عادة ما يكون فيها الجانب المدين اكبر من الدائن والتي تريد "
"طباعتها كمبالغ سالبة في التقارير الخاصه بك , فيجب عليك ان تعكس علامه الرصيد "
"مثال لذلك حساب المصروفات وينطبق الشئ نفسه على  الحسابات التى عادة ما يكون "
"فيها جانب الدائن اكبر من جانب المدين والتي تريد طباعتها كمبالغ  موجبه في "
"التقارير الخاصه بك مثال ذلك حساب الدخل"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line_value_amount
msgid "For percent enter a ratio between 0-100."
msgstr "للنسبة المئوية ادخل نسبة بين 0-100."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"For the Odoo Team,<br/>\n"
"                            Fabien Pinckaers, Founder"
msgstr ""
"لفريق أودو, <br/>\n"
"                  Fabien Pinckaers, المؤسس"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_currency_id
msgid "Forces all moves for this account to have this account currency."
msgstr "اجبار جميع الحركات لهذا الحساب ان تكون بنفس عملة الحساب."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_currency_id
#: model:ir.model.fields,help:account.field_account_bank_accounts_wizard_currency_id
msgid "Forces all moves for this account to have this secondary currency."
msgstr "إجبار جميع الحركات لهذا الحساب أن تكون لها هذه العملة الثانية."

#. module: account
#: code:addons/account/report/account_aged_partner_balance.py:205
#: code:addons/account/report/account_balance.py:64
#: code:addons/account/report/account_general_ledger.py:114
#: code:addons/account/report/account_journal.py:100
#: code:addons/account/report/account_partner_ledger.py:74
#: code:addons/account/report/account_report_financial.py:149
#: code:addons/account/report/account_tax.py:13
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr "بعض بيانات النموذج فارغة، لا يمكن طباعة هذا التقارير."

#. module: account
#: code:addons/account/models/account_invoice.py:96
#, python-format
msgid "Free Reference"
msgstr "رقم إشارة حر"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "From Payable accounts"
msgstr "من الحسابات الدائنة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "From Receivable accounts"
msgstr "من الحسابات المدينة"

#. module: account
#: model:ir.actions.act_window,help:account.action_account_invoice_report_all_supp
msgid ""
"From this report, you can have an overview of the amount invoiced from your "
"vendors. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"من هذا التقرير, يمكن أن تكون فكرة عامة المبالغ المفوترة من الموردين الخاصين "
"بك. أداة البحث يمكن أن تستخدم أيضا لإضافة الطابع الشخصى على الفواتير الخاصة "
"بك و هكذا, و مطابقة هذا التحليل مع احتياجاتك."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_invoice_report_all
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customers. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"من هذا التقرير, يمكن أن تكون فكرة عامة المبالغ المفوترة من العملاء الخاصين "
"بك. أداة البحث يمكن أن تستخدم أيضا لإضافة الطابع الشخصي على الفواتير الخاصة "
"بك و هكذا, و مطابقة هذا التحليل مع احتياجاتك."

#. module: account
#: model:ir.model,name:account.model_account_full_reconcile
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_full_reconcile_id
msgid "Full Reconcile"
msgstr "ربط تام"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:113
#, python-format
msgid "Future"
msgstr "مستقبل"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Future Activities"
msgstr "الأنشطة المستقبلية"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "GROSS PROFIT"
msgstr "إجمالي الأرباح"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_income_currency_exchange_account_id
#: model:ir.model.fields,field_description:account.field_res_company_income_currency_exchange_account_id
msgid "Gain Exchange Rate Account"
msgstr "حساب ارباح سعر الصرف"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_general_ledger_menu
#: model:ir.actions.report,name:account.action_report_general_ledger
#: model:ir.ui.menu,name:account.menu_general_ledger
msgid "General Ledger"
msgstr "دفتر الحسابات العام "

#. module: account
#: model:ir.model,name:account.model_account_report_general_ledger
msgid "General Ledger Report"
msgstr "تقرير دفتر الحسابات العام"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_generate_entries
msgid "Generate Entries"
msgstr "إصدار القيود"

#. module: account
#: model:ir.ui.menu,name:account.account_reports_legal_statements_menu
msgid "Generic Statements"
msgstr "البيانات العامة"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Get started"
msgstr "ابدأ"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Get warnings when invoicing specific customers"
msgstr "التنبيه عند إصدار فواتير لعملاء معينين"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Get your bank statements automatically imported every 4 hours, or in one-"
"click, using Yodlee and Plaid services. Once installed, set “Bank Feeds” to "
"“Bank Synchronization” in bank account settings. Then, click “Configure” on "
"the online account to enter your bank credentials."
msgstr ""
"الحصول علي البيانات المصرفية الخاصة بك تلقائيا استيراد كل 4 ساعات ، أو بنقره"
" واحده ، وذلك باستخدام الخدمات ومنقوشة. بمجرد تثبيت ، تعيين \"البنك يغذي\" "
"إلى \"البنك التزامن\" في إعدادات الحساب المصرفي. ثم انقر فوق \"تكوين\" علي "
"الحساب عبر الإنترنت لإدخال بيانات اعتماد البنك الخاص بك."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_sequence
msgid "Gives the sequence of this line when displaying the invoice."
msgstr "يعطي التسلسل لهذا البند عند اظهار الفاتورة"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_sequence
msgid ""
"Gives the sequence order when displaying a list of bank statement lines."
msgstr "يعطيك ترتيب المسلسل حين يعرض قائمة لبنود كشف حساب البنك"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_tax_sequence
msgid "Gives the sequence order when displaying a list of invoice tax."
msgstr "تعطيك ترتيب المسلسل حين عرض القائمة للفاتورة الضريبية."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line_sequence
msgid ""
"Gives the sequence order when displaying a list of payment terms lines."
msgstr "اعطي امر بالتسلسل  عند عرض قائمة من بنود الدفع الآجل"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:57
#, python-format
msgid "Go to bank statement(s)"
msgstr "الذهاب لكشوفات الحساب البنكي"

#. module: account
#: code:addons/account/models/account_invoice.py:642
#, python-format
msgid "Go to the configuration panel"
msgstr "انتقل إلى لوحة الإعدادات"

#. module: account
#: selection:res.partner,trust:0
msgid "Good Debtor"
msgstr "مدين جيد"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:46
#, python-format
msgid "Good Job!"
msgstr "عمل رائع!"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_group_id
#: model:ir.model.fields,field_description:account.field_account_account_template_group_id
msgid "Group"
msgstr "المجموعة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_search
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#: model:ir.ui.view,arch_db:account.view_account_template_search
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_group_invoice_lines
msgid "Group Invoice Lines"
msgstr "بنود فاتورة المجموعة"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Group of Taxes"
msgstr "مجموعة من الضرائب"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Group received checks before depositing them to the bank"
msgstr "تجميع الشيكات المحصلة قبل إيداعهم في البنك"

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "HALF-UP"
msgstr "نصف المتابعة"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_has_accounting_entries
msgid "Has Accounting Entries"
msgstr "توجد قيود محاسبية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_has_invoices
msgid "Has Invoices"
msgstr "توجد فواتير"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_has_outstanding
msgid "Has Outstanding"
msgstr "له أرصدة مستحقة"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_has_unreconciled_entries
#: model:ir.model.fields,field_description:account.field_res_users_has_unreconciled_entries
msgid "Has Unreconciled Entries"
msgstr "له قيود غير مسواة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_hide_payment_method
#: model:ir.model.fields,field_description:account.field_account_payment_hide_payment_method
#: model:ir.model.fields,field_description:account.field_account_register_payments_hide_payment_method
msgid "Hide Payment Method"
msgstr "إخفاء طريقة السداد"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_account_hide_setup_bar
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Hide Setup Bar"
msgstr "إخفاء شريط الإعداد"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_hide_tax_exigibility
msgid "Hide Use Cash Basis Option"
msgstr "إخفاء خيار استخدام الأساس النقدي"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "How do account types affect your reports?"
msgstr "كيف تؤثر أنواع الحسابات على تقاريرك؟"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "How total tax amount is computed in orders and invoices"
msgstr "كيف يحتسب مبلغ الضريبة في أوامر البيع والشراء وفي الفواتير"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_id
#: model:ir.model.fields,field_description:account.field_account_account_id
#: model:ir.model.fields,field_description:account.field_account_account_tag_id
#: model:ir.model.fields,field_description:account.field_account_account_template_id
#: model:ir.model.fields,field_description:account.field_account_account_type_id
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_id
#: model:ir.model.fields,field_description:account.field_account_balance_report_id
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_id
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_id
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_id
#: model:ir.model.fields,field_description:account.field_account_chart_template_id
#: model:ir.model.fields,field_description:account.field_account_common_account_report_id
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_id
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_id
#: model:ir.model.fields,field_description:account.field_account_common_report_id
#: model:ir.model.fields,field_description:account.field_account_financial_report_id
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_id
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_id
#: model:ir.model.fields,field_description:account.field_account_group_id
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_id
#: model:ir.model.fields,field_description:account.field_account_invoice_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_id
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_id
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_id
#: model:ir.model.fields,field_description:account.field_account_journal_id
#: model:ir.model.fields,field_description:account.field_account_move_id
#: model:ir.model.fields,field_description:account.field_account_move_line_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_id
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_id
#: model:ir.model.fields,field_description:account.field_account_move_reversal_id
#: model:ir.model.fields,field_description:account.field_account_opening_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_id
#: model:ir.model.fields,field_description:account.field_account_payment_id
#: model:ir.model.fields,field_description:account.field_account_payment_method_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_id
#: model:ir.model.fields,field_description:account.field_account_print_journal_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_id
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_id
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_id
#: model:ir.model.fields,field_description:account.field_account_tax_group_id
#: model:ir.model.fields,field_description:account.field_account_tax_id
#: model:ir.model.fields,field_description:account.field_account_tax_report_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_id
#: model:ir.model.fields,field_description:account.field_account_unreconcile_id
#: model:ir.model.fields,field_description:account.field_accounting_report_id
#: model:ir.model.fields,field_description:account.field_cash_box_in_id
#: model:ir.model.fields,field_description:account.field_cash_box_out_id
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance_id
#: model:ir.model.fields,field_description:account.field_report_account_report_financial_id
#: model:ir.model.fields,field_description:account.field_report_account_report_generalledger_id
#: model:ir.model.fields,field_description:account.field_report_account_report_journal_id
#: model:ir.model.fields,field_description:account.field_report_account_report_overdue_id
#: model:ir.model.fields,field_description:account.field_report_account_report_partnerledger_id
#: model:ir.model.fields,field_description:account.field_report_account_report_tax_id
#: model:ir.model.fields,field_description:account.field_report_account_report_trialbalance_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_id
#: model:ir.model.fields,field_description:account.field_validate_account_move_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_id
msgid "ID"
msgstr "المعرف"

#. module: account
#: code:addons/account/models/chart_template.py:193
#, python-format
msgid "INV"
msgstr "INV"

#. module: account
#: code:addons/account/models/account_bank_statement.py:389
#, python-format
msgid "If \"Amount Currency\" is specified, then \"Amount\" must be as well."
msgstr "إذا تم تحديد \"عملة المبلغ\"، ثم \"المبلغ\" يجب أن يكون كذلك."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_nocreate
msgid ""
"If checked, the new chart of accounts will not contain this by default."
msgstr "إذا حددته,  الدليل المحاسبي لن يحتوى على هذا افتراضيا."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_reversal_journal_id
msgid "If empty, uses the journal of the journal entry to be reversed."
msgstr "إذا كان فارغا, سيتم عكس استخدام دفتر يومية قيد اليومية"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_include_base_amount
#: model:ir.model.fields,help:account.field_account_tax_template_include_base_amount
msgid ""
"If set, taxes which are computed after this one will be computed based on "
"the price tax included."
msgstr ""
"لو تم اختياره, الضرائب المحتسبة بعد هذا سيتم حسابها متضمنة ضريبة السعر."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_analytic
#: model:ir.model.fields,help:account.field_account_tax_template_analytic
msgid ""
"If set, the amount computed by this tax will be assigned to the same "
"analytic account as the invoice line (if any)"
msgstr ""
"لو تم اختياره, المبلغ المحسوب بهذه الضرائب سيتم توكيلها لنفس الحساب التحليلي"
" مثل قيد الفاتورة(إذا كان هناك أي)"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_active
msgid ""
"If the active field is set to False, it will allow you to hide the payment "
"terms without removing it."
msgstr ""
"إذا تم تعيين الحقل النشط إلى خطأ، سوف تسمح لك باخفاء فريق المبيعات دون "
"إزالته."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_group_invoice_lines
msgid ""
"If this box is checked, the system will try to group the accounting lines "
"when generating them from invoices."
msgstr ""
"اذا تم تحديد هذه الخانة, سيحاول النظام بتجميع بنود المحاسبة عند انشائهم من "
"الفواتير."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you check this box, you will be able to collect payments using SEPA "
"Direct Debit mandates."
msgstr ""
"إذا قمت بالتحقق من هذا المربع ، سوف تكون قادرا علي جمع المدفوعات باستخدام "
"sepa الخصم المباشر الولايات."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you check this box, you will be able to register your payment using SEPA."
msgstr "إذا قمت بالتحقق من هذا المربع ، ستتمكن من تسجيل الدفع باستخدام sepa."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"If you have less than 200 contacts, we recommend you\n"
"                                        create them manually."
msgstr ""
"إذا كان لديك عدد جهات اتصال أقل من 200, ننصحك\n"
"            بإنشائهم يدويا"

#. module: account
#: model:ir.model.fields,help:account.field_account_report_general_ledger_initial_balance
msgid ""
"If you selected date, this field allow you to add a row to display the "
"amount of debit/credit/balance that precedes the filter you've set."
msgstr ""
"إذا قمت باختيار تاريخ, سيتيح لك هذا الحقل إضافة صف لعرض مبالغ "
"الدائن/المدين/الرصيد الذى يسبق الفلتر الذى اخترته."

#. module: account
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
msgid ""
"If you unreconcile transactions, you must also verify all the actions that "
"are linked to those transactions because they will not be disabled"
msgstr ""
"إذا لم تقم بتسوية المعاملات , يجب عليك أيضا التحقق من كافة الإجراءات "
"المرتبطة بتلك المعاملات لأنهم لن يتم تعطيلهم ."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_payment_term_id
msgid ""
"If you use payment terms, the due date will be computed automatically at the"
" generation of accounting entries. If you keep the payment terms and the due"
" date empty, it means direct payment. The payment terms may compute several "
"due dates, for example 50% now, 50% in one month."
msgstr ""
"في حاله استخدام شروط الدفع ، سيتم حساب تاريخ الاستحقاق تلقائيا عند إنشاء "
"إدخالات الحساب. إذا احتفظت بشروط الدفع وتاريخ الاستحقاق فارغه ، فهذا يعني "
"الدفع المباشر. قد تحسب شروط الدفع عده تواريخ استحقاق ، علي سبيل المثال 50% "
"الآن ، 50% في شهر واحد."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_date_due
msgid ""
"If you use payment terms, the due date will be computed automatically at the"
" generation of accounting entries. The Payment terms may compute several due"
" dates, for example 50% now and 50% in one month, but if you want to force a"
" due date, make sure that the payment term is not set on the invoice. If you"
" keep the Payment terms and the due date empty, it means direct payment."
msgstr ""
"إذا كنت تستخدم طرق الدفع ، فسيتم احتساب تاريخ الاستحقاق تلقائياً في احتساب "
"القيود المحاسبية. طريقة الدفع الآجل من المحتمل أن تحتوى على عدة تواريخ "
"إستحقاق , على سبيل المثال ٥٠% الآن و ٥٠% فى خلال شهر , و لكن إذا كنت ترغب فى"
" فرض تاريخ الإستحقاق , تأكد من أن طريقة  الدفع لا يظهر على الفاتورة .إذا قمت"
" بالإستمرار على طريقة الدفع و تاريخ الاستحقاق الفارغ , فهذا يعنى أن الدفع "
"مباشر ."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"If you want to be able to send customer statements from Odoo, you must:"
msgstr ""
"إذا كنت تريد أن تكون قادر على إرسال كشوفات حساب العميل من خلال أودو يجب "
"عليك:"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"If you want to be able to send your customers their statements \n"
"                        from Odoo, you first need to record all outstanding transactions \n"
"                        in the payable and receivable accounts. These would be invoices \n"
"                        that have not been paid or payments that have not been reconciled."
msgstr ""
"إذا كنت تريد أن تكون قادر على إرسال كشوفات حساب العميل\n"
"                  من خلال أودو, ستحتاج أولا لتسجيل جميع المعاملات المعلقة\n"
"                  في  الحسابات الدائنة و الحسابات المدينة. هذه ستكون الفواتير\n"
"                  التي لم تدفع بعد أو المدفوعات التي لم يتم تسويتها بعد."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "If you want to do it yourself:"
msgstr "إذا كنت ترغب في فعل ذلك بنفسك:"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you're selling digital goods to customers in the EU, you must charge VAT "
"based on your customers' locations. This rule applies regardless of you are "
"located. Digital goods are defined in the legislation as broadcasting, "
"telecommunications, and services that are electronically supplied instead of"
" shipped. Gift cards sent online are not included in the definition."
msgstr ""
"إذا كنت تبيع السلع الرقمية للعملاء في الاتحاد الأوروبي ، فيجب عليك تحميل "
"ضريبة القيمة المضافة استنادا إلى مواقع عملائك. يتم تطبيق هذه القاعدة بغض "
"التحديد عن موقعك. ويتم تعريف السلع الرقمية في التشريعات بأنها البث "
"والاتصالات والخدمات التي يتم توريدها الكترونيا بدلا من شحنها. لا يتم تضمين "
"بطاقات الهدايا المرسلة عبر إنترنت في التعريف."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_product_image
msgid ""
"Image of the product variant (Big-sized image of product template if false)."
" It is automatically resized as a 1024x1024px image, with aspect ratio "
"preserved."
msgstr ""
"صورة لمتغير المنتج (صورة كبيرة الحجم من قالب المنتج إذا خاطئة). يتم تغيير "
"الحجم تلقائيا كصورة 1024x1024px، مع المحافظة على نسبة الارتفاع."

#. module: account
#: model:account.payment.term,name:account.account_payment_term_immediate
msgid "Immediate Payment"
msgstr "الدفع الفورى"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_qif
msgid "Import .qif files"
msgstr "استيراد ملفات .qif "

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_csv
msgid "Import in .csv format"
msgstr "استيراد بفورمات csv."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_ofx
msgid "Import in .ofx format"
msgstr "استيراد بفورمات .ofx"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_bank_statement_import_camt
msgid "Import in CAMT.053 format"
msgstr "استيراد في شكل camt. 053"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Import using the \"Import\" button on the top left corner of"
msgstr "استيراد باستخدام زر \"استرداد\" من اعلى الركن الأيسر ل"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements automatically"
msgstr "استيراد كشوفات الحسابات البنكية آلياً"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in CAMT.053"
msgstr "استيراد كشوفات الحسابات البنكية بصيغة CAMT.053"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in CSV"
msgstr "استيراد كشوفات الحسابات البنكية بصيغة CSV"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in OFX"
msgstr "استيراد كشوفات الحسابات البنكية بصيغة OFX"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in QIF"
msgstr "استيراد كشوفات الحسابات البنكية بصيغة QIF"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Importing your statements in via a supported file format (QIF, OFX, CODA or "
"CSV format)"
msgstr ""
"استيراد كشوفاتك المحاسبية عن طريق فورمات ملفات مدعومة (QIF, OFX, CODA or CSV"
" )"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "In Odoo,"
msgstr "في أودو"

#. module: account
#: code:addons/account/models/account_bank_statement.py:409
#, python-format
msgid ""
"In order to delete a bank statement line, you must first cancel it to delete"
" related journal items."
msgstr ""
"لكي يتم حذف بند كشف الحساب البنكي، يجب عليك أولا إلغائه لحذف عناصر اليومية "
"ذات الصلة."

#. module: account
#: code:addons/account/models/account_bank_statement.py:199
#, python-format
msgid ""
"In order to delete a bank statement, you must first cancel it to delete "
"related journal items."
msgstr ""
"لكي يتم مسح كشف حساب بنكي, عليك أولا إلغائه لحذف عناصر اليومية ذات الصلة "

#. module: account
#: code:addons/account/models/account_payment.py:144
#, python-format
msgid ""
"In order to pay multiple invoices at once, they must use the same currency."
msgstr "لكى تقوم بدفع فواتير متعددة في مرة واحدة, يجب ان يكونوا بنفس العملة."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "In your old accounting software, print a trial balance"
msgstr "في برنامج المحاسبة القديم الخاص بك, اطبع ميزان المراجعة."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Inactive"
msgstr "غير نشط"

#. module: account
#: selection:account.payment.method,payment_type:0
msgid "Inbound"
msgstr "مكالمة واردة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_initial_balance
msgid "Include Initial Balances"
msgstr "نشمل الارصدة الابتدائية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_analytic
msgid "Include in Analytic Cost"
msgstr "يشمل في التكلفة التحليلية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_price_include
#: model:ir.model.fields,field_description:account.field_account_tax_template_price_include
msgid "Included in Price"
msgstr "مشمول في السعر"

#. module: account
#: model:account.account.type,name:account.data_account_type_revenue
#: model:account.financial.report,name:account.account_financial_report_income0
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Income"
msgstr "الدخل"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_category_property_account_income_categ_id
#: model:ir.model.fields,field_description:account.field_product_product_property_account_income_id
#: model:ir.model.fields,field_description:account.field_product_template_property_account_income_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Income Account"
msgstr "حساب الدخل"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_income_id
msgid "Income Account on Product Template"
msgstr "قالب حساب الدخل على المنتج"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:700
#, python-format
msgid "Incorrect Operation"
msgstr "عملية خاطئة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Information"
msgstr "المعلومات"

#. module: account
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
msgid "Information addendum"
msgstr "إضافة معلومات"

#. module: account
#. openerp-web
#: code:addons/account/models/company.py:296
#: code:addons/account/models/company.py:311
#: code:addons/account/static/src/xml/account_dashboard_setup_bar.xml:9
#, python-format
msgid "Initial Balances"
msgstr "الأرصدة الافتتاحية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_stock_account_input_categ_id
#: model:ir.model.fields,field_description:account.field_res_company_property_stock_account_input_categ_id
msgid "Input Account for Stock Valuation"
msgstr "حساب الإدخال لتقييم المخزون"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Install Chart of Account"
msgstr "تثبيت الدليل المحاسبي"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Install More Packages"
msgstr "تثبيت حزم أكثر"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_transfer_account_id
msgid "Inter-Banks Transfer Account"
msgstr "حساب النقل بين البنوك"

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_transfer_account_id
#: model:ir.model.fields,help:account.field_res_company_transfer_account_id
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_transfer_account_id
msgid ""
"Intermediary account used when moving money from a liquidity account to "
"another"
msgstr "يستخدم حساب وسيط عند نقل الأموال من حساب جارى إلى حساب أخر."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_narration
msgid "Internal Note"
msgstr "ملاحظة داخلية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_note
msgid "Internal Notes"
msgstr "ملاحظات داخلية"

#. module: account
#: selection:account.payment,payment_type:0
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Internal Transfer"
msgstr "نقل داخلي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_internal_type
msgid "Internal Type"
msgstr "النوع الداخلي"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_template_form
msgid "Internal notes..."
msgstr "الملاحظات الداخلية"

#. module: account
#: code:addons/account/models/partner.py:45
#, python-format
msgid "Invalid \"Zip Range\", please configure it properly."
msgstr "\"رمز بريدى\" غير صحيح, الرجاء إعادة تكوينه بشكل صحيح"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Invite Users"
msgstr "دعوة مستخدمين"

#. module: account
#: code:addons/account/models/account_invoice.py:1214
#: model:ir.model,name:account.model_account_invoice
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_invoice_id
#: model:ir.model.fields,field_description:account.field_account_move_line_invoice_id
#: model:ir.model.fields,field_description:account.field_res_partner_invoice_warn
#: model:ir.model.fields,field_description:account.field_res_users_invoice_warn
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:res.request.link,name:account.req_link_invoice
#, python-format
msgid "Invoice"
msgstr "الفاتورة"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid "Invoice #"
msgstr "فاتورة #"

#. module: account
#: code:addons/account/models/account_invoice.py:440
#, python-format
msgid "Invoice - %s"
msgstr "الفاتورة - %s"

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_created
#: model:mail.message.subtype,name:account.mt_invoice_created
msgid "Invoice Created"
msgstr "الفاتورة انشأت"

#. module: account
#: code:addons/account/controllers/portal.py:70
#: model:ir.model.fields,field_description:account.field_account_invoice_date_invoice
#: model:ir.ui.view,arch_db:account.portal_my_invoices
#: model:ir.ui.view,arch_db:account.report_payment_receipt
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Invoice Date"
msgstr "تاريخ الفاتورة"

#. module: account
#: model:ir.model,name:account.model_account_invoice_line
#: model:ir.ui.view,arch_db:account.view_invoice_line_form
#: model:ir.ui.view,arch_db:account.view_invoice_line_tree
msgid "Invoice Line"
msgstr "بند الفاتورة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_invoice_line_ids
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Invoice Lines"
msgstr "بنود الفاتورة"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Invoice Number"
msgstr "رقم الفاتورة"

#. module: account
#: sql_constraint:account.invoice:0
msgid "Invoice Number must be unique per Company!"
msgstr "رقم الفاتورة يجب أن يكون فريداً لكل مؤسسة!"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Invoice Number:"
msgstr "رقم الفاتورة:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_invoice_id
msgid "Invoice Reference"
msgstr "رقم اشارة الفاتورة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_state
msgid "Invoice Status"
msgstr "حالة الفاتورة"

#. module: account
#: model:ir.model,name:account.model_account_invoice_tax
msgid "Invoice Tax"
msgstr "ضريبة الفاتورة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_invoice_id
msgid "Invoice for which this invoice is the credit note"
msgstr "الفاتورة التي تعتبر هذه الفاتورة هي اشعار الدائن"

#. module: account
#: code:addons/account/models/account_invoice.py:753
#, python-format
msgid "Invoice must be cancelled in order to reset it to draft."
msgstr "يجب ان تكون الفاتوره ملغية من أجل تعيينها كمسودة."

#. module: account
#: code:addons/account/models/account_invoice.py:801
#, python-format
msgid "Invoice must be in draft or open state in order to be cancelled."
msgstr "يجب أن تكون الفاتورة في حالة مسودة أو مفتوحة لتتمكن من إلغاءها."

#. module: account
#: code:addons/account/models/account_invoice.py:775
#, python-format
msgid "Invoice must be in draft state in order to validate it."
msgstr "يجب أن تكون الفاتورة في حالة مسودة لتتمكن من اعتمادها."

#. module: account
#: code:addons/account/models/account_invoice.py:795
#, python-format
msgid "Invoice must be paid in order to set it to register payment."
msgstr "يجب أن تكون الفاتورة مدفوعة من أجل تعيينها فى سجل المدفوعات."

#. module: account
#: code:addons/account/models/account_invoice.py:787
#, python-format
msgid "Invoice must be validated in order to set it to register payment."
msgstr "يجب ان تكون الفاتورة صالحة من أجل تعيينها فى سجل المدفوعات."

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_paid
msgid "Invoice paid"
msgstr "تم دفع الفاتورة"

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_validated
msgid "Invoice validated"
msgstr "تم تأكيد الفاتورة"

#. module: account
#: model:mail.template,report_name:account.email_template_edi_invoice
msgid ""
"Invoice_${(object.number or '').replace('/','_')}_${object.state == 'draft' "
"and 'draft' or ''}"
msgstr ""
"الفاتورة_${(object.number or '').replace('/','_')}_${object.state == 'draft'"
" and 'draft' or ''}"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Invoiced"
msgstr "فاتوره محرره "

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_refund_out_tree
#: model:ir.actions.act_window,name:account.action_invoice_tree
#: model:ir.actions.report,name:account.account_invoices
#: model:ir.model.fields,field_description:account.field_account_payment_invoice_ids
#: model:ir.model.fields,field_description:account.field_account_register_payments_invoice_ids
#: model:ir.model.fields,field_description:account.field_res_partner_invoice_ids
#: model:ir.model.fields,field_description:account.field_res_users_invoice_ids
#: model:ir.ui.menu,name:account.menu_action_account_invoice_report_all
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.portal_my_home_invoice
#: model:ir.ui.view,arch_db:account.portal_my_home_menu_invoice
#: model:ir.ui.view,arch_db:account.portal_my_invoices
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_account_payment_form
#: model:ir.ui.view,arch_db:account.view_account_payment_graph
#: model:ir.ui.view,arch_db:account.view_invoice_graph
#: model:ir.ui.view,arch_db:account.view_invoice_line_calendar
#: model:ir.ui.view,arch_db:account.view_invoice_pivot
msgid "Invoices"
msgstr "الفواتير"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_report_all
#: model:ir.actions.act_window,name:account.action_account_invoice_report_all_supp
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_graph
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_pivot
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Invoices Analysis"
msgstr "تحليل الفواتير"

#. module: account
#: model:ir.model,name:account.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "إحصائيات الفواتير"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:186
#, python-format
msgid "Invoices owed to you"
msgstr "الفواتير المستحقة لك"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Invoices to validate"
msgstr "فواتير للاعتماد "

#. module: account
#: model:ir.actions.report,name:account.account_invoices_without_payment
msgid "Invoices without Payment"
msgstr "الفواتير بدون سداد"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance
#: model:ir.ui.view,arch_db:account.product_template_form_view
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Invoicing"
msgstr "تحرير الفواتير"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_is_unaffected_earnings_line
msgid "Is Unaffected Earnings Line"
msgstr "هو خط الأرباح غير متاثر"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_is_rounding_line
msgid "Is a rounding line in case of cash rounding."
msgstr "هو خط التقريب في حاله التقريب النقدي."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_is_difference_zero
msgid "Is zero"
msgstr "هو صفر"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_default_credit_account_id
#: model:ir.model.fields,help:account.field_res_company_income_currency_exchange_account_id
msgid "It acts as a default account for credit amount"
msgstr "وهي تعمل كحساب افتراضي لمبلغ الدائن"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_default_debit_account_id
#: model:ir.model.fields,help:account.field_res_company_expense_currency_exchange_account_id
msgid "It acts as a default account for debit amount"
msgstr "وهي تمثل الحساب الافتراضي لمبلغ المدين"

#. module: account
#: model:ir.model.fields,help:account.field_account_report_partner_ledger_amount_currency
msgid ""
"It adds the currency column on report if the currency differs from the "
"company currency."
msgstr ""
"تقوم باضافة خانة العملة في التقرير اذا كانت العملة مختلفة عن العملة "
"الافتراضية للشركة."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_reconciled
msgid ""
"It indicates that the invoice has been paid and the journal entry of the "
"invoice has been reconciled with one or several journal entries of payment."
msgstr ""
"يوضح هذا ان الفاتوره تم دفعها والقيود اليوميه للفاتورة تم تسويتها مع واحده "
"أو عدة قيود يوميه من الدفع"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_sent
msgid "It indicates that the invoice has been sent."
msgstr "هذا يشير أن الفاتورة قد تم ارسالها."

#. module: account
#: code:addons/account/models/account_move.py:1050
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr "انه الزامى ان تحدد حساب و دفتر يومية لتفعيل الشطب"

#. module: account
#: code:addons/account/models/account_payment.py:470
#, python-format
msgid ""
"It is not allowed to delete a payment that already created a journal entry "
"since it would create a gap in the numbering. You should create the journal "
"entry again and cancel it thanks to a regular revert."
msgstr ""
"غير مسموح حذف امر دفع انشأ بالفعل قيد يومية لانة سيؤدى لعمل فجوة رقمية. يجب "
"انشاء قيد يومية مرة اخرى والغاءة بفضل العودة النظامية"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"It's common practice to change your accounting software \n"
"                            at the end of a fiscal year. This allows you to have less \n"
"                            data to import and balances to set. If you plan to do so, \n"
"                            we recommend you start using Odoo for invoicing and payments \n"
"                            now, and then move all other accounting transactions at a later time."
msgstr ""
"تغيير البرنامج المحاسبي الخاص بك أمرا شائعا\n"
"           في نهاية السنة المالية. يسمح لك هذا امتلاك\n"
"           بيانات أقل لاستيرادها و أرصدة أقل لإدخالها. إذا كنت تخطط لذلك\n"
"           فنحن نوصيك بإستخدام أودو لإصدار الفواتير و المدفوعات\n"
"           الأن, و بعد ذلك نقل جميع المعاملات المحاسبية في وقت لاحق."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"It's recommended that you do not delete any accounts, even if they are not "
"relevant. Simply make them inactive."
msgstr ""
"من المستحسن أن لا تحذف أي حسابات, حتى إذا كانوا غير ذي صلة. ببساطة اجعلهم "
"غير نشيطين. "

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Italic Text (smaller)"
msgstr "نص مائل (أصغر)"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Items"
msgstr "العناصر"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
msgid "JRNL"
msgstr "اليومية"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "January"
msgstr "يناير"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:185
#: code:addons/account/static/src/xml/account_reconciliation.xml:229
#: model:ir.model,name:account.model_account_journal
#: model:ir.model.fields,field_description:account.field_account_bank_statement_journal_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_journal_id
#: model:ir.model.fields,field_description:account.field_account_invoice_journal_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_journal_id
#: model:ir.model.fields,field_description:account.field_account_move_journal_id
#: model:ir.model.fields,field_description:account.field_account_move_line_journal_id
#: model:ir.model.fields,field_description:account.field_account_opening_journal_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_journal_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_journal_id
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_journal_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Journal"
msgstr "دفتر اليومية"

#. module: account
#: selection:account.report.general.ledger,sortby:0
msgid "Journal & Partner"
msgstr "اليومية و الشريك"

#. module: account
#: code:addons/account/models/account_bank_statement.py:254
#: model:ir.actions.act_window,name:account.action_move_journal_line
#: model:ir.actions.act_window,name:account.action_move_select
#: model:ir.ui.menu,name:account.menu_action_move_journal_line_form
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#: model:ir.ui.view,arch_db:account.view_move_tree
#, python-format
msgid "Journal Entries"
msgstr "قيود اليومية"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Journal Entries by Month"
msgstr "قيود اليومية بالشهر"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_move_id
#: model:ir.model.fields,field_description:account.field_account_move_line_move_id
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Journal Entry"
msgstr "قيد يومية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_move_name
#: model:ir.model.fields,field_description:account.field_account_invoice_move_name
#: model:ir.model.fields,field_description:account.field_account_payment_move_name
msgid "Journal Entry Name"
msgstr "إسم قيد اليومية"

#. module: account
#: selection:account.print.journal,sort_selection:0
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Journal Entry Number"
msgstr "رقم قيد اليومية"

#. module: account
#: model:ir.model,name:account.model_account_move_line
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Journal Item"
msgstr "عنصر يومية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_writeoff_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_label
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Journal Item Label"
msgstr "تسمية عنصر اليومية"

#. module: account
#: code:addons/account/models/account_payment.py:414
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_move_line
#: model:ir.actions.act_window,name:account.act_account_move_to_account_move_line_open
#: model:ir.actions.act_window,name:account.action_account_moves_all_a
#: model:ir.actions.act_window,name:account.action_account_moves_all_tree
#: model:ir.actions.act_window,name:account.action_move_line_graph
#: model:ir.actions.act_window,name:account.action_move_line_graph_cash_basis
#: model:ir.actions.act_window,name:account.action_move_line_graph_cash_basis_posted
#: model:ir.actions.act_window,name:account.action_move_line_graph_posted
#: model:ir.actions.act_window,name:account.action_move_line_select
#: model:ir.actions.act_window,name:account.action_move_line_select_by_partner
#: model:ir.actions.act_window,name:account.action_move_line_select_by_type
#: model:ir.actions.act_window,name:account.action_move_line_select_tax_audit
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_journal_entry_ids
#: model:ir.model.fields,field_description:account.field_account_move_line_ids
#: model:ir.model.fields,field_description:account.field_res_partner_journal_item_count
#: model:ir.model.fields,field_description:account.field_res_users_journal_item_count
#: model:ir.ui.menu,name:account.menu_action_account_moves_all
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.setup_opening_move_lines_tree
#: model:ir.ui.view,arch_db:account.view_account_payment_form
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_pivot
#: model:ir.ui.view,arch_db:account.view_move_line_tree
#, python-format
msgid "Journal Items"
msgstr "عناصر اليومية"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_action.js:323
#: model:ir.actions.client,name:account.action_manual_reconcile
#, python-format
msgid "Journal Items to Reconcile"
msgstr "القيود اليومية المراد تسويتها"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_name
msgid "Journal Name"
msgstr "اسم اليومية"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
msgid "Journal and Partner"
msgstr "اليومية و الشريك"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Journal invoices with period in current year"
msgstr "فواتير اليومية بفترة خلال السنة الحالية"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Journal items where matching number isn't set"
msgstr "عناصر اليومية المراد مطابقتها لا تحتوي على رقم."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_opening_journal_id
msgid ""
"Journal where the opening entry of this company's accounting has been "
"posted."
msgstr "دفتر اليومية الذي تم فيه ترحيل الإدخال الافتتاحي لحساب هذه الشركة."

#. module: account
#: model:ir.actions.act_window,name:account.action_account_journal_form
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_journal_ids
#: model:ir.model.fields,field_description:account.field_account_balance_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_account_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_report_journal_ids
#: model:ir.model.fields,field_description:account.field_account_print_journal_journal_ids
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_journal_ids
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_journal_ids
#: model:ir.model.fields,field_description:account.field_account_tax_report_journal_ids
#: model:ir.model.fields,field_description:account.field_accounting_report_journal_ids
#: model:ir.ui.menu,name:account.menu_action_account_journal_form
msgid "Journals"
msgstr "اليوميات"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_print_journal_menu
#: model:ir.actions.report,name:account.action_report_journal
#: model:ir.ui.menu,name:account.menu_print_journal
msgid "Journals Audit"
msgstr "تدقيق الدفاتر"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "July"
msgstr "يوليو"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "June"
msgstr "يونيو"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_reason
msgid "Justification"
msgstr "المبررات"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_kanban_dashboard
msgid "Kanban Dashboard"
msgstr "لوحة تحكم Kanban"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "الرسم البياني للوحة تحكم Kanban"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Keep empty for no control"
msgstr "اتركه فارغا لعدم التحكم"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_date_invoice
msgid "Keep empty to use the current date"
msgstr "اتركه فارغا لإستخدام التاريخ الحالي"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_date
msgid "Keep empty to use the invoice date."
msgstr "اتركه فارغا لاستخدام تاريخ الفاتورة"

#. module: account
#: selection:account.payment,payment_difference_handling:0
msgid "Keep open"
msgstr "إحفظها مفتوحة"

#. module: account
#: model:ir.model.fields,help:account.field_product_product_property_account_income_id
#: model:ir.model.fields,help:account.field_product_template_property_account_income_id
msgid ""
"Keep this field empty to use the default value from the product category."
msgstr "احتفظ بهذا الحقل فارغا لاستخدام القيمة الافتراضية من فئة المنتج."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:522
#: code:addons/account/static/src/xml/account_reconciliation.xml:189
#: code:addons/account/static/src/xml/account_reconciliation.xml:230
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_name
#: model:ir.model.fields,field_description:account.field_account_move_line_name
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#, python-format
msgid "Label"
msgstr "تسمية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_description
msgid "Label on Invoices"
msgstr "تسمية الفواتير"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment___last_update
#: model:ir.model.fields,field_description:account.field_account_account___last_update
#: model:ir.model.fields,field_description:account.field_account_account_tag___last_update
#: model:ir.model.fields,field_description:account.field_account_account_template___last_update
#: model:ir.model.fields,field_description:account.field_account_account_type___last_update
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance___last_update
#: model:ir.model.fields,field_description:account.field_account_balance_report___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance___last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line___last_update
#: model:ir.model.fields,field_description:account.field_account_cash_rounding___last_update
#: model:ir.model.fields,field_description:account.field_account_cashbox_line___last_update
#: model:ir.model.fields,field_description:account.field_account_chart_template___last_update
#: model:ir.model.fields,field_description:account.field_account_common_account_report___last_update
#: model:ir.model.fields,field_description:account.field_account_common_journal_report___last_update
#: model:ir.model.fields,field_description:account.field_account_common_partner_report___last_update
#: model:ir.model.fields,field_description:account.field_account_common_report___last_update
#: model:ir.model.fields,field_description:account.field_account_financial_report___last_update
#: model:ir.model.fields,field_description:account.field_account_financial_year_op___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template___last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template___last_update
#: model:ir.model.fields,field_description:account.field_account_full_reconcile___last_update
#: model:ir.model.fields,field_description:account.field_account_group___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_line___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_refund___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_report___last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_tax___last_update
#: model:ir.model.fields,field_description:account.field_account_journal___last_update
#: model:ir.model.fields,field_description:account.field_account_move___last_update
#: model:ir.model.fields,field_description:account.field_account_move_line___last_update
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile___last_update
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff___last_update
#: model:ir.model.fields,field_description:account.field_account_move_reversal___last_update
#: model:ir.model.fields,field_description:account.field_account_opening___last_update
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile___last_update
#: model:ir.model.fields,field_description:account.field_account_payment___last_update
#: model:ir.model.fields,field_description:account.field_account_payment_method___last_update
#: model:ir.model.fields,field_description:account.field_account_payment_term___last_update
#: model:ir.model.fields,field_description:account.field_account_payment_term_line___last_update
#: model:ir.model.fields,field_description:account.field_account_print_journal___last_update
#: model:ir.model.fields,field_description:account.field_account_reconcile_model___last_update
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template___last_update
#: model:ir.model.fields,field_description:account.field_account_register_payments___last_update
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger___last_update
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger___last_update
#: model:ir.model.fields,field_description:account.field_account_tax___last_update
#: model:ir.model.fields,field_description:account.field_account_tax_group___last_update
#: model:ir.model.fields,field_description:account.field_account_tax_report___last_update
#: model:ir.model.fields,field_description:account.field_account_tax_template___last_update
#: model:ir.model.fields,field_description:account.field_account_unreconcile___last_update
#: model:ir.model.fields,field_description:account.field_accounting_report___last_update
#: model:ir.model.fields,field_description:account.field_cash_box_in___last_update
#: model:ir.model.fields,field_description:account.field_cash_box_out___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_financial___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_generalledger___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_journal___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_overdue___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_partnerledger___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_tax___last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_trialbalance___last_update
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard___last_update
#: model:ir.model.fields,field_description:account.field_validate_account_move___last_update
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts___last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Last Month"
msgstr "آخر شهر"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:119
#, python-format
msgid "Last Reconciliation:"
msgstr "آخر تسوية:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_write_uid
#: model:ir.model.fields,field_description:account.field_account_account_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_account_type_write_uid
#: model:ir.model.fields,field_description:account.field_account_account_write_uid
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_write_uid
#: model:ir.model.fields,field_description:account.field_account_balance_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_write_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_write_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_account_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_common_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_financial_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_write_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_write_uid
#: model:ir.model.fields,field_description:account.field_account_group_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_write_uid
#: model:ir.model.fields,field_description:account.field_account_journal_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal_write_uid
#: model:ir.model.fields,field_description:account.field_account_move_write_uid
#: model:ir.model.fields,field_description:account.field_account_opening_write_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_write_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal_write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_write_uid
#: model:ir.model.fields,field_description:account.field_account_register_payments_write_uid
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_write_uid
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_report_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template_write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_write_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile_write_uid
#: model:ir.model.fields,field_description:account.field_accounting_report_write_uid
#: model:ir.model.fields,field_description:account.field_cash_box_in_write_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out_write_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_write_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move_write_uid
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_write_date
#: model:ir.model.fields,field_description:account.field_account_account_template_write_date
#: model:ir.model.fields,field_description:account.field_account_account_type_write_date
#: model:ir.model.fields,field_description:account.field_account_account_write_date
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_write_date
#: model:ir.model.fields,field_description:account.field_account_balance_report_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_accounts_wizard_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_write_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_write_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_write_date
#: model:ir.model.fields,field_description:account.field_account_chart_template_write_date
#: model:ir.model.fields,field_description:account.field_account_common_account_report_write_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_write_date
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_write_date
#: model:ir.model.fields,field_description:account.field_account_common_report_write_date
#: model:ir.model.fields,field_description:account.field_account_financial_report_write_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_write_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_write_date
#: model:ir.model.fields,field_description:account.field_account_group_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_confirm_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_line_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_write_date
#: model:ir.model.fields,field_description:account.field_account_journal_write_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_write_date
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_write_date
#: model:ir.model.fields,field_description:account.field_account_move_line_write_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal_write_date
#: model:ir.model.fields,field_description:account.field_account_move_write_date
#: model:ir.model.fields,field_description:account.field_account_opening_write_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_method_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_write_date
#: model:ir.model.fields,field_description:account.field_account_payment_write_date
#: model:ir.model.fields,field_description:account.field_account_print_journal_write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_write_date
#: model:ir.model.fields,field_description:account.field_account_register_payments_write_date
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_write_date
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_group_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_report_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_template_write_date
#: model:ir.model.fields,field_description:account.field_account_tax_write_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile_write_date
#: model:ir.model.fields,field_description:account.field_accounting_report_write_date
#: model:ir.model.fields,field_description:account.field_cash_box_in_write_date
#: model:ir.model.fields,field_description:account.field_cash_box_out_write_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard_write_date
#: model:ir.model.fields,field_description:account.field_validate_account_move_write_date
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Last day of current month"
msgstr "اخر يوم في الشهر الحالى"

#. module: account
#: selection:account.payment.term.line,option:0
msgid "Last day of following month"
msgstr "اخر يوم في الشهر التالى"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_last_time_entries_checked
#: model:ir.model.fields,help:account.field_res_users_last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed for this partner. "
"It is set either if there's not at least an unreconciled debit and an "
"unreconciled credit or if you click the \"Done\" button."
msgstr ""
"آخر مرة تم إجراء تسوية بين فواتير ومدفوعات هذا الشريك. يتم تحديد هذا الحقل "
"إذا كان هناك مبلغ مدين غير مسوى ومبلغ دائن غير مسوى - أو - إذا نقرت زر "
"\"تم\"."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed on this account. It"
" is set either if there's not at least an unreconciled debit and an "
"unreconciled credit Or if you click the \"Done\" button."
msgstr ""
"آخر مرة تم إجراء تسوية بين فواتير ومدفوعات هذا الحساب. يتم تحديد هذا الحقل "
"إذا كان هناك مبلغ مدين غير مسوى ومبلغ دائن غير مسوى - أو - إذا نقرت زر "
"\"تم\"."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Late Activities"
msgstr "الأنشطة المتأخرة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_last_time_entries_checked
#: model:ir.model.fields,field_description:account.field_res_partner_last_time_entries_checked
#: model:ir.model.fields,field_description:account.field_res_users_last_time_entries_checked
msgid "Latest Invoices & Payments Matching Date"
msgstr "تاريخ اخر مطابقة للفواتير و المدفوعات"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group_parent_left
msgid "Left Parent"
msgstr "اساس المتروك"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Legal Name"
msgstr "الاسم القانوني"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Legal Notes..."
msgstr "ملاحظات قانونية..."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position_note
msgid "Legal mentions that have to be printed on the invoices."
msgstr "الإشارات القانونية التم تم طباعتها على الفواتير"

#. module: account
#: code:addons/account/models/account_invoice.py:216
#, python-format
msgid "Less Payment"
msgstr "اقل مدفوعات"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:24
#, python-format
msgid "Let odoo try to reconcile entries for the user"
msgstr "اترك لأودو محاولة تسوية القيود للمستخدم"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Let your customers pay their invoices online"
msgstr "اسمح لعملائك بسداد فواتيرهم عبر الإنترنت"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_level
msgid "Level"
msgstr "المستوى"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_liability0
#: model:account.financial.report,name:account.account_financial_report_liabilitysum0
msgid "Liability"
msgstr "مسؤولية"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_move_id
msgid "Link to the automatically generated Journal Items."
msgstr "اربط بالعناصر اليومية الناشئة تلقائيًا."

#. module: account
#: selection:account.account.type,type:0
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Liquidity"
msgstr "السيولة"

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_tax_template_ids
msgid "List of all the taxes that have to be installed by the wizard"
msgstr "يجب أن تكون قائمة جميع الضرائب مثبتة من قبل المعالج"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Litigation"
msgstr "إجراء قانوني"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:29
#, python-format
msgid "Load more"
msgstr "تحميل المزيد"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_fiscalyear_lock_date
msgid "Lock Date"
msgstr "اغلاق التاريخ"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "اغلاق التاريخ لغير المستشارين"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Logo"
msgstr "الشعار"

#. module: account
#: code:addons/account/models/account_bank_statement.py:173
#, python-format
msgid "Loss"
msgstr "خسائر"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_loss_account_id
msgid "Loss Account"
msgstr "حساب الخسارة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_expense_currency_exchange_account_id
#: model:ir.model.fields,field_description:account.field_res_company_expense_currency_exchange_account_id
msgid "Loss Exchange Rate Account"
msgstr "حساب معدل خسائر سعر الصرف"

#. module: account
#: code:addons/account/models/chart_template.py:195
#, python-format
msgid "MISC"
msgstr "المتفرقات"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Mail your invoices in one-click using"
msgstr "أرسل فواتيرك بنقرة واحدة باستخدام"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Main Currency"
msgstr "العملة الرئيسية"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Main Title 1 (bold, underlined)"
msgstr "العنوان الرئيسي(سميك,تحته خط)"

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_currency_id
msgid "Main currency of the company."
msgstr "العملة الرئيسية للمؤسسة "

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Main currency of your company"
msgstr "العملة الرئيسية لمؤسستك"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Manage time &amp; material contracts or fixed-price recurring subscriptions."
msgstr "إدارة الوقت &amp;  عقود المواد أو الاشتراكات الدورية ثبتة الثمن."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Manage your employee expenses, from encoding, to payments and reporting."
msgstr "إدارة مصروفات الموظفين, من التكويد, للمدفوعات و عمل التقارير"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Manage your various fixed assets, such as buildings, machinery, materials, "
"cars, etc..., and calculate their associated depreciation over time."
msgstr ""
"ادارة الأصول الثابتة المختلفة الخاصة بك, مثل المباني, الآلات, السيارات, "
"الخ...., و حساب الاستهلاك المرتبطة بها على مر الزمان."

#. module: account
#: model:ir.ui.menu,name:account.account_management_menu
#: model:ir.ui.menu,name:account.account_reports_management_menu
#: model:ir.ui.menu,name:account.menu_finance_entries_management
msgid "Management"
msgstr "الإدارة "

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Managing bank fees"
msgstr "ادارة الرسوم البنكية"

#. module: account
#: model:account.payment.method,name:account.account_payment_method_manual_in
#: model:account.payment.method,name:account.account_payment_method_manual_out
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_manual
msgid "Manual"
msgstr "يدوى"

#. module: account
#: model:ir.ui.view,arch_db:account.view_invoice_tax_form
#: model:ir.ui.view,arch_db:account.view_invoice_tax_tree
msgid "Manual Invoice Taxes"
msgstr "فاتورة الضرائب يدويا"

#. module: account
#: model:ir.actions.client,name:account.action_manual_reconciliation
#: model:ir.ui.menu,name:account.menu_action_manual_reconciliation
msgid "Manual Reconciliation"
msgstr "تسوية يدوية"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_inbound_payment_method_ids
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo,you are suggested to reconcile the transaction with the batch deposit. Enable this option from the settings."
msgstr ""
"دليل: يتقاضون رواتبهم عن طريق النقد ، والاختيار أو اي وسيله أخرى خارج odoo.\n"
"الكترونيه: الحصول علي دفع تلقائيا من خلال مشتر الدفع عن طريق طلب المعاملة علي بطاقة المحفوظة من قبل العملاء عند شراء أو الاشتراك علي الإنترنت (الدفع الرمزية).\n"
"إيداع دفعه: encase عده شيكات العملاء في ان واحد عن طريق توليد دفعه وديعة لتقديمها إلى البنك الخاص بك. عند ترميز البيان البنكي في odoo ، فانك تقترح التوفيق بين الحركة والوديعة الدفعي. تمكين هذا الخيار من الإعدادات."

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_payment_method_id
#: model:ir.model.fields,help:account.field_account_payment_payment_method_id
#: model:ir.model.fields,help:account.field_account_register_payments_payment_method_id
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Check: Pay bill by check and print it from Odoo.\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo, you are suggested to reconcile the transaction with the batch deposit.To enable batch deposit,module account_batch_deposit must be installed.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. To enable sepa credit transfer, module account_sepa must be installed "
msgstr ""
"دليل: يتقاضون رواتبهم عن طريق النقد ، والاختيار أو اي وسيله أخرى خارج odoo.\n"
"الكترونيه: الحصول علي دفع تلقائيا من خلال مشتر الدفع عن طريق طلب المعاملة علي بطاقة المحفوظة من قبل العملاء عند شراء أو الاشتراك علي الإنترنت (الدفع الرمزية).\n"
"الاختيار: دفع الفاتورة عن طريق الاختيار وطباعته من odoo.\n"
"إيداع دفعه: encase عده شيكات العملاء في ان واحد عن طريق توليد دفعه وديعة لتقديمها إلى البنك الخاص بك. عند ترميز البيان البنكي في odoo ، فانك تقترح التوفيق بين الحركة والوديعة الدفعي. لتمكين الإيداع الدفعي ، يجب تثبيت الوحدة النمطية account_batch_deposit.\n"
"sepa تحويل الائتمان: فاتورة الدفع من sepa نقل الائتمان الملف الذي تقدمه إلى البنك الخاص بك. لتمكين نقل الائتمان sepa, يجب تثبيت الوحدة النمطية account_sepa"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_outbound_payment_method_ids
msgid ""
"Manual:Pay bill by cash or any other method outside of Odoo.\n"
"Check:Pay bill by check and print it from Odoo.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. Enable this option from the settings."
msgstr ""
"دليل: دفع الفاتورة نقدا أو اي طريقه أخرى خارج odoo.\n"
"الاختيار: دفع الفاتورة عن طريق الاختيار وطباعته من odoo.\n"
"sepa تحويل الائتمان: فاتورة الدفع من sepa نقل الائتمان الملف الذي تقدمه إلى البنك الخاص بك. تمكين هذا الخيار من الإعدادات."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Manually enter your transactions using our"
msgstr "ادخل المعاملات يدويا باستخدام "

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "March"
msgstr "مارس"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Margin Analysis"
msgstr "تحليل الهوامش"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model:ir.ui.view,arch_db:account.setup_view_company_form
msgid "Mark as done"
msgstr "تحديد كمنتهي"

#. module: account
#: selection:account.payment,payment_difference_handling:0
msgid "Mark invoice as fully paid"
msgstr "علم أن الفاتورة تم دفعها بالكامل"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_payables_master_data
#: model:ir.ui.menu,name:account.menu_finance_receivables_master_data
msgid "Master Data"
msgstr "بيانات الاستاذ"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_matched_credit_ids
msgid "Matched Credit"
msgstr "مطابقه الائتمان"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_matched_debit_ids
msgid "Matched Debit"
msgstr "الخصم المطابق"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_reconciled_line_ids
#: model:ir.ui.view,arch_db:account.view_full_reconcile_form
msgid "Matched Journal Items"
msgstr "مطابقة بنود الدفتر اليومي"

#. module: account
#: model:ir.ui.view,arch_db:account.view_full_reconcile_form
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Matching"
msgstr "مطابقة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_full_reconcile_id
msgid "Matching Number"
msgstr "رقم التطابق"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile_max_date
msgid "Max Date of Matched Lines"
msgstr "تاريخ الحد الأقصى للخطوط المتطابقة"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "May"
msgstr "مايو "

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_communication
#: model:ir.model.fields,field_description:account.field_account_payment_communication
#: model:ir.model.fields,field_description:account.field_account_register_payments_communication
msgid "Memo"
msgstr "مذكرة"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:64
#, python-format
msgid "Memo:"
msgstr "مذكرة:"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_invoice_warn_msg
#: model:ir.model.fields,field_description:account.field_res_users_invoice_warn_msg
msgid "Message for Invoice"
msgstr "رسالة للفاتورة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Cost of Revenue"
msgstr "يخصم منه تكلفة الإيرادات"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Credit Card Accounts"
msgstr "يحصم منه البطاقات الإئتمانية-دائنة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Current Liabilities"
msgstr "يخصم منه الخصوم المتداولة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Expenses"
msgstr "يخصم منه المصروفات"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Non-Current Liabilities"
msgstr "يخصم منه الخصوم غير المتداولة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Minus Payable Accounts"
msgstr "يخصم منه الحسابات الدائنة"

#. module: account
#: selection:account.journal,type:0
#: model:ir.ui.view,arch_db:account.view_account_journal_form
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Miscellaneous"
msgstr "المتنوعة"

#. module: account
#: code:addons/account/models/chart_template.py:195
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#, python-format
msgid "Miscellaneous Operations"
msgstr "عمليات متنوعة"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:164
#, python-format
msgid "Modify models"
msgstr "تعديل القالب"

#. module: account
#: selection:account.cash.rounding,strategy:0
msgid "Modify tax amount"
msgstr "تعديل مبلغ الضريبة"

#. module: account
#: selection:account.invoice.refund,filter_refund:0
msgid "Modify: create credit note, reconcile and create a new draft invoice"
msgstr "تعديل: إنشاء إشعار دائن، ثم التسوية ثم إنشاء مسودة فاتورة جديدة"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Monitor your product margins from invoices"
msgstr "راقب هوامش الربح على منتجاتك من الفواتير"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_invoice_partner_relation
msgid "Monthly Turnover"
msgstr "التداول الشهري"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Most currencies are already created by default. If you plan\n"
"                        to use some of them, you should check their <strong>Active</strong>\n"
"                        field."
msgstr ""
"أغلب العملات تم إنشاؤها مسبقا بشكل افتراضي. إذا كنت تخطط\n"
"                     لاستخدام بعضا منهم, يجب عليك التجقق من <strong>نشاط</strong>\n"
"                     حقلهم"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Move"
msgstr "حركة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_move_id
#: model:ir.model.fields,field_description:account.field_account_payment_move_line_ids
msgid "Move Line"
msgstr "بند الحركة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_move_line_count
msgid "Move Line Count"
msgstr "عدد بنود الحركة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_move_reconciled
msgid "Move Reconciled"
msgstr "تم تسوية الحركة"

#. module: account
#: code:addons/account/models/account_move.py:1362
#, python-format
msgid "Move name (id): %s (%s)"
msgstr "اسم الحركة (id): %s (%s)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_register_payments_multi
msgid "Multi"
msgstr "متعدد"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Multi Currency"
msgstr "متعدد العملات"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Multi-Currencies"
msgstr "عملات متعددة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "My Activities"
msgstr "أنشطتي"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "My Invoices"
msgstr "فواتيرى"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "NET ASSETS"
msgstr "صافي الأصول"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "NET PROFIT"
msgstr "صافي الأرباح"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_name
#: model:ir.model.fields,field_description:account.field_account_account_tag_name
#: model:ir.model.fields,field_description:account.field_account_account_template_name
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_name
#: model:ir.model.fields,field_description:account.field_account_chart_template_name
#: model:ir.model.fields,field_description:account.field_account_group_name
#: model:ir.model.fields,field_description:account.field_account_payment_method_name
#: model:ir.model.fields,field_description:account.field_account_payment_name
#: model:ir.model.fields,field_description:account.field_account_tax_group_name
#: model:ir.ui.view,arch_db:account.report_financial
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Name"
msgstr "الاسم"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:52
#, python-format
msgid "Name:"
msgstr "الاسم:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_narration
msgid "Narration"
msgstr "البيان"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Navigate easily through reports and see what is behind the numbers"
msgstr "أبحر بسهولة عبر التقارير واستقرئ ما بين السطور"

#. module: account
#: model:ir.ui.view,arch_db:account.report_tax
msgid "Net"
msgstr "الصفي"

#. module: account
#: selection:account.bank.statement,state:0
msgid "New"
msgstr "جديد"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "New Statement"
msgstr "كشف حساب جديد"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "New Transactions"
msgstr "معاملات جديدة"

#. module: account
#: code:addons/account/models/account_move.py:1339
#, python-format
msgid "New expected payment date: "
msgstr "تاريخ سداد جديد متوقع"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_sequence_number_next
#: model:ir.model.fields,field_description:account.field_account_invoice_sequence_number_next_prefix
#: model:ir.model.fields,field_description:account.field_account_journal_sequence_number_next
msgid "Next Number"
msgstr "العدد التالي"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Next, register any unmatched payments:<br/>"
msgstr "التالى, سجل أي مدفوعات غير متطابقة:<br/>"

#. module: account
#: selection:accounting.report,filter_cmp:0
msgid "No Filters"
msgstr "لا فلتر"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_blocked
msgid "No Follow-up"
msgstr "عدم متابعة"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "No Message"
msgstr "لا توجد رسالة"

#. module: account
#: selection:account.financial.report,display_detail:0
msgid "No detail"
msgstr "لا تفاصيل"

#. module: account
#: code:addons/account/models/account.py:116
#, python-format
msgid "No opening move defined !"
msgstr "لم يتم تعريف قيد افتتاحي للآن!"

#. module: account
#: model:ir.model.fields,help:account.field_res_company_fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""
"لا مستخدمين, بما في ذلك المستشارين, يمكنهم تعديل حسابات سابقة قبل و أثناء "
"هذا التاريخ. على سبيل المثال استخدمه لإغلاق سنة مالية"

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_code_digits
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_code_digits
msgid "No. of Digits to use for account code"
msgstr "عدد الخانات المستخدمة لكود الحساب"

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_code_digits
msgid "No. of digits to use for account code"
msgstr "عدد الخانات المستخدمة لكود الحساب"

#. module: account
#: model:account.account.type,name:account.data_account_type_non_current_assets
msgid "Non-current Assets"
msgstr "الأصول الغير حالية"

#. module: account
#: model:account.account.type,name:account.data_account_type_non_current_liabilities
msgid "Non-current Liabilities"
msgstr "الالتزامات الغير حالية"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
msgid "None"
msgstr "لا شئ"

#. module: account
#: selection:res.partner,trust:0
msgid "Normal Debtor"
msgstr "المدين العادي"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Normal Text"
msgstr "نص عادي"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:254
#: model:ir.model.fields,field_description:account.field_account_account_template_note
#, python-format
msgid "Note"
msgstr "ملاحظة"

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_out_refund
msgid ""
"Note that the easiest way to create a credit note is to do it directly form\n"
"                the customer invoice, to refund it totally or partially."
msgstr ""
"لاحظ ان أسهل طريقه لإنشاء اشعار دائن هي ان تفعل ذلك مباشره شكل فاتورة العميل"
" ، لاسترداده كليا أو جزئيا."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_note
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_note
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_note
#: model:ir.ui.view,arch_db:account.view_account_template_form
msgid "Notes"
msgstr "ملاحظات"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Nothing to Reconcile"
msgstr "لا شيء للتسوية"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:34
#, python-format
msgid "Nothing to do!"
msgstr "لا شيء لتفعله!"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "November"
msgstr "نوفمبر"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_name
#: model:ir.model.fields,field_description:account.field_account_invoice_number
#: model:ir.model.fields,field_description:account.field_account_move_name
msgid "Number"
msgstr "الرقم"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Number (Move)"
msgstr "الرقم (حركة)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_number
msgid "Number of Coins/Bills"
msgstr "عدد العملات/الفواتير"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_days
msgid "Number of Days"
msgstr "عدد الأيام"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_accounts_code_digits
msgid "Number of digits in an account code"
msgstr "عدد الخانات في كود الحساب"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "OFX Import"
msgstr "استيراد QFX"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:17
#, python-format
msgid "OK"
msgstr "موافق"

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "October"
msgstr "أكتوبر"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Odoo Accounting has many extra-features:"
msgstr "تطبيق المحاسبة لديه ميزات إضافية مجانية كثيرة"

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_line
msgid ""
"Odoo allows you to reconcile a statement line directly with\n"
"                the related sale or puchase invoice(s)."
msgstr ""
"يسمح لك أودو بتسوية بند كشف الحساب مباشرة\n"
"               بفاتورة البيع أو الشراء المتصلة به. "

#. module: account
#: model:ir.actions.act_window,help:account.action_bank_statement_tree
msgid ""
"Odoo allows you to reconcile a statement line directly with\n"
"                the related sale or puchase invoices."
msgstr ""
"يسمح لك أودو بتسوية بند كشف الحساب مباشرة\n"
"               بفاتورة البيع أو الشراء المتصلة به. "

#. module: account
#: model:ir.actions.act_window,help:account.action_move_journal_line
msgid ""
"Odoo automatically creates one journal entry per accounting\n"
"                document: invoice, refund, vendor payment, bank statements,\n"
"                etc. So, you should record journal entries manually only/mainly\n"
"                for miscellaneous operations."
msgstr ""
"ينشئ أودو تلقائيا قيد يومية واحد لكل\n"
"        مستند حسابى: فاتورة, مرتجع, سداد مورد, كشف حساب بنكى,\n"
"        الخ, لذلك, يجب عليك تسجيل قيود اليومية يدويا فقط/بالأساس\n"
"        للعمليات المتنوعة."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Odoo can manage multiple companies, but we suggest to setup everything for "
"your first company before configuring the other ones."
msgstr ""
"يستطيع أودو أن يدير مؤسسات متعددة, لكننا نقترح تأسيس كل شيء لمؤسستك أولا قبل"
" اعداد المؤسسات الأخرى."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Odoo has already preconfigured a few taxes according to your "
"country.<br/>Simply review them and check if you need more."
msgstr ""
"أعد أودو بالفعل مسبقا عدد من الضرائب وفقا لبلدك.<br/> فقط اعرضهم و انظر إذا "
"كنت تريد المزيد"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Odoo should do most of the reconciliation work automatically, so you'll only"
" need to review a few of them when a <strong>'Reconcile Items'</strong> "
"button appears on your Vendor Bills dash."
msgstr ""
" أودو يجب ان يقوم بمعظم التسويات تلقائيا, لذلك ستحتاج فقط إلى عرضهم عندما "
"يظهر زر <strong>'تسوية العناصر'</strong> في فواتير المورد"

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree1
msgid ""
"Odoo's electronic invoicing allows to ease and fasten the\n"
"                collection of customer payments. Your customer receives the\n"
"                invoice by email and he can pay online and/or import it\n"
"                in his own system."
msgstr ""
"تسمح فواتير أودو الالكترونية بتخفيف و ربط\n"
"        تحصيل مدفوعات العميل. يستلم عميلك\n"
"        الفاتورة بالبريد الالكترونى و يستطيع أن يدفع أونلاين و/أو استيرادها\n"
"        في البرنامج الخاص به"

#. module: account
#: model:ir.ui.view,arch_db:account.account_invoice_confirm_view
msgid ""
"Once draft invoices are confirmed, you will not be able\n"
"                        to modify them. The invoices will receive a unique\n"
"                        number and journal items will be created in your chart\n"
"                        of accounts."
msgstr ""
"بمجرد تأكيد فواتير المسودة, لن تتمكن \n"
"          من تعديلهم. الشهادات سوف تأخذ رقم\n"
"          مميز و عناصر اليومية سيتم انشائه في\n"
"          دليلك المحاسبي."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Once installed, set 'Bank Feeds' to 'File Import' in bank account "
"settings.This adds a button to import from the Accounting dashboard."
msgstr ""
"بمجرد تثبيت ، قم بتعيين ' التغذية البنكية ' إلى ' استيراد الملف ' في إعدادات"
" الحساب البنكي. يؤدي هذا إلى أضافه زر للاستيراد من لوحه معلومات الحسابات."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Once you have created your chart of accounts, you will need to update your "
"account balances."
msgstr ""
"بمجرد انشائك دليلك المحاسبي, ستحتاج إلى تحديث أرصدة الحسابات الخاصة بك."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Once your bank accounts are registered, you will be able \n"
"                        to access your statements from the Accounting Dashboard. \n"
"                        The available methods for synchronization are as follows."
msgstr ""
"بمجرد تسجيل حساباتك البنكية, ستكون قادر \n"
"           على الدخول الى كشوفات الحسابات من لوحة المحاسبة.\n"
"           الطرق المتاحة للمزامنة كالتالى."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Once your company information is correct, you should"
msgstr "مجرد أن تكون بيانات شركتك صحيحة: يجب عليك أن"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "One bank statement for each bank account you hold."
msgstr "كشف حساب بنكي واحد لكل حساب بنكي تملكه"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_payment
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Online Payment"
msgstr "السداد عبر الإنترنت"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_only_one_chart_template
msgid "Only One Chart Template Available"
msgstr "قالب رسم بيانى واحد فقط متاح"

#. module: account
#: code:addons/account/models/account_payment.py:484
#, python-format
msgid "Only a draft payment can be posted."
msgstr "يمكن ترحيل مسودة  الدفع فقط."

#. module: account
#: code:addons/account/models/chart_template.py:866
#, python-format
msgid "Only administrators can change the settings"
msgstr "المسؤولين فقط يمكنهم تغيير الإعدادات"

#. module: account
#: model:ir.model.fields,help:account.field_res_company_period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""
"يمكن فقط للمستخدمين مع حق 'المستشارين' التعديل على حسابات سابقة قبل و حتى "
"هذا التاريخ. على سبيل المثال, استخدمه للإغلاق داخل سنة مالية مفتوحه."

#. module: account
#. openerp-web
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: code:addons/account/static/src/xml/account_payment.xml:82
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#, python-format
msgid "Open"
msgstr "فتح"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:664
#: code:addons/account/static/src/xml/account_reconciliation.xml:131
#, python-format
msgid "Open balance"
msgstr "افتح الرصيد"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_opening_date
#: model:ir.model.fields,field_description:account.field_account_opening_date
#: model:ir.model.fields,field_description:account.field_res_company_account_opening_date
msgid "Opening Date"
msgstr "تاريخ الفتح"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_opening_journal_id
msgid "Opening Journal"
msgstr "اليومية الافتتاحية"

#. module: account
#: code:addons/account/models/company.py:339
#: model:ir.model.fields,field_description:account.field_account_opening_opening_move_id
#: model:ir.model.fields,field_description:account.field_res_company_account_opening_move_id
#, python-format
msgid "Opening Journal Entry"
msgstr "القيد الافتتاحي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_opening_opening_move_line_ids
msgid "Opening Journal Items"
msgstr "بنود القيد الافتتاحي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op_opening_move_posted
msgid "Opening Move Posted"
msgstr "تم ترحيل القيد الافتتاحي"

#. module: account
#: model:ir.model.fields,help:account.field_account_cashbox_line_number
msgid "Opening Unit Numbers"
msgstr "فتح وحدة الأرقام"

#. module: account
#: code:addons/account/models/account.py:138
#, python-format
msgid "Opening balance"
msgstr "الرصيد الافتتاحي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_opening_credit
msgid "Opening credit"
msgstr "فتح الائتمان"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_opening_credit
msgid "Opening credit value for this account."
msgstr "فتح قيمه الائتمان لهذا الحساب."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_opening_debit
msgid "Opening debit"
msgstr "فتح الخصم"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_opening_debit
msgid "Opening debit value for this account."
msgstr "فتح قيمه المدين لهذا الحساب."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Operation Templates"
msgstr "قوالب العملية"

#. module: account
#: code:addons/account/models/account_bank_statement.py:1014
#, python-format
msgid ""
"Operation not allowed. Since your statement line already received a number, "
"you cannot reconcile it entirely with existing journal entries otherwise it "
"would make a gap in the numbering. You should book an entry and make a "
"regular revert of it in case you want to cancel it."
msgstr ""
"عملية غير مسموح بها. منذ حصول بند الكشف الخاص بك على رقم, لا يمكنك تسويته "
"بالكامل مع قيد موجود مسبقا لأن هذا سيسبب فرقا في الترقيم. يتوجب عليك حجز قيد"
" و من ثم القيام بتجاوزه في صورة رغبتك في إلغاء هذا البند من الكشف."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_nocreate
msgid "Optional Create"
msgstr "انشاء اختيارى"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag_ids
#: model:ir.model.fields,help:account.field_account_account_template_tag_ids
#: model:ir.model.fields,help:account.field_account_tax_tag_ids
#: model:ir.model.fields,help:account.field_account_tax_template_tag_ids
msgid "Optional tags you may want to assign for custom reporting"
msgstr "وسوم اختيارية قد تريدها لعمل تقارير مخصصة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_option
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Options"
msgstr "خيارات"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Original Amount"
msgstr "القيمة الأصلية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_payment_id
msgid "Originator Payment"
msgstr "سداد المنشأ"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_line_id
msgid "Originator tax"
msgstr "ضريبة المنشأ"

#. module: account
#: model:account.account.type,name:account.data_account_type_other_income
msgid "Other Income"
msgstr "دخل آخر"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Other Info"
msgstr "معلومات أخرى"

#. module: account
#: selection:account.payment.method,payment_type:0
msgid "Outbound"
msgstr "الصادرة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_stock_account_output_categ_id
#: model:ir.model.fields,field_description:account.field_res_company_property_stock_account_output_categ_id
msgid "Output Account for Stock Valuation"
msgstr "حساب المنصرف لتقييم المخزون"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_outstanding_credits_debits_widget
msgid "Outstanding Credits Debits Widget"
msgstr "الخصومات  الغير مسددة"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Outstanding Transactions"
msgstr "المعاملات المعلقة"

#. module: account
#: code:addons/account/models/account_invoice.py:131
#, python-format
msgid "Outstanding credits"
msgstr "الأرصدة المعلقة"

#. module: account
#: code:addons/account/models/account_invoice.py:134
#, python-format
msgid "Outstanding debits"
msgstr "الديون المعلقة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Overdue"
msgstr "المستحقات المتأخرة"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_overdue_msg
msgid "Overdue Payments Message"
msgstr "رسالة المدفوعات المتأخرة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Overdue invoices, maturity date passed"
msgstr "الفواتير المتأخرة, تاريخ استحقاقها انتهى"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_legal_statement
msgid "PDF Reports"
msgstr "تقارير PDF "

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Package"
msgstr "طرد"

#. module: account
#: selection:account.invoice,state:0 selection:account.invoice.report,state:0
#: model:ir.ui.view,arch_db:account.report_overdue_document
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:mail.message.subtype,name:account.mt_invoice_paid
msgid "Paid"
msgstr "مدفوع"

#. module: account
#: code:addons/account/models/account_payment.py:430
#, python-format
msgid "Paid Invoices"
msgstr "الفواتير المدفوعة"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:28
#: model:ir.ui.view,arch_db:account.report_invoice_document_with_payments
#, python-format
msgid "Paid on"
msgstr "المدفوعة على"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_reconciled
msgid "Paid/Reconciled"
msgstr "مدفوعة/تم التسوية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_parent_id
#: model:ir.model.fields,field_description:account.field_account_group_parent_id
msgid "Parent"
msgstr "الأصل"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_parent_id
msgid "Parent Chart Template"
msgstr "القالب الأصلى للرسم البيانى"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
msgid "Parent Report"
msgstr "تقرير أصلى"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_parent_state
msgid "Parent State"
msgstr "الحالة الام"

#. module: account
#: model:ir.model,name:account.model_account_partial_reconcile
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Partial Reconcile"
msgstr "تمت التسوية جزئيا"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:232
#: code:addons/account/static/src/xml/account_reconciliation.xml:249
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_partner_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_partner_id
#: model:ir.model.fields,field_description:account.field_account_move_line_partner_id
#: model:ir.model.fields,field_description:account.field_account_move_partner_id
#: model:ir.model.fields,field_description:account.field_account_payment_partner_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_partner_id
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_journal
#: model:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#, python-format
msgid "Partner"
msgstr "الشريك"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_commercial_partner_id
msgid "Partner Company"
msgstr "مؤسسة شريكة"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_partner_ledger_menu
#: model:ir.actions.report,name:account.action_report_partnerledger
#: model:ir.ui.menu,name:account.menu_partner_ledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
msgid "Partner Ledger"
msgstr "دفتر الشركاء"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_partner_name
msgid "Partner Name"
msgstr "اسم المستفيد"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_partner_type
#: model:ir.model.fields,field_description:account.field_account_payment_partner_type
#: model:ir.model.fields,field_description:account.field_account_register_payments_partner_type
msgid "Partner Type"
msgstr "نوع الشريك"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_result_selection
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_result_selection
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_result_selection
msgid "Partner's"
msgstr "ملك الشريك"

#. module: account
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Partners"
msgstr "الشركاء"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:106
#, python-format
msgid "Past"
msgstr "ماضي (سابق)"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Pay your bills in one-click using Euro SEPA service"
msgstr "سدد فواتيرك بضغة زر واحدة باستخدام خدمة Euro SEPA"

#. module: account
#: model:account.account.type,name:account.data_account_type_payable
#: selection:account.account.type,type:0
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Payable"
msgstr "الدائن"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_payable_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Payable Account"
msgstr "الحساب الدائن"

#. module: account
#: selection:account.aged.trial.balance,result_selection:0
#: selection:account.common.partner.report,result_selection:0
#: selection:account.report.partner.ledger,result_selection:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.view_account_search
msgid "Payable Accounts"
msgstr "الحسابات الدائنة"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_debit_limit
#: model:ir.model.fields,field_description:account.field_res_users_debit_limit
msgid "Payable Limit"
msgstr "حدود الدين المستحق"

#. module: account
#: model:ir.ui.view,arch_db:account.product_template_form_view
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Payables"
msgstr "دائن"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_amount
#: model:ir.model.fields,field_description:account.field_account_payment_amount
#: model:ir.model.fields,field_description:account.field_account_register_payments_amount
msgid "Payment Amount"
msgstr "القيمة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_date
#: model:ir.model.fields,field_description:account.field_account_payment_payment_date
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_date
msgid "Payment Date"
msgstr "تاريخ السداد"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_payment_difference
#: model:ir.model.fields,field_description:account.field_account_payment_payment_difference_handling
msgid "Payment Difference"
msgstr "الفرق فى السداد"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_journal_id
#: model:ir.model.fields,field_description:account.field_account_payment_journal_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_journal_id
msgid "Payment Journal"
msgstr "يومية السداد"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Payment Matching"
msgstr "مطابقةالدفعات "

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Payment Method"
msgstr "طرق السداد"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_method_id
#: model:ir.model.fields,field_description:account.field_account_payment_payment_method_id
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_method_id
msgid "Payment Method Type"
msgstr "طريقة البيع"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:60
#, python-format
msgid "Payment Method:"
msgstr "طرق السداد:"

#. module: account
#: model:ir.model,name:account.model_account_payment_method
#: model:ir.model.fields,field_description:account.field_account_journal_outbound_payment_method_ids
msgid "Payment Methods"
msgstr "طرق السداد"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_payment_move_line_ids
msgid "Payment Move Lines"
msgstr "خطوط الدفع"

#. module: account
#: model:ir.actions.report,name:account.action_report_payment_receipt
msgid "Payment Receipt"
msgstr "إيصال السداد"

#. module: account
#: model:ir.ui.view,arch_db:account.report_payment_receipt
msgid "Payment Receipt:"
msgstr "إيصال السداد:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_reference_type
#: model:ir.model.fields,field_description:account.field_account_payment_payment_reference
msgid "Payment Reference"
msgstr "رقم اشارة السداد"

#. module: account
#: model:ir.actions.act_window,name:account.action_payment_term_form
#: model:ir.model,name:account.model_account_payment_term
#: model:ir.model.fields,field_description:account.field_account_invoice_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_payment_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_name
#: model:ir.ui.menu,name:account.menu_action_payment_term_form
#: model:ir.ui.view,arch_db:account.account_planner
#: model:ir.ui.view,arch_db:account.view_payment_term_form
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
#: model:ir.ui.view,arch_db:account.view_payment_term_line_tree
#: model:ir.ui.view,arch_db:account.view_payment_term_search
#: model:ir.ui.view,arch_db:account.view_payment_term_tree
msgid "Payment Terms"
msgstr "شروط السداد"

#. module: account
#: model:ir.model,name:account.model_account_payment_term_line
msgid "Payment Terms Line"
msgstr "بند شروط السداد"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_abstract_payment_payment_type
#: model:ir.model.fields,field_description:account.field_account_payment_method_payment_type
#: model:ir.model.fields,field_description:account.field_account_payment_payment_type
#: model:ir.model.fields,field_description:account.field_account_register_payments_payment_type
msgid "Payment Type"
msgstr "نوع السداد"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_form
msgid "Payment terms explanation for the customer..."
msgstr "تفسير وتفاصيل شروط السداد للعميل.."

#. module: account
#: model:account.payment.term,note:account.account_payment_term_15days
msgid "Payment terms: 15 Days"
msgstr "شروط السداد: 15 يوم"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_net
msgid "Payment terms: 30 Net Days"
msgstr "شروط السداد: 30 يوم صافي"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_advance
msgid "Payment terms: 30% Advance End of Following Month"
msgstr "شروط السداد: 30% مقدم ونهاية الشهر التالي"

#. module: account
#: model:account.payment.term,note:account.account_payment_term
msgid "Payment terms: End of Following Month"
msgstr "شروط السداد: نهاية الشهر التالي"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_immediate
msgid "Payment terms: Immediate Payment"
msgstr "شروط السداد: سداد فوري"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_payment_id
msgid "Payment that created this entry"
msgstr "السداد الذى انشاء هذا القيد"

#. module: account
#: code:addons/account/models/account_payment.py:229
#: model:ir.actions.act_window,name:account.action_account_payments
#: model:ir.actions.act_window,name:account.action_account_payments_payable
#: model:ir.model,name:account.model_account_payment
#: model:ir.model.fields,field_description:account.field_account_invoice_payment_ids
#: model:ir.ui.menu,name:account.menu_action_account_payments_payable
#: model:ir.ui.menu,name:account.menu_action_account_payments_receivable
#: model:ir.ui.view,arch_db:account.partner_view_buttons
#: model:ir.ui.view,arch_db:account.view_account_payment_search
#, python-format
msgid "Payments"
msgstr "المدفوعات"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "تطابق المدفوعات"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_payments_widget
msgid "Payments Widget"
msgstr "أداة تطبيق الدفعات"

#. module: account
#: model:ir.actions.act_window,help:account.action_account_payments
#: model:ir.actions.act_window,help:account.action_account_payments_payable
msgid ""
"Payments are used to register liquidity movements (send, collect or transfer money).\n"
"                  You can then process those payments by your own means or by using installed facilities."
msgstr ""
"تستخدم المدفوعات لتسجيل تحركات السيولة (ارسال, جمع أو نقل المال).\n"
"          بعد ذلك يمكنك إتمام هذه المدفوعات بالوسائل الخاصة بك أو باستخدام تسهيلات مثبتة."

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Payments to do"
msgstr "مدفوعات للسداد"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_tree_pending_invoice
msgid "Pending Invoice"
msgstr "فاتورة معلقة"

#. module: account
#: selection:account.payment.term.line,value:0
msgid "Percent"
msgstr "في المئة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_matched_percentage
msgid "Percentage Matched"
msgstr "النسبة متطابقة"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Percentage of Price"
msgstr "نسبة السعر"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Percentage of Price Tax Included"
msgstr "نسبة السعر متضمنة الضريبة"

#. module: account
#: selection:account.reconcile.model,second_amount_type:0
#: selection:account.reconcile.model.template,second_amount_type:0
msgid "Percentage of amount"
msgstr "نسبة المبلغ"

#. module: account
#: selection:account.reconcile.model,amount_type:0
#: selection:account.reconcile.model.template,amount_type:0
msgid "Percentage of balance"
msgstr "نسبة الرصيد"

#. module: account
#: code:addons/account/models/account_invoice.py:1776
#, python-format
msgid "Percentages for Payment Terms Line must be between 0 and 100."
msgstr "لنسب لبند شرط السداد يجب أن تكون بين 0 و 100"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Period"
msgstr "فترة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_period_length
msgid "Period Length (days)"
msgstr "طول الفترة (أيام)"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_plaid
msgid "Plaid Connector"
msgstr "موصل منقوش"

#. module: account
#: model:ir.model,name:account.model_web_planner
msgid "Planner"
msgstr "مخطط"

#. module: account
#: code:addons/account/wizard/pos_box.py:27
#, python-format
msgid "Please check that the field 'Journal' is set on the Bank Statement"
msgstr "من فضلك تأكد من أن حقل \"اليومية\" معين على كشف الحساب البنكي. "

#. module: account
#: code:addons/account/wizard/pos_box.py:29
#, python-format
msgid "Please check that the field 'Transfer Account' is set on the company."
msgstr "من فضلك تأكد من أن حقل \"حساب النقل\" معين على المؤسسة. "

#. module: account
#: code:addons/account/models/account_invoice.py:1070
#, python-format
msgid "Please create some invoice lines."
msgstr "من فضلك أنشئ بعض قيود الفاتورة."

#. module: account
#: code:addons/account/models/account_move.py:157
#, python-format
msgid "Please define a sequence for the credit notes"
msgstr "الرجاء تحديد مسلسل للإشعارات الدائنة"

#. module: account
#: code:addons/account/models/account_move.py:162
#, python-format
msgid "Please define a sequence on the journal."
msgstr "من فضلك حدد تسلسل  في دفتر اليومية."

#. module: account
#: code:addons/account/models/account_invoice.py:1068
#, python-format
msgid "Please define sequence on the journal related to this invoice."
msgstr "من فضلك حدد تسلسل  في دفتر اليومية المتصل بهذه الفاتورة."

#. module: account
#: code:addons/account/models/company.py:336
#, python-format
msgid ""
"Please install a chart of accounts or create a miscellaneous journal before "
"proceeding."
msgstr "الرجاء تثبيت دليل محاسبي أو إنشاء دفتر يومية عامة قبل المتابعة."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Bank"
msgstr "بالإضافة إلى البنك"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Fixed Assets"
msgstr "بالإضافة الى الأصول الثابتة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Net Profit"
msgstr "بالإضافة الى صافى الربح"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Non-Current Assets"
msgstr "بالإضافة الى الأصول غير الحالية"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Plus Other Income"
msgstr "بالإضافة إلى الإيرادات الأخرى"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_wizard_form
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Post"
msgstr "تأجيل"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Post All Entries"
msgstr "تأجيل جميع القيود"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Post Difference In"
msgstr "تأجيل الفرق فى"

#. module: account
#: model:ir.actions.act_window,name:account.action_validate_account_move
#: model:ir.ui.view,arch_db:account.validate_account_move_view
msgid "Post Journal Entries"
msgstr "تأجيل قيود اليومية"

#. module: account
#: selection:account.move,state:0 selection:account.payment,state:0
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Posted"
msgstr "مُؤجلة  "

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Posted Journal Entries"
msgstr "القيود اليومية المُؤجلة  "

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Posted Journal Items"
msgstr "العناصر اليومية المُؤجلة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_bank_account_code_prefix
#: model:ir.model.fields,field_description:account.field_res_company_bank_account_code_prefix
msgid "Prefix of the bank accounts"
msgstr "بادئة الحسابات البنكية"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_cash_account_code_prefix
msgid "Prefix of the cash accounts"
msgstr "بادئة الحسابات النقدية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_cash_account_code_prefix
msgid "Prefix of the main cash accounts"
msgstr "بادئة الحسابات النقدية الأساسية"

#. module: account
#: model:account.account.type,name:account.data_account_type_prepayments
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Prepayments"
msgstr "الدفعات المسبقة"

#. module: account
#: selection:account.financial.report,sign:0
msgid "Preserve balance sign"
msgstr "الحفاظ على علامة الرصيد"

#. module: account
#: model:ir.model,name:account.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr "الإعداد المسبق لإنشاء قيود يومية خلال مطابقة الفواتير و المدفوعات"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.account_common_report_view
msgid "Print"
msgstr "طباعة"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Print Invoice"
msgstr "طباعة الفاتورة"

#. module: account
#: model:ir.model.fields,help:account.field_account_common_journal_report_amount_currency
#: model:ir.model.fields,help:account.field_account_print_journal_amount_currency
msgid ""
"Print Report with the currency column if the currency differs from the "
"company currency."
msgstr ""
"اطبع التقرير مع خانة العملة اذا كانت العملة تختلف عن العملة الافتراضية "
"للشركة"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Print checks to pay your vendors"
msgstr "طباعة الشيكات لسداد مستحقات مورديك"

#. module: account
#: model:ir.model,name:account.model_product_product
#: model:ir.model.fields,field_description:account.field_account_analytic_line_product_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_product_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report_product_id
#: model:ir.model.fields,field_description:account.field_account_move_line_product_id
#: model:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
msgid "Product"
msgstr "المنتج"

#. module: account
#: model:ir.model,name:account.model_product_category
#: model:ir.model.fields,field_description:account.field_account_invoice_report_categ_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Product Category"
msgstr "فئة المنتج"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_product_image
msgid "Product Image"
msgstr "صورة المنتج"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_product_qty
msgid "Product Quantity"
msgstr "جودة المنتج"

#. module: account
#: model:ir.model,name:account.model_product_template
msgid "Product Template"
msgstr "قالب المنتج"

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action
#: model:ir.ui.view,arch_db:account.product_product_view_tree
msgid "Products"
msgstr "المنتجات"

#. module: account
#: code:addons/account/models/account_bank_statement.py:177
#, python-format
msgid "Profit"
msgstr "الربح"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "Profit &amp; Loss"
msgstr "الربح &amp; الخسارة"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_profitloss_toreport0
msgid "Profit (Loss) to report"
msgstr "تقرير للربح (الخسارة)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_profit_account_id
msgid "Profit Account"
msgstr "حساب الربح"

#. module: account
#: model:account.financial.report,name:account.account_financial_report_profitandloss0
#: model:ir.actions.act_window,name:account.action_account_report_pl
#: model:ir.ui.menu,name:account.menu_account_report_pl
msgid "Profit and Loss"
msgstr "الأرباح و الخسائر"

#. module: account
#: code:addons/account/models/account_payment.py:135
#, python-format
msgid ""
"Programming error: wizard action executed without active_ids in context."
msgstr "خطأ برمجي: تم تنفيذ إجراء المعالج دون تحديد active_ids في السياق."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_form
msgid "Properties"
msgstr "خصائص"

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action_purchasable
#: model:ir.ui.menu,name:account.product_product_menu_purchasable
msgid "Purchasable Products"
msgstr "المنتجات القابلة للشراء"

#. module: account
#: selection:account.journal,type:0
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Purchase"
msgstr "الشراء"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_wizard_multi_chart
msgid "Purchase Tax"
msgstr "ضريبة الشراء"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_purchase_tax_rate
msgid "Purchase Tax(%)"
msgstr "ضريبة الشراء(%)"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:35
#, python-format
msgid "Purchase: Untaxed Total"
msgstr "الشراء: الإجمالي بدون ضرائب"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
#: model:ir.ui.menu,name:account.menu_finance_payables
#: model:ir.ui.view,arch_db:account.view_account_journal_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Purchases"
msgstr "المشتريات"

#. module: account
#: model:ir.actions.act_window,name:account.action_cash_box_in
#: model:ir.ui.view,arch_db:account.cash_box_in_form
msgid "Put Money In"
msgstr "ضع المال فى"

#. module: account
#: selection:account.tax,amount_type:0
#: selection:account.tax.template,amount_type:0
msgid "Python Code"
msgstr "كود بايثون"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "QIF Import"
msgstr "استيراد QIF"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_quantity
#: model:ir.model.fields,field_description:account.field_account_move_line_quantity
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Quantity"
msgstr "كمية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_description
#: model:ir.model.fields,field_description:account.field_cash_box_in_name
#: model:ir.model.fields,field_description:account.field_cash_box_out_name
msgid "Reason"
msgstr "السبب"

#. module: account
#: model:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Reason..."
msgstr "السبب..."

#. module: account
#: model:account.account.type,name:account.data_account_type_receivable
#: selection:account.account.type,type:0
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Receivable"
msgstr "المدين"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_property_account_receivable_id
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Receivable Account"
msgstr "الحساب المدين"

#. module: account
#: selection:account.aged.trial.balance,result_selection:0
#: selection:account.common.partner.report,result_selection:0
#: selection:account.report.partner.ledger,result_selection:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.view_account_form
#: model:ir.ui.view,arch_db:account.view_account_search
msgid "Receivable Accounts"
msgstr "الحسابات المدينة"

#. module: account
#: selection:account.aged.trial.balance,result_selection:0
#: selection:account.common.partner.report,result_selection:0
#: selection:account.report.partner.ledger,result_selection:0
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
msgid "Receivable and Payable Accounts"
msgstr "حسابات الدائنون و المدينون"

#. module: account
#: model:ir.ui.view,arch_db:account.product_template_form_view
#: model:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Receivables"
msgstr "المدينين"

#. module: account
#: selection:account.abstract.payment,payment_type:0
#: selection:account.payment,payment_type:0
#: selection:account.register.payments,payment_type:0
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Receive Money"
msgstr "استقبال المال"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:73
#: code:addons/account/static/src/xml/account_reconciliation.xml:105
#: code:addons/account/static/src/xml/account_reconciliation.xml:106
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#, python-format
msgid "Reconcile"
msgstr "تسوية"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_account_move_line_reconcile
msgid "Reconcile Entries"
msgstr "تسوية القيود"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Reconcile With Write-Off"
msgstr "تسوية مع الشطب"

#. module: account
#: code:addons/account/wizard/account_reconcile.py:86
#, python-format
msgid "Reconcile Writeoff"
msgstr "شطب التسوية"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/tour_bank_statement_reconciliation.js:11
#, python-format
msgid "Reconcile the demo bank statement"
msgstr "التسوية على كشف الحساب البنكي التجريبي"

#. module: account
#: selection:account.payment,state:0
#: model:ir.model.fields,field_description:account.field_account_move_line_reconciled
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Reconciled"
msgstr "تم التسوية "

#. module: account
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_reconciled
msgid "Reconciled Entries"
msgstr "قيود متزنة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Reconciled entries"
msgstr "قيود مسواه"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Reconciliation"
msgstr "التسوية"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_reconcile_model
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Reconciliation Models"
msgstr "نماذج التسوية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile_partial_reconcile_ids
msgid "Reconciliation Parts"
msgstr "التسوية الجزئية"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
msgid "Reconciliation Transactions"
msgstr "معاملات التسوية"

#. module: account
#: model:ir.actions.client,name:account.action_bank_reconcile
#: model:ir.actions.client,name:account.action_bank_reconcile_bank_statements
msgid "Reconciliation on Bank Statements"
msgstr "التسوية على كشوفات الحسابات البنكية"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Reconciling journal entries"
msgstr "تسوية قيود اليومية"

#. module: account
#: selection:account.journal,bank_statements_source:0
msgid "Record Manually"
msgstr "التسجيل يدويا"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Record transactions in foreign currencies"
msgstr "تسجيل معاملات بعملات أجنبية"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Recording invoices"
msgstr "تسجيل الفواتير"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:231
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_partnerledger
#, python-format
msgid "Ref"
msgstr "رقم الإشارة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_ref
msgid "Ref."
msgstr "رقم الإشارة."

#. module: account
#: code:addons/account/controllers/portal.py:72
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_ref
#: model:ir.model.fields,field_description:account.field_account_bank_statement_name
#: model:ir.model.fields,field_description:account.field_account_move_line_ref
#: model:ir.model.fields,field_description:account.field_account_move_ref
#: model:ir.model.fields,field_description:account.field_cash_box_in_ref
#, python-format
msgid "Reference"
msgstr "المرجع"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_uom_name
msgid "Reference Unit of Measure"
msgstr "رقم إشارة وحدة القياس"

#. module: account
#: model:ir.ui.view,arch_db:account.report_overdue_document
msgid "Reference number"
msgstr "رقم الأشارة"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_origin
#: model:ir.model.fields,help:account.field_account_invoice_origin
msgid "Reference of the document that produced this invoice."
msgstr "رقم إشارة المستندات المنتجة لتلك الفاتورة."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_payment_reference
msgid ""
"Reference of the document used to issue this payment. Eg. check number, file"
" name, etc."
msgstr ""
"رقم إشارة المستند المستخدم لإصدار السداد. مثال لذلك رقم الشيك, اسم الملف, "
"الخ."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_name
msgid "Reference/Description"
msgstr "رقم الإشارة/الوصف"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_invoice_ids
msgid "Refund Invoices"
msgstr "فواتير المردودات"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_filter_refund
msgid "Refund Method"
msgstr "طريقة الإرجاع"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_refund_filter_refund
msgid ""
"Refund base on this type. You can not Modify and Cancel if the invoice is "
"already reconciled"
msgstr ""
"في المرتجع المعتمد على هذا النوع. لا يمكنك التعديل أو الإلغاء إذا كان قد تم "
"تسوية الفاتورة "

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_payment
#: model:ir.actions.act_window,name:account.action_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.view_account_payment_form
#: model:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Register Payment"
msgstr "تسجيل السداد"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Register Payments"
msgstr "تسجيل المدفوعات"

#. module: account
#: model:ir.model,name:account.model_account_register_payments
msgid "Register payments on multiple invoices"
msgstr "تسجيل المدفوعات على عدة فواتير"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Registering outstanding invoices and payments can be a huge undertaking, \n"
"                        but you can start using Odoo without it by:"
msgstr ""
"تسجيل الفواتير و المدفوعات العالقة يمكن أن يكون مهمة كبيرة\n"
"             و لكن يمكنك البدء في استخدام أودو دونه بواسطة:"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Registering payments related to outstanding invoices separately in a "
"different account (e.g. Account Receivables 2014)"
msgstr ""
"تسجيل المدفوعات المتصلة بالفواتير العالقة على حدة فى حساب مختلف (مثال لذلك "
"الحسابات المدينة 2014 )"

#. module: account
#: selection:account.account.type,type:0
msgid "Regular"
msgstr "منتظم "

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_residual_company_signed
msgid "Remaining amount due in the currency of the company."
msgstr "المبلغ المستحق المتبقي بعملة المؤسسة"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_residual_signed
msgid "Remaining amount due in the currency of the invoice."
msgstr "المبلغ المستحق المتبقي بعملة الفاتورة."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_residual
msgid "Remaining amount due."
msgstr "المبلغ المستحق المتبقي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_tax_dest_id
msgid "Replacement Tax"
msgstr "ضريبة الاستبدال "

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_financial_report_form
msgid "Report"
msgstr "التقرير"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_name
msgid "Report Name"
msgstr "اسم التقرير"

#. module: account
#: model:ir.ui.view,arch_db:account.account_aged_balance_view
#: model:ir.ui.view,arch_db:account.account_common_report_view
msgid "Report Options"
msgstr "خيارات التقرير"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_financial_report_search
msgid "Report Type"
msgstr "نوع التقرير"

#. module: account
#: selection:account.financial.report,type:0
#: model:ir.model.fields,field_description:account.field_account_financial_report_account_report_id
msgid "Report Value"
msgstr "قيمة التقرير"

#. module: account
#: model:ir.ui.menu,name:account.account_report_folder
#: model:ir.ui.menu,name:account.menu_finance_reports
msgid "Reporting"
msgstr "اعداد التقرير "

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding_rounding
msgid "Represent the non-zero value smallest coinage (for example, 0.05)."
msgstr "أصغر فئة عملة غير صفرية (مثلاً فئة خمسة قروش والتي تمثل 0.05)."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Reset to Draft"
msgstr "إعادة الى وضع المسودة"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:236
#, python-format
msgid "Residual"
msgstr "المتبقي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_amount_residual
msgid "Residual Amount"
msgstr "المبلغ المتبقى"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_amount_residual_currency
msgid "Residual Amount in Currency"
msgstr "المبلغ المتبقي بالعملة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_user_id
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Responsible"
msgstr "مسئول"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_deferred_revenue
msgid "Revenue Recognition"
msgstr "تقدير الإيرادات"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal_date
msgid "Reversal date"
msgstr "تاريخ العكس"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_form
msgid "Reverse Entry"
msgstr "عكس القيد"

#. module: account
#: code:addons/account/wizard/account_move_reversal.py:20
#: model:ir.actions.act_window,name:account.action_view_account_move_reversal
#: model:ir.ui.view,arch_db:account.view_account_move_reversal
#, python-format
msgid "Reverse Moves"
msgstr "عكس الحركات"

#. module: account
#: selection:account.financial.report,sign:0
msgid "Reverse balance sign"
msgstr "عكس علامة الرصيد"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review Fiscal Positions"
msgstr "عرض الوضع المالى"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review Terms"
msgstr "عرض شروط السداد"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review existing Taxes"
msgstr "عرض الضرائب السابقة"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review the Chart of Accounts"
msgstr "عرض الدليل المحاسبي"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Review the list of available currencies (from the"
msgstr "عرض قائمة بالعملات المتاحة ( من ال "

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group_parent_right
msgid "Right Parent"
msgstr "الاساس المناسب"

#. module: account
#: selection:res.company,tax_calculation_rounding_method:0
msgid "Round Globally"
msgstr "تقريب على الصعيد العالمى"

#. module: account
#: selection:res.company,tax_calculation_rounding_method:0
msgid "Round per Line"
msgstr "التقريب لكل بند"

#. module: account
#: model:ir.ui.view,arch_db:account.rounding_form_view
msgid "Rounding Form"
msgstr "طريقة التقريب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_is_rounding_line
msgid "Rounding Line"
msgstr "بند التقريب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_rounding_method
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Rounding Method"
msgstr "طريقة التقريب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_rounding
msgid "Rounding Precision"
msgstr "دقة التقريب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding_strategy
msgid "Rounding Strategy"
msgstr "استراتيجية التقريب"

#. module: account
#: model:ir.ui.view,arch_db:account.rounding_tree_view
msgid "Rounding Tree"
msgstr "شجرة التقريب"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_sepa
msgid "SEPA Credit Transfer (SCT)"
msgstr "تحويل رصيد SEPA"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "SEPA Direct Debit (SDD)"
msgstr "نظام الخصم المباشر من SEPA"

#. module: account
#: selection:account.journal,type:0
#: model:ir.ui.view,arch_db:account.report_tax
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Sale"
msgstr "المبيعات"

#. module: account
#: model:ir.ui.view,arch_db:account.view_wizard_multi_chart
msgid "Sale Tax"
msgstr "ضريبة مبيعات"

#. module: account
#: selection:account.tax,type_tax_use:0
#: selection:account.tax.template,type_tax_use:0
#: model:ir.ui.menu,name:account.menu_finance_receivables
#: model:ir.ui.view,arch_db:account.view_account_journal_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Sales"
msgstr "مبيعات"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Sales Tax"
msgstr "ضرائب المبيعات"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_sale_tax_rate
msgid "Sales Tax(%)"
msgstr "ضريبة المبيعات(%)"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:33
#, python-format
msgid "Sales: Untaxed Total"
msgstr "المبيعات: الإجمالي بدون ضرائب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_id
#: model:ir.model.fields,field_description:account.field_account_invoice_user_id
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Salesperson"
msgstr "مسئول المبيعات"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model:ir.ui.view,arch_db:account.setup_opening_move_wizard_form
#: model:ir.ui.view,arch_db:account.setup_posted_move_form
#: model:ir.ui.view,arch_db:account.setup_view_company_form
msgid "Save"
msgstr "حفظ"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:199
#, python-format
msgid "Save and New"
msgstr "حفظ و جديد"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "احفظ هذه الصفحة ثم عد لضبط الخاصية."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Search Account Journal"
msgstr "البحث في حساب دفتر اليومية"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_template_search
msgid "Search Account Templates"
msgstr "البحث في قوالب الحساب"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Search Bank Statements"
msgstr "البحث في كشوفات الحسابات البنكية "

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_line_search
msgid "Search Bank Statements Line"
msgstr "البحث في بند كشوفات الحسابات البنكية "

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Search Chart of Account Templates"
msgstr "البحث في قوالب الدليل المحاسبي"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Search Invoice"
msgstr "البحث فى الفاتورة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Search Journal Items"
msgstr "البحث عن عناصر دفتر اليومية"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Search Move"
msgstr "البحث في الحركة"

#. module: account
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Search Operations"
msgstr "البحث في العمليات"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Search Tax Templates"
msgstr "البحث في قوالب الضريبة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Search Taxes"
msgstr "البحث في الضرائب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_account_id
msgid "Second Account"
msgstr "الحساب الثاني"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_amount
msgid "Second Amount"
msgstr "المبلغ الثاني"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_amount_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_amount_type
msgid "Second Amount type"
msgstr "نوع المبلغ الثاني"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_analytic_account_id
msgid "Second Analytic Account"
msgstr "الحساب التحليلي الثاني"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_journal_id
msgid "Second Journal"
msgstr "اليومية الثانية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_label
msgid "Second Journal Item Label"
msgstr "بيان عنصر اليومية الثاني"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_second_tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_second_tax_id
msgid "Second Tax"
msgstr "الضريبة الثانية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_access_token
msgid "Security Token"
msgstr "رمز الحماية"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_type
msgid ""
"Select 'Sale' for customer invoices journals.\n"
"Select 'Purchase' for vendor bills journals.\n"
"Select 'Cash' or 'Bank' for journals that are used in customer or vendor payments.\n"
"Select 'General' for miscellaneous operations journals."
msgstr ""
"اختر \"بيع\" لدفتر اليومية لفواتير العملاء. \n"
"اختر \"شراء\" لدفتر اليومية فواتير الموردين. \n"
"اختر \"نقدى\" أو \"بنك\" لدفاتر اليومية المستخدمة في مدفوعات العميل أو المورد.\n"
" اختر \"عام\" للعمليات المتنوعة لدفتر اليومية. "

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:74
#, python-format
msgid "Select a partner or choose a counterpart"
msgstr "اختر الشريك أو اختر القيد المناظر"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line_value
msgid "Select here the kind of valuation related to this payment terms line."
msgstr "اختر هنا نوع التقييم المقترن ببند شروط السداد هذا."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Select this if the taxes should use cash basis, which will create an entry "
"for such taxes on a given account during reconciliation."
msgstr ""
"اختر هذا الخيار إن كانت الضرائب ستستخدم نظام الأساس النقدي، سوف ينشئ هذا "
"الخيار قيدًا لهذه الضرائب في حساب معين خلال التسوية."

#. module: account
#: code:addons/account/wizard/account_invoice_state.py:21
#, python-format
msgid ""
"Selected invoice(s) cannot be confirmed as they are not in 'Draft' state."
msgstr "لا يمكن تأكيد الفواتير المحددة لانها ليست في وضع المسودة أو التحرير"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_invoice_warn
#: model:ir.model.fields,help:account.field_res_users_invoice_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"اختيار الخيار \"تحذير\" سوف يخطر المستخدم مع الرسالة، واختيار \"حجب رسالة\" "
"رمي استثناء مع الرسالة ومنع التدفق. يجب كتابة الرسالة في الحقل التالي."

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action_sellable
#: model:ir.ui.menu,name:account.product_product_menu_sellable
msgid "Sellable Products"
msgstr "المنتجات القابلة للبيع"

#. module: account
#: selection:account.abstract.payment,payment_type:0
#: selection:account.payment,payment_type:0
#: selection:account.register.payments,payment_type:0
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Send Money"
msgstr "ارسال المال"

#. module: account
#: model:ir.actions.act_window,name:account.account_send_payment_receipt_by_email_action
msgid "Send Receipt By Email"
msgstr "إرسال الإيصال بالبريد الإلكتروني"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Send by Email"
msgstr "ارسال بالبريد الإلكتروني."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Sending customer statements (outstanding invoices) manually during the "
"transition period"
msgstr "ارسال كشوفات حساب العميل (الفواتير المعلقة) يدويا خلال مدة المعاملة"

#. module: account
#: selection:account.payment,state:0
#: model:ir.model.fields,field_description:account.field_account_invoice_sent
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Sent"
msgstr "تم الارسال."

#. module: account
#: selection:res.company,fiscalyear_last_month:0
msgid "September"
msgstr "سبتمبر"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_sequence
#: model:ir.model.fields,field_description:account.field_account_financial_report_sequence
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_sequence
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_sequence
#: model:ir.model.fields,field_description:account.field_account_invoice_line_sequence
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_sequence
#: model:ir.model.fields,field_description:account.field_account_journal_sequence
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_sequence
#: model:ir.model.fields,field_description:account.field_account_payment_term_sequence
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_sequence
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_sequence
#: model:ir.model.fields,field_description:account.field_account_tax_group_sequence
#: model:ir.model.fields,field_description:account.field_account_tax_sequence
#: model:ir.model.fields,field_description:account.field_account_tax_template_sequence
msgid "Sequence"
msgstr "التسلسل"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Set To Draft"
msgstr "وضع كمسودة"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag_active
msgid "Set active to false to hide the Account Tag without removing it."
msgstr "قم بتغيير قيمة الاختيار نشط لخطأ لإخفاء وسم الحساب دون إزالته."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_active
msgid "Set active to false to hide the Journal without removing it."
msgstr "قم بتغيير قيمة الاختيار نشط لخطأ لإخفاء دفتر اليومية دون إزالته."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_active
#: model:ir.model.fields,help:account.field_account_tax_template_active
msgid "Set active to false to hide the tax without removing it."
msgstr "قم بتفعيل خيار الخاطئ لإخفاء الضريبة بدون مسحها"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Set the default Sales and Purchase taxes"
msgstr "تعيين ضرائب  الافتراضية للمبيعات و المشتريات"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_tax_adjustment
msgid ""
"Set this field to true if this tax can be used in the tax adjustment wizard,"
" used to manually fill some data in the tax declaration"
msgstr ""
"ضع هذا الحقل الى نعم إذا كان يمكن استخدام هذه الضريبة في معالج تسوية الضرائب"
" , وتستخدم لملء بعض البيانات يدويا في البيان الضريبي"

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_visible
msgid ""
"Set this to False if you don't want this template to be used actively in the"
" wizard that generate Chart of Accounts from templates, this is useful when "
"you want to generate accounts of this template only when loading its child "
"template."
msgstr ""
"تعيين هذا إلى خاطئ إذا كنت لا تريد هذا القالب أن يستخدم بفاعلية في المعالج "
"الذى ينشئ الدليل المحاسبي من القوالب,و هذا يفيد عندما تريد فقط انشاء حسابات "
"لهذا القالب عندما تقوم بتحميل قالبه الصغير."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Set to Draft"
msgstr "تعيين كمسودة"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_config
#: model:ir.ui.menu,name:account.menu_account_config
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Settings"
msgstr "إعدادات"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Setup"
msgstr "الإعداد"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_account_setup_bar_closed
msgid "Setup Bar Closed"
msgstr "اغلاق شريط الاعدادات"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_code
msgid "Short Code"
msgstr "الكود المختصر"

#. module: account
#: model:res.groups,name:account.group_account_user
msgid "Show Full Accounting Features"
msgstr "عرض كامل خصائص الحسابات"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Show active taxes"
msgstr "عرض الضرائب النشطة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"عرض كافة السجلات التي تاريخ الإجراء التالي فيها يسبق تاريخ اليوم الجاري"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Show inactive taxes"
msgstr "عرض الضرائب غير النشطة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_show_on_dashboard
msgid "Show journal on dashboard"
msgstr "اظهار دفتر اليومية في لوحة المعلومات"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_report_sign
msgid "Sign on Reports"
msgstr "التوقيع على التقارير"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Simplify your cash, checks, or credit cards deposits with an integrated "
"batch payment function."
msgstr ""
"تبسيط النقدية, الشيكات، أو ودائع بطاقات الائتمان الخاصة بك عن طريق خاصية "
"سداد دفعة متكاملة"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:107
#, python-format
msgid "Skip"
msgstr "تجاوز"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Smallest Text"
msgstr "نص أصغر"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_sortby
msgid "Sort by"
msgstr "ترتيب حسب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_origin
#: model:ir.model.fields,field_description:account.field_account_invoice_origin
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Source Document"
msgstr "المستند المصدر"

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding_strategy
msgid ""
"Specify which way will be used to round the invoice amount to the rounding "
"precision"
msgstr "اختيار أي طريقة ستستخدم لتقريب كمية الفاتورة إلى دقة التقريب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_date_from
#: model:ir.model.fields,field_description:account.field_account_balance_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_account_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_date_from
#: model:ir.model.fields,field_description:account.field_account_common_report_date_from
#: model:ir.model.fields,field_description:account.field_account_print_journal_date_from
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_date_from
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_date_from
#: model:ir.model.fields,field_description:account.field_account_tax_report_date_from
#: model:ir.model.fields,field_description:account.field_accounting_report_date_from
#: model:ir.model.fields,field_description:account.field_accounting_report_date_from_cmp
msgid "Start Date"
msgstr "تاريخ البدء"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_balance_start
msgid "Starting Balance"
msgstr "الرصيد الافتتاحي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox_start_id
msgid "Starting Cashbox"
msgstr "صندوق الكاشير الافتتاحي "

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "State"
msgstr "المحافظة"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_parent_state
msgid "State of the parent account.move"
msgstr "حالة الحساب الأصلي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_statement_id
#: model:ir.model.fields,field_description:account.field_account_move_line_statement_id
msgid "Statement"
msgstr "كشف الحساب"

#. module: account
#: code:addons/account/models/account_bank_statement.py:245
#, python-format
msgid "Statement %s confirmed, journal items were created."
msgstr "كشف الحساب %s مؤكد, تم انشاء عناصر اليومية"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_line_form
msgid "Statement Line"
msgstr "بند كشف الحساب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_ids
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_line_tree
msgid "Statement lines"
msgstr "بنود كشف الحساب"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_tree
msgid "Statements"
msgstr "كشوفات الحسابات"

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "States"
msgstr "المحافظات"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_states_count
msgid "States Count"
msgstr "عدد الحالات"

#. module: account
#: code:addons/account/controllers/portal.py:73
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line_state
#: model:ir.model.fields,field_description:account.field_account_bank_statement_state
#: model:ir.model.fields,field_description:account.field_account_invoice_state
#: model:ir.model.fields,field_description:account.field_account_move_state
#: model:ir.model.fields,field_description:account.field_account_payment_state
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Status"
msgstr "الحالة "

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line_subtotal
msgid "Subtotal"
msgstr "الإجمالي الفرعي"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Supplier Payments"
msgstr "مدفوعات الموردين"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "TOTAL ASSETS"
msgstr "مجموع الأصول"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "TOTAL EQUITY"
msgstr "مجموع الاسهم"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag_ids
#: model:ir.model.fields,field_description:account.field_account_tax_tag_ids
#: model:ir.ui.view,arch_db:account.account_tag_view_form
msgid "Tags"
msgstr "الوسوم"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Tags for Multidimensional Analytics"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_cash_box_out
#: model:ir.ui.view,arch_db:account.cash_box_out_form
msgid "Take Money Out"
msgstr "إخراج المال"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_aged_trial_balance_target_move
#: model:ir.model.fields,field_description:account.field_account_balance_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_account_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_partner_report_target_move
#: model:ir.model.fields,field_description:account.field_account_common_report_target_move
#: model:ir.model.fields,field_description:account.field_account_print_journal_target_move
#: model:ir.model.fields,field_description:account.field_account_report_general_ledger_target_move
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_target_move
#: model:ir.model.fields,field_description:account.field_account_tax_report_target_move
#: model:ir.model.fields,field_description:account.field_accounting_report_target_move
msgid "Target Moves"
msgstr "تحركات الهدف"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:174
#: model:ir.model,name:account.model_account_tax
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_tax
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_tax_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template_tax_id
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_tax
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model:ir.ui.view,arch_db:account.view_account_tax_search
#, python-format
msgid "Tax"
msgstr "الضريبة"

#. module: account
#: code:addons/account/models/chart_template.py:842
#: code:addons/account/models/chart_template.py:845
#, python-format
msgid "Tax %.2f%%"
msgstr "الضريبة %.2f%%"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_account_id
msgid "Tax Account"
msgstr "حساب الضريبة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_refund_account_id
msgid "Tax Account on Credit Notes"
msgstr "حساب الضرائب في إشعارات الخصم"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_refund_account_id
msgid "Tax Account on Refunds"
msgstr "حساب الضريبة على المرتجعات"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_tax_adjustment
#: model:ir.model.fields,field_description:account.field_account_tax_template_tax_adjustment
msgid "Tax Adjustment"
msgstr "تصحيح الضريبة"

#. module: account
#: model:ir.actions.act_window,name:account.tax_adjustments_form
#: model:ir.ui.menu,name:account.menu_action_tax_adjustment
msgid "Tax Adjustments"
msgstr "تصحيحات الضرائب"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Tax Amount"
msgstr "قيمة الضريبة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Tax Application"
msgstr "تطبيق الضريبة"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_tax_calculation_rounding_method
msgid "Tax Calculation Rounding Method"
msgstr "طريقة التقريب لحساب الضريبة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_tax_cash_basis_rec_id
msgid "Tax Cash Basis Entry of"
msgstr "ضرائب الأساس النقدي مدخلات ل"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_tax_cash_basis_journal_id
msgid "Tax Cash Basis Journal"
msgstr "الدفتر اليومية لضرائب الأساس النقدي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_amount_type
#: model:ir.model.fields,field_description:account.field_account_tax_template_amount_type
msgid "Tax Computation"
msgstr "توقع الضريبة"

#. module: account
#: model:ir.ui.view,arch_db:account.report_journal
msgid "Tax Declaration"
msgstr "الإعلان الضريبي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_name
msgid "Tax Description"
msgstr "وصف الضريبة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_tax_exigibility
#: model:ir.model.fields,field_description:account.field_account_tax_template_tax_exigibility
msgid "Tax Due"
msgstr "الضريبة المستحقة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_tax_group_id
#: model:ir.model.fields,field_description:account.field_account_tax_template_tax_group_id
msgid "Tax Group"
msgstr "تجميع الضريبة"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Tax ID"
msgstr "رقم الضرائب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_tax_line_ids
msgid "Tax Lines"
msgstr "بنود الضريبة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_tax_ids
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Tax Mapping"
msgstr "تفاصيل الضريبة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_name
#: model:ir.model.fields,field_description:account.field_account_tax_template_name
msgid "Tax Name"
msgstr "اسم الضريبة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_cash_basis_account
#: model:ir.model.fields,field_description:account.field_account_tax_template_cash_basis_account
msgid "Tax Received Account"
msgstr "ضرائب حساب الواردة"

#. module: account
#: model:ir.actions.report,name:account.action_report_account_tax
#: model:ir.model,name:account.model_account_tax_report
#: model:ir.ui.menu,name:account.menu_account_report
#: model:ir.ui.view,arch_db:account.report_tax
msgid "Tax Report"
msgstr "تقرير الضريبة"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_tax_report
msgid "Tax Reports"
msgstr "تقارير الضريبة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template_type_tax_use
#: model:ir.model.fields,field_description:account.field_account_tax_type_tax_use
msgid "Tax Scope"
msgstr "نطاق الضريبة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template_tax_src_id
msgid "Tax Source"
msgstr "مصدر الضريبة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Tax Template"
msgstr "قالب الضريبة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_tax_template_ids
msgid "Tax Template List"
msgstr "قائمة قالب الضريبة"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_tax_template_form
msgid "Tax Templates"
msgstr "قوالب الضريبة"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr "طريقة التقريب لحساب الضريبة"

#. module: account
#: sql_constraint:account.tax:0 sql_constraint:account.tax.template:0
msgid "Tax names must be unique !"
msgstr "أسماء الضريبة يجب أن تكون فريدة !"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_tax_src_id
msgid "Tax on Product"
msgstr "الضريبة على المنتج"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_tax_dest_id
msgid "Tax to Apply"
msgstr "ضريبة للتطبيق"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "TaxCloud"
msgstr "TaxCloud"

#. module: account
#: selection:account.account.tag,applicability:0
#: model:account.tax.group,name:account.tax_group_taxes
#: model:ir.actions.act_window,name:account.action_tax_form
#: model:ir.model.fields,field_description:account.field_account_invoice_line_invoice_line_tax_ids
#: model:ir.model.fields,field_description:account.field_account_move_line_tax_ids
#: model:ir.ui.menu,name:account.menu_action_tax_form
#: model:ir.ui.view,arch_db:account.account_planner
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model:ir.ui.view,arch_db:account.view_move_line_form
msgid "Taxes"
msgstr "ضرائب"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_tax
msgid "Taxes Fiscal Position"
msgstr "ضرائب الوضع المالى"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_template_form
msgid "Taxes Mapping"
msgstr "تفاصيل الضرائب"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Taxes used in Purchases"
msgstr "الضرائب المستخدمة في الشراء"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Taxes used in Sales"
msgstr "الضرائب المستخدمة في البيع"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Taxes, fiscal positions, chart of accounts &amp; legal statements for your "
"country"
msgstr ""
"الضرائب، والمواقف الضريبية، والدليل المحاسبي، والتقارير المحاسبية القانونية "
"التابعة لدولتك"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_balance
msgid ""
"Technical field holding the debit - credit in order to open meaningful graph"
" views from reports"
msgstr ""
"الحقل التقني يحتفظ بالدائن - المدين لكى يفتح رسم بياني ذات معنى يعرض من "
"التقارير"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_balance_cash_basis
msgid ""
"Technical field holding the debit_cash_basis - credit_cash_basis in order to"
" open meaningful graph views from reports"
msgstr ""
"الحقل التقني يحتفظ بالديون على حركة النقدية - الرصيد الدائن على حركة النقدية"
" لكى يفتح رسم بياني ذات معنى يعرض من التقارير"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_move_name
msgid ""
"Technical field holding the number given to the invoice, automatically set "
"when the invoice is validated then stored to set the same number again if "
"the invoice is cancelled, set to draft and re-validated."
msgstr ""
"الحقل التقني يحتفظ بالرقم المعطى للفاتورة, يعين تلقائيا عندما تُعتمد "
"الفاتورة ثم تُخزن لتعيين نفس الرقم مرة أخرى إذا اُلغيت الفاتورة,  يتم "
"التعيين للمسودة و إعادة اعتمادها."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_move_name
#: model:ir.model.fields,help:account.field_account_payment_move_name
msgid ""
"Technical field holding the number given to the journal entry, automatically"
" set when the statement line is reconciled then stored to set the same "
"number again if the line is cancelled, set to draft and re-processed again."
msgstr ""
"الحقل التقني يحتفظ بالرقم المعطى لقيد اليومية, يعين تلقائيا عندما يتم تسوية "
"سطر كشقف الحساب ثم يخزن لتعيين نفس العدد مرة أخرى إذا تم إلغاء السطر، تعيين "
"إلى مسودة وإعادة معالجتها مرة أخرى."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_bank_data_done
msgid "Technical field holding the status of the bank setup step."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_coa_done
msgid "Technical field holding the status of the chart of account setup step."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_company_data_done
msgid "Technical field holding the status of the company setup step."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_fy_data_done
msgid "Technical field holding the status of the financial year setup step."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_register_payments_multi
msgid ""
"Technical field indicating if the user selected invoices from multiple "
"partners or from different types."
msgstr ""
"حقل تقني يوضح ما إن كان المستخدم قام باختيار فواتير من شركاء متعددين أم من "
"أنواع مختلفة."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_account_setup_bar_closed
msgid ""
"Technical field set to True when setup bar has been closed by the user."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_refund_refund_only
msgid ""
"Technical field to hide filter_refund in case invoice is partially paid"
msgstr "الحقل التقني لإخفاء فلتر المرتجع في حالة الدفع الجزئي للفاتورة "

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_journal_type
#: model:ir.model.fields,help:account.field_account_payment_has_invoices
msgid "Technical field used for usability purposes"
msgstr "الحقل التقني يُستخدم لأغراض الاستخدام "

#. module: account
#: model:ir.model.fields,help:account.field_account_move_matched_percentage
msgid "Technical field used in cash basis method"
msgstr "الحقل التقني يُستخدم في طريقة الحركة النقدية"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_account_setup_bank_data_done
msgid "Technical field used in the special view for the setup bar step."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_payment_method_code
#: model:ir.model.fields,help:account.field_account_payment_payment_method_code
#: model:ir.model.fields,help:account.field_account_register_payments_payment_method_code
msgid ""
"Technical field used to adapt the interface to the payment type selected."
msgstr "الحقل التقني يُستخدم لتهيئة الواجهة لنوع السداد المُختار"

#. module: account
#: model:ir.model.fields,help:account.field_account_partial_reconcile_max_date
msgid ""
"Technical field used to determine at which date this reconciliation needs to"
" be shown on the aged receivable/payable reports."
msgstr ""
"حقل تقني يُستخدم لتحديد التاريخ الذي يجب أن تُعرض به هذه التسوية على تقارير "
"الدائن/المدين القديمة."

#. module: account
#: model:ir.model.fields,help:account.field_account_abstract_payment_hide_payment_method
#: model:ir.model.fields,help:account.field_account_payment_hide_payment_method
#: model:ir.model.fields,help:account.field_account_register_payments_hide_payment_method
msgid ""
"Technical field used to hide the payment method if the selected journal has "
"only one available which is 'manual'"
msgstr ""
"الحقل التقني يُستخدم لإخفاء طريقة السداد إذا كان دفتر اليومية المُختار متاح "
"لديه واحدٌ فقط وهو \"يدوياً\""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_tax_cash_basis_rec_id
msgid ""
"Technical field used to keep track of the tax cash basis reconciliation. "
"This is needed when cancelling the source: it will post the inverse journal "
"entry to cancel that part too."
msgstr ""
"حقل تقني يُستخدم لتتبع تسوية الضرائب على حركة النقدية. وهذا مطلوب عند إلغاء "
"المصدر: سيقوم بترحيل قيد اليومية المعكوس لإلغاء ذلك الجزء أيضًا."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_tax_exigible
msgid ""
"Technical field used to mark a tax line as exigible in the vat report or not"
" (only exigible journal items are displayed). By default all new journal "
"items are directly exigible, but with the feature cash_basis on taxes, some "
"will become exigible only when the payment is recorded."
msgstr ""
"حقل تقني يُستخدم لتحديد ما إن كان البند الضريبي في تقرير ضريبة القيمة "
"المضافة قابلًا للتغير أم لا. افتراضيًا، تكون جميع العناصر اليومية قابلة "
"للتغير، لكن بتطبيق خاصية cash_basis على الضرائب، سيصبح بعضهم ملائمًا فقط عند"
" تسجيل المدفوعات."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_is_unaffected_earnings_line
msgid ""
"Tells whether or not this line belongs to an unaffected earnings account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_chart_template_id
msgid "Template"
msgstr "قالب"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_account_template
msgid "Template Account Fiscal Mapping"
msgstr "قالب تفاصيل الحساب المالى"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_tax_template
msgid "Template Tax Fiscal Position"
msgstr "قالب ضرائب الوضع المالى"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "قالب للوضع المالى"

#. module: account
#: model:ir.model,name:account.model_account_chart_template
msgid "Templates for Account Chart"
msgstr "قوالب للدليل المحاسبى"

#. module: account
#: model:ir.model,name:account.model_account_account_template
msgid "Templates for Accounts"
msgstr "قوالب للحسابات"

#. module: account
#: model:ir.model,name:account.model_account_tax_template
msgid "Templates for Taxes"
msgstr "قوالب للضرائب"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Term Type"
msgstr "نوع شرط السداد"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_ids
#: model:ir.ui.view,arch_db:account.view_payment_term_form
msgid "Terms"
msgstr "شروط السداد"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Terms &amp; Conditions"
msgstr "الشروط &amp; الأحكام"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "Terms and conditions..."
msgstr "الشروط والأحكام..."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "That's on average"
msgstr "هذا في المتوسط"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_internal_type
#: model:ir.model.fields,help:account.field_account_account_type_type
msgid ""
"The 'Internal Type' is used for features available on different types of "
"accounts: liquidity type is for cash or bank accounts, payable/receivable is"
" for vendor/customer accounts."
msgstr ""
"يستخدم النوع \"الداخلي\" للخصائص المتاحة على الأنواع المختلفة للحسابات: نوع "
"السيولة للحسابات النقدية أو البنكية, دائنة أو مدينة لحسابات المورد/العميل"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The Advisors have full access to the Accounting application, \n"
"                                        plus access to miscellaneous operations such as salary and asset management."
msgstr ""
"يمتلك المستشارين حق الدخول الكامل الى التطبيق المحاسبي,\n"
"         بالإضافة إلى الدخول إلى عمليات متنوعة مثل المرتبات و إدارة الأصول."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The Deposit Ticket module can also be used to settle credit card batch made "
"of multiple transactions."
msgstr ""
"موديول بطاقة الإيداع يمكن أن يُستخدم لتسوية دفعات بطاقة الائتمان الناتجة عن "
"المعاملات المتعددة."

#. module: account
#: code:addons/account/models/account_move.py:1209
#, python-format
msgid "The account %s (%s) is deprecated !"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:1021
#, python-format
msgid "The account %s (%s) is not marked as reconciliable !"
msgstr "الحساب %s (%s) لم يُحدد كقابل للتسوية !"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_bank_journal_id
msgid "The accounting journal corresponding to this bank account."
msgstr "المحاسبة اليومية المقابلة لهذا الحساب البنكي."

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_currency_exchange_journal_id
msgid ""
"The accounting journal where automatic exchange differences will be "
"registered"
msgstr "دفتر اليومية المحاسبي حيث تُسجل فروق أسعار الصرف التلقائية"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_amount_currency
#: model:ir.model.fields,help:account.field_account_move_line_amount_currency
msgid ""
"The amount expressed in an optional other currency if it is a multi-currency"
" entry."
msgstr "المبلغ مُعبر عنه بعملات اختيارية أخرى !ذا كان قيد متعدد العملات."

#. module: account
#: model:ir.model.fields,help:account.field_account_analytic_line_amount_currency
#: model:ir.model.fields,help:account.field_account_analytic_line_analytic_amount_currency
msgid ""
"The amount expressed in the related account currency if not equal to the "
"company one."
msgstr ""
"المبلغ مُعبر عنه بعملة الحساب المتصل به إذا لم يكن مساوى لحساب المؤسسة."

#. module: account
#: code:addons/account/models/account_move.py:508
#, python-format
msgid ""
"The amount expressed in the secondary currency must be positive when account"
" is debited and negative when account is credited."
msgstr ""
"المبلغ مُعبر عنه بالعملة الثانية يجب أن يكون موجب عندما يكون الحساب مدين و "
"سالب عندما يكون الحساب دائن."

#. module: account
#: code:addons/account/models/account.py:804
#, python-format
msgid ""
"The application scope of taxes in a group must be either the same as the "
"group or \"None\"."
msgstr "نطاق تطبيق الضرائب في مجموعة يجب أن يكون اما مثل المجموعة أو \"لا\"."

#. module: account
#: code:addons/account/models/account.py:452
#, python-format
msgid ""
"The bank account of a bank journal must belong to the same company (%s)."
msgstr "الحساب البنكي  ليومية البنك يجب أن ينتمى لنفس المؤسسة (%s)."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_statement_id
msgid "The bank statement used for bank reconciliation"
msgstr "كشف الحساب البنكي يُستخدم للتسوية "

#. module: account
#: code:addons/account/models/account_invoice.py:1165
#, python-format
msgid ""
"The cash rounding cannot be computed because the difference must be added on the biggest tax found and no tax are specified.\n"
"Please set up a tax or change the cash rounding method."
msgstr ""
"لا يمكن احتساب التقريب النقدي لأن الفرق يجب أن يضاف على أكبر ضريبة موجودة ولم يتم تحديد ضريبة.\n"
"برجاء قم بإعداد ضريبة أو غير طريقة التقريب النقدي."

#. module: account
#: model:ir.model.fields,help:account.field_res_company_chart_template_id
msgid "The chart template for the company (if any)"
msgstr "قالب الرسم البياني للمؤسسة (اذا وُجد) "

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
msgid "The closing balance is different than the computed one!"
msgstr "حساب الاغلاق مختلف عن الحساب المُتوقع."

#. module: account
#: sql_constraint:account.journal:0
msgid "The code and name of the journal must be unique per company !"
msgstr "كود و اسم دفتر اليومية يجب أن يكون مُميز لكل مؤسسة !"

#. module: account
#: sql_constraint:account.account:0
msgid "The code of the account must be unique per company !"
msgstr "كود الحساب يجب أن يكون مُميز لكل مؤسسة !"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_commercial_partner_id
msgid ""
"The commercial entity that will be used on Journal Entries for this invoice"
msgstr "الكيان التجاري الذى سوف يُستخدم في قيود اليومية للفاتورة"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_currency_id
msgid "The currency used to enter statement"
msgstr "العملة المُستخدمة لإدخال كشف الحساب"

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree1
msgid ""
"The discussions with your customer are automatically displayed at\n"
"                the bottom of each invoice."
msgstr ""
"المناقشة مع العميل الخاص بك قد تم عرضها تلقائياً \n"
"          أسفل كل فاتورة."

#. module: account
#: code:addons/account/models/account_bank_statement.py:191
#, python-format
msgid ""
"The ending balance is incorrect !\n"
"The expected balance (%s) is different from the computed one. (%s)"
msgstr ""
"رصيد الإقفال صحيح !\n"
"الرصيد المُتوقع (%s) مختلف عن المحسوب. (%s)"

#. module: account
#: model:ir.model.fields,help:account.field_product_category_property_account_expense_categ_id
msgid ""
"The expense is accounted for when a vendor bill is validated, except in "
"anglo-saxon accounting with perpetual inventory valuation in which case the "
"expense (Cost of Goods Sold account) is recognized at the customer invoice "
"validation."
msgstr ""
"يتم احتساب المصروفات عند اعتماد فاتورة مورد، فيما عدا عند استخدام نظام "
"المحاسبة الأنجلو-ساكسونية مع التقييم الدائم للمخزون، في هذه الحالة تُحتسب "
"المصروفات (تكلفة السلع المباعة) عند اعتماد فاتورة العميل."

#. module: account
#: model:ir.model.fields,help:account.field_product_product_property_account_expense_id
#: model:ir.model.fields,help:account.field_product_template_property_account_expense_id
msgid ""
"The expense is accounted for when a vendor bill is validated, except in "
"anglo-saxon accounting with perpetual inventory valuation in which case the "
"expense (Cost of Goods Sold account) is recognized at the customer invoice "
"validation. If the field is empty, it uses the one defined in the product "
"category."
msgstr ""

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The first step is to set up your company's information. This is mostly used "
"in printed business documents like orders and invoices."
msgstr ""
"الخطوة الاولى لتثبيت معلومات شركتك. يستخدم هذا عادة لطباعة مستندات عمل مثل "
"الاوامر والفواتير."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_account_position_id
#: model:ir.model.fields,help:account.field_res_users_property_account_position_id
msgid ""
"The fiscal position will determine taxes and accounts used for the partner."
msgstr "الوضع المالي سيُحدد الضرائب و الحسابات المُستخدمة للشريك. "

#. module: account
#: code:addons/account/models/account.py:456
#, python-format
msgid "The holder of a journal's bank account must be the company (%s)."
msgstr "مالك يوميات الحساب البنكي يجب أن يكون المؤسسة  (%s)."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_account_id
msgid "The income or expense account related to the selected product."
msgstr "حساب الايراد أو حساب المصروف مرتبط بالمنتج المُختار."

#. module: account
#: code:addons/account/models/account_payment.py:643
#, python-format
msgid "The journal %s does not have a sequence, please specify one."
msgstr "دفتر اليومية %s ليس له تسلسل, من فضلك حدد له واحد."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_code
msgid "The journal entries of this journal will be named using this prefix."
msgstr "قيود اليومية لدفتر اليومية هذا سوف تُسمى باستخدام هذه البادئة. "

#. module: account
#: model:ir.model.fields,help:account.field_account_opening_opening_move_id
#: model:ir.model.fields,help:account.field_res_company_account_opening_move_id
msgid ""
"The journal entry containing the initial balance of all this company's "
"accounts."
msgstr "قيد اليومية المحتوي الرصيد الابتدائي لجميع حسابات المؤسسة المُختارة."

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op_fiscalyear_last_day
#: model:ir.model.fields,help:account.field_account_financial_year_op_fiscalyear_last_month
msgid ""
"The last day of the month will be taken if the chosen day doesn't exist."
msgstr ""

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_form
msgid ""
"The last line's computation type should be \"Balance\" to ensure that the "
"whole amount will be allocated."
msgstr "نوع حساب البند الأخير يجب أن يكون \"رصيد\" للتأكيد بأن كل المبلغ سيُحدد."

#. module: account
#: code:addons/account/models/company.py:94
#, python-format
msgid "The lock date for advisors is irreversible and can't be removed."
msgstr "لا رجعة في تاريخ القفل للاستشاريين ولا يمكن حذفه."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_move_id
msgid "The move of this entry line."
msgstr "الحركة لبند القيد هذا."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"The multi-currency option allows you to send or receive invoices \n"
"                        in difference currencies, set up foreign bank accounts \n"
"                        and run reports on your foreign financial activities."
msgstr ""
"خيار تعدد العملات يتيح لك ارسال و استقبال الفواتير\n"
"            بعملات مختلفة, ثبت حسابات بنك أجنبي \n"
"            و قم بتشغيل تقارير حول الأنشطة المالية الأجنبية الخاصة بك."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_name
msgid "The name that will be used on account move lines"
msgstr "الاسم الذى سيُستخدم  على بنود الحركة المحاسبية"

#. module: account
#: code:addons/account/models/company.py:98
#, python-format
msgid ""
"The new lock date for advisors must be set after the previous lock date."
msgstr "يجب أن يكون تاريخ القفل الجديد للاستشاريين بعد تاريخ القفل السابق."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_refund_sequence_number_next
msgid "The next sequence number will be used for the next credit note."
msgstr "سوف يُستخدم الرقم التسلسلي التالي في إشعار الخصم التالي."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_sequence_number_next
msgid "The next sequence number will be used for the next invoice."
msgstr "سوف يُستخدم الرقم التسلسلي التالي في الفاتورة التالية."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_currency_id
#: model:ir.model.fields,help:account.field_account_move_line_currency_id
msgid "The optional other currency if it is a multi-currency entry."
msgstr "العملة الاختيارية الأخرى إذا كان القيد متعدد العملات."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_quantity
msgid ""
"The optional quantity expressed by this line, eg: number of product sold. "
"The quantity is not a legal requirement but is very useful for some reports."
msgstr ""
"الكمية الاختيارية المُعبر عنها بهذا البند, مثل: عدد المنتجات المُباعة. "
"الكمية ليست متطلب قانونى و لكنها مفيدة جداً لبعض التقارير. "

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_account_id
msgid "The partner account used for this invoice."
msgstr "حساب الشريك المُستخدم لهذه الفاتورة."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_has_unreconciled_entries
#: model:ir.model.fields,help:account.field_res_users_has_unreconciled_entries
msgid ""
"The partner has at least one unreconciled debit and credit since last time "
"the invoices & payments matching was performed."
msgstr ""
"الشريك لديه حساب مدين و دائن واحد على الأقل لم يُسوى منذ أخر تطابق تم بين "
"الفواتير و المدفوعات."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_reference
msgid "The partner reference of this invoice."
msgstr "رقم إشارة الشريك لهذه الفاتورة."

#. module: account
#: code:addons/account/models/account.py:522
#, python-format
msgid ""
"The partners of the journal's company and the related bank account mismatch."
msgstr "شركاء مؤسسة دفتر اليومية والحساب البنكي المرتبط غير متطابقين."

#. module: account
#: code:addons/account/models/account_payment.py:60
#, python-format
msgid "The payment amount cannot be negative."
msgstr "يجب ألا يكون المبلغ المدفوع رقمًا سالبًا."

#. module: account
#: code:addons/account/models/account_payment.py:487
#, python-format
msgid "The payment cannot be processed because the invoice is not open!"
msgstr "لا يمكن إتمام السداد لأن الفاتورة ليست مفتوحة!"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_amount_residual_currency
msgid ""
"The residual amount on a journal item expressed in its currency (possibly "
"not the company currency)."
msgstr ""
"المبلغ المتبقي لعنصر اليومية مُعبر عنه بعملته (من الممكن ألا تكون عملة "
"المؤسسة) "

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_amount_residual
msgid ""
"The residual amount on a journal item expressed in the company currency."
msgstr "المبلغ المتبقي لعنصر اليومية مُعبر عنه بعملة المؤسسة."

#. module: account
#: code:addons/account/models/account_move.py:493
#, python-format
msgid ""
"The selected account of your Journal Entry forces to provide a secondary "
"currency. You should remove the secondary currency on the account."
msgstr ""
"الحساب المحدد لقيد اليومية يجبرك على توفير عمله ثانوية. يجب عليك حذف العمله "
"الثانوية من الحساب."

#. module: account
#: code:addons/account/models/account_invoice.py:1612
#, python-format
msgid ""
"The selected unit of measure is not compatible with the unit of measure of "
"the product."
msgstr "وحدة القياس المُختارة غير متوافقة مع وحدة قياس المنتج"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_sequence
#: model:ir.model.fields,help:account.field_account_tax_template_sequence
msgid ""
"The sequence field is used to define order in which the tax lines are "
"applied."
msgstr "حقل التسلسل يُستخدم لتعريف الترتيب الذى سيُطبق به الضريبة. "

#. module: account
#: code:addons/account/models/account_payment.py:645
#, python-format
msgid "The sequence of journal %s is deactivated."
msgstr "تسلسل دفتر اليومية %s غير مُفعل."

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding_rounding_method
msgid "The tie-breaking rule used for float rounding operations"
msgstr "قاعدة التحكيم المستخدمة في عمليات تقريب الأرقام غير الصحيحة"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_my_invoices
msgid "There are currently no invoices and payments for your account."
msgstr "لا يوجد حاليا اي فواتير أو دفعات متعلقة بحسابك"

#. module: account
#: code:addons/account/models/company.py:178
#, python-format
msgid ""
"There are still unposted entries in the period you want to lock. You should "
"either post or delete them."
msgstr ""
"لا زالت هنالك قيود غير مرحلة في الفترة التي تريد إقفالها. يجب عليك اما ترحيل"
" او حذف تلك القيود"

#. module: account
#: code:addons/account/models/account_bank_statement.py:179
#, python-format
msgid ""
"There is no account defined on the journal %s for %s involved in a cash "
"difference."
msgstr "لا يوجد حساب مُعرف في دفتر اليومية %s ل %s يشارك في فرق النقدية."

#. module: account
#: code:addons/account/wizard/account_validate_account_move.py:18
#, python-format
msgid "There is no journal items in draft state to post."
msgstr "لا يوجد عناصر يومية في حالة المسودة لتأجيلها."

#. module: account
#: code:addons/account/models/account_move.py:1744
#, python-format
msgid ""
"There is no tax cash basis journal defined for this company: \"%s\" \n"
"Configure it in Accounting/Configuration/Settings"
msgstr ""
"لا يوجد أساس نقدي ضريبي تم تعريفه لهذه الشركة: \"%s\"\n"
"لضبطه في المحاسبة / الضبط/ إعدادات"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:46
#, python-format
msgid "There is nothing to reconcile."
msgstr "لا يوجد شيء للتسوية."

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_error
msgid "There was an error processing this page."
msgstr "حدثت مشكلة أثناء معالجة هذه الصفحة."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "These taxes are set in any new product created."
msgstr "يتم إضافة هذه الضرائب عند إضافة أي منتج."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_user_type_id
msgid ""
"These types are defined according to your country. The type contains more "
"information about the account and its specificities."
msgstr ""
"هذه الأنواع تُعرف وفقاً لبلدك. يحتوى النوع على معلومات أكثر عن الحساب و "
"خواصه."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "These users handle billing specifically."
msgstr "يتعامل هؤلاء المستخدمين مع فواتير على وجه التحديد."

#. module: account
#: code:addons/account/models/account_invoice.py:1337
#, python-format
msgid ""
"This %s has been created from: <a href=# data-oe-model=account.invoice data-"
"oe-id=%d>%s</a>"
msgstr ""
"هذه %s تم انشائها من : <a href=# data-oe-model=account.invoice data-oe-"
"id=%d>%s</a>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "This Month"
msgstr "هذا الشهر"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:111
#, python-format
msgid "This Week"
msgstr "هذا الأسبوع"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "This Year"
msgstr "هذه السنة"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_account_payable_id
#: model:ir.model.fields,help:account.field_res_users_property_account_payable_id
msgid ""
"This account will be used instead of the default one as the payable account "
"for the current partner"
msgstr ""
"سيُستخدم هذا الحساب بدلاً من الحساب الافتراضي كحساب دائن للشريك الحالي"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_account_receivable_id
#: model:ir.model.fields,help:account.field_res_users_property_account_receivable_id
msgid ""
"This account will be used instead of the default one as the receivable "
"account for the current partner"
msgstr ""
"سيُستخدم هذا الحساب بدلاً من الحساب الافتراضي كحساب مدين للشريك الحالي"

#. module: account
#: model:ir.model.fields,help:account.field_product_category_property_account_income_categ_id
msgid "This account will be used when validating a customer invoice."
msgstr "سيتم استخدام هذا الحساب عند مراجعة وتأكيد فاتورة العميل"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows accountants to manage analytic and crossovered budgets. Once the"
" master budgets and the budgets are defined, the project managers can set "
"the planned amount on each analytic account."
msgstr ""
"يسمح هذا للمحاسبين بإدارة الميزانيات التحليلية والمتقاطعة. بمجرد تحديد "
"الميزانيات الرئيسية، يمكن لمديري المشروع تحديد الكمية المخطط لها في كل حساب "
"تحليلي."

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_module_account_batch_deposit
msgid ""
"This allows you to group received checks before you deposit them to the bank.\n"
"-This installs the module account_batch_deposit."
msgstr ""
"يسمح لك هذا بتجميع الشيكات المُستلمة قبل ايداعهم بالبنك\n"
"-يثبت هذا موديول إيداع دفعة_حساب."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows you to manage the assets owned by a company or a person. It "
"keeps track of the depreciation occurred on those assets, and creates "
"account move for those depreciation lines."
msgstr ""
"يسمح لك هذا بإدارة الأصول التي تملكها مؤسسة أو شخص. يتابع استهلاك هذه الأصول"
" وينشئ بحسابك بنود حركة حسب هذا الاستهلاك."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows you to manage the revenue recognition on selling products. It "
"keeps track of the installments occurred on those revenue recognitions, and "
"creates account moves for those installment lines."
msgstr ""
"يسمح لك هذا بإدارة إثباتات إيراد بيع المنتجات. يتابع التحركات الحادثة على "
"تلك الإثباتات، وينفذ حركات محاسبية لبنود تلك التحركات."

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template_complete_tax_set
msgid ""
"This boolean helps you to choose if you want to propose to the user to "
"encode the sale and purchase rates or choose from list of taxes. This last "
"choice assumes that the set of tax defined on this template is complete"
msgstr ""
"تساعدك هذة القيمة المنطقية على اختيار اذا كنت تريد الاقتراح على المستخدم "
"ترميز معدلات المبيعات و المشتريات  أو الاختيار من قائمة الضرائب. هذا الخيار "
"الأخير يفترض أن مجموعة الضرائب المُعرفة للقالب المُختار انتهت "

#. module: account
#: model:ir.model.fields,help:account.field_wizard_multi_charts_accounts_complete_tax_set
msgid ""
"This boolean helps you to choose if you want to propose to the user to "
"encode the sales and purchase rates or use the usual m2o fields. This last "
"choice assumes that the set of tax defined for the chosen template is "
"complete"
msgstr ""
"تساعدك هذة القيمة المنطقية على اختيار اذا كنت تريد الاقتراح على المستخدم "
"ترميز معدلات المبيعات و المشتريات  أو استخدام حقول m2o المعتادة. هذا الخيار "
"الأخير يفترض أن مجموعة الضرائب المُعرفة للقالب المُختار انتهت "

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "This feature is useful if you issue a high amounts of invoices."
msgstr "هذه الخاصية مفيدة إذا أصدرت عددًا كبيرًا من الفواتير."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_refund_sequence_id
msgid ""
"This field contains the information related to the numbering of the credit "
"note entries of this journal."
msgstr ""
"يحتوى هذا الحقل على المعلومات المرتبطة بترقيم إشعارات الخصم لدفتر اليومية "
"المُختار."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_sequence_id
msgid ""
"This field contains the information related to the numbering of the journal "
"entries of this journal."
msgstr ""
"هذا الحقل يحتوى على معلومات متصلة بترقيم قيود اليومية لدفتر اليومية هذا. "

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model_journal_id
#: model:ir.model.fields,help:account.field_account_reconcile_model_second_journal_id
msgid "This field is ignored in a bank statement reconciliation."
msgstr "يهمل هذا الحقل في تسوية كشف الحساب البنكي."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_date_maturity
msgid ""
"This field is used for payable and receivable journal entries. You can put "
"the limit date for the payment of this line."
msgstr ""
"يُستخدم هذا الحقل للقيود الدائنة و المدينة اليومية. يمكنك وضع حد زمني لدفع "
"هذا البند."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_partner_name
msgid ""
"This field is used to record the third party name when importing bank "
"statement in electronic format, when the partner doesn't exist yet in the "
"database (or cannot be found)."
msgstr ""
"يستخدم هذا الحقل لتسجيل اسم الطرف الثالث عند استيراد كشف الحساب البنكي في "
"شكل الكترونى,  عندما يكون الشريك غير موجود بعد في قاعدة البيانات (أو لا يمكن"
" ايجاده)."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"This guide will help you get started with Odoo Accounting.\n"
"                        Once you're done, you'll benefit from:"
msgstr ""
"سيساعدك هذا الدليل على بداية استخدام أودو للمحاسبة.\n"
"                وبمجرد الانتهاء من ذلك، يمكنك الاستفادة من:"

#. module: account
#: model:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid ""
"This is the accounting dashboard. If you have not yet\n"
"                installed a chart of account, please install one first."
msgstr ""
"هذه هي لوحة المعلومات المحاسبية. إذا لم تقم حتى الان\n"
"          بتثبيت دليل محاسبي, من فضلك ثبت واحداً أولاً."

#. module: account
#: code:addons/account/models/account.py:494
#, python-format
msgid ""
"This journal already contains items, therefore you cannot modify its "
"company."
msgstr ""
"دفتر اليومية هذا يحتوى بالفعل على عناصر, لذلك لا يمكنك التعديل على مؤسسته."

#. module: account
#: code:addons/account/models/account.py:503
#, python-format
msgid ""
"This journal already contains items, therefore you cannot modify its short "
"name."
msgstr ""
"دفتر اليومية هذا يحتوى بالفعل على عناصر, لذلك لا يمكنك تعديل اسمه المُختصر."

#. module: account
#: model:ir.model.fields,help:account.field_accounting_report_label_filter
msgid ""
"This label will be displayed on report to show the balance computed for the "
"given comparison filter."
msgstr ""
"سيتم عرض هذه التسمية على التقرير لإظهار الرصيد المحسوب من قيم فلتر المقارنة "
"المعطى."

#. module: account
#: code:addons/account/models/account_payment.py:533
#, python-format
msgid ""
"This method should only be called to process a single invoice's payment."
msgstr "يجب أن تُستدعى هذه الطريقة لمعالجة نفقات فاتورة واحدة فقط."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:395
#, python-format
msgid ""
"This move's amount is higher than the transaction's amount. Click to "
"register a partial payment and keep the payment balance open."
msgstr ""
"مبلغ هذه الخطوة أعلى من مبلغ المعاملة. انقر لتسجيل دفعة جزئية والحفاظ على "
"رصيد الدفع مفتوحا."

#. module: account
#: model:ir.model.fields,help:account.field_accounting_report_debit_credit
msgid ""
"This option allows you to get more details about the way your balances are "
"computed. Because it is space consuming, we do not allow to use it while "
"doing a comparison."
msgstr ""
"هذا الخيار يسمح لك بلحصول على مزيد من التفاصيل حول الطريقة التي يتم بها "
"احتساب الأرصدة الخاصة بك . لأنها تستهلك مساحة كبيرة, فلا نسمح باستخدامها "
"أثناء القيام بالمقارنة."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template_chart_template_id
msgid ""
"This optional field allow you to link an account template to a specific "
"chart template that may differ from the one its root parent belongs to. This"
" allow you to define chart templates that extend another and complete it "
"with few new accounts (You don't need to define the whole structure that is "
"common to both several times)."
msgstr ""
"يسمح لك هذا الحقل الاختياري بربط قالب حساب بقالب رسم بياني محدد التي قد "
"تختلف عن الأصلية التي تنتمى لها. يسمح لك هذا بتعريف قوالب الرسم البياني "
"الممتد لأخر و تنهيها مع حسابات جديدة قليلة (لن تحتاج لتعريف البنية كلها "
"المشتركة عدة مرات)"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:35
#, python-format
msgid ""
"This page displays all the bank transactions that are to be reconciled and "
"provides with a neat interface to do so."
msgstr ""
"تعرض هذه الصفحة جميع المعاملات البنكية التي سيتم تسويتها و تقدمها بواجهة "
"انيقة للقيام بذلك."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:240
#, python-format
msgid "This payment is registered but not reconciled."
msgstr "هذا السداد مُسجل و لكن لم يتم تسويته."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_supplier_payment_term_id
#: model:ir.model.fields,help:account.field_res_users_property_supplier_payment_term_id
msgid ""
"This payment term will be used instead of the default one for purchase "
"orders and vendor bills"
msgstr ""
"شرط السداد هذا سيُستخدم بدلاً من الشرط الافتراضي لأوامر الشراء و فواتير "
"المورد. "

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_property_payment_term_id
#: model:ir.model.fields,help:account.field_res_users_property_payment_term_id
msgid ""
"This payment term will be used instead of the default one for sales orders "
"and customer invoices"
msgstr ""
"سيُستخدم شرط السداد هذا بدلًا من الشرط الافتراضي لإصدار أوامر المبيعات "
"وفواتير العملاء"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"This role is best suited for managing the day to day accounting operations:"
msgstr "هذا الدور هو الأمثل لإدارة العمليات المحاسبية يوماُ بيوم."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line_account_id
msgid ""
"This technical field can be used at the statement line creation/import time "
"in order to avoid the reconciliation process on it later on. The statement "
"line will simply create a counterpart on this account"
msgstr ""
"هذا الحقل التقني يمكن أن يُستخدم عند انشاء/استيراد بند كشف الحساب لكى نتجنب "
"عملية تسويته بعد ذلك. بند كشف الحساب سيُنشئ ببساطة القيد المناظر لهذا "
"الحساب. "

#. module: account
#: model:ir.actions.act_window,help:account.action_validate_account_move
msgid ""
"This wizard will validate all journal entries selected. Once journal entries"
" are validated, you can not update them anymore."
msgstr ""
"هذا المعالج سوف يعتمد كل قيود اليومية المُختارة. بمجرد اعتماد قيود اليومية, "
"لا يمكنك تحديثه بعد ذلك."

#. module: account
#: model:ir.actions.act_window,help:account.action_account_reconcile_model
msgid ""
"Those can be used to quickly create a journal items when reconciling\n"
"                a bank statement or an account."
msgstr ""
"يمكن استخدام هؤلاء لإنشاء عناصر يومية سريعاً عند تسوية\n"
"             كشف حساب بنكي أو حساب."

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings_account_hide_setup_bar
msgid "Tick if you wish to hide the setup bar on the dashboard"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:31
#, python-format
msgid "Tip: Hit CTRL-Enter to reconcile all the balanced items in the sheet."
msgstr "ملاحظة: إضغط CTRL-Enter لتسوية كل العناصر المتطابقة في الكشف."

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Title 2 (bold)"
msgstr "العنوان رقم 2 (خط سميك)"

#. module: account
#: selection:account.financial.report,style_overwrite:0
msgid "Title 3 (bold, smaller)"
msgstr "العنوان رقم 3 (خط سميك, أصغر)"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "To Invoice"
msgstr "إلى الفاتوره"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "To Pay"
msgstr "للدفع"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"To manage the tax applied when invoicing a Company, Odoo uses the concept of"
" Fiscal Position: they serve to automatically set the right tax and/or "
"account according to the customer country and state."
msgstr ""
"لإدارة الضريبة المُطبقة عند اصدار فاتورة مؤسسة, أودو يستخدم مفهوم الوضع "
"المالي: أنها تستخدم لتعيين الضريبة الصحيحة تلقائيا و/أو الحساب وفقا لدولة "
"العميل و محافظته."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
msgid "To pay"
msgstr "للسداد"

#. module: account
#: code:addons/account/models/account_move.py:1017
#, python-format
msgid "To reconcile the entries company should be the same for all entries!"
msgstr "لتسوية القيود يجب أن تكون نفس المؤسسة لكل القيود!"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "To use the <strong>multi-currency option:</strong>"
msgstr "لاستخدام <strong> خيار تعدد العملات: </strong>"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Today Activities"
msgstr "نشاطات اليوم"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_total
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model:ir.ui.view,arch_db:account.invoice_tree
#: model:ir.ui.view,arch_db:account.report_agedpartnerbalance
#: model:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox
msgid "Total"
msgstr "الإجمالي "

#. module: account
#: model:ir.ui.view,arch_db:account.view_move_tree
msgid "Total Amount"
msgstr "إجمالي المبلغ"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_lines_tree
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_tree
msgid "Total Credit"
msgstr "إجمالي المدين"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_opening_move_lines_tree
#: model:ir.ui.view,arch_db:account.view_move_form
#: model:ir.ui.view,arch_db:account.view_move_line_tree
msgid "Total Debit"
msgstr "إجمالي الدين"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_total_invoiced
#: model:ir.model.fields,field_description:account.field_res_users_total_invoiced
msgid "Total Invoiced"
msgstr "الإجمالي الفواتير الصادرة "

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_debit
#: model:ir.model.fields,field_description:account.field_res_users_debit
msgid "Total Payable"
msgstr "الاجمالى الدائن"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_credit
#: model:ir.model.fields,field_description:account.field_res_users_credit
msgid "Total Receivable"
msgstr "الإجمالي المدين"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_currency_residual
msgid "Total Residual"
msgstr "الإجمالي المتبقى"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report_price_total
#: model:ir.model.fields,field_description:account.field_account_invoice_report_user_currency_price_total
msgid "Total Without Tax"
msgstr "الإجمالي بدون الضريبة"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_price_subtotal_signed
msgid "Total amount in the currency of the company, negative for credit note."
msgstr "إجمالي النقدية للشركة، القيمة السالبة لملاحظة مدين"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_amount_total_company_signed
msgid ""
"Total amount in the currency of the company, negative for credit notes."
msgstr "إجمالي المبلغ بعملة المؤسسة, سالب لكل الملاحظات الدائنة."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_amount_total_signed
msgid ""
"Total amount in the currency of the invoice, negative for credit notes."
msgstr "إجمالي المبلغ بعملة الفاتورة, سالب لكل الملاحظات الدائنة."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_credit
#: model:ir.model.fields,help:account.field_res_users_credit
msgid "Total amount this customer owes you."
msgstr "إجمالي المبلغ المُدان به هذا العميل لك."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_price_total
msgid "Total amount with taxes"
msgstr "إجمالي المبلغ مع الضرائب"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_line_price_subtotal
msgid "Total amount without taxes"
msgstr "المبلغ دون ضرائب"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner_debit
#: model:ir.model.fields,help:account.field_res_users_debit
msgid "Total amount you have to pay to this vendor."
msgstr "إجمالي المبلغ الذى يجب عليك دفعه لهذا المورد."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_total_company_signed
msgid "Total in Company Currency"
msgstr "الأجمالى بعملة المؤسسة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_total_signed
msgid "Total in Invoice Currency"
msgstr "الإجمالي بعملة الفاتورة"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_total_entry_encoding
msgid "Total of transaction lines."
msgstr "إجمالي بنود المعاملة."

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Track costs &amp; revenues by project, department, etc."
msgstr "تتبع التكاليف والإيرادات حسب المشروع والإدارة وما إلى ذلك."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:250
#: model:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#, python-format
msgid "Transaction"
msgstr "المعاملة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "Transactions"
msgstr "المعاملات"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_total_entry_encoding
msgid "Transactions Subtotal"
msgstr "الإجمالي الفرعي للمعاملات."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_transfer_account_id
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_transfer_account_id
msgid "Transfer Account"
msgstr "نقل الحساب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_destination_journal_id
msgid "Transfer To"
msgstr "النقل إلى"

#. module: account
#: code:addons/account/models/account_payment.py:357
#, python-format
msgid "Transfer account not defined on the company."
msgstr "نقل الحساب غير مُعرف في المؤسسة."

#. module: account
#: code:addons/account/models/account_payment.py:618
#, python-format
msgid "Transfer from %s"
msgstr "النقل من %s"

#. module: account
#: code:addons/account/models/account_payment.py:699
#, python-format
msgid "Transfer to %s"
msgstr "النقل إلى %s"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Transfers"
msgstr "تحويلات"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_balance_menu
#: model:ir.actions.report,name:account.action_report_trial_balance
#: model:ir.ui.menu,name:account.menu_general_Balance_report
msgid "Trial Balance"
msgstr "ميزان المراجعة"

#. module: account
#: model:ir.model,name:account.model_account_balance_report
msgid "Trial Balance Report"
msgstr "تقرير ميزان المراجعة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template_user_type_id
#: model:ir.model.fields,field_description:account.field_account_account_type_type
#: model:ir.model.fields,field_description:account.field_account_account_user_type_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_journal_type
#: model:ir.model.fields,field_description:account.field_account_financial_report_type
#: model:ir.model.fields,field_description:account.field_account_invoice_line_invoice_type
#: model:ir.model.fields,field_description:account.field_account_invoice_report_type
#: model:ir.model.fields,field_description:account.field_account_invoice_type
#: model:ir.model.fields,field_description:account.field_account_journal_type
#: model:ir.model.fields,field_description:account.field_account_move_line_user_type_id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_value
msgid "Type"
msgstr "النوع"

#. module: account
#: selection:account.cash.rounding,rounding_method:0
msgid "UP"
msgstr "لأعلى"

#. module: account
#: selection:account.journal,bank_statements_source:0
msgid "Undefined Yet"
msgstr "غير محدد بعد"

#. module: account
#: code:addons/account/models/company.py:367
#, python-format
msgid "Undistributed Profits/Losses"
msgstr "الأرباح/الخسائر غير الموزعة"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:392
#, python-format
msgid "Undo the partial reconciliation."
msgstr "الرجوع عن التسوية الجزئية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_line_price_unit
#: model:ir.ui.view,arch_db:account.portal_invoice_report
#: model:ir.ui.view,arch_db:account.report_invoice_document
msgid "Unit Price"
msgstr "سعر الوحده "

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line_product_uom_id
#: model:ir.model.fields,field_description:account.field_account_invoice_line_uom_id
#: model:ir.model.fields,field_description:account.field_account_move_line_product_uom_id
msgid "Unit of Measure"
msgstr "وحده القياس "

#. module: account
#: code:addons/account/report/account_aged_partner_balance.py:194
#, python-format
msgid "Unknown Partner"
msgstr "شريك غير معروف"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Unless you are starting a new business, you probably have a list of "
"customers and vendors you'd like to import."
msgstr ""
"ربما لديك قائمة من العملاء و الموردين تحب أن تستوردهم, إلا إذا كنت تبدأ عمل "
"تجارى جديد."

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
#: model:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model:ir.ui.view,arch_db:account.setup_view_company_form
msgid "Unmark as done"
msgstr "إلغاء التحديد كمنتهي"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_journal_2_account_invoice_opened
msgid "Unpaid Invoices"
msgstr "فواتير غير مدفوعة"

#. module: account
#: selection:account.move,state:0
#: model:ir.ui.view,arch_db:account.view_account_move_filter
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unposted"
msgstr "غير مُؤجل "

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Unposted Journal Entries"
msgstr "قيود يومية غير مؤجلة"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unposted Journal Items"
msgstr "عناصر يومية غير مؤجلة"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:81
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
#, python-format
msgid "Unreconcile"
msgstr "بلا تسوية"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_unreconcile
msgid "Unreconcile Entries"
msgstr "قيود بلا تسوية"

#. module: account
#: model:ir.ui.view,arch_db:account.account_unreconcile_view
msgid "Unreconcile Transactions"
msgstr "معاملات بلا تسوية"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unreconciled"
msgstr "بلا تسوية"

#. module: account
#: model:ir.actions.act_window,name:account.act_account_acount_move_line_open_unreconciled
msgid "Unreconciled Entries"
msgstr "قيود بلا تسوية"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_untaxed
msgid "Untaxed Amount"
msgstr "المبلغ بدون ضرائب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_amount_untaxed_signed
msgid "Untaxed Amount in Company Currency"
msgstr "الإجمالي بدون الضرائب بعملة الشركة"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Update exchange rates automatically"
msgstr "قم بتحديث اسعار صرف العملات تلقائيا"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Use"
msgstr "استخدم"

#. module: account
#: model:ir.model.fields,field_description:account.field_wizard_multi_charts_accounts_use_anglo_saxon
msgid "Use Anglo-Saxon Accounting"
msgstr "استخدم حسابات Anglo-Saxon"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template_use_anglo_saxon
msgid "Use Anglo-Saxon accounting"
msgstr "استخدم حسابات Anglo-Saxon"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_tax_exigibility
msgid "Use Cash Basis"
msgstr "استخدام أساس الدفع النقدي"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_sepa_direct_debit
msgid "Use SEPA Direct Debit"
msgstr "استخدام الخصم المباشر بنظام SEPA"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal_journal_id
msgid "Use Specific Journal"
msgstr "استخدم دفتر يومية محدد"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company_anglo_saxon_accounting
msgid "Use anglo-saxon accounting"
msgstr "استخدم حسابات Anglo-Saxon"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_module_account_batch_deposit
msgid "Use batch deposit"
msgstr "استخدم دفعة الإيداعات"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Use depreciation boards, automate amortization entries"
msgstr "استخدام لوحات الإهلاك، تجعل قيود استهلاك الدين تلقائية"

#. module: account
#: model:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Use follow-up levels and schedule actions"
msgstr "استخدام مستويات متابعة وإجراءات الجدولة"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"Use in conjunction with contracts to calculate your monthly revenue for "
"multi-month contracts."
msgstr "يستخدم بالتزامن مع العقود لحساب عائدك الشهري للعقود المتعددة الشهور."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"Use this option if you want to cancel an invoice and create a new\n"
"                                one. The credit note will be created, validated and reconciled\n"
"                                with the current invoice. A new draft invoice will be created\n"
"                                so that you can edit it."
msgstr ""
"استخدم هذا الخيار إذا أردت إلغاء فاتورة وإنشاء\n"
"                                واحدة جديدة. سيتم إنشاء إشعار الخصم واعتماده وتسويته\n"
"                                من خلال الفاتورة الحالية. وسيتم إنشاء فاتورة مسودة جديدة\n"
"                                لكي تستطيع تعديلها."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"Use this option if you want to cancel an invoice you should not\n"
"                                have issued. The credit note will be created, validated and reconciled\n"
"                                with the invoice. You will not be able to modify the credit note."
msgstr ""
"استخدم هذا الخيار إذا كنت تريد إلغاء فاتورة لم تكن عليك\n"
"              إصدارها. سيتم انشاء ملاحظة الدائن, معتمده و مسواه\n"
"                 مع الفاتورة. لن تتمكن من تعديل ملاحظة الدائن."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_type_include_initial_balance
msgid ""
"Used in reports to know if we should consider journal items from the "
"beginning of time instead of from the fiscal year only. Account types that "
"should be reset to zero at each new fiscal year (like expenses, revenue..) "
"should not have this option set."
msgstr ""
"تُستخدم في التقارير لمعرفة ما إذا كان يجب علينا أخذ عناصر اليومية في "
"الاعتبار  من البداية بدلاً من السنة المالية فقط. أنواع الحساب التي يجب أن "
"ترجع إلى وضع الصفر لكل سنة مالية(مثل المصروفات, الربح..) لا يجب تفعيل هذا "
"الخيار."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_reference
msgid ""
"Used to hold the reference of the external mean that created this statement "
"(name of imported file, reference of online synchronization...)"
msgstr ""
"يستخدم لحفظ المرجع الخارجي يعنى انشاء هذا البيان (اسم الملف المستوردة، إشارة"
" التزامن على الانترنت ...)"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_sequence
msgid "Used to order Journals in the dashboard view"
msgstr "تستخدم لترتيب دفاتر اليومية في طريقة عرض لوحة المعلومات"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_loss_account_id
msgid ""
"Used to register a loss when the ending balance of a cash register differs "
"from what the system computes"
msgstr ""
"يستخدم لتسجيل خسارة عندما يكون رصيد الإغلاق لصندوق كاشير مختلف عما حسبه "
"النظام."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_profit_account_id
msgid ""
"Used to register a profit when the ending balance of a cash register differs"
" from what the system computes"
msgstr ""
"يستخدم لتسجيل ربح عندما يكون رصيد الإغلاق لصندوق كاشير مختلف عما حسبه "
"النظام."

#. module: account
#: model:ir.model.fields,help:account.field_account_analytic_line_company_currency_id
#: model:ir.model.fields,help:account.field_account_bank_statement_line_journal_currency_id
#: model:ir.model.fields,help:account.field_account_move_line_company_currency_id
#: model:ir.model.fields,help:account.field_account_partial_reconcile_company_currency_id
#: model:ir.model.fields,help:account.field_res_partner_currency_id
#: model:ir.model.fields,help:account.field_res_users_currency_id
msgid "Utility field to express amount currency"
msgstr "مستوى الفائدة للتعبير عن عملة المبلغ"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_vat_required
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_vat_required
msgid "VAT required"
msgstr "مطلوب VAT "

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_report
msgid "VAT:"
msgstr "ضريبة القيمة المضافة:"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:72
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.view_account_payment_from_invoices
#: model:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
#, python-format
msgid "Validate"
msgstr "اعتماد "

#. module: account
#: model:ir.model,name:account.model_validate_account_move
msgid "Validate Account Move"
msgstr "اعتماد حركة محاسبية"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Validate purchase orders and control vendor bills by departments."
msgstr "اعتماد طلبات الشراء و التحكم في فواتير المورد بالإدارات."

#. module: account
#: selection:account.bank.statement,state:0
#: model:mail.message.subtype,name:account.mt_invoice_validated
msgid "Validated"
msgstr "تم الإعتماد"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line_value_amount
msgid "Value"
msgstr "القيمة"

#. module: account
#: selection:account.abstract.payment,partner_type:0
#: selection:account.payment,partner_type:0
#: selection:account.register.payments,partner_type:0
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_tree
#: model:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model:ir.ui.view,arch_db:account.view_account_supplier_payment_tree
msgid "Vendor"
msgstr "المورد"

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_invoice.py:443
#: code:addons/account/models/account_invoice.py:1215
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Vendor Bill"
msgstr "فاتورة المورد"

#. module: account
#: code:addons/account/models/account_invoice.py:444
#, python-format
msgid "Vendor Bill - %s"
msgstr "فاتورة المورد - %s"

#. module: account
#: code:addons/account/models/chart_template.py:194
#: model:ir.actions.act_window,name:account.action_invoice_tree2
#: model:ir.ui.menu,name:account.menu_action_invoice_tree2
#: model:ir.ui.view,arch_db:account.account_planner
#, python-format
msgid "Vendor Bills"
msgstr "فواتير المورد "

#. module: account
#: selection:account.invoice,type:0 selection:account.invoice.report,type:0
#: code:addons/account/models/account_invoice.py:445
#: code:addons/account/models/account_payment.py:680
#: model:ir.ui.view,arch_db:account.portal_invoice_page
#: model:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Vendor Credit Note"
msgstr "إشعار خصم المورد"

#. module: account
#: code:addons/account/models/account_invoice.py:446
#, python-format
msgid "Vendor Credit Note - %s"
msgstr "إشعار خصم المورد - %s"

#. module: account
#: model:ir.actions.act_window,name:account.action_invoice_in_refund
#: model:ir.ui.menu,name:account.menu_action_invoice_in_refund
msgid "Vendor Credit Notes"
msgstr ""

#. module: account
#: code:addons/account/models/account_invoice.py:1217
#, python-format
msgid "Vendor Credit note"
msgstr "إشعار خصم المورد"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Vendor Flow"
msgstr "تدفق المورد"

#. module: account
#: code:addons/account/models/account_payment.py:682
#, python-format
msgid "Vendor Payment"
msgstr "سداد المورد"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner_property_supplier_payment_term_id
#: model:ir.model.fields,field_description:account.field_res_users_property_supplier_payment_term_id
msgid "Vendor Payment Terms"
msgstr "شروط السداد كمورد"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_reference
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "Vendor Reference"
msgstr "رقم إشارة المورد"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_product_supplier_taxes_id
#: model:ir.model.fields,field_description:account.field_product_template_supplier_taxes_id
msgid "Vendor Taxes"
msgstr "ضرائب البائع"

#. module: account
#: model:ir.ui.menu,name:account.menu_account_supplier
msgid "Vendors"
msgstr "الموردين"

#. module: account
#: selection:account.financial.report,type:0
msgid "View"
msgstr "مشاهدة"

#. module: account
#: selection:res.partner,invoice_warn:0
msgid "Warning"
msgstr "تحذير"

#. module: account
#: code:addons/account/models/account_invoice.py:662
#, python-format
msgid "Warning for %s"
msgstr "تحذير لـ %s"

#. module: account
#: model:ir.ui.view,arch_db:account.partner_view_buttons
msgid "Warning on the Invoice"
msgstr "تحذير على الفاتورة"

#. module: account
#: code:addons/account/models/account_invoice.py:1554
#: code:addons/account/models/account_invoice.py:1611
#, python-format
msgid "Warning!"
msgstr "تحذير!"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings_group_warning_account
msgid "Warnings"
msgstr "التحذيرات"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"We can handle the whole import process\n"
"                                        for you: simply send your Odoo project\n"
"                                        manager a CSV file containing all your\n"
"                                        data."
msgstr ""
"يمكننا التعامل مع عملية الاستيراد كلها\n"
"         لأجلك: ببساطة ارسل ملف CSV مدير مشروع أودو الخاص بك \n"
"         يحتوى على جميع بياناتك."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"We can handle the whole import process\n"
"                                        for you: simply send your Odoo project\n"
"                                        manager a CSV file containing all your\n"
"                                        products."
msgstr ""
"يمكننا التعامل مع عملية الاستيراد كلها\n"
"         لأجلك: ببساطة ارسل ملف CSV مدير مشروع أودو الخاص بك \n"
"         يحتوى على جميع منتجاتك."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "We hope this tool helped you implement our accounting application."
msgstr "نتمنى أن تكون هذه الأداة قد ساعدتك لإنجاز تطبيقنا المحاسبي."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Welcome"
msgstr "أهلا بك"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid ""
"When inviting users, you will need to define which access rights they are allowed to have. \n"
"                        This is done by assigning a role to each user."
msgstr ""
"عند دعوة المستخدمين, ستحتاج لتعريف أي حقوق دخول مسموح بها لهم.\n"
"            يتم هذا بتحديد دور لكل مستخدم."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal_show_on_dashboard
msgid "Whether this journal should be displayed on the dashboard or not"
msgstr "ما إذا كان ينبغي عرض دفتر اليومية هذا على لوحة المعلومات أو  لا"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report_amount_currency
#: model:ir.model.fields,field_description:account.field_account_print_journal_amount_currency
#: model:ir.model.fields,field_description:account.field_account_report_partner_ledger_amount_currency
msgid "With Currency"
msgstr "مع العملة"

#. module: account
#: selection:account.balance.report,display_account:0
#: selection:account.common.account.report,display_account:0
#: selection:account.report.general.ledger,display_account:0
msgid "With balance is not equal to 0"
msgstr "برصيد لا يساوي صفر"

#. module: account
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "With balance not equal to zero"
msgstr "برصيد لا يساوي صفر"

#. module: account
#: selection:account.balance.report,display_account:0
#: selection:account.common.account.report,display_account:0
#: selection:account.report.general.ledger,display_account:0
#: model:ir.ui.view,arch_db:account.report_generalledger
#: model:ir.ui.view,arch_db:account.report_trialbalance
msgid "With movements"
msgstr "مع حركات"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_search
msgid "With tax"
msgstr "مع الضريبة"

#. module: account
#: model:ir.model,name:account.model_tax_adjustments_wizard
msgid "Wizard for Tax Adjustments"
msgstr "Wizard تسوية الضرائب"

#. module: account
#: code:addons/account/models/account_move.py:1056
#: model:ir.ui.view,arch_db:account.view_account_move_line_reconcile_full
#, python-format
msgid "Write-Off"
msgstr "الشطب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_journal_id
msgid "Write-Off Journal"
msgstr "شطب دفتر اليومية"

#. module: account
#: model:ir.ui.view,arch_db:account.account_move_line_reconcile_writeoff
msgid "Write-Off Move"
msgstr "شطب الحركة"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff_writeoff_acc_id
msgid "Write-Off account"
msgstr "شطب الحساب"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line_reconcile_writeoff
msgid "Write-Off amount"
msgstr "شطب المبلغ"

#. module: account
#: sql_constraint:account.move.line:0
msgid "Wrong credit or debit value in accounting entry !"
msgstr "قيمة دائن أو مدين خاطئة في قيد المحاسبة"

#. module: account
#: code:addons/account/models/account_move.py:1015
#, python-format
msgid "You are trying to reconcile some entries that are already reconciled!"
msgstr "أنت تحاول تسوية قيود قد تم تسويتها بالفعل."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line_blocked
msgid ""
"You can check this box to mark this journal item as a litigation with the "
"associated partner"
msgstr "يمكنك اختيار هذا المربع لتحديد هذا العنصر كدعوة للشريك المرتبط"

#. module: account
#: model:ir.actions.act_window,help:account.action_invoice_tree2
msgid ""
"You can control the invoice from your vendor according to\n"
"                what you purchased or received."
msgstr ""
"يمكنك التحكم في الفاتورة من المورد وفقاً\n"
"          لما اشتريته أو استقبلته"

#. module: account
#: model:ir.ui.view,arch_db:account.setup_bank_journal_form
msgid "You can define additional accounts here"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:468
#, python-format
msgid "You can not delete a payment that is already posted"
msgstr "لا يمكنك حذف السداد المؤجل بالفعل "

#. module: account
#: code:addons/account/models/account_invoice.py:1633
#, python-format
msgid "You can only delete an invoice line if the invoice is in draft state."
msgstr "بإمكانك حذف سطر الفاتورة فقط إذا كانت الفاتورة في حالة المسودة."

#. module: account
#: code:addons/account/models/account_payment.py:141
#, python-format
msgid "You can only register payments for open invoices"
msgstr "يمكنك تسجيل المدفوعات للفواتير المفتوحة"

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_report_style_overwrite
msgid ""
"You can set up here the format you want this record to be displayed. If you "
"leave the automatic formatting, it will be computed based on the financial "
"reports hierarchy (auto-computed field 'level')."
msgstr ""
"يمكنك هنا تثبيت الفورمات الذى تريد عرض هذا السجل به. إذا تركت الفورمات "
"التلقائي, سيتم حسابه معتمداً على التقرير الهرمى المالي (حقل الحساب التلقائي "
"\"مستوى\")"

#. module: account
#: code:addons/account/models/account_move.py:209
#, python-format
msgid ""
"You cannot add/modify entries prior to and inclusive of the lock date %s"
msgstr "لا يمكنك إضافة/تعديل القيود قبل و اثناء تاريخ القفل %s"

#. module: account
#: code:addons/account/models/account_move.py:211
#, python-format
msgid ""
"You cannot add/modify entries prior to and inclusive of the lock date %s. "
"Check the company settings or ask someone with the 'Adviser' role"
msgstr ""
"لا يمكنك إضافة/تعديل القيود قبل و اثناء تاريخ القفل %s. اختر اعدادات المؤسسة"
" أو اسأل شخص له دور \"المستشار\""

#. module: account
#: code:addons/account/models/account_invoice.py:1196
#, python-format
msgid ""
"You cannot cancel an invoice which is partially paid. You need to "
"unreconcile related payment entries first."
msgstr "لا يمكنك الغاء فاتورة مدفوعة جزئياً. تحتاج قيد سداد مسوي أولاً."

#. module: account
#: code:addons/account/models/company.py:200
#, python-format
msgid ""
"You cannot change the currency of the company since some journal items "
"already exist"
msgstr "انت لاتستطيع تغيير عملة المؤسسة لأن دفتر اليومية موجود بالفعل "

#. module: account
#: code:addons/account/models/account.py:235
#, python-format
msgid ""
"You cannot change the owner company of an account that already contains "
"journal items."
msgstr "لا يمكنك تغيير مالك المؤسسة لحساب يحتوى بالفعل على عناصر يومية."

#. module: account
#: code:addons/account/models/account.py:242
#, python-format
msgid ""
"You cannot change the value of the reconciliation on this account as it "
"already has some moves"
msgstr "لا يمكنك تغيير قيمة التسوية في هذا الحساب ذلك انه به بعض الحركات."

#. module: account
#: code:addons/account/models/account_move.py:500
#, python-format
msgid ""
"You cannot create journal items with a secondary currency without filling "
"both 'currency' and 'amount currency' field."
msgstr ""
"لا يمكنك انشاء عناصر يومية لها عملة ثانية بدون ملء حقلي \"العملة\" و \"عملة "
"المبلغ\""

#. module: account
#: code:addons/account/models/company.py:120
#, python-format
msgid ""
"You cannot define stricter conditions on advisors than on users. Please make"
" sure that the lock date on advisor is set before the lock date for users."
msgstr ""
"لا يمكنك وضع شروط أكثر صرامة علي الاستشاريين من المستخدمين. برجاء التأكد من "
"تعيين تاريخ قفل علي الاستشاري قبل تاريخ القفل المعين للمستخدمين."

#. module: account
#: code:addons/account/models/account_invoice.py:613
#, python-format
msgid ""
"You cannot delete an invoice after it has been validated (and received a "
"number). You can set it back to \"Draft\" state and modify its content, then"
" re-confirm it."
msgstr ""
"لا يمكنك حذف فاتورة بعد اعتمادها (و قد أخذت رقم). يمكنك ارجاعها الي "
"\"المسودة\" و التعديل في مكوناتها, ثم يتم إعادة تأكيدها."

#. module: account
#: code:addons/account/models/account_invoice.py:611
#, python-format
msgid ""
"You cannot delete an invoice which is not draft or cancelled. You should "
"create a credit note instead."
msgstr ""
"لا يمكنك حذف فاتورة غير مسودة أو ملغية. ينبغي أن تنشئ إشعار خصم بدلًا من "
"هذا."

#. module: account
#: code:addons/account/models/res_config_settings.py:133
#, python-format
msgid ""
"You cannot disable this setting because some of your taxes are cash basis. "
"Modify your taxes first before disabling this setting."
msgstr ""
"لا يمكنك عرض هذا الإعداد لأن بعض ضرائبك تعتمد على حركة النقدية. عدل ضرائبك "
"أولًا قبل عرض هذا الإعداد."

#. module: account
#: code:addons/account/models/account.py:248
#, python-format
msgid "You cannot do that on an account that contains journal items."
msgstr "لا يمكنك فعل هذا على حساب يحتوى عناصر يومية."

#. module: account
#: code:addons/account/models/account_move.py:1364
#, python-format
msgid ""
"You cannot do this modification on a posted journal entry, you can just change some non legal fields. You must revert the journal entry to cancel it.\n"
"%s."
msgstr ""
"لا يمكنك اجراء هذا التعديل على قيد يومية مؤجل, يمكنك فقط تغيير بعد الحقول الغير قانونية. يجب عليك إعادة قيد اليومية لإلغائه.\n"
"%s."

#. module: account
#: code:addons/account/models/account_move.py:1366
#, python-format
msgid ""
"You cannot do this modification on a reconciled entry. You can just change some non legal fields or you must unreconcile first.\n"
"%s."
msgstr ""
"لا يمكنك اجراء هذا التعديل على قيد تم تسويته, يمكنك فقط تغيير بعد الحقول الغير قانونية أو يجب عليك الغاء تسويتها أولاً.\n"
"%s."

#. module: account
#: code:addons/account/models/account.py:518
#, python-format
msgid "You cannot empty the bank account once set."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:55
#, python-format
msgid ""
"You cannot have a receivable/payable account that is not reconcilable. "
"(account code: %s)"
msgstr "لا يمكنك امتلاك حساب دائن/مدين غير قابل للتسوية. (كود الحساب: %s)"

#. module: account
#: code:addons/account/models/company.py:109
#, python-format
msgid ""
"You cannot lock a period that is not finished yet. Please make sure that the"
" lock date for advisors is not set after the last day of the previous month."
msgstr ""
"لا يمكنك قفل مدة لم تنتهِ بعد. برجاء مراجعة تاريخ القفل للاستشاريين والتأكد "
"من أنه لا يجاوز تاريخ آخر أيام الشهر الماضي."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:701
#, python-format
msgid "You cannot mix items from receivable and payable accounts."
msgstr "لا يمكنك خلط عناصر من الحسابات المدينة و الحسابات الدائنة."

#. module: account
#: code:addons/account/models/account_move.py:172
#, python-format
msgid ""
"You cannot modify a posted entry of this journal.\n"
"First you should set the journal to allow cancelling entries."
msgstr ""
"لا يمكنك تعديل قيد مؤجل لقيد اليومية هذا\n"
"يجب عليك تعيين دفتر اليومية أولاً للسماح بإلغاء القيود."

#. module: account
#: code:addons/account/models/account_invoice.py:789
#, python-format
msgid ""
"You cannot pay an invoice which is partially paid. You need to reconcile "
"payment entries first."
msgstr "لا يمكنك سداد فاتورة مدفوعة جزئيا. يجب عليك تسوية قيود السداد أولا."

#. module: account
#: code:addons/account/wizard/pos_box.py:36
#, python-format
msgid "You cannot put/take money in/out for a bank statement which is closed."
msgstr "لا يمكنك وضع/أخذ نقود في/من كشف حساب بنكي مغلق"

#. module: account
#: code:addons/account/models/account.py:261
#, python-format
msgid ""
"You cannot remove/deactivate an account which is set on a customer or "
"vendor."
msgstr "لا يمكنك حذف/الغاء تفعيل حساب مرتبط بعميل أو مورد"

#. module: account
#: code:addons/account/models/account.py:249
#, python-format
msgid ""
"You cannot set a currency on this account as it already has some journal "
"entries having a different foreign currency."
msgstr ""
"لا يمكنك ضبط عملة لهذا الحساب لأنه يحتوي قيود يومية بعملات أجنبية مختلفة."

#. module: account
#: code:addons/account/models/account_move.py:1368
#, python-format
msgid "You cannot use deprecated account."
msgstr "لا يمكنك استخدام حساب مُهلك"

#. module: account
#: code:addons/account/models/account_move.py:1283
#, python-format
msgid ""
"You cannot use this general account in this journal, check the tab 'Entry "
"Controls' on the related journal."
msgstr ""
"لا يمكنك استخدام هذا الحساب الرئيسي في دفتر اليومية هذا,  افحص لسان التبويب "
"'التحكم في القيد' على دفتر اليومية ذات الصلة ."

#. module: account
#: code:addons/account/models/account_invoice.py:74
#: code:addons/account/models/account_invoice.py:780
#, python-format
msgid ""
"You cannot validate an invoice with a negative total amount. You should "
"create a credit note instead."
msgstr ""
"لا يمكنك اعتماد فاتورة بقيمة إجمالية سالبة. عليك إنشاء إشعار خصم بدلًا من "
"ذلك."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:158
#, python-format
msgid "You did not configure any reconcile model yet, you can do it"
msgstr ""

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "You have"
msgstr "لديك"

#. module: account
#: code:addons/account/models/account_payment.py:505
#, python-format
msgid "You have to define a sequence for %s in your company."
msgstr "يجب عليك تعريف تسلسل %s في شركتك."

#. module: account
#: code:addons/account/wizard/account_report_general_ledger.py:21
#, python-format
msgid "You must define a Start Date"
msgstr "يجب أن تقوم بتعريف تاريخ البدء."

#. module: account
#: code:addons/account/models/account_invoice.py:1555
#, python-format
msgid "You must first select a partner!"
msgstr "يجب أن تختار شريك أولاً!"

#. module: account
#: code:addons/account/wizard/account_report_aged_partner_balance.py:26
#, python-format
msgid "You must set a period length greater than 0."
msgstr "يجب أن تضع مدة الفترة أكبر من 0."

#. module: account
#: code:addons/account/wizard/account_report_aged_partner_balance.py:28
#, python-format
msgid "You must set a start date."
msgstr "يجب أن تقوم بتحديد تاريخ البدء."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "You reconciled"
msgstr "أتممت التسوية"

#. module: account
#: code:addons/account/models/account_move.py:1884
#, python-format
msgid ""
"You should configure the 'Exchange Rate Journal' in the accounting settings,"
" to manage automatically the booking of accounting entries related to "
"differences between exchange rates."
msgstr ""
"يجب عليك تكوين \"سعر الصرف لدفتر اليومية\" في اعدادات المحاسبة, لإدارة حجز "
"قيود المحاسبة للفروق بين سعر الصرف تلقائياً:"

#. module: account
#: code:addons/account/models/account_move.py:1886
#, python-format
msgid ""
"You should configure the 'Gain Exchange Rate Account' in the accounting "
"settings, to manage automatically the booking of accounting entries related "
"to differences between exchange rates."
msgstr ""
"يجب عليك تكوين \"حساب الربح في سعر الصرف\" في اعدادات المحاسبة, لإدارة حجز "
"قيود المحاسبة للفروق بين سعر الصرف تلقائياً:"

#. module: account
#: code:addons/account/models/account_move.py:1888
#, python-format
msgid ""
"You should configure the 'Loss Exchange Rate Account' in the accounting "
"settings, to manage automatically the booking of accounting entries related "
"to differences between exchange rates."
msgstr ""
"يجب عليك تكوين \"حساب الخسارة في سعر الصرف\" في اعدادات المحاسبة, لإدارة حجز"
" قيود المحاسبة للفروق بين سعر الصرف تلقائياً:"

#. module: account
#: code:addons/account/wizard/pos_box.py:49
#: code:addons/account/wizard/pos_box.py:67
#, python-format
msgid ""
"You should have defined an 'Internal Transfer Account' in your cash "
"register's journal!"
msgstr ""
"كان يجب عليك تعريف \"حساب نقل داخلي\" في دفتر يومية صندوق الكاشير الخاص بك!"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_invoice_refund
msgid ""
"You will be able to edit and validate this\n"
"                                credit note directly or keep it draft,\n"
"                                waiting for the document to be issued by\n"
"                                your supplier/customer."
msgstr ""
"ستتمكن من تعديل واعتماد\n"
"هذا الإشعار الدائن مباشرة أو الاحتفاظ به كمسودة،\n"
"في انتظار إصدار المستند بواسطة من طرف\n"
"المورد/العميل."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Bank Accounts"
msgstr "حساباتك البنكية"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Company"
msgstr "مؤسستك"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Customers"
msgstr "عملائك"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Products"
msgstr "منتجاتك"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your Trial Balance (list of accounts and their balances)."
msgstr "ميزان المراجعة (قائمة بالحسابات و أرصدتها)."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your company's legal name, tax ID, address, and logo."
msgstr ""
"اسم مؤسستك الرسمي، رقم التسجيل أو البطاقة الضريبية، العنوان وشعار المؤسسة."

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "Your outstanding invoices, payments, and undeposited funds."
msgstr "فواتيرك، مدفوعاتك والمبالغ غير المودعة المستحقة."

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_position_form
msgid "Zip Range"
msgstr "نطاق الرمز البريدي"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_zip_from
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_zip_from
msgid "Zip Range From"
msgstr "نطاق الرمز البريدي من"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template_zip_to
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_zip_to
msgid "Zip Range To"
msgstr "نطاق الرمز البريدي إلى"

#. module: account
#: model:ir.model,name:account.model_account_bank_accounts_wizard
msgid "account.bank.accounts.wizard"
msgstr "account.bank.accounts.wizard"

#. module: account
#: model:ir.model,name:account.model_account_financial_year_op
msgid "account.financial.year.op"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_group
msgid "account.group"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_opening
msgid "account.opening"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_reconcile_model_template
msgid "account.reconcile.model.template"
msgstr "account.reconcile.model.template"

#. module: account
#: model:ir.model,name:account.model_account_tax_group
msgid "account.tax.group"
msgstr "account.tax.group"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "activate this feature"
msgstr "تنشيط هذه الخاصية"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:17
#, python-format
msgid "assign to invoice"
msgstr "ربط بفاتورة"

#. module: account
#: model:ir.model,name:account.model_cash_box_in
msgid "cash.box.in"
msgstr "cash.box.in"

#. module: account
#: model:ir.model,name:account.model_cash_box_out
msgid "cash.box.out"
msgstr "cash.box.out"

#. module: account
#: model:ir.ui.view,arch_db:account.portal_invoice_error
#: model:ir.ui.view,arch_db:account.portal_invoice_success
msgid "close"
msgstr "إغلاق"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_form
msgid "code"
msgstr "كود"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "create a journal entry"
msgstr "إنشاء قيد اليومية"

#. module: account
#: model:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "days"
msgstr "أيام"

#. module: account
#: model:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "e.g. Bank Fees"
msgstr "مثل المصروفات البنكية"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "fast recording interface"
msgstr "واجهة التسجيل سريع"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "first)"
msgstr "الأول)"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "for this customer. You can allocate them to mark this invoice as paid."
msgstr ""
"لهذا العميل. يمكنك توزيعها لإتمام سداد هذه الفاتورة وتحويلها إلى مدفوعة."

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "for this supplier. You can allocate them to mark this bill as paid."
msgstr ""
"لهذا المورد. يمكنك توزيعها لإتمام سداد هذه الفاتورة وتحويلها إلى مدفوعة."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:41
#, python-format
msgid "o_manual_statement"
msgstr "o_manual_statement"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_supplier_form
msgid "outstanding debits"
msgstr "الديون المستحقة"

#. module: account
#: model:ir.ui.view,arch_db:account.invoice_form
msgid "outstanding payments"
msgstr "المدفوعات المستحقة"

#. module: account
#: model:ir.model,name:account.model_report_account_report_agedpartnerbalance
msgid "report.account.report_agedpartnerbalance"
msgstr "report.account.report_agedpartnerbalance"

#. module: account
#: model:ir.model,name:account.model_report_account_report_financial
msgid "report.account.report_financial"
msgstr "report.account.report_financial"

#. module: account
#: model:ir.model,name:account.model_report_account_report_generalledger
msgid "report.account.report_generalledger"
msgstr "report.account.report_generalledger"

#. module: account
#: model:ir.model,name:account.model_report_account_report_journal
msgid "report.account.report_journal"
msgstr "report.account.report_journal"

#. module: account
#: model:ir.model,name:account.model_report_account_report_overdue
msgid "report.account.report_overdue"
msgstr "report.account.report_overdue"

#. module: account
#: model:ir.model,name:account.model_report_account_report_partnerledger
msgid "report.account.report_partnerledger"
msgstr "report.account.report_partnerledger"

#. module: account
#: model:ir.model,name:account.model_report_account_report_tax
msgid "report.account.report_tax"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_report_account_report_trialbalance
msgid "report.account.report_trialbalance"
msgstr "report.account.report_trialbalance"

#. module: account
#: model:ir.model,name:account.model_res_config_settings
msgid "res.config.settings"
msgstr "الدقة.التكوين.إعدادات"

#. module: account
#: code:addons/account/models/account_move.py:238
#, python-format
msgid "reversal of: "
msgstr "قيد عكسي لـ:"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "seconds per transaction."
msgstr "ثواني لكل معاملة"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "send us an email"
msgstr "أرسل لنا بريد إلكتروني"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "set company logo"
msgstr "ضبط شعار المؤسسة"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "setup your bank accounts."
msgstr "إعداد حساباتك البنكية الخاصة بك"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "the customer list"
msgstr "قائمة العملاء"

#. module: account
#: model:ir.ui.view,arch_db:account.view_partner_property_form
msgid "the parent company"
msgstr "الشركة الأم"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "the product list"
msgstr "قائمة المنتجات"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:158
#, python-format
msgid "there"
msgstr ""

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "to describe<br/> your experience or to suggest improvements !"
msgstr "لكي تشرح<br/> تجربتك او لكي تقترح تحسينات"

#. module: account
#: model:ir.ui.view,arch_db:account.account_planner
msgid "to set the balance of all of your accounts."
msgstr "لضبط الرصيد لجميع حساباتك"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:53
#, python-format
msgid "transactions in"
msgstr "معاملات في"

#. module: account
#: model:ir.model,name:account.model_wizard_multi_charts_accounts
msgid "wizard.multi.charts.accounts"
msgstr "wizard.multi.charts.accounts"

#. module: account
#: model:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "→ Count"
msgstr "← إحصاء"
