<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data noupdate="0">

    <record id="group_account_invoice" model="res.groups">
        <field name="name">Billing</field>
        <field name="category_id" ref="base.module_category_accounting_and_finance"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="group_account_user" model="res.groups">
        <field name="name">Show Full Accounting Features</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="group_account_manager" model="res.groups">
        <field name="name">Billing Manager</field>
        <field name="category_id" ref="base.module_category_accounting_and_finance"/>
        <field name="implied_ids" eval="[(4, ref('group_account_invoice'))]"/>
    </record>

    <record id="base.user_root" model="res.users">
        <field name="groups_id" eval="[(4, ref('account.group_account_invoice')), (4, ref('account.group_account_manager'))]"/>
    </record>

    <record id="base.group_system" model="res.groups">
        <field name="implied_ids" eval="[(4, ref('account.group_account_manager'))]"/>
    </record>

    <record id="group_warning_account" model="res.groups">
        <field name="name">A warning can be set on a partner (Account)</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="group_cash_rounding" model="res.groups">
        <field name="name">Allow the cash rounding management</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

</data>

<data noupdate="1">
    <record id="base.default_user" model="res.users">
        <field name="groups_id" eval="[(4,ref('account.group_account_manager'))]"/>
    </record>

    <record id="account_move_comp_rule" model="ir.rule">
        <field name="name">Account Entry</field>
        <field name="model_id" ref="model_account_move"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>

    <record id="account_move_line_comp_rule" model="ir.rule">
        <field name="name">Entry lines</field>
        <field name="model_id" ref="model_account_move_line"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>

    <record id="journal_comp_rule" model="ir.rule">
        <field name="name">Journal multi-company</field>
        <field name="model_id" ref="model_account_journal"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>

    <record id="account_comp_rule" model="ir.rule">
        <field name="name">Account multi-company</field>
        <field name="model_id" ref="model_account_account"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>

    <record id="tax_comp_rule" model="ir.rule">
        <field name="name">Tax multi-company</field>
        <field name="model_id" ref="model_account_tax"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>

    <record id="invoice_comp_rule" model="ir.rule">
        <field name="name">Invoice multi-company</field>
        <field name="model_id" ref="model_account_invoice"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>

    <record id="invoice_analysis_comp_rule" model="ir.rule">
        <field name="name">Invoice Analysis multi-company</field>
        <field name="model_id" ref="model_account_invoice_report"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>

    <record id="account_fiscal_position_comp_rule" model="ir.rule">
        <field name="name">Account fiscal Mapping company rule</field>
        <field name="model_id" ref="model_account_fiscal_position"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>

    <record model="ir.rule" id="account_invoice_line_comp_rule">
        <field name="name">Invoice Line company rule</field>
        <field name="model_id" ref="model_account_invoice_line"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>

    <record model="ir.rule" id="account_bank_statement_comp_rule">
        <field name="name">Account bank statement company rule</field>
        <field name="model_id" ref="model_account_bank_statement"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>

    <record model="ir.rule" id="account_bank_statement_line_comp_rule">
        <field name="name">Account bank statement line company rule</field>
        <field name="model_id" ref="model_account_bank_statement_line"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>

    <record model="ir.rule" id="account_reconcile_model_template_comp_rule">
        <field name="name">Account reconcile model template company rule</field>
        <field name="model_id" ref="model_account_reconcile_model"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>

    <record model="ir.rule" id="account_payment_comp_rule">
        <field name="name">Account payment company rule</field>
        <field name="model_id" ref="model_account_payment"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>

    <!-- Portal for invoice and invoice lines -->
    <record id="account_invoice_rule_portal" model="ir.rule">
        <field name="name">Portal Personal Account Invoices</field>
        <field name="model_id" ref="account.model_account_invoice"/>
        <field name="domain_force">[('message_partner_ids','child_of',[user.commercial_partner_id.id])]</field>
        <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
    </record>

    <record id="account_invoice_line_rule_portal" model="ir.rule">
        <field name="name">Portal Invoice Lines</field>
        <field name="model_id" ref="account.model_account_invoice_line"/>
        <field name="domain_force">[('invoice_id.message_partner_ids','child_of',[user.commercial_partner_id.id])]</field>
        <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
    </record>
</data>
</odoo>
