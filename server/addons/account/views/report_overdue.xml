<?xml version="1.0" encoding="utf-8"?>
<odoo>
<template id="account.report_overdue_document">
    <t t-call="web.external_layout">
        <div class="page">
            <div class="row">
                <div class="col-xs-5 col-xs-offset-7">
                    <!-- When partner is a company, its name is included in contact_address -->
                    <span t-if="not o.is_company"><span t-field="o.name"/><br/></span>
                    <span t-raw="o.contact_address.replace('\n\n', '\n').replace('\n', '&lt;br&gt;')"/>
                    <span t-field="o.vat"/>
                </div>
                <p>
                    Document: Customer account statement<br/>
                    Date: <span t-esc="Date"/><br/>
                    <t t-if="o.ref">Customer ref: <span t-field="o.ref"/></t>
                </p>

                <t t-if="Lines[o.id]">
                    <span t-field="res_company.with_context(lang=o.lang).overdue_msg"/>
                    <table class="table table-condensed" t-foreach="Lines[o.id]" t-as="currency">
                        <thead>
                            <tr>
                                <th>Reference number</th>
                                <th class="text-center">Date</th>
                                <th class="text-center">Due Date</th>
                                <th>Communication</th>
                                <th class="text-right">Due</th>
                                <th class="text-right">Paid</th>
                                <th class="text-center">Litigation</th>
                            </tr>
                        </thead>
                        <tr t-foreach="Lines[o.id][currency]" t-as="line">
                            <td>
                                <span t-esc="line['move_id']"/>
                            </td>
                            <td>
                                <span t-esc="line['date']"/>
                            </td>
                            <td>
                                <span t-esc="line['date_maturity']"/>
                            </td>
                            <td>
                                <t t-if="line['name'] != '/'"><span t-esc="line['name']"/></t>
                                <span t-esc="line['ref']"/>
                            </td>
                            <td class="text-right">
                                <span t-esc="line['debit']" t-options="{'widget': 'monetary', 'display_currency': currency}"/>
                            </td>
                            <td class="text-right">
                                <span t-esc="line['credit']" t-options="{'widget': 'monetary', 'display_currency': currency}"/>
                            </td>
                            <td class="text-center">
                                <span t-if="line['blocked']">x</span>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="3"></td>
                            <td><strong>Subtotal :</strong></td>
                            <td class="text-right"><span t-esc="Totals[o.id][currency]['due']" t-options="{'widget': 'monetary', 'display_currency': currency}"/></td>
                            <td class="text-right"><span t-esc="Totals[o.id][currency]['paid']" t-options="{'widget': 'monetary', 'display_currency': currency}"/></td>
                            <td class="text-right"><span t-esc="Totals[o.id][currency]['mat']" t-options="{'widget': 'monetary', 'display_currency': currency}"/></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td colspan="3"></td>
                            <td><strong>Balance :</strong></td>
                            <td class="text-right">
                                <span t-esc="Totals[o.id][currency]['due'] - Totals[o.id][currency]['paid']" t-options="{'widget': 'monetary', 'display_currency': currency}"/>
                            </td>
                        </tr>
                    </table>
                </t>
                <p t-if="not Lines[o.id]">
                    <strong>There is nothing due with this customer.</strong>
                </p>
            </div>
        </div>
   </t>
</template>

<template id="report_overdue">
    <t t-call="web.html_container">
        <t t-foreach="docs" t-as="o">
            <t t-call="account.report_overdue_document" t-lang="o.lang"/>
        </t>
    </t>
</template>
</odoo>
