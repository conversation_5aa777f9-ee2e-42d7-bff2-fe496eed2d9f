<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="report_invoice_document">
            <t t-call="web.external_layout">
                <t t-set="o" t-value="o.with_context(lang=o.partner_id.lang)" />
                <div class="page">
                    <div class="row">
                        <div name="invoice_address" class="col-xs-5 col-xs-offset-7">
                            <address t-field="o.partner_id"
                                t-options='{"widget": "contact", "fields": ["address", "name"], "no_marker": True}' />
                            <div t-if="o.partner_id.vat" class="mt16"><t t-esc="o.company_id.country_id.vat_label or 'TIN'"/>: <span t-field="o.partner_id.vat"/></div>
                        </div>
                    </div>

                    <h2>
                        <span t-if="o.type == 'out_invoice' and (o.state == 'open' or o.state == 'paid')">Invoice</span>
                        <span t-if="o.type == 'out_invoice' and o.state == 'draft'">Draft Invoice</span>
                        <span t-if="o.type == 'out_invoice' and o.state == 'cancel'">Cancelled Invoice</span>
                        <span t-if="o.type == 'out_refund'">Credit Note</span>
                        <span t-if="o.type == 'in_refund'">Vendor Credit Note</span>
                        <span t-if="o.type == 'in_invoice'">Vendor Bill</span>
                        <span t-field="o.number"/>
                    </h2>

                    <div class="row mt32 mb32">
                        <div class="col-xs-2" t-if="o.name">
                            <strong>Description:</strong>
                            <p t-field="o.name"/>
                        </div>
                        <div class="col-xs-2" t-if="o.date_invoice">
                            <strong>Invoice Date:</strong>
                            <p t-field="o.date_invoice"/>
                        </div>
                        <div class="col-xs-2" t-if="o.date_due and o.type == 'out_invoice' and (o.state == 'open' or o.state == 'paid')">
                            <strong>Due Date:</strong>
                            <p t-field="o.date_due"/>
                        </div>
                        <div class="col-xs-2" t-if="o.origin">
                            <strong>Source:</strong>
                            <p t-field="o.origin"/>
                        </div>
                        <div class="col-xs-2" t-if="o.partner_id.ref">
                            <strong>Customer Code:</strong>
                            <p t-field="o.partner_id.ref"/>
                        </div>
                        <div name="reference" class="col-xs-2" t-if="o.reference">
                            <strong>Reference:</strong>
                            <p t-field="o.reference"/>
                        </div>
                    </div>

                    <!-- Is there a discount on at least one line? -->
                    <t t-set="display_discount" t-value="any([l.discount for l in o.invoice_line_ids])"/>

                    <table class="table table-condensed" name="invoice_line_table">
                        <thead>
                            <tr>
                                <th>Description</th>
                                <th class="hidden">Source Document</th>
                                <th class="text-right">Quantity</th>
                                <th class="text-right">Unit Price</th>
                                <th t-if="display_discount" class="text-right">Disc.(%)</th>
                                <th class="text-right">Taxes</th>
                                <th class="text-right">Amount</th>
                            </tr>
                        </thead>
                        <tbody class="invoice_tbody">
                            <tr t-foreach="o.invoice_line_ids" t-as="l">
                                <td><span t-field="l.name"/></td>
                                <td class="hidden"><span t-field="l.origin"/></td>
                                <td class="text-right">
                                    <span t-field="l.quantity"/>
                                    <span t-field="l.uom_id"  groups="product.group_uom"/>
                                </td>
                                <td class="text-right">
                                    <span t-field="l.price_unit"/>
                                </td>
                                <td t-if="display_discount" class="text-right">
                                    <span t-field="l.discount"/>
                                </td>
                                <td class="text-right">
                                    <span t-esc="', '.join(map(lambda x: (x.description or x.name), l.invoice_line_tax_ids))"/>
                                </td>
                                <td class="text-right" id="subtotal">
                                    <span t-field="l.price_subtotal"
                                        t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                </td>
                            </tr>
                            <tr t-foreach="range(max(5-len(o.invoice_line_ids),0))" t-as="l">
                                <td t-translation="off">&amp;nbsp;</td>
                                <td class="hidden"></td>
                                <td></td>
                                <td></td>
                                <td t-if="display_discount"></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>

                    <div class="clearfix">
                        <div id="total" class="row">
                            <div class="col-xs-4 pull-right">
                                <table class="table table-condensed" style="min-width: 250px;max-width: 400px;page-break-inside: avoid;">
                                    <tr class="border-black" style="border-bottom:1px solid #dddddd;">
                                        <td><strong>Subtotal</strong></td>
                                        <td class="text-right">
                                            <span t-field="o.amount_untaxed" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                        </td>
                                    </tr>
                                    <t t-foreach="o._get_tax_amount_by_group()" t-as="amount_by_group">
                                        <tr style="border-bottom:1px solid #dddddd;">
                                            <t t-if="len(o.tax_line_ids) == 1 and o.amount_untaxed == amount_by_group[2]">
                                                <td><span t-esc="amount_by_group[0]"/></td>
                                                <td class="text-right">
                                                    <span t-esc="amount_by_group[3]" />
                                                </td>
                                            </t>
                                            <t t-else="">
                                                <td>
                                                    <span t-esc="amount_by_group[0]"/>
                                                    <span>&amp;nbsp;<span>on</span>
                                                        <t t-esc="amount_by_group[4]"/>
                                                    </span>
                                                </td>
                                                <td class="text-right">
                                                    <span t-esc="amount_by_group[3]"/>
                                                </td>
                                            </t>
                                        </tr>
                                    </t>
                                    <tr class="border-black">
                                        <td><strong>Total</strong></td>
                                        <td class="text-right">
                                            <span t-field="o.amount_total" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <p t-if="o.comment">
                        <span t-field="o.comment"/>
                    </p>
                    <p t-if="o.payment_term_id">
                        <span t-field="o.payment_term_id.note"/>
                    </p>
                    <p t-if="o.fiscal_position_id.note">
                        <span t-field="o.fiscal_position_id.note"/>
                    </p>
                </div>
            </t>
        </template>

        <template id="report_invoice_document_with_payments" inherit_id="account.report_invoice_document" primary="True">
            <xpath expr="//div[@id='total']/div/table" position="inside">
                <t t-set="payments_vals" t-value="o._get_payments_vals()"/>
                <t t-foreach="payments_vals" t-as="payment_vals">
                    <tr>
                        <td>
                            <i class="oe_form_field text-right oe_payment_label">Paid on <t t-esc="payment_vals['date']"/></i>
                        </td>
                        <td class="text-right">
                            <span t-esc="payment_vals['amount']" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                        </td>
                    </tr>
                </t>
                <t t-if="len(payments_vals) > 0">
                    <tr class="border-black">
                        <td><strong>Amount Due</strong></td>
                        <td class="text-right">
                             <span t-field="o.residual" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                        </td>
                    </tr>
                </t>
            </xpath>
        </template>

        <template id="report_invoice">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="o">
                    <t t-call="account.report_invoice_document" t-lang="o.partner_id.lang"/>
                </t>
            </t>
        </template>

        <template id="report_invoice_with_payments">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="o">
                    <t t-call="account.report_invoice_document_with_payments" t-lang="o.partner_id.lang"/>
                </t>
            </t>
        </template>
    </data>
</odoo>
