<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="report_generalledger">
    <t t-call="web.html_container">
        <t t-set="data_report_margin_top" t-value="12"/>
        <t t-set="data_report_header_spacing" t-value="9"/>
        <t t-set="data_report_dpi" t-value="110"/>

        <t t-call="web.internal_layout">
            <div class="page">
                <h2><span t-esc="res_company.name"/>: General ledger</h2>

                <div class="row mt32">
                    <div class="col-xs-4">
                        <strong>Journals:</strong>
                          <p t-esc="', '.join([ lt or '' for lt in print_journal ])"/>
                    </div>
                    <div class="col-xs-4">
                        <strong>Display Account</strong>
                        <p>
                            <span t-if="data['display_account'] == 'all'">All accounts'</span>
                            <span t-if="data['display_account'] == 'movement'">With movements</span>
                            <span t-if="data['display_account'] == 'not_zero'">With balance not equal to zero</span>
                        </p>
                    </div>
                    <div class="col-xs-4">
                        <strong>Target Moves:</strong>
                        <p t-if="data['target_move'] == 'all'">All Entries</p>
                        <p t-if="data['target_move'] == 'posted'">All Posted Entries</p>
                    </div>
                </div>
                <div class="row mb32">
                    <div class="col-xs-4">
                        <strong>Sorted By:</strong>
                        <p t-if="data['sortby'] == 'sort_date'">Date</p>
                        <p t-if="data['sortby'] == 'sort_journal_partner'">Journal and Partner</p>
                    </div>
                    <div class="col-xs-4">
                        <t t-if="data['date_from']"><strong>Date from :</strong> <span t-esc="data['date_from']"/><br/></t>
                        <t t-if="data['date_to']"><strong>Date to :</strong> <span t-esc="data['date_to']"/></t>
                    </div>
                </div>

                <table class="table table-condensed">
                    <thead>
                        <tr class="text-center">
                            <th>Date</th>
                            <th>JRNL</th>
                            <th>Partner</th>
                            <th>Ref</th>
                            <th>Move</th>
                            <th>Entry Label</th>
                            <th>Debit</th>
                            <th>Credit</th>
                            <th>Balance</th>
                            <th groups="base.group_multi_currency">Currency</th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="Accounts" t-as="account">
                            <tr style="font-weight: bold;">
                                <td colspan="6">
                                    <span style="color: white;" t-esc="'..'"/>
                                    <span t-esc="account['code']"/>
                                    <span t-esc="account['name']"/>
                                </td>
                                <td class="text-right">
                                    <span t-esc="account['debit']" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                </td>
                                <td class="text-right">
                                    <span t-esc="account['credit']" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                </td>
                                <td class="text-right">
                                    <span t-esc="account['balance']" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                </td>
                                <td groups="base.group_multi_currency"/>
                            </tr>
                            <tr t-foreach="account['move_lines']" t-as="line">
                                <td><span t-esc="line['ldate']"/></td>
                                <td><span t-esc="line['lcode']"/></td>
                                <td><span t-esc="line['partner_name']"/></td>
                                <td><span t-if="line['lref']" t-esc="line['lref']"/></td>
                                <td><span t-esc="line['move_name']"/></td>
                                <td><span t-esc="line['lname']"/></td>
                                <td class="text-right">
                                    <span t-esc="line['debit']" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                </td>
                                <td class="text-right">
                                    <span t-esc="line['credit']" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                </td>
                                <td class="text-right">
                                    <span t-esc="line['balance']" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                </td>
                                <td class="text-right" groups="base.group_multi_currency">
                                    <span t-esc="line['amount_currency'] if line['amount_currency'] > 0.00 else ''"/>
                                    <span t-esc="line['currency_code'] if line['amount_currency'] > 0.00 else ''"/>
                                </td>
                            </tr>
                        </t>
                    </tbody>
                </table>
            </div>
        </t>
    </t>
</template>
</odoo>
