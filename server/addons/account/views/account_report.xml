<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- QWeb Reports -->
        <report 
            id="account_invoices"
            model="account.invoice"
            string="Invoices"
            report_type="qweb-pdf"
            name="account.report_invoice_with_payments"
            file="account.report_invoice_with_payments"
            attachment="(object.state in ('open','paid')) and ('INV'+(object.number or '').replace('/','')+'.pdf')"
            print_report_name="(object._get_printed_report_name())"
            groups="account.group_account_invoice"
        />

        <report
            id="account_invoices_without_payment"
            model="account.invoice"
            string="Invoices without Payment"
            report_type="qweb-pdf"
            name="account.report_invoice"
            file="account.report_invoice"
            attachment="(object.state in ('open','paid')) and ('INV'+(object.number or '').replace('/','')+'.pdf')"
            print_report_name="(object._get_printed_report_name())"
        />

        <report 
            id="action_report_print_overdue"
            model="res.partner"
            report_type="qweb-pdf"
            string="Due Payments"
            name="account.report_overdue"
            file="account.report_overdue"
            print_report_name="'Due Payments - %s' % (object.name)"
        />

        <report 
            id="action_report_general_ledger"
            model="account.report.general.ledger"
            string="General Ledger"
            report_type="qweb-pdf"
            name="account.report_generalledger"
            file="account.report_generalledger"
            />

        <report 
            id="action_report_trial_balance"
            model="account.balance.report"
            string="Trial Balance"
            report_type="qweb-pdf"
            name="account.report_trialbalance"
            file="account.report_trialbalance"
            />

        <report 
            id="action_report_financial"
            model="account.financial.report"
            string="Financial report"
            menu="False"
            report_type="qweb-pdf"
            name="account.report_financial"
            file="account.report_financial"
            />

        <report
            id="action_report_aged_partner_balance"
            model="res.partner"
            string="Aged Partner Balance"
            menu="False"
            report_type="qweb-pdf"
            name="account.report_agedpartnerbalance"
            file="account.report_agedpartnerbalance"
        />

        <report 
            id="action_report_journal"
            model="account.common.journal.report"
            string="Journals Audit"
            report_type="qweb-pdf"
            name="account.report_journal"
            file="account.report_journal"
            />

        <report 
            id="action_report_partnerledger"
            model="account.report.partner.ledger"
            string="Partner Ledger"
            report_type="qweb-pdf"
            name="account.report_partnerledger"
            file="account.report_partnerledger"
            />

        <report
            id="action_report_payment_receipt"
            model="account.payment"
            string="Payment Receipt"
            report_type="qweb-pdf"
            name="account.report_payment_receipt"
            file="account.report_payment_receipt"
            menu="True"
        />

        <report
            id="action_report_account_tax"
            model="account.tax.report"
            string="Tax Report"
            report_type="qweb-pdf"
            name="account.report_tax"
            file="account.report_tax"
            />

    </data>
</odoo>
