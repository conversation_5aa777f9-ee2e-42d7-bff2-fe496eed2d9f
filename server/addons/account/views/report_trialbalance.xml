<?xml version="1.0" encoding="utf-8"?>
<odoo>
<template id="report_trialbalance">
    <t t-call="web.html_container">
        <t t-call="web.internal_layout">
            <div class="page">
                <h2><span t-esc="res_company.name"/>: Trial Balance</h2>

                <div class="row mt32">
                    <div class="col-xs-4">
                        <strong>Display Account:</strong>
                        <p>
                            <span t-if="data['display_account'] == 'all'">All accounts</span>
                            <span t-if="data['display_account'] == 'movement'">With movements</span>
                            <span t-if="data['display_account'] == 'not_zero'">With balance not equal to zero</span>
                        </p>
                    </div>
                    <div class="col-xs-4">
                        <p>
                            <t t-if="data['date_from']"><strong>Date from :</strong> <span t-esc="data['date_from']"/><br/></t>
                            <t t-if="data['date_to']"><strong>Date to :</strong> <span t-esc="data['date_to']"/></t>
                        </p>
                    </div>
                    <div class="col-xs-4">
                        <strong>Target Moves:</strong>
                        <p>
                            <span t-if="data['target_move'] == 'all'">All Entries</span>
                            <span t-if="data['target_move'] == 'posted'">All Posted Entries</span>
                        </p>
                    </div>
                </div>

                <table class="table table-condensed">
                    <thead>
                        <tr>
                            <th>Code</th>
                            <th>Account</th>
                            <th class="text-right">Debit</th>
                            <th class="text-right">Credit</th>
                            <th class="text-right">Balance</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr t-foreach="Accounts" t-as="account">
                            <td>
                                <span t-att-style="style" t-esc="account['code']"/>
                            </td>
                            <td>
                                <span style="color: white;" t-esc="'..'"/>
                                <span t-att-style="style" t-esc="account['name']"/>
                            </td>
                            <td class="text-right">
                                 <span t-att-style="style" t-esc="account['debit']" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                            </td>
                            <td class="text-right">
                                <span t-att-style="style" t-esc="account['credit']" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                            </td>
                            <td class="text-right">
                                <span t-att-style="style" t-esc="account['balance']" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </t>
    </t>
</template>
</odoo>
