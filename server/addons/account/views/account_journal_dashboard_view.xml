<odoo>
    <record id="account_journal_dashboard_kanban_view" model="ir.ui.view">
        <field name="name">account.journal.dashboard.kanban</field>
        <field name="model">account.journal</field>
        <field name="arch" type="xml">
            <kanban create="false" class="oe_background_grey o_kanban_dashboard o_account_kanban">
                <field name="id"/>
                <field name="name"/>
                <field name="type"/>
                <field name="color"/>
                <field name="show_on_dashboard"/>
                <field name="kanban_dashboard"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="#{kanban_color(record.color.raw_value)}">
                            <t t-value="JSON.parse(record.kanban_dashboard.raw_value)" t-set="dashboard"/>
                            <t t-value="record.type.raw_value" t-set="journal_type"/>
                            <t t-call="JournalTop"/>
                            <div class="container o_kanban_card_content">
                                <div class="row">
                                    <t t-if="(journal_type == 'bank' || journal_type == 'cash')" t-call="JournalBodyBankCash"/>
                                    <t t-if="journal_type == 'sale' || journal_type == 'purchase'" t-call="JournalBodySalePurchase"/>
                                    <t t-if="journal_type == 'general' || journal_type == 'situation'">
                                        <div class="col-xs-6 o_kanban_primary_left">
                                            <button type="object" name="open_action" context="{'action_name': 'action_move_journal_line', 'view_no_maturity': 1}" class="btn btn-primary btn-sm">
                                                <span>Journal Entries</span>
                                            </button>
                                        </div>
                                    </t>
                                </div>
                                <t t-if="journal_type == 'bank' || journal_type == 'cash' || journal_type == 'sale' || journal_type == 'purchase'" t-call="JournalBodyGraph"/>
                            </div><div class="container o_kanban_card_manage_pane">
                                <t t-call="JournalManage"/>
                            </div>
                        </div>
                    </t>

                    <t t-name="JournalTop">
                        <div t-attf-class="o_kanban_card_header">
                            <div class="o_kanban_card_header_title">
                                <div class="o_primary">
                                    <a type="object" name="open_action"><field name="name"/></a>
                                </div>
                                <div class="o_secondary" t-att-title="dashboard.title">
                                    <field name="type"/>
                                </div>
                            </div>
                            <div class="o_kanban_manage_button_section">
                                <a class="o_kanban_manage_toggle_button" href="#"><i class="fa fa-ellipsis-v" /></a>
                            </div>
                        </div>
                    </t>

                    <t t-name="JournalManage">

                        <!-- For bank and cash -->
                        <div t-if="journal_type == 'bank' || journal_type == 'cash'" class="row">
                             <div class="col-xs-4 o_kanban_card_manage_section o_kanban_manage_view">
                                <div class="o_kanban_card_manage_title">
                                    <span>View</span>
                                </div>
                                <div>
                                    <a type="object" name="open_action">
                                        <t t-if="journal_type == 'bank'">Bank Statements</t>
                                        <t t-if="journal_type == 'cash'">Cash Statements</t>
                                    </a>
                                </div>
                                <div>
                                    <a type="object" name="open_action_with_context" context="{'action_name': 'action_bank_statement_line', 'search_default_journal': True}">Search Operations</a>
                                </div>
                                <div>
                                    <a type="object" name="open_action" context="{'action_name': 'action_account_moves_all_a'}" groups="base.group_no_one">Journal Items</a>
                                </div>
                            </div>

                             <div class="col-xs-4 o_kanban_card_manage_section o_kanban_manage_new">
                                <div class="o_kanban_card_manage_title">
                                    <span>New</span>
                                </div>
                                <div name="bank_cash_commands">
                                    <t t-if="journal_type == 'bank'">
                                        <a type="object" name="create_bank_statement">Transaction</a>
                                    </t>
                                    <t t-if="journal_type == 'cash'">
                                        <a type="object" name="create_cash_statement">Transaction</a>
                                    </t>
                                </div>
                                <div>
                                    <a type="object" name="open_spend_money">Send Money</a>
                                </div>
                                <div>
                                    <a type="object" name="open_collect_money">Receive Money</a>
                                </div>
                                <div>
                                    <a type="object" name="open_transfer_money">Internal Transfer</a>
                                </div>
                            </div>

                             <div class="col-xs-4 o_kanban_card_manage_section o_kanban_manage_reconciliation">
                                <div class="o_kanban_card_manage_title">
                                    <span>Reconciliation</span>
                                </div>
                                <div>
                                    <a t-if="dashboard.number_to_reconcile > 0" type="object" name="action_open_reconcile">Reconcile</a>
                                    <span t-if="dashboard.number_to_reconcile == 0" class="text-muted">Nothing to Reconcile</span>
                                </div>
                                <div>
                                    <a type="object" name="open_action_with_context" context="{'action_name': 'action_account_reconcile_model', 'use_domain': True}" groups="account.group_account_manager">Reconciliation Models</a>
                                </div>
                            </div>
                        </div>

                        <!-- For purchase and sale -->
                        <div t-if="journal_type == 'purchase' || journal_type == 'sale'" class="row">
                             <div class="col-xs-4 o_kanban_card_manage_section o_kanban_manage_view">
                                <div class="o_kanban_card_manage_title">
                                    <span>View</span>
                                </div>
                                <div>
                                    <a t-if="journal_type == 'sale'" type="object" name="open_action" context="{'use_domain': [('type', '=', 'out_invoice')]}">Invoices</a>
                                    <a t-if="journal_type == 'purchase'" type="object" name="open_action" context="{'use_domain': [('type', '=', 'in_invoice')]}">Bills</a>
                                </div>
                                <div>
                                    <a t-if="journal_type == 'sale'" type="object" name="open_action" context="{'use_domain': [('type', '=', 'out_refund')], 'invoice_type': 'refund'}">Credit Notes</a>
                                    <a t-if="journal_type == 'purchase'" type="object" name="open_action" context="{'use_domain': [('type', '=', 'in_refund')], 'invoice_type': 'refund'}">Credit Notes</a>
                                </div>
                                <div>
                                    <a type="object" name="action_open_reconcile">Payments Matching</a>
                                </div>
                                <div>
                                    <a type="object" name="open_action" context="{'action_name': 'action_account_moves_all_a'}" groups="base.group_no_one">Journal Items</a>
                                </div>
                            </div>

                             <div class="col-xs-4 o_kanban_card_manage_section o_kanban_manage_new">
                                <div class="o_kanban_card_manage_title">
                                    <span>New</span>
                                </div>
                                <div>
                                    <a type="object" name="action_create_new">
                                        <span t-if="journal_type == 'sale'">Invoice</span>
                                        <span t-if="journal_type == 'purchase'">Bill</span>
                                    </a>
                                </div>
                                <div>
                                    <a type="object" name="action_create_new"  context="{'refund':True}">
                                        <span t-if="journal_type == 'sale'">Credit Note</span>
                                        <span t-if="journal_type == 'purchase'">Credit Note Bill</span>
                                    </a>
                                </div>
                            </div>

                             <div class="col-xs-4 o_kanban_card_manage_section o_kanban_manage_reports">
                                <div class="o_kanban_card_manage_title">
                                    <span>Reporting</span>
                                </div>
                                <div>
                                    <a t-if="journal_type == 'sale'" type="action" name="%(action_account_invoice_report_all)d" groups="account.group_account_manager">Invoices Analysis</a>
                                    <a t-if="journal_type == 'purchase'" type="action" name="%(action_account_invoice_report_all_supp)d" groups="account.group_account_manager">Bills Analysis</a>
                                </div>
                            </div>
                        </div>

                        <!-- For general and situation -->
                        <div t-if="journal_type == 'general' || journal_type == 'situation'" class="row">
                             <div class="col-xs-4 o_kanban_card_manage_section o_kanban_manage_view">
                                <div class="o_kanban_card_manage_title">
                                    <span>View</span>
                                </div>
                                <div>
                                    <a type="object" name="open_action" context="{'action_name': 'action_move_journal_line', 'view_no_maturity': 1}">Journal Entries</a>
                                </div>
                                <div>
                                    <a type="object" name="open_action" context="{'action_name': 'action_move_journal_line', 'search_default_toreview': 1, 'view_no_maturity': 1}">Entries to Review</a>
                                </div>
                                <div>
                                    <a type="object" name="open_action" context="{'action_name': 'action_account_moves_all_a'}" groups="base.group_no_one">Journal Items</a>
                                </div>
                            </div>

                             <div class="col-xs-4 o_kanban_card_manage_section o_kanban_manage_new">
                                <div class="o_kanban_card_manage_title">
                                    <span>New</span>
                                </div>
                                <div>
                                    <a type="object" name="action_create_new">Journal Entry</a>
                                </div>
                            </div>

                             <div class="col-xs-4 o_kanban_card_manage_section o_kanban_manage_operations">
                                <div class="o_kanban_card_manage_title">
                                    <span>Operations</span>
                                </div>
                                <div>
                                    <a type="object" name="open_action_with_context" context="{'action_name': 'action_validate_account_move', 'search_default_journal': True}">Post All Entries</a>
                                </div>
                            </div>
                        </div>

                        <div t-if="widget.editable" class="o_kanban_card_manage_settings row">
                            <div class="col-xs-8">
                                <ul class="oe_kanban_colorpicker" data-field="color"/>
                            </div>
                        </div>

                        <div groups="account.group_account_manager" class="row o_kanban_card_settings">
                            <div class="col-xs-6">
                                <field name="show_on_dashboard" widget="boolean_favorite" />
                            </div>
                            <div class="col-xs-6 text-right">
                                <a t-if="widget.editable" type="edit">Settings</a>
                            </div>
                        </div>
                    </t>

                    <t t-name="JournalBodyBankCash">
                        <!-- On the left, display :
                            - A button corresponding to the bank_statements_source, if it wasn't configured, a button for each of them
                            - If there are statements to reconcile, a link to reconcile them -->
                        <div class="col-xs-6 o_kanban_primary_left">
                            <t t-if="dashboard.number_to_reconcile > 0">
                                <button type="object" name="action_open_reconcile" class="btn btn-primary btn-sm"> Reconcile <t t-esc="dashboard.number_to_reconcile"/> Items</button>
                            </t>
                            <t t-if="journal_type == 'bank'">
                                <div name="bank_journal_default_cta" t-if="dashboard.bank_statements_source == 'undefined'">
                                    <button t-if="dashboard.number_to_reconcile == 0" type="object" name="create_bank_statement" class="btn btn-primary btn-sm">New Statement</button>
                                    <a t-if="dashboard.number_to_reconcile > 0" type="object" name="create_bank_statement" class="oe_inline">New Statement</a>
                                </div>
                                <div name="bank_journal_cta" t-if="dashboard.bank_statements_source != 'undefined'">
                                    <button t-if="dashboard.bank_statements_source == 'manual' &amp;&amp; dashboard.number_to_reconcile == 0" type="object" name="create_bank_statement" class="btn btn-primary btn-sm">New Statement</button>
                                    <a t-if="dashboard.bank_statements_source == 'manual' &amp;&amp; dashboard.number_to_reconcile > 0" type="object" name="create_bank_statement" class="oe_inline">New Statement</a>
                                </div>
                            </t>
                            <t t-if="dashboard.number_to_reconcile > 0">
                                <a t-if="journal_type == 'cash'" type="object" name="create_cash_statement" class="oe_inline">New Transactions</a>
                            </t>
                            <t t-if="dashboard.number_to_reconcile == 0">
                                <button t-if="journal_type == 'cash'" type="object" name="create_cash_statement" class="btn btn-primary btn-sm">New Transactions</button>
                            </t>
                        </div>
                        <!-- On the right, show other common informations/actions -->
                        <div class="col-xs-6 o_kanban_primary_right">
                            <div class="row">
                                <div class="col-xs-6">
                                    <span title="Balance in Odoo">Balance in GL</span>
                                </div>
                                <div class="col-xs-6 text-right">
                                    <span><t t-esc="dashboard.account_balance"/></span>
                                </div>
                            </div>
                            <t t-if="dashboard.last_balance != dashboard.account_balance">
                                <div class="row" name="latest_statement">
                                    <div class="col-xs-6">
                                        <span title="Latest Statement">Latest Statement</span>
                                    </div>
                                    <div class="col-xs-6 text-right">
                                        <span><t t-esc="dashboard.last_balance"/></span>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </t>
                    <t t-name="JournalBodySalePurchase" id="account.JournalBodySalePurchase">
                        <div class="col-xs-6 o_kanban_primary_left">
                            <t t-if="journal_type == 'sale'">
                                <button type="object" name="action_create_new" class="btn btn-primary btn-sm o_invoice_new">
                                    <span>New Invoice</span>
                                </button>
                            </t>
                            <t t-if="journal_type == 'purchase'">
                                <button type="object" name="action_create_new" class="btn btn-primary btn-sm">
                                    <span>New Bill</span>
                                </button>
                            </t>
                        </div>
                        <div class="col-xs-6 o_kanban_primary_right">
                            <div class="row">
                                <div class="col-xs-6">
                                    <a type="object" name="open_action" context="{'search_default_draft': '1'}">
                                        <span t-if="journal_type == 'sale'" title="Invoices to validate"><t t-esc="dashboard.number_draft"/> Invoices to validate</span>
                                        <span t-if="journal_type == 'purchase'" title="Draft bills"><t t-esc="dashboard.number_draft"/> Draft bills</span>
                                    </a>
                                </div>
                                <div class="col-xs-6 text-right">
                                    <span><t t-esc="dashboard.sum_draft"/></span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xs-6">
                                    <a type="object" t-if="journal_type == 'sale'" name="open_action"
                                    context="{'search_default_unpaid':1}" id="account_dashboard_sale_pay_link">
                                        <t t-esc="dashboard.number_waiting"/> Awaiting payments
                                    </a>

                                    <a type="object" t-if="journal_type == 'purchase'" name="open_action"
                                    context="{'search_default_unpaid':1}" id="account_dashboard_purchase_pay_link">
                                        <t t-esc="dashboard.number_waiting"/> Payments to do
                                    </a>
                                </div>
                                <div class="col-xs-6 text-right">
                                    <span><t t-esc="dashboard.sum_waiting"/></span>
                                </div>
                            </div>
                        </div>
                    </t>
                    <t t-name="JournalBodyGraph">
                        <field name="kanban_dashboard_graph" t-att-graph_type="_.contains(['cash','bank'],journal_type) ? 'line' : 'bar'" widget="dashboard_graph"/>
                    </t>
            </templates>
            </kanban>
        </field>
    </record>

    <record id="open_account_journal_dashboard_kanban" model="ir.actions.act_window">
        <field name="name">Accounting Dashboard</field>
        <field name="res_model">account.journal</field>
        <field name="view_type">form</field>
        <field name="view_mode">kanban,form</field>
        <field name="view_id" ref="account_journal_dashboard_kanban_view"/>
        <field name="usage">menu</field>
        <field name="context">{'search_default_dashboard':1}</field>
        <field name="domain">[('belongs_to_company', '=', True)]</field>
        <field name="help" type="html">
            <p>
                This is the accounting dashboard. If you have not yet
                installed a chart of account, please install one first.
               <a type="action" name="%(open_account_charts_modules)d">Browse available countries.</a>
            </p>
        </field>
    </record>

    <menuitem id="menu_board_journal_1" name="Dashboard" action="open_account_journal_dashboard_kanban" parent="menu_finance" sequence="1" groups="account.group_account_user"/>
</odoo>
