<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Invoices -->
        <record id="view_invoice_line_calendar" model="ir.ui.view">
            <field name="name">account.invoice.calendar</field>
            <field name="model">account.invoice</field>
            <field name="arch" type="xml">
                <calendar string="Invoices" date_start="date_invoice" color="journal_id">
                    <field name="partner_id"/>
                    <field name="amount_total_signed"/>
                </calendar>
            </field>
        </record>

        <record model="ir.ui.view" id="view_invoice_pivot">
            <field name="name">account.invoice.pivot</field>
            <field name="model">account.invoice</field>
            <field name="arch" type="xml">
                <pivot string="Invoices" display_quantity="true">
                    <field name="partner_id"/>
                    <field name="amount_total_signed" type="measure"/>
                </pivot>
            </field>
        </record>

        <record model="ir.ui.view" id="view_invoice_graph">
            <field name="name">account.invoice.graph</field>
            <field name="model">account.invoice</field>
            <field name="arch" type="xml">
                <graph string="Invoices">
                    <field name="partner_id"/>
                    <field name="amount_total_signed" type="measure"/>
                </graph>
            </field>
        </record>

        <record id="view_invoice_line_tree" model="ir.ui.view">
            <field name="name">account.invoice.line.tree</field>
            <field name="model">account.invoice.line</field>
            <field name="arch" type="xml">
                <tree string="Invoice Line">
                    <field name="name"/>
                    <field name="account_id" groups="account.group_account_user"/>
                    <field name="quantity"/>
                    <field name="uom_id" groups="product.group_uom"/>
                    <field name="price_unit"/>
                    <field name="discount" groups="base.group_no_one"/>
                    <field name="price_subtotal"/>
                    <field name="currency_id" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="view_invoice_line_form" model="ir.ui.view">
            <field name="name">account.invoice.line.form</field>
            <field name="model">account.invoice.line</field>
            <field name="arch" type="xml">
                <form string="Invoice Line">
                    <group>
                        <group>
                            <field name="partner_id" invisible="1"/>
                            <field name="invoice_type" invisible="1"/>
                            <field name="company_id" invisible="1"/>
                            <field name="product_id"
                                 context="{'partner_id': partner_id}"/>
                            <label for="quantity"/>
                            <div>
                                <field name="quantity" class="oe_inline"/>
                                <field name="uom_id" class="oe_inline" groups="product.group_uom"/>
                            </div>
                            <field name="price_unit"/>
                            <field name="discount" groups="base.group_no_one"/>
                            <field name="currency_id" invisible="1"/>
                        </group>
                        <group>
                            <field name="analytic_tag_ids" groups="analytic.group_analytic_accounting" widget="many2many_tags" options="{'color_field': 'color'}"/>
                            <field domain="[('company_id', '=', company_id)]" name="account_id" groups="account.group_account_user"/>
                            <field name="invoice_line_tax_ids" context="{'type': invoice_type}" domain="[('type_tax_use','!=','none'),('company_id', '=', company_id)]" widget="many2many_tags" options="{'no_create': True}"/>
                            <field domain="[('company_id', '=', company_id)]" name="account_analytic_id" groups="analytic.group_analytic_accounting"/>
                            <field name="company_id" groups="base.group_multi_company" readonly="1"/>
                        </group>
                    </group>
                    <label for="name"/>
                    <field name="name"/>
                </form>
            </field>
        </record>

        <record id="view_invoice_tax_tree" model="ir.ui.view">
            <field name="name">account.invoice.tax.tree</field>
            <field name="model">account.invoice.tax</field>
            <field name="arch" type="xml">
                <tree string="Manual Invoice Taxes">
                    <field name="sequence"/>
                    <field name="manual"/>
                    <field name="name"/>
                    <field name="account_id" groups="account.group_account_user"/>
                    <field name="base"/>
                    <field name="amount_total"/>
                    <field name="currency_id" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="view_invoice_tax_form" model="ir.ui.view">
            <field name="name">account.invoice.tax.form</field>
            <field name="model">account.invoice.tax</field>
            <field name="arch" type="xml">
                <form string="Manual Invoice Taxes">
                    <group col="4">
                        <field name="name"/>
                        <field name="sequence"/>
                        <field name="account_id" groups="account.group_account_user"/>
                        <field name="account_analytic_id" domain="[('company_id', '=', parent.company_id)]" groups="analytic.group_analytic_accounting"/>
                        <field name="manual"/>
                        <field name="amount_total"/>
                        <field name="base"/>
                        <field name="currency_id" invisible="1"/>
                    </group>
                </form>
            </field>
        </record>

        <record id="invoice_tree" model="ir.ui.view">
            <field name="name">account.invoice.tree</field>
            <field name="model">account.invoice</field>
            <field name="arch" type="xml">
                <tree decoration-info="state == 'draft'" decoration-muted="state == 'cancel'" string="Invoice">
                    <field name="partner_id" groups="base.group_user" string="Customer"/>
                    <field name="date_invoice"/>
                    <field name="number"/>
                    <field name="commercial_partner_id" invisible="1"/>
                    <field name="reference" invisible="1"/>
                    <field name="name" invisible="1"/>
                    <field name="journal_id" invisible="1"/>
                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                    <field name="user_id"/>
                    <field name="date_due"/>
                    <field name="origin"/>
                    <field name="amount_total_signed" string="Total" sum="Total"/>
                    <field name="residual_signed" string="Amount Due" sum="Amount Due"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="company_currency_id" invisible="1"/>
                    <field name="state"/>
                    <field name="type" invisible="context.get('type',True)"/>
                </tree>
            </field>
        </record>

        <record model="ir.ui.view" id="invoice_kanban">
            <field name="name">account.invoice.kanban</field>
            <field name="model">account.invoice</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="number"/>
                    <field name="partner_id"/>
                    <field name="amount_total_signed"/>
                    <field name="date_invoice"/>
                    <field name="state"/>
                    <field name="currency_id"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title"><span><t t-esc="record.partner_id.value"/></span></strong>
                                    </div>
                                    <strong><field name="amount_total_signed" widget="monetary"/></strong>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left text-muted">
                                        <span><t t-esc="record.number.value"/> <t t-esc="record.date_invoice.value"/></span>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <span class="pull-right text-right">
                                            <field name="state" widget="label_selection" options="{'classes': {'draft': 'default', 'cancel': 'default', 'none': 'danger', 'open': 'warning',
                                            'paid': 'success'}}"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="invoice_supplier_tree" model="ir.ui.view">
            <field name="name">account.invoice.supplier.tree</field>
            <field name="model">account.invoice</field>
            <field name="arch" type="xml">
                <tree decoration-info="state == 'draft'" decoration-muted="state == 'cancel'" string="Invoice">
                    <field name="partner_id" groups="base.group_user" string="Vendor"/>
                    <field name="date_invoice" string="Bill Date"/>
                    <field name="number"/>
                    <field name="reference"/>
                    <field name="commercial_partner_id" invisible="1"/>
                    <field name="name" invisible="1"/>
                    <field name="journal_id" invisible="1"/>
                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                    <field name="date_due"/>
                    <field name="origin"/>
                    <field name="amount_total_signed" string="Total" sum="Total"/>
                    <field name="residual_signed" string="To Pay" sum="To pay"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="company_currency_id" invisible="1"/>
                    <field name="state"/>
                    <field name="type" invisible="context.get('type',True)"/>
                </tree>
            </field>
        </record>

        <record id="invoice_supplier_form" model="ir.ui.view">
            <field name="name">account.invoice.supplier.form</field>
            <field name="model">account.invoice</field>
            <field name="priority">2</field>
            <field name="arch" type="xml">
                <form string="Vendor Bill">
                <header>
                        <button name="action_invoice_open" type="object" states="draft" string="Validate" class="oe_highlight" groups="account.group_account_invoice"/>
                        <button name="%(action_account_invoice_payment)d" type="action" states="open" string="Register Payment" groups="account.group_account_invoice" class="oe_highlight"/>
                        <button name="%(action_account_invoice_refund)d" type='action' string='Ask for a Credit Note' groups="account.group_account_invoice" attrs="{'invisible': ['|',('type', 'in', ['in_refund','out_refund']),('state','not in',('open','paid'))]}"/>
                        <button name="action_invoice_draft" states="cancel" string="Set to Draft" type="object" groups="account.group_account_invoice"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,open,paid" />
                </header>
                <div groups="account.group_account_invoice" class="alert alert-info" role="alert" style="margin-bottom:0px;" attrs="{'invisible': [('has_outstanding','=',False)]}">
                    You have <bold><a class="alert-link" href="#outstanding" role="button">outstanding debits</a></bold> for this supplier. You can allocate them to mark this bill as paid.
                </div>
                <field name="has_outstanding" invisible="1"/>
                <sheet string="Vendor Bill">
                    <div>
                        <label string="Draft Bill" attrs="{'invisible': ['|',('state','!=','draft'), ('type','!=','in_invoice')]}"/>
                        <label string="- First Number:" attrs="{'invisible': [('sequence_number_next_prefix','=',False)]}"/>
                        <label string="Draft Credit Note" attrs="{'invisible': ['|',('state','!=','draft'), ('type','!=','in_refund')]}"/>
                        <label string="Bill " attrs="{'invisible': ['|',('state', '=', 'draft'), ('type','!=','in_invoice')]}"/>
                        <label string="Credit Note " attrs="{'invisible': ['|',('state', '=', 'draft'), ('type','!=','in_refund')]}"/>
                        <h1 class="mt0">
                            <field name="number" class="oe_inline"  attrs="{'invisible': [('state', '=', 'draft')]}"/>
                            <div  attrs="{'invisible': [('sequence_number_next_prefix','=',False)]}">
                                <field name="sequence_number_next_prefix" class="oe_inline"/>
                                <field name="sequence_number_next" class="oe_inline"/>
                            </div>
                        </h1>
                    </div>
                    <field name="type" invisible="1"/>
                    <group>
                        <group>
                            <field string="Vendor" name="partner_id"
                              context="{'default_customer': 0, 'search_default_supplier': 1, 'default_supplier': 1, 'default_company_type': 'company'}"
                              domain="[('supplier', '=', True)]"/>
                            <field name="reference" string="Vendor Reference"/>
                        </group>
                        <group>
                            <field name="origin" attrs="{'invisible': [('origin', '=', False)]}"/>
                            <field name="date_invoice" string="Bill Date"/>
                            <field name="date_due" attrs="{'readonly': ['|',('payment_term_id','!=',False), ('state', 'in', ['open', 'paid'])]}" force_save="1"/>
                            <field name="move_name" invisible="1"/>
                            <field name="currency_id" options="{'no_create': True, 'no_open': True}" groups="base.group_multi_currency"/>
                            <field name="company_currency_id" invisible="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Bill">
                            <field context="{'type': type, 'journal_id': journal_id}" name="invoice_line_ids">
                                <tree string="Bill lines" editable="bottom">
                                    <field name="sequence" widget="handle" />
                                    <field name="product_id"
                                        context="{'partner_id': parent.partner_id}" domain="[('purchase_ok','=',True)]"/>
                                    <field name="name"/>
                                    <field name="company_id" invisible="1"/>
                                    <field name="account_id" groups="account.group_account_user"
                                        domain="[('company_id', '=', parent.company_id), ('internal_type', '=', 'other'), ('deprecated', '=', False)]"/>
                                    <field name="account_analytic_id" groups="analytic.group_analytic_accounting"
                                        domain="[('company_id', '=', parent.company_id)]"
                                        context="{'default_partner_id': parent.partner_id}"/>
                                    <field name="analytic_tag_ids" groups="analytic.group_analytic_accounting" widget="many2many_tags" options="{'color_field': 'color'}"/>
                                    <field name="quantity"/>
                                    <field name="uom_id" groups="product.group_uom"/>
                                    <field name="price_unit"/>
                                    <field name="discount" groups="base.group_no_one"/>
                                    <field name="invoice_line_tax_ids" widget="many2many_tags" options="{'no_create': True}" context="{'type':parent.type}"
                                        domain="[('type_tax_use','=','purchase'),('company_id', '=', parent.company_id)]"/>
                                    <field name="price_subtotal"/>
                                    <field name="currency_id" invisible="1"/>
                                </tree>
                            </field>
                            <group>
                                <group>
                                    <div class="oe_inline">
                                        <field name="tax_line_ids">
                                            <tree editable="bottom" string="Taxes">
                                                <field name="name"/>
                                                <!--Need invisible fields for on_change to create all necessary info -->
                                                <field name="tax_id" invisible="1"/>
                                                <field name="sequence" invisible="1"/>
                                                <field name="manual" invisible="1"/>
                                                <field name="account_id" groups="account.group_account_user"/>
                                                <field name="account_analytic_id" domain="[('company_id', '=', parent.company_id)]" groups="analytic.group_analytic_accounting"/>
                                                <field name="amount"/>
                                                <field name="amount_rounding" invisible="1"/>
                                                <field name="amount_total" invisible="1"/>
                                                <field name="currency_id" invisible="1" force_save="1"/>
                                            </tree>
                                        </field>
                                    </div>
                                </group>
                                <group class="oe_subtotal_footer oe_right">
                                    <field name="amount_untaxed"/>
                                    <field name="amount_tax"/>
                                    <field name="amount_total" class="oe_subtotal_footer_separator"/>
                                    <field name="payments_widget" colspan="2" nolabel="1" widget="payment"/>
                                    <field name="residual" class="oe_subtotal_footer_separator" attrs="{'invisible': [('state', '=', 'draft')]}"/>
                                    <field name="reconciled" invisible="1"/>
                                    <field name="outstanding_credits_debits_widget" colspan="2" nolabel="1" widget="payment" attrs="{'invisible': [('state', 'not in', 'open')]}"/>
                                </group>
                            </group>
                            <div>
                                <field name="comment" placeholder="Additional notes..."/>
                            </div>
                        </page>
                        <page string="Other Info" name="other_info">
                            <group>
                                <group>
                                    <field name="journal_id" groups="account.group_account_user" options="{'no_create': True}"
                                        attrs="{'readonly':[('move_name','!=',False)]}"/>
                                    <field domain="[('partner_id', '=', partner_id)]" name="partner_bank_id" invisible="1"/>
                                    <field name="user_id" string="Responsible"/>
                                    <field domain="[('company_id', '=', company_id), ('internal_type', '=', 'payable'), ('deprecated', '=', False)]"
                                name="account_id" groups="account.group_account_user"/>
                                    <field name="payment_term_id" options="{'no_create': True}"/>
                                    <field name="name"/>
                                </group>
                                <group>
                                    <field name="fiscal_position_id" options="{'no_create': True}" placeholder="Auto-detect"/>
                                    <field name="move_id" groups="account.group_account_user" attrs="{'invisible': [('move_id', '=', False)]}"/>
                                    <field name="date" domain="[('state', '=', 'draft'), ('company_id', '=', company_id)]" groups="account.group_account_user"/>
                                    <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
                </form>
            </field>
        </record>

        <record id="invoice_form" model="ir.ui.view">
            <field name="name">account.invoice.form</field>
            <field name="model">account.invoice</field>
            <field name="arch" type="xml">
                <form string="Invoice" class="o_invoice_form">
                <header>
                    <button name="action_invoice_sent" type="object" string="Send by Email" attrs="{'invisible':['|',('sent','=',True), ('state', 'not in', ('open','paid'))]}" class="oe_highlight o_invoice_send" groups="base.group_user"/>
                    <button name="invoice_print" string="Print Invoice" type="object" attrs="{'invisible':['|',('sent','=',True), ('state', 'not in', ('open','paid'))]}" class="oe_highlight" groups="base.group_user"/>
                    <button name="action_invoice_sent" type="object" string="Send by Email" attrs="{'invisible':['|',('sent','=',False), ('state', 'not in', ('open','paid'))]}" groups="base.group_user"/>
                    <button name="invoice_print" string="Print Invoice" type="object" attrs="{'invisible':['|',('sent','=',False), ('state', 'not in', ('open','paid'))]}" groups="base.group_user"/>
                    <button name="%(action_account_invoice_payment)d" id="account_invoice_payment_btn" type="action" states="open" string="Register Payment" groups="account.group_account_invoice" class="oe_highlight"/>
                    <button name="action_invoice_open" type="object" states="draft" string="Validate" class="oe_highlight o_invoice_validate" groups="account.group_account_invoice"/>
                    <button name="%(action_account_invoice_refund)d" type='action' string='Add Credit Note' groups="account.group_account_invoice" attrs="{'invisible': ['|',('type', '=', 'out_refund'), ('state', 'not in', ('open','paid'))]}"/>
                    <button name="action_invoice_draft" states="cancel" string="Reset to Draft" type="object" groups="account.group_account_invoice"/>
                    <field name="state" widget="statusbar" nolabel="1" statusbar_visible="draft,open,paid"/>
                </header>
                <div groups="account.group_account_invoice" class="alert alert-info" role="alert" style="margin-bottom:0px;" attrs="{'invisible': [('has_outstanding','=',False)]}">
                    You have <bold><a class="alert-link" href="#outstanding" role="button">outstanding payments</a></bold> for this customer. You can allocate them to mark this invoice as paid.
                </div>
                <field name="has_outstanding" invisible="1"/>
                <sheet string="Invoice">
                    <div class="oe_button_box" name="button_box">
                    </div>
                    <label string="Credit Note" attrs="{'invisible': ['|',('state','=','draft'), ('type','!=','out_refund')]}"/>
                    <h1 class="mt0">
                        <div attrs="{'invisible': [('sequence_number_next_prefix','!=',False)]}">
                            <label string="Draft Invoice" attrs="{'invisible': ['|',('state','not in',('draft',)), ('type','!=','out_invoice')]}"/>
                            <label string="Draft Credit Note" attrs="{'invisible': ['|',('state','not in',('draft',)), ('type','!=','out_refund')]}"/>
                            <field name="number" readonly="1" class="oe_inline" attrs="{'invisible': [('state','in',('draft',))]}"/>
                        </div>
                        <div  attrs="{'invisible': [('sequence_number_next_prefix','=',False)]}">
                            <label string="Invoice Number:"/><br/>
                            <field name="sequence_number_next_prefix" class="oe_inline"/>
                            <field name="sequence_number_next" class="oe_inline"/>
                        </div>
                    </h1>
                    <field name="type" invisible="1"/>
                    <group>
                        <group>
                            <field string="Customer" name="partner_id"
                                context="{'search_default_customer':1, 'show_address': 1, 'default_company_type': 'company'}"
                                options='{"always_reload": True, "no_quick_create": True}'
                                domain="[('customer', '=', True)]"/>
                            <field name="payment_term_id"/>
                            <field name="cash_rounding_id" groups="account.group_cash_rounding"/>
                        </group>
                        <group>
                            <field name="date_invoice"/>
                            <field name="date_due" attrs="{'readonly': ['|',('payment_term_id','!=',False), ('state', 'in', ['open', 'paid'])]}" force_save="1"/>
                            <field name="move_name" invisible="1"/>
                            <field name="user_id" groups="base.group_user"/>
                            <label for="currency_id" groups="base.group_multi_currency"/>
                            <div groups="base.group_multi_currency">
                                <field name="currency_id" options="{'no_create': True, 'no_open': True}" class="oe_inline"/>
                                <field name="company_currency_id" invisible="1"/>
                            </div>
                        </group>
                    </group>
                    <field name="sent" invisible="1"/>
                    <notebook colspan="4">
                        <page string="Invoice Lines">
                            <field name="invoice_line_ids" nolabel="1" widget="one2many_list" mode="tree,kanban" context="{'type': type, 'journal_id': journal_id, 'default_invoice_id': id}">
                                <tree string="Invoice Lines" editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="product_id" domain="[('sale_ok','=',True)]"/>
                                    <field name="origin" invisible="1"/>
                                    <field name="is_rounding_line" invisible="1"/>
                                    <field name="name"/>
                                    <field name="company_id" invisible="1"/>
                                    <field name="account_id" groups="account.group_account_user"
                                        domain="[('company_id', '=', parent.company_id), ('internal_type', '=', 'other'), ('deprecated', '=', False)]"/>
                                    <field name="account_analytic_id" groups="analytic.group_analytic_accounting"
                                        domain="[('company_id', '=', parent.company_id)]"
                                        context="{'default_partner_id': parent.partner_id}"/>
                                    <field name="analytic_tag_ids" groups="analytic.group_analytic_accounting" widget="many2many_tags" options="{'color_field': 'color'}"/>
                                    <field name="quantity"/>
                                    <field name="uom_id" groups="product.group_uom"/>
                                    <field name="price_unit"/>
                                    <field name="discount" groups="base.group_no_one"/>
                                    <field name="invoice_line_tax_ids" widget="many2many_tags" options="{'no_create': True}" context="{'type':parent.type}"
                                        domain="[('type_tax_use','=','sale'),('company_id', '=', parent.company_id)]"/>
                                    <field name="price_subtotal"/>
                                    <field name="currency_id" invisible="1"/>
                                </tree>
                                <kanban class="o_kanban_mobile">
                                    <field name="product_id"/>
                                    <field name="price_subtotal"/>
                                    <field name="quantity"/>
                                    <field name="uom_id" groups="product.group_uom"/>
                                    <templates>
                                        <t t-name="kanban-box">
                                            <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                                                <div class="row">
                                                    <div class="col-xs-12">
                                                        <strong><span><t t-esc="record.product_id.value or 'None'"/></span></strong>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-xs-8 text-muted">
                                                        <span><t t-esc="record.quantity.value"/> <t t-esc="record.uom_id.value"/></span>
                                                    </div>
                                                    <div class="col-xs-4 text-muted">
                                                        <span class="text-right"><t t-esc="record.price_subtotal.value"/></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </t>
                                    </templates>
                                </kanban>
                            </field>
                            <group class="oe_subtotal_footer oe_right">
                                <field name="amount_untaxed"/>
                                <field name="amount_tax"/>
                                <field name="amount_total" class="oe_subtotal_footer_separator"/>
                                <field name="payments_widget" colspan="2" nolabel="1" widget="payment"/>
                                <field name="residual" class="oe_subtotal_footer_separator" attrs="{'invisible': [('state', '=', 'draft')]}"/>
                                <field name="reconciled" invisible="1"/>
                                <field name="outstanding_credits_debits_widget" colspan="2" nolabel="1" widget="payment" attrs="{'invisible': [('state', 'not in', 'open')]}"/>
                            </group>
                            <field name="comment" placeholder="Terms and conditions..."/>
                        </page>
                        <page string="Other Info" name="other_info">
                            <group>
                                <group>
                                    <field name="fiscal_position_id" options="{'no_create': True}" />
                                    <field name="journal_id" groups="account.group_account_user"
                                        options="{'no_create': True}" attrs="{'readonly':[('move_name','!=',False)]}"/>
                                    <field domain="[('company_id', '=', company_id),('internal_type','=', 'receivable'), ('deprecated', '=', False)]"
                                        name="account_id" groups="account.group_account_user"/>
                                    <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                                    <field domain="[('partner_id.ref_company_ids', 'in', [company_id])]" name="partner_bank_id" invisible="1"/>
                                </group>
                                <group>
                                    <field name="move_id" groups="account.group_account_user" attrs="{'invisible': [('move_id', '=', False)]}"/>
                                    <field name="origin" groups="base.group_user" attrs="{'invisible': [('origin', '=', False)]}"/>
                                    <field name="name"/>
                                </group>
                            </group>
                            <field name="tax_line_ids">
                                <tree editable="bottom" string="Taxes" create="0">
                                    <field name="name"/>
                                    <!--Need invisible fields for on_change to create all necessary info -->
                                    <field name="tax_id" invisible="1"/>
                                    <field name="account_analytic_id" invisible="1"/>
                                    <field name="sequence" invisible="1"/>
                                    <field name="manual" invisible="1"/>
                                    <field name="account_id" groups="account.group_account_user"/>
                                    <field name="amount" invisible="1"/>
                                    <field name="amount_rounding" invisible="1"/>
                                    <field name="amount_total"/>
                                    <field name="currency_id" invisible="1" force_save="1"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers" groups="base.group_user"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
                </form>
            </field>
        </record>

        <!-- Custom reports (aka filters) -->
        <record id="filter_invoice_salespersons" model="ir.filters">
            <field name="name">By Salespersons</field>
            <field name="model_id">account.invoice</field>
            <field name="user_id" eval="False"/>
            <field name="context">{'group_by': ['date_invoice:month', 'user_id']}</field>
        </record>

        <record id="view_account_invoice_filter" model="ir.ui.view">
            <field name="name">account.invoice.select</field>
            <field name="model">account.invoice</field>
            <field name="arch" type="xml">
                <search string="Search Invoice">
                    <field name="number" string="Invoice" filter_domain="['|','|','|', ('number','ilike',self), ('origin','ilike',self), ('reference', 'ilike', self), ('partner_id', 'child_of', self)]"/>
                    <field name="journal_id" />
                    <filter name="draft" string="Draft" domain="[('state','=','draft')]"/>
                    <filter name="unpaid" string="Open" domain="[('state', '=', 'open')]"/>
                    <filter name="paid" string="Paid" domain="[('state', '=', 'paid')]"/>
                    <filter name="late" string="Overdue" domain="['&amp;', ('date_due', '&lt;', time.strftime('%%Y-%%m-%%d')), ('state', '=', 'open')]" help="Overdue invoices, maturity date passed"/>
                    <separator/>
                    <field name="partner_id" operator="child_of"/>
                    <field name="user_id" string="Salesperson" domain="[('share','=', False)]"/>
                    <field name="date" string="Period"/>
                    <separator/>
                    <filter domain="[('user_id','=',uid)]" help="My Invoices"/>
                    <separator/>
                    <filter string="My Activities" name="activities_my"
                        domain="[('activity_ids.user_id', '=', uid)]"/>
                    <separator/>
                    <filter string="Late Activities" name="activities_overdue"
                        domain="[('activity_ids.date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                        help="Show all records which has next action date is before today"/>
                    <filter string="Today Activities" name="activities_today"
                        domain="[('activity_ids.date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter string="Future Activities" name="activities_upcoming_all"
                        domain="[('activity_ids.date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))
                        ]"/>
                    <group expand="0" string="Group By">
                        <filter name="group_by_partner_id" string="Partner" context="{'group_by':'commercial_partner_id'}"/>
                        <filter string="Salesperson" context="{'group_by':'user_id'}"/>
                        <filter name="status" string="Status" context="{'group_by':'state'}"/>
                        <separator/>
                        <filter string="Invoice Date" context="{'group_by':'date_invoice'}"/>
                        <filter string="Due Date" context="{'group_by':'date_due'}"/>
                    </group>
               </search>
            </field>
        </record>

        <!-- TODO: remove in master -->
        <record id="view_account_invoice_filter_inherit_invoices" model="ir.ui.view">
            <field name="name">account.invoice.select.invoices</field>
            <field name="model">account.invoice</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="view_account_invoice_filter"/>
            <field name="arch" type="xml">
                <data/>
            </field>
        </record>

        <!-- TODO: remove in master -->
        <record id="view_account_invoice_filter_inherit_credit_notes" model="ir.ui.view">
            <field name="name">account.invoice.select.credit.notes</field>
            <field name="model">account.invoice</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="view_account_invoice_filter"/>
            <field name="arch" type="xml">
                <data/>
            </field>
        </record>

        <record id="action_invoice_tree" model="ir.actions.act_window">
            <field name="name">Invoices</field>
            <field name="res_model">account.invoice</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form,calendar,graph</field>
            <field name="view_id" ref="invoice_tree"/>
            <field name="context">{'type':'out_invoice'}</field>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
        </record>

        <record id="action_invoice_refund_out_tree" model="ir.actions.act_window">
            <field name="name">Invoices</field>
            <field name="res_model">account.invoice</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form,kanban,calendar,graph,pivot</field>
            <field name="view_id" ref="invoice_tree"/>
            <field name="domain">[('type','in', ['out_invoice', 'out_refund']), ('state', 'not in', ['draft', 'cancel'])]</field>
            <field name="context">{'default_type':'out_invoice', 'type':'out_invoice', 'journal_type': 'sale'}</field>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
        </record>

        <record id="action_invoice_refund_out_tree_tree" model="ir.actions.act_window.view">
            <field eval="1" name="sequence"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="invoice_tree"/>
            <field name="act_window_id" ref="action_invoice_refund_out_tree"/>
        </record>

        <record id="action_invoice_refund_out_tree_form" model="ir.actions.act_window.view">
            <field eval="2" name="sequence"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="invoice_form"/>
            <field name="act_window_id" ref="action_invoice_refund_out_tree"/>
        </record>

        <record id="action_invoice_tree_pending_invoice" model="ir.actions.act_window">
            <field name="name">Pending Invoice</field>
            <field name="res_model">account.invoice</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form,calendar,graph</field>
            <field name="view_id" ref="invoice_tree"/>
            <field name="context">{'type':'out_invoice'}</field>
            <field name="domain">[('state','=','draft')]</field>
           <!-- <field name="search_view_id" ref="view_account_invoice_filter"/>-->
        </record>

        <record id="action_invoice_tree_view1" model="ir.actions.act_window.view">
            <field eval="1" name="sequence"/>
            <field name="view_mode">tree</field>
            <field name="act_window_id" ref="action_invoice_tree"/>
        </record>

        <record id="action_invoice_tree_view2" model="ir.actions.act_window.view">
            <field eval="2" name="sequence"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="invoice_form"/>
            <field name="act_window_id" ref="action_invoice_tree"/>
        </record>

        <record id="action_invoice_tree1" model="ir.actions.act_window">
            <field name="name">Customer Invoices</field>
            <field name="res_model">account.invoice</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,kanban,form,calendar,pivot,graph</field>
            <field eval="False" name="view_id"/>
            <field name="domain">[('type','=','out_invoice')]</field>
            <field name="context">{'type':'out_invoice', 'journal_type': 'sale'}</field>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                Click to create a customer invoice.
              </p><p>
                Odoo's electronic invoicing allows to ease and fasten the
                collection of customer payments. Your customer receives the
                invoice by email and he can pay online and/or import it
                in his own system.
              </p><p>
                The discussions with your customer are automatically displayed at
                the bottom of each invoice.
              </p>
            </field>
        </record>


        <record id="action_invoice_tree1_view1" model="ir.actions.act_window.view">
            <field eval="1" name="sequence"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="invoice_tree"/>
            <field name="act_window_id" ref="action_invoice_tree1"/>
        </record>

        <record id="action_invoice_tree1_view2" model="ir.actions.act_window.view">
            <field eval="2" name="sequence"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="invoice_form"/>
            <field name="act_window_id" ref="action_invoice_tree1"/>
        </record>

        <menuitem action="action_invoice_tree1" id="menu_action_invoice_tree1" parent="menu_finance_receivables_documents" sequence="1"/>

        <record id="action_invoice_out_refund" model="ir.actions.act_window">
            <field name="name">Customer Credit Notes</field>
            <field name="res_model">account.invoice</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,kanban,form,calendar,pivot,graph</field>
            <field eval="False" name="view_id"/>
            <field name="domain">[('type','=','out_refund')]</field>
            <field name="context">{'default_type': 'out_refund', 'type': 'out_refund', 'journal_type': 'sale'}</field>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                Click to create a credit note.
              </p><p>
                Note that the easiest way to create a credit note is to do it directly form
                the customer invoice, to refund it totally or partially.
              </p>
            </field>
        </record>

        <record id="action_invoice_out_refund_tree" model="ir.actions.act_window.view">
            <field eval="1" name="sequence"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="invoice_tree"/>
            <field name="act_window_id" ref="action_invoice_out_refund"/>
        </record>

        <record id="action_invoice_out_refund_form" model="ir.actions.act_window.view">
            <field eval="2" name="sequence"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="invoice_form"/>
            <field name="act_window_id" ref="action_invoice_out_refund"/>
        </record>

        <menuitem action="action_invoice_out_refund"
                  id="menu_action_invoice_out_refund"
                  parent="menu_finance_receivables_documents"
                  sequence="1"/>

        <record id="action_invoice_tree2" model="ir.actions.act_window">
            <field name="name">Vendor Bills</field>
            <field name="res_model">account.invoice</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,kanban,form,calendar,pivot,graph</field>
            <field eval="False" name="view_id"/>
            <field name="domain">[('type','=','in_invoice')]</field>
            <field name="context">{'default_type': 'in_invoice', 'type': 'in_invoice', 'journal_type': 'purchase'}</field>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                Click to record a new vendor bill.
              </p><p>
                You can control the invoice from your vendor according to
                what you purchased or received.
              </p>
            </field>
        </record>

        <record id="action_invoice_supplier_tree1_view1" model="ir.actions.act_window.view">
            <field eval="1" name="sequence"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="invoice_supplier_tree"/>
            <field name="act_window_id" ref="action_invoice_tree2"/>
        </record>

        <record id="action_invoice__supplier_tree1_view2" model="ir.actions.act_window.view">
            <field eval="2" name="sequence"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="invoice_supplier_form"/>
            <field name="act_window_id" ref="action_invoice_tree2"/>
        </record>

        <menuitem action="action_invoice_tree2" id="menu_action_invoice_tree2" parent="menu_finance_payables_documents" sequence="1"/>


        <record id="action_invoice_in_refund" model="ir.actions.act_window">
            <field name="name">Vendor Credit Notes</field>
            <field name="res_model">account.invoice</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,kanban,form,calendar,pivot,graph</field>
            <field eval="False" name="view_id"/>
            <field name="domain">[('type','=','in_refund')]</field>
            <field name="context">{'default_type': 'in_refund', 'type': 'in_refund', 'journal_type': 'purchase'}</field>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                Click to record a new vendor credit note.
              </p>
            </field>
        </record>

        <record id="action_invoice_in_refund_tree" model="ir.actions.act_window.view">
            <field eval="1" name="sequence"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="invoice_supplier_tree"/>
            <field name="act_window_id" ref="action_invoice_in_refund"/>
        </record>

        <record id="action_invoice_in_refund_form" model="ir.actions.act_window.view">
            <field eval="2" name="sequence"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="invoice_supplier_form"/>
            <field name="act_window_id" ref="action_invoice_in_refund"/>
        </record>

        <menuitem action="action_invoice_in_refund"
                  id="menu_action_invoice_in_refund"
                  parent="menu_finance_payables_documents"
                  sequence="1"/>

        <act_window
           id="act_account_journal_2_account_invoice_opened"
           name="Unpaid Invoices"
           context="{'search_default_journal_id': [active_id], 'search_default_unpaid':1, 'default_journal_id': active_id}"
           domain="[('journal_id','=', active_id)]"
           res_model="account.invoice"
           src_model="account.journal"/>

    </data>
</odoo>
