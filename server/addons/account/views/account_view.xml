<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>


        <!-- Accounts -->

        <record id="view_account_form" model="ir.ui.view">
            <field name="name">account.account.form</field>
            <field name="model">account.account</field>
            <field name="arch" type="xml">
                <form string="Account">
                   <sheet>
                       <group>
                           <group>
                             <field name="code" placeholder="code"/>
                             <field name="name"/>
                             <field name="user_type_id" widget="selection"/>
                             <field name="tax_ids" widget="many2many_tags" domain="[('company_id','=',company_id)]"/>
                             <field name="tag_ids" widget="many2many_tags" domain="[('applicability', '!=', 'taxes')]" context="{'default_applicability': 'accounts'}" options="{'no_create_edit': True}"/>
                             <field name="group_id"/>
                             <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                             <field name="currency_id" options="{'no_create': True}" groups="base.group_multi_currency"/>
                             <field name="internal_type" invisible="1" readonly="1"/>
                             <label for="reconcile" attrs="{'invisible': [('internal_type','=','liquidity')]}"/>
                             <div attrs="{'invisible': [('internal_type','=','liquidity')]}">
                                <field name="reconcile"/>
                                <button name="action_open_reconcile" class="oe_link" type="object" string=" -> Reconcile" attrs="{'invisible': [('reconcile', '=', False)]}"/>
                             </div>
                             <field name="deprecated"/>
                           </group>
                           <group>
                               <div class="row">
                                   <div class="col-md-11 col-md-offset-1 col-sm-12 no-margin-top" style="background-color: #eee">
                                       <p class="text-center" style="font-weight: bold;">How do account types affect your reports?</p>
                                       <div class="col-md-6 col-sm-6">
                                           <div class="oe_account_help">
                                                <p class="oe_account_font_help text-center">Profit &amp; Loss</p>
                                                <p class="oe_account_font_help oe_account_font_title">Income</p>
                                                <p class="oe_account_font_help oe_account_font_title">Minus Cost of Revenue</p>
                                                <p class="oe_account_font_help" style="color: grey;border-top: 2px solid;border-bottom: 2px solid;">GROSS PROFIT</p>
                                                <p class="oe_account_font_help oe_account_font_title">Plus Other Income</p>
                                                <p class="oe_account_font_help oe_account_font_title">Minus Expenses</p>
                                                <p class="oe_account_font_help oe_account_font_content">Expenses</p>
                                                <p class="oe_account_font_help oe_account_font_content">Depreciation</p>
                                                <p class="oe_account_font_help" style="color: grey;border-top: 2px solid;border-bottom: 2px solid;">NET PROFIT</p>
                                                <div style="position: relative; width: 250px; height: 277px;" id="layer">
                                                   <div style="position: relative; left: 0px; top: 0px; visibility: visible; width: 250px; height: 277px;">
                                                     <svg pointer-events="none" overflow="auto" width="712.5" height="265" viewBox="-4 -4 712.5 265" style="position: relative; top: -4px; left: -4px; overflow: auto; visibility: visible; width: 400.5px; height: 265px;">
                                                       <defs>
                                                         <marker id="arrow" markerWidth="70" markerHeight="70" refx="10" refy="6" orient="auto">
                                                             <path d="M2,1 L2,12 L10,6 L2,1" stroke="rgb(173,216,230)" fill="rgb(173,216,230)"/>
                                                         </marker>
                                                       </defs>
                                                       <path pointer-events="visiblePainted" shape-rendering="auto" d="M 150 -110 q 270 100 150 150 t 160 120" fill="none" stroke-dasharray="8,3" stroke="rgb(173,216,230)" stroke-linecap="butt" stroke-width="4" stroke-opacity="1" stroke-linejoin="round" transform="matrix(1,0,0,1,0,0)" fill-opacity="1" style="marker-end: url(#arrow);">
                                                       </path>
                                                     </svg>
                                                   </div>
                                               </div>
                                           </div>
                                       </div>
                                       <div class="col-md-6 col-sm-6">
                                           <div class="oe_account_help">
                                               <p class="oe_account_font_help text-center">Balance Sheet</p>
                                               <p class="oe_account_font_help oe_account_font_title" style="color: grey;">Current Assets</p>
                                               <p class="oe_account_font_help oe_account_font_content">Receivable Accounts</p>
                                               <p class="oe_account_font_help oe_account_font_content">Prepayments</p>
                                               <p class="oe_account_font_help oe_account_font_title">Plus Bank</p>
                                               <p class="oe_account_font_help oe_account_font_title">Plus Fixed Assets</p>
                                               <p class="oe_account_font_help oe_account_font_title">Plus Non-Current Assets</p>
                                               <p class="oe_account_font_help" style="color: grey;border-top: 2px solid;border-bottom: 2px solid;">TOTAL ASSETS</p>
                                               <p class="oe_account_font_help oe_account_font_title">Minus Current Liabilities</p>
                                               <p class="oe_account_font_help oe_account_font_title">Minus Credit Card Accounts</p>
                                               <p class="oe_account_font_help oe_account_font_title">Minus Payable Accounts</p>
                                               <p class="oe_account_font_help oe_account_font_title">Minus Non-Current Liabilities</p>
                                               <p class="oe_account_font_help" style="color: grey;border-top: 2px solid;border-bottom: 2px solid;">NET ASSETS</p>
                                               <p class="oe_account_font_help oe_account_font_title">Equity</p>
                                               <p class="oe_account_font_help oe_account_font_title">Plus Net Profit</p>
                                               <p class="oe_account_font_help" style="color: grey;border-top: 2px solid;border-bottom: 2px solid;">TOTAL EQUITY</p>
                                           </div>
                                       </div>
                                   </div>
                               </div>
                           </group>
                       </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="view_account_list" model="ir.ui.view">
            <field name="name">account.account.list</field>
            <field name="model">account.account</field>
            <field name="arch" type="xml">
                <tree string="Chart of accounts" toolbar="1" >
                    <field name="code"/>
                    <field name="name"/>
                    <field name="user_type_id"/>
                    <field name="currency_id" options="{'no_create': True}" groups="base.group_multi_currency"/>
                    <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <record id="view_account_account_kanban" model="ir.ui.view">
            <field name="name">account.account.kanban</field>
            <field name="model">account.account</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="name"/>
                    <field name="code"/>
                    <field name="user_type_id"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_global_click">
                                <div class="row">
                                    <div class="col-xs-6">
                                        <strong><field name="name"/></strong>
                                    </div>
                                    <div class="col-xs-6 text-right">
                                        <span class="badge"><t t-esc="record.code.value"/></span>
                                    </div>
                                </div>
                                <div>
                                    <strong>Type: </strong><t t-esc="record.user_type_id.value"/>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="view_account_search" model="ir.ui.view">
            <field name="name">account.account.search</field>
            <field name="model">account.account</field>
            <field name="arch" type="xml">
                <search string="Accounts">
                    <field name="name" filter_domain="['|', ('name','ilike',self), ('code','=like',str(self)+'%')]" string="Account"/>
                    <filter string="Receivable Accounts" domain="[('internal_type','=','receivable')]"/>
                    <filter string="Payable Accounts" domain="[('internal_type','=','payable')]"/>
                    <field name="user_type_id"/>
                    <group expand="0" string="Group By">
                        <filter string="Account Type" domain="" context="{'group_by':'user_type_id'}"/>
                    </group>
                </search>
            </field>
        </record>
        <record id="action_account_form" model="ir.actions.act_window">
            <field name="name">Chart of Accounts</field>
            <field name="res_model">account.account</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_account_search"/>
            <field name="view_id" ref="view_account_list"/>
            <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                Click to add an account.
              </p><p>
                An account is part of a ledger allowing your company
                to register all kinds of debit and credit transactions.
                Companies present their annual accounts in two main parts: the
                balance sheet and the income statement (profit and loss
                account). The annual accounts of a company are required by law
                to disclose a certain amount of information.
              </p>
            </field>
        </record>
        <menuitem action="action_account_form" id="menu_action_account_form" parent="account_account_menu" groups="account.group_account_user" sequence="0"/>

        <act_window
            id="account.action_all_partner_invoices"
            name="All Invoices"
            res_model="account.invoice"
            domain="[('partner_id', 'child_of', active_id)]"
            src_model="res.partner"/>

        <act_window
            id="act_account_acount_move_line_open_unreconciled"
            name="Unreconciled Entries"
            res_model="account.move.line"
            context="{'search_default_account_id':[active_id], 'search_default_unreconciled':1, 'default_account_id': active_id}"
            src_model="account.account"/>

        <act_window
            id="action_move_line_select"
            name="Journal Items"
            context="{'search_default_account_id': [active_id]}"
            res_model="account.move.line"
            src_model="account.account"/>

        <act_window
            id="action_move_select"
            name="Journal Entries"
            context="{'search_default_dummy_account_id': [active_id]}"
            res_model="account.move"/>

        <act_window
            id="action_move_line_select_by_type"
            name="Journal Items"
            context="{'search_default_user_type_id': [active_id]}"
            res_model="account.move.line"
            src_model="account.account.type"/>

        <act_window
            id="action_move_line_select_by_partner"
            name="Journal Items"
            context="{'search_default_partner_id': [active_id]}"
            res_model="account.move.line"
            src_model="res.partner"/>

        <!-- Account Journal -->
        <record id="view_account_journal_tree" model="ir.ui.view">
            <field name="name">account.journal.tree</field>
            <field name="model">account.journal</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <tree string="Account Journal">
                    <field name='sequence' widget='handle'/>
                    <field name="name"/>
                    <field name="type"/>
                    <field name="belongs_to_company" invisible="1"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>
        <record id="account_journal_view_kanban" model="ir.ui.view">
            <field name="name">account.journal.kanban</field>
            <field name="model">account.journal</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_content oe_kanban_global_click">
                                <div class="row">
                                    <div class="col-xs-6">
                                        <strong><field name="name"/></strong>
                                    </div>
                                    <div class="col-xs-6">
                                        <span class="pull-right"><field name="type"/></span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        <record id="view_account_journal_search" model="ir.ui.view">
            <field name="name">account.journal.search</field>
            <field name="model">account.journal</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <search string="Search Account Journal">
                    <field name="name" filter_domain="['|', ('name','ilike',self), ('code','ilike',self)]" string="Journal"/>
                    <filter string="Archived" name="inactive" domain="[('active','=',False)]"/>
                    <filter domain="[('type', '=', 'sale')]" string="Sales"/>
                    <filter domain="[('type', '=', 'purchase')]" string="Purchases"/>
                    <filter domain="['|', ('type', '=', 'cash'), ('type', '=', 'bank')]" string="Liquidity"/>
                    <filter domain="[('type', 'not in', ['sale', 'purchase', 'cash', 'bank'])]" string="Miscellaneous"/>
                    <filter name="dashboard" string="Favorites" domain="[('show_on_dashboard', '=', True)]"/>
                </search>
            </field>
        </record>
        <record id="view_account_journal_form" model="ir.ui.view">
            <field name="name">account.journal.form</field>
            <field name="model">account.journal</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <form string="Account Journal">
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button class="oe_stat_button" type="object" name="toggle_active" icon="fa-archive">
                                <field name="active" widget="boolean_button" options='{"terminology": "archive"}'/>
                            </button>
                        </div>
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1><field name="name" class="oe_inline"/></h1>
                        </div>
                        <group>
                            <group>
                                <field name="type"/>
                            </group>
                            <group>
                                <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                            </group>
                        </group>
                        <notebook>
                            <page name="journal_entries" string="Journal Entries">
                                <group>
                                    <group>
                                        <field name="code"/>
                                        <label for="sequence_number_next"/>
                                        <div>
                                            <field name="sequence_number_next" style="padding-right: 1.0em"/>
                                            <field name="sequence_id" required="0"
                                                   attrs="{'readonly': 1}" groups="base.group_no_one"/>
                                        </div>
                                        <field name="refund_sequence"
                                               attrs="{'invisible': [('type', 'not in', ['sale', 'purchase'])]}"
                                               groups="base.group_no_one"/>
                                        <label for="refund_sequence_number_next"
                                               attrs="{'invisible': ['|',('type', 'not in', ['sale', 'purchase']), ('refund_sequence', '!=', True)]}"/>
                                        <div attrs="{'invisible': ['|',('type', 'not in', ['sale', 'purchase']), ('refund_sequence', '!=', True)]}">
                                            <field name="refund_sequence_number_next" style="padding-right: 1.0em"/>
                                            <field name="refund_sequence_id" required="0"
                                                   attrs="{'readonly': 1}" groups="base.group_no_one"/>
                                        </div>
                                    </group>
                                    <group>
                                        <field name="default_debit_account_id" domain="[('deprecated', '=', False)]" groups="account.group_account_user"/>
                                        <field name="default_credit_account_id" domain="[('deprecated', '=', False)]" groups="account.group_account_user"/>
                                        <field name="currency_id" options="{'no_create': True}" groups="base.group_multi_currency"/>
                                    </group>
                                </group>
                            </page>
                            <page name="advanced_settings" string="Advanced Settings">
                                <group>
                                    <group string="Control-Access" groups="base.group_no_one">
                                        <div class="text-muted" colspan="2">Keep empty for no control</div>
                                        <field name="type_control_ids" widget="many2many_tags"/>
                                        <field name="account_control_ids" widget="many2many_tags"/>
                                    </group>
                                    <group string="Miscellaneous">
                                        <field name="inbound_payment_method_ids" widget="many2many_checkboxes" attrs="{'invisible': [('type', 'not in', ['bank', 'cash'])]}"/>
                                        <field name="outbound_payment_method_ids" widget="many2many_checkboxes" attrs="{'invisible': [('type', 'not in', ['bank', 'cash'])]}"/>
                                        <field name="group_invoice_lines" attrs="{'invisible': [('type', 'not in', ['sale', 'purchase'])]}"/>
                                    </group>
                                    <group string="Accounting App Options" groups="account.group_account_user">
                                        <field name="profit_account_id" attrs="{'invisible': [('type', '!=', 'cash')]}"/>
                                        <field name="loss_account_id" attrs="{'invisible': [('type', '!=', 'cash')]}"/>
                                        <field name="show_on_dashboard" groups="base.group_no_one"/>
                                    </group>
                                </group>
                            </page>
                            <page name="bank_account" string="Bank Account" attrs="{'invisible': [('type', '!=', 'bank')]}">
                                <group>
                                    <group>
                                        <field name="bank_account_id"/>
                                        <field name="bank_id"/>
                                    </group>
                                    <group >
                                        <field name="bank_statements_source" widget="radio" attrs="{'required': [('type', '=', 'bank')]}"  groups="account.group_account_user"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="view_account_bank_journal_tree" model="ir.ui.view">
            <field name="name">account.bank.journal.tree</field>
            <field name="model">account.journal</field>
            <field name="priority">10</field>
            <field name="arch" type="xml">
                <tree string="Account Journal">
                    <field name='sequence' widget='handle'/>
                    <field name="name"/>
                    <field name="bank_acc_number"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>
        <record id="account_bank_journal_view_kanban" model="ir.ui.view">
            <field name="name">account.bank.journal.kanban</field>
            <field name="model">account.journal</field>
            <field name="priority">10</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_content oe_kanban_global_click">
                                <div class="row">
                                    <div class="col-xs-12">
                                        <strong><field name="name"/></strong>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        <record id="view_account_bank_journal_form" model="ir.ui.view">
            <field name="name">account.bank.journal.form</field>
            <field name="model">account.journal</field>
            <field name="priority">10</field>
            <field name="arch" type="xml">
                <form string="Bank Account">
                    <sheet>
                        <field name="type" invisible="1"/>
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only" string="Bank Account Name"/>
                            <h1><field name="name"/></h1>
                        </div>
                        <group>
                            <group>
                                <field name="bank_account_id"/>
                                <field name="bank_acc_number" attrs="{'readonly': [('bank_account_id', '!=', False)]}"/>
                                <field name="bank_id" attrs="{'readonly': [('bank_account_id', '!=', False)]}"/>
                            </group>
                            <group>
                                <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                                <field name="currency_id" options="{'no_create': True}" groups="base.group_multi_currency"/>
                            </group>
                        </group>
                        <group>
                            <div name="advanced_info"> <!--The content of this div is hidden in the setup wizard inheriting this form view-->
                                <group name="receivables" string="Receivables">
                                    <field name="inbound_payment_method_ids" widget="many2many_checkboxes"/>
                                </group>
                                <group name="payables" string="Payables">
                                    <field name="outbound_payment_method_ids" widget="many2many_checkboxes"/>
                                </group>
                            </div>

                            <group name="bank_statement" string="Bank Statements" groups="account.group_account_user">
                                <label for="bank_statements_source"/>
                                <div name="bank_source">
                                    <field name="bank_statements_source" widget="radio" attrs="{'required': [('type', '=', 'bank')]}"/>
                                </div>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="action_account_bank_journal_form" model="ir.actions.act_window">
            <field name="name">Bank Accounts</field>
            <field name="res_model">account.journal</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('view_account_bank_journal_tree')}),
                (0, 0, {'view_mode': 'kanban', 'view_id': ref('account_bank_journal_view_kanban')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('view_account_bank_journal_form')})]"/>
            <field name="domain">[('type', '=', 'bank')]</field>
            <field name="context">{'default_type': 'bank'}</field>
            <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                Click to add a bank account.
              </p>
            </field>
        </record>
        <menuitem action="action_account_bank_journal_form" id="menu_action_account_bank_journal_form" parent="account_account_menu" groups="account.group_account_manager" sequence="8"/>

        <record id="action_account_journal_form" model="ir.actions.act_window">
            <field name="name">Journals</field>
            <field name="res_model">account.journal</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('view_account_journal_tree')}),
                (0, 0, {'view_mode': 'kanban', 'view_id': ref('account_journal_view_kanban')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('view_account_journal_form')})]"/>
            <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                Click to add a journal.
              </p><p>
                A journal is used to record transactions of all accounting data
                related to the day-to-day business.
              </p><p>
                A typical company may use one journal per payment method (cash,
                bank accounts, checks), one purchase journal, one sales journal
                and one for miscellaneous information.
              </p>
            </field>
        </record>
        <menuitem action="action_account_journal_form" id="menu_action_account_journal_form" parent="account_account_menu" groups="account.group_account_manager"/>

        <act_window
           id="act_account_journal_2_account_bank_statement"
           name="Bank statements"
           context="{'search_default_journal_id': active_id, 'default_journal_id': active_id}"
           res_model="account.bank.statement"
           src_model="account.journal"/>

        <act_window
           id="act_account_journal_2_account_move_line"
           name="Journal Items"
           context="{'search_default_journal_id': active_id, 'default_journal_id': active_id}"
           res_model="account.move.line"
           src_model="account.journal"
           view_mode="tree"/>

        <!-- Tags -->
        <record id="account_tag_view_form" model="ir.ui.view">
            <field name="name">Tags</field>
            <field name="model">account.account.tag</field>
            <field name="arch" type="xml">
                <form string="Tags">
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button class="oe_stat_button" type="object" name="toggle_active" icon="fa-archive">
                                <field name="active" widget="boolean_button" options='{"terminology": "archive"}'/>
                            </button>
                        </div>
                        <group>
                            <field name="name"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="account_tag_action" model="ir.actions.act_window">
            <field name="name">Account Tags</field>
            <field name="res_model">account.account.tag</field>
            <field name="view_type">form</field>
            <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                Click to add a new tag.
              </p>
            </field>
        </record>

        <!-- Bank statement -->

        <record id="view_account_bnk_stmt_cashbox" model="ir.ui.view">
            <field name="name">account.bnk_stmt_cashbox.form</field>
            <field name="model">account.bank.statement.cashbox</field>
            <field name="arch" type="xml">
                <form>
                    <group>
                        <field name="cashbox_lines_ids" nolabel="1">
                            <tree editable="bottom">
                                <field name="coin_value"/>
                                <field name="number"/>
                                <field name="subtotal" sum="Total"/>
                            </tree>
                        </field>
                    </group>
                    <footer>
                        <button string="Confirm" name="validate" type="object" class="btn-primary"/>
                        <button string="Cancel" class="btn-default" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_view_account_bnk_stmt_cashbox" model="ir.actions.act_window">
            <field name="name">Cash Control</field>
            <field name="res_model">account.bank.statement.cashbox</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="view_account_bnk_stmt_cashbox"/>
            <field name="target">new</field>
        </record>

        <record id="view_account_bnk_stmt_check" model="ir.ui.view">
            <field name="name">account.bnk_stmt_check.form</field>
            <field name="model">account.bank.statement.closebalance</field>
            <field name="arch" type="xml">
                <form>
                <div>
                    <p>The closing balance is different than the computed one!</p>
                    <p>Confirming this will create automatically a journal entry with the difference in the profit/loss account set on the cash journal.</p>
                    <footer>
                        <button string="Confirm" name="validate" type="object" class="btn-primary"/>
                        <button string="Cancel" class="btn-default" special="cancel"/>
                    </footer>
                </div>
                </form>
            </field>
        </record>

        <record id="action_view_account_bnk_stmt_check" model="ir.actions.act_window">
            <field name="name">Check Closing Balance</field>
            <field name="res_model">account.bank.statement.closebalance</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="view_account_bnk_stmt_check"/>
            <field name="target">new</field>
        </record>

        <record id="action_bank_reconcile_bank_statements" model="ir.actions.client">
            <field name="name">Reconciliation on Bank Statements</field>
            <field name="tag">bank_statement_reconciliation_view</field>
            <field name="context">{'statement_ids': [active_id]}</field>
        </record>

        <record id="view_bank_statement_tree" model="ir.ui.view">
            <field name="name">account.bank.statement.tree</field>
            <field name="model">account.bank.statement</field>
            <field name="arch" type="xml">
                <tree decoration-danger="not is_difference_zero and state=='open'" decoration-info="state=='open' and is_difference_zero" string="Statements">
                    <field name="name"/>
                    <field name="date"/>
                    <field name="journal_id"/>
                    <field name="balance_start"/>
                    <field name="balance_end_real"/>
                    <field name="balance_end" invisible="1"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="state"/>
                    <field name="is_difference_zero" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="view_bank_statement_search" model="ir.ui.view">
            <field name="name">account.bank.statement.search</field>
            <field name="model">account.bank.statement</field>
            <field name="arch" type="xml">
                <search string="Search Bank Statements">
                    <field name="name" string="Bank Statement"/>
                    <field name="date"/>
                    <filter string="Draft" domain="[('state','=','open')]"/>
                    <filter string="Confirmed" domain="[('state','=','confirm')]"/>
                    <field name="journal_id" domain="[('type', '=', 'bank')]" />
                    <group expand="0" string="Group By">
                        <filter string="Journal" context="{'group_by': 'journal_id'}"/>
                        <filter string="Status" context="{'group_by': 'state'}"/>
                        <filter string="Date" context="{'group_by': 'date'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="view_bank_statement_form" model="ir.ui.view">
            <field name="name">account.bank.statement.form</field>
            <field name="model">account.bank.statement</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <form string="Bank Statement">
                <header>
                    <field name="all_lines_reconciled" invisible="1" />
                    <button name="%(action_bank_reconcile_bank_statements)d" string="Reconcile" type="action" class="oe_highlight" attrs="{'invisible':['|','|',('all_lines_reconciled','=',True),('line_ids','=',[]),('state', '!=', 'open')]}"/>
                    <button name="check_confirm_bank" string="Validate" type="object" class="oe_highlight" attrs="{'invisible':['|','|',('all_lines_reconciled','=',False),('line_ids','=',[]),('state', '!=', 'open')]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="open,confirm"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button class="oe_stat_button" name="button_journal_entries"
                                string="Journal Entries" type="object"
                                attrs="{'invisible':[('move_line_count','=', 0)]}" icon="fa-bars" />
                        <field name="move_line_count" invisible="1"/>

                    </div>
                    <div class="oe_title oe_inline">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="journal_type" invisible="1"/>
                            <field name="cashbox_start_id" invisible="1"/>
                            <field name="cashbox_end_id" invisible="1"/>
                            <field name="journal_id" domain="[('type', '=', journal_type)]" attrs="{'readonly': [('move_line_count','!=', 0)]}" widget="selection"/>
                            <field name="date"/>
                            <field name='company_id' options="{'no_create': True}" groups="base.group_multi_company" />
                            <field name="currency_id" invisible="1"/>
                        </group><group>
                            <label for="balance_start"/>
                            <div>
                                <field class="oe_inline" name="balance_start"/>
                                <button class="oe_inline oe_link oe_edit_only" name="%(action_view_account_bnk_stmt_cashbox)d"
                                string="&#8594; Count" type="action"
                                attrs="{'invisible':['|','|',('state','!=','open'),('journal_type','!=','cash'),('cashbox_start_id','!=',False)]}" context="{'balance':'start'}"/>
                                <button name="open_cashbox_id" attrs="{'invisible': ['|','|',('state','!=','open'),('journal_type','!=','cash'),('cashbox_start_id', '=', False)]}" string="&#8594; Count" type="object" class="oe_edit_only oe_link oe_inline" context="{'balance':'start', 'cashbox_id': cashbox_start_id}"/>
                            </div>
                            <label for="balance_end_real"/>
                            <div>
                                <field class="oe_inline" name="balance_end_real"/>
                                <button class="oe_inline oe_link oe_edit_only" name="%(action_view_account_bnk_stmt_cashbox)d"
                                string="&#8594; Count" type="action"
                                attrs="{'invisible':['|','|',('state','!=','open'),('journal_type','!=','cash'),('cashbox_end_id','!=',False)]}" context="{'balance':'close'}"/>
                                <button name="open_cashbox_id" attrs="{'invisible': ['|','|',('state','!=','open'),('journal_type','!=','cash'),('cashbox_end_id', '=', False)]}" string="&#8594; Count" type="object" class="oe_edit_only oe_link oe_inline" context="{'balance':'close', 'cashbox_id': cashbox_end_id}"/>
                            </div>
                        </group>
                    </group>

                    <notebook>
                        <page string="Transactions" name="statement_line_ids">
                            <field name="line_ids" context="{'date':date}">
                                <tree editable="bottom" string="Statement lines" decoration-muted="journal_entry_ids">
                                    <field name="sequence"  widget="handle"/>
                                    <field name="journal_entry_ids" invisible="1"/>
                                    <field name="date" attrs="{'readonly' : [('journal_entry_ids', '!=', [])] }"/>
                                    <field name="name" attrs="{'readonly' : [('journal_entry_ids', '!=', [])] }"/>
                                    <field name="partner_id" domain="['|',('parent_id','=',False),('is_company','=',True)]"
                                         context="{'default_supplier': 1}"
                                         attrs="{'readonly' : [('journal_entry_ids', '!=', [])] }"/>
                                    <field name="ref" attrs="{'readonly' : [('journal_entry_ids', '!=', [])] }" groups="base.group_no_one"/>
                                    <field name="amount" attrs="{'readonly' : [('journal_entry_ids', '!=', [])] }"/>
                                    <field name="journal_currency_id" invisible="1"/>
                                    <field name="amount_currency" invisible="1" attrs="{'readonly' : [('journal_entry_ids', '!=', [])] }"/>
                                    <field name="currency_id" options="{'no_create': True}" invisible="1" attrs="{'readonly' : [('journal_entry_ids', '!=', [])] }"/>
                                    <field name="bank_account_id" invisible="1" attrs="{'readonly' : [('journal_entry_ids', '!=', [])] }"
                                        domain="['|', ('partner_id', '=', partner_id), ('partner_id', '=', False)]"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                    <group class="oe_subtotal_footer oe_right" colspan="2" name="sale_total">
                        <div class="oe_subtotal_footer_separator oe_inline">
                            <label for="balance_end" />
                        </div>
                        <field name="balance_end" nolabel="1" class="oe_subtotal_footer_separator"/>
                    </group>
                    <div class="oe_clear"/>
                </sheet>
                </form>
            </field>
        </record>

        <record id="action_bank_statement_tree" model="ir.actions.act_window">
            <field name="name">Bank Statements</field>
            <field name="res_model">account.bank.statement</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form,pivot,graph</field>
            <field name="domain">[('journal_id.type', '=', 'bank')]</field>
            <field name="context">{'journal_type':'bank'}</field>
            <field name="search_view_id" ref="view_bank_statement_search"/>
            <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                Click to register a bank statement.
              </p><p>
                A bank statement is a summary of all financial transactions
                occurring over a given period of time on a bank account. You
                should receive this periodicaly from your bank.
              </p><p>
                Odoo allows you to reconcile a statement line directly with
                the related sale or puchase invoices.
              </p>
            </field>
        </record>
        <record model="ir.actions.act_window.view" id="action_bank_statement_tree_bank">
            <field name="sequence" eval="1"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="view_bank_statement_tree"/>
            <field name="act_window_id" ref="action_bank_statement_tree"/>
        </record>
        <record model="ir.actions.act_window.view" id="action_bank_statement_form_bank">
            <field name="sequence" eval="2"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="view_bank_statement_form"/>
            <field name="act_window_id" ref="action_bank_statement_tree"/>
        </record>

        <record id="view_bank_statement_line_search" model="ir.ui.view">
            <field name="name">account.bank.statement.line.search</field>
            <field name="model">account.bank.statement.line</field>
            <field name="arch" type="xml">
                <search string="Search Bank Statements Line">
                    <field name="name"/>
                    <field name="statement_id"/>
                    <field name="partner_id"/>
                    <field name="date"/>
                    <field name="journal_id" domain="[('type', 'in', ('bank', 'cash'))]" />
                </search>
            </field>
        </record>

        <record id="view_bank_statement_line_form" model="ir.ui.view">
            <field name="name">bank.statement.line.form</field>
            <field name="model">account.bank.statement.line</field>
            <field name="priority">8</field>
            <field name="arch" type="xml">
            <form string="Statement Line" create="false">
                <group col="4">
                    <field name="statement_id"/>
                    <field name="date"/>
                    <field name="name"/>
                    <field name="ref"/>
                    <field name="partner_id"/>
                    <field name="amount"/>
                    <field name="journal_currency_id" invisible="1"/>
                    <field name="sequence"/>
                    <field name="note"/>
                </group>
            </form>
            </field>
        </record>
        <record id="view_bank_statement_line_tree" model="ir.ui.view">
            <field name="name">account.bank.statement.line.tree</field>
            <field name="model">account.bank.statement.line</field>
            <field name="priority">8</field>
            <field name="arch" type="xml">
                <tree string="Statement lines" create="false">
                    <field name="sequence" readonly="1" invisible="1"/>
                    <field name="statement_id" />
                    <field name="journal_id" invisible="1" />
                    <field name="date"/>
                    <field name="name"/>
                    <field name="ref"/>
                    <field name="partner_id"/>
                    <field name="amount"/>
                    <field name="journal_currency_id" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="action_bank_statement_line" model="ir.actions.act_window">
            <field name="name">Bank Statement Lines</field>
            <field name="res_model">account.bank.statement.line</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[]</field>
            <field name="context">{}</field>
            <field name="search_view_id" ref="view_bank_statement_line_search"/>
            <field name="help" type="html">
              <p>
                A bank statement line is a financial transaction on a bank account.
              </p><p>
                Odoo allows you to reconcile a statement line directly with
                the related sale or puchase invoice(s).
              </p>
            </field>
        </record>

        <record id="action_bank_statement_draft_tree" model="ir.actions.act_window">
            <field name="name">Draft statements</field>
            <field name="res_model">account.bank.statement</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('state','=','draft')]</field>
            <field name="filter" eval="True"/>
        </record>

        <record id="action_bank_reconcile" model="ir.actions.client">
            <field name="name">Reconciliation on Bank Statements</field>
            <field name="res_model">account.bank.statement.line</field>
            <field name="tag">bank_statement_reconciliation_view</field>
        </record>

        <record id="action_manual_reconcile" model="ir.actions.client">
            <field name="name">Journal Items to Reconcile</field>
            <field name="res_model">account.move.line</field>
            <field name="tag">manual_reconciliation_view</field>
        </record>

        <record id="view_account_reconcile_model_form" model="ir.ui.view">
            <field name="name">account.reconcile.model.form</field>
            <field name="model">account.reconcile.model</field>
            <field name="arch" type="xml">
                <form string="Operation Templates">
                    <sheet>
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1><field name="name" placeholder="e.g. Bank Fees"/></h1>
                        </div>
                        <group>
                            <group>
                                <field name="account_id" domain="[('company_id', '=', company_id)]"/>
                                <field name="amount_type"/>
                                <field name="tax_id" domain="[('company_id', '=', company_id)]" options="{'no_create': True}" />
                                <field name="analytic_account_id" groups="analytic.group_analytic_accounting" domain="[('company_id', '=', company_id)]"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                            <group>
                                <field name="label"/>
                                <label for="amount"/>
                                <div>
                                    <field name="amount" class="oe_inline"/>
                                    <label string="%" class="oe_inline" attrs="{'invisible':[('amount_type','!=','percentage')]}" />
                                </div>
                                <field name="journal_id" domain="[('type', '!=', 'general'), ('company_id', '=', company_id)]" widget="selection"/>
                            </group>
                        </group>
                        <div class="oe_edit_only">
                            <field name="has_second_line" />
                            <label for="has_second_line" string="Add a second line"/>
                        </div>
                        <group attrs="{'invisible':[('has_second_line','!=',True)]}">
                            <group>
                                <field name="second_account_id" string="Account" domain="[('company_id', '=', company_id)]"/>
                                <field name="second_amount_type" string="Amount type"/>
                                <field name="second_tax_id" string="Tax" domain="[('company_id', '=', company_id)]" widget="selection"/>
                                <field name="second_analytic_account_id" string="Analytic Account" groups="analytic.group_analytic_accounting" domain="[('company_id', '=', company_id)]"/>
                            </group>
                            <group>
                                <field name="second_label" string="Journal Item Label"/>
                                <label for="second_amount" string="Amount"/>
                                <div>
                                    <field name="second_amount" class="oe_inline"/>
                                    <label string="%" class="oe_inline" attrs="{'invisible':[('amount_type','!=','percentage')]}" />
                                </div>
                                <field name="second_journal_id" string="Journal" domain="[('type', '!=', 'general'), ('company_id', '=', company_id)]" widget="selection"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="view_account_reconcile_model_tree" model="ir.ui.view">
            <field name="name">account.reconcile.model.tree</field>
            <field name="model">account.reconcile.model</field>
            <field name="arch" type="xml">
                <tree string="Bank Reconciliation Move Presets">
                    <field name="sequence" widget="handle" />
                    <field name="name"/>
                    <field name="account_id"/>
                    <field name="amount_type"/>
                    <field name="journal_id" invisible="1"/>
                </tree>
            </field>
        </record>
        <record id="view_account_reconcile_model_search" model="ir.ui.view">
            <field name="name">account.reconcile.model.search</field>
            <field name="model">account.reconcile.model</field>
            <field name="arch" type="xml">
                <search string="Bank Reconciliation Move preset">
                        <filter string="With tax" domain="[('tax_id','!=',False)]"/>
                        <field name="amount_type"/>
                        <field name="journal_id"/>
                </search>
            </field>
        </record>
        <record id="action_account_reconcile_model" model="ir.actions.act_window">
            <field name="name">Reconciliation Models</field>
            <field name="res_model">account.reconcile.model</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_account_reconcile_model_search"/>
            <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                Click to create a reconciliation model.
              </p><p>
                Those can be used to quickly create a journal items when reconciling
                a bank statement or an account.
              </p>
            </field>
        </record>

        <menuitem
            id="account_report_folder"
            name="Reporting"
            parent="account_account_menu"
            groups="account.group_account_manager"
            sequence="4"/>

        <!-- Account Types -->
        <record id="view_account_type_search" model="ir.ui.view">
            <field name="name">account.account.type.search</field>
            <field name="model">account.account.type</field>
            <field name="arch" type="xml">
                <search string="Account Type">
                    <field name="name" filter_domain="['|', ('name','ilike',self), ('type','ilike',self)]" string="Account Type"/>
                </search>
            </field>
        </record>
        <record id="view_account_type_tree" model="ir.ui.view">
            <field name="name">account.account.type.tree</field>
            <field name="model">account.account.type</field>
            <field name="arch" type="xml">
                <tree string="Account Type">
                    <field name="name"/>
                    <field name="type"/>
                </tree>
            </field>
        </record>
        <record id="view_account_type_form" model="ir.ui.view">
            <field name="name">account.account.type.form</field>
            <field name="model">account.account.type</field>
            <field name="arch" type="xml">
                <form string="Account Type">
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="type"/>
                        </group>
                        <group>
                            <field name="include_initial_balance"/>
                        </group>
                    </group>
                    <separator string="Description"/>
                    <field name="note"/>
                </form>
            </field>
        </record>
        <record id="action_account_type_form" model="ir.actions.act_window">
            <field name="name">Account Types</field>
            <field name="res_model">account.account.type</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_account_type_search"/>
            <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                Click to define a new account type.
              </p><p>
                An account type is used to determine how an account is used in
                each journal. The deferral method of an account type determines
                the process for the annual closing. Reports such as the Balance
                Sheet and the Profit and Loss report use the category
                (profit/loss or balance sheet).
              </p>
            </field>
        </record>

        <!-- Tax -->

        <record id="view_tax_tree" model="ir.ui.view">
            <field name="name">account.tax.tree</field>
            <field name="model">account.tax</field>
            <field name="arch" type="xml">
                <tree string="Account Tax" decoration-muted="not active">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="type_tax_use"/>
                    <field name="description"/>
                    <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                    <field name="active" widget="boolean_toggle"/>
                </tree>
            </field>
        </record>
        <record id="view_tax_kanban" model="ir.ui.view">
            <field name="name">account.tax.kanban</field>
            <field name="model">account.tax</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="name"/>
                    <field name="type_tax_use"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                                <div class="row mb4">
                                    <div class="col-xs-6">
                                        <strong><span><t t-esc="record.name.value"/></span></strong>
                                    </div>
                                    <div class="col-xs-6 pull-right text-right">
                                        <span class="badge"><t t-esc="record.type_tax_use.value"/></span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        <record id="view_account_tax_search" model="ir.ui.view">
            <field name="name">account.tax.search</field>
            <field name="model">account.tax</field>
            <field name="arch" type="xml">
                <search string="Search Taxes">
                    <field name="name" filter_domain="['|', ('name','ilike',self), ('description','ilike',self)]" string="Tax"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <filter string="Sale" name="sale" domain="[('type_tax_use','=','sale')]" />
                    <filter string="Purchase" name="purchase" domain="[('type_tax_use','=','purchase')]" />
                    <separator/>
                    <filter name="active" string="Active" domain="[('active','=',True)]" help="Show active taxes"/>
                    <filter name="inactive" string="Inactive" domain="[('active','=',False)]" help="Show inactive taxes"/>
                    <group string="Group By">
                        <filter string="Company" domain="[]" context="{'group_by':'company_id'}" groups="base.group_multi_company"/>
                        <filter string="Tax Application" domain="[]" context="{'group_by':'type_tax_use'}"/>
                    </group>
                </search>
            </field>
        </record>
        <record id="view_tax_form" model="ir.ui.view">
            <field name="name">account.tax.form</field>
            <field name="model">account.tax</field>
            <field name="arch" type="xml">
                <form string="Account Tax">
                    <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button" options='{"terminology": "archive"}'/>
                        </button>
                    </div>
                    <group>
                        <group>
                            <field name="name"/>
                        </group>
                        <group>
                            <field name="type_tax_use"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Definition">
                            <group>
                                <group>
                                    <field name="amount_type" />
                                    <label for="amount" attrs="{'invisible':[('amount_type','not in', ('fixed', 'percent', 'division'))]}"/>
                                    <div attrs="{'invisible':[('amount_type','not in', ('fixed', 'percent', 'division'))]}">
                                        <field name="amount" class="oe_inline" />
                                        <label string="%" class="oe_inline" attrs="{'invisible':[('amount_type','=','fixed')]}" />
                                    </div>
                                </group>
                                <group attrs="{'invisible':[('amount_type','=', 'group')]}" groups="account.group_account_user">
                                    <field name="account_id"/>
                                    <field name="refund_account_id"/>
                                </group>
                            </group>
                            <field name="children_tax_ids"
                                attrs="{'invisible':['|', ('amount_type','!=','group'), ('type_tax_use','=','none')]}"
                                domain="[('type_tax_use','in',('none',type_tax_use)), ('amount_type','!=','group')]">
                                <tree string="Children Taxes">
                                    <field name="sequence" widget="handle" />
                                    <field name="name"/>
                                    <field name="amount_type" />
                                    <field name="amount" />
                                </tree>
                            </field>
                        </page>
                        <page string="Advanced Options">
                            <group>
                                <group>
                                    <field name="description" attrs="{'invisible':[('amount_type','=', 'group')]}"/>
                                    <field name="tax_group_id"/>
                                    <field name="tag_ids" domain="[('applicability', '!=', 'accounts')]" widget="many2many_tags" context="{'default_applicability': 'taxes'}"/>
                                    <field name="analytic" attrs="{'invisible':[('amount_type','=', 'group')]}" groups="analytic.group_analytic_accounting" />
                                    <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                                </group>
                                <group name="advanced_booleans">
                                    <field name="price_include" attrs="{'invisible':[('amount_type','=', 'group')]}" />
                                    <field name="include_base_amount" attrs="{'invisible':[('amount_type','=', 'group')]}" />
                                    <field name="tax_adjustment" groups="account.group_account_user"/>
                                    <field name="hide_tax_exigibility" invisible="1"/>
                                    <field name="tax_exigibility" widget="radio" attrs="{'invisible':['|', ('amount_type','=', 'group'), ('hide_tax_exigibility', '=', False)]}" groups="account.group_account_user"/>
                                    <field name="cash_basis_account" attrs="{'invisible': [('tax_exigibility', '=', 'on_invoice')], 'required': [('tax_exigibility', '=', 'on_payment')]}" groups="account.group_account_user"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                </form>
              </field>
        </record>
        <record id="action_tax_form" model="ir.actions.act_window">
            <field name="name">Taxes</field>
            <field name="res_model">account.tax</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="domain" eval="False"/> <!-- Force empty -->
            <field name="context">{'search_default_sale': True, 'search_default_purchase': True, 'active_test': False}</field>
            <field name="view_id" ref="view_tax_tree"/>
        </record>
        <menuitem action="action_tax_form" id="menu_action_tax_form" parent="account_account_menu" sequence="1"/>

        <!-- Journal Items -->
        <record id="view_move_line_form" model="ir.ui.view">
            <field name="name">account.move.line.form</field>
            <field name="model">account.move.line</field>
            <field eval="2" name="priority"/>
            <field name="arch" type="xml">
                <form string="Journal Item">
                    <sheet>
                        <field name="company_id" invisible="1"/>
                        <field name="parent_state" invisible="1"/>
                        <group>
                            <field name="name"/>
                            <field name="partner_id"
                                domain="['|', ('parent_id', '=', False), ('is_company', '=', True)]"
                                attrs="{'readonly': [('parent_state', '=', 'posted')]}"/>
                        </group>
                        <notebook colspan="4">
                            <page string="Information">
                                <group>
                                    <group string="Amount">
                                        <field name="account_id" domain="[('company_id', '=', company_id)]" attrs="{'readonly':[('parent_state','=','posted')]}"/>
                                        <field name="debit" attrs="{'readonly':[('parent_state','=','posted')]}"/>
                                        <field name="credit" attrs="{'readonly':[('parent_state','=','posted')]}"/>
                                        <field name="quantity"/>
                                    </group>
                                    <group string="Accounting Documents">
                                        <field name="move_id" attrs="{'readonly':[('parent_state','=','posted')]}"/>
                                        <field name="statement_id" readonly="True" attrs="{'invisible': [('statement_id','=',False)]}"/>
                                        <field name="invoice_id" attrs="{'invisible': [('invoice_id','=',False)]}"/>
                                    </group>
                                    <group string="Dates">
                                        <field name="date"/>
                                        <field name="date_maturity"/>
                                    </group>

                                    <group string="Taxes" attrs="{'invisible': [('tax_line_id','=',False), ('tax_ids','=',[])]}">
                                        <field name="tax_line_id" readonly="1" attrs="{'invisible': [('tax_line_id','=',False)]}"/>
                                        <field name="tax_ids" widget="many2many_tags" readonly="1" attrs="{'invisible': [('tax_ids','=',[])]}"/>
                                    </group>
                                    <group string="Matching" attrs="{'invisible':[('matched_debit_ids', '=', []),('matched_credit_ids', '=', [])]}">
                                        <label for="full_reconcile_id"/>
                                        <div>
                                            <field name="full_reconcile_id" attrs="{'invisible':[('full_reconcile_id','=',False)]}"/>
                                            <field name="matched_debit_ids" invisible="1"/>
                                            <field name="matched_credit_ids" invisible="1"/>
                                            <button name="open_reconcile_view"
                                                class="oe_link"
                                                type="object"
                                                string="-> View partially reconciled entries"
                                                attrs="{'invisible': ['|', ('full_reconcile_id', '!=', False), '&amp;', ('matched_debit_ids', '=', []),('matched_credit_ids', '=', [])]}">
                                            </button>
                                        </div>
                                    </group>
                                    <group string="Currency" groups="base.group_multi_currency">
                                        <field name="currency_id" invisible="1"/>
                                        <field name="amount_currency"/>
                                    </group>
                                    <group string="States">
                                        <field name="blocked"/>
                                    </group>
                                    <group groups="analytic.group_analytic_accounting" string="Analytic">
                                        <field name="analytic_account_id"/>
                                        <field name="analytic_tag_ids" widget="many2many_tags"/>
                                    </group>
                                </group>
                                <field name="narration" colspan="4" nolabel="1" placeholder="Add an internal note..."/>
                            </page>
                            <page string="Analytic Lines" groups="analytic.group_analytic_accounting">
                                <field name="date" invisible="1"/>
                                <field name="analytic_line_ids" context="{'tree_view_ref':'analytic.view_account_analytic_line_tree','default_general_account_id':account_id, 'default_name': name, 'default_date':date, 'amount': (debit or 0.0)-(credit or 0.0)}"/>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="view_move_line_pivot" model="ir.ui.view">
            <field name="name">account.move.line.pivot</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <pivot string="Journal Items">
                    <field name="journal_id" type="row"/>
                    <field name="date" type="col"/>
                    <field name="balance" type="measure"/>
                </pivot>
            </field>
        </record>
        <record id="view_move_line_tree" model="ir.ui.view">
            <field name="name">account.move.line.tree</field>
            <field name="model">account.move.line</field>
            <field eval="1" name="priority"/>
            <field name="arch" type="xml">
                <tree string="Journal Items" create="false">
                    <field name="date"/>
                    <field name="move_id" required="0" groups="account.group_account_user"/>
                    <field name="journal_id" options='{"no_open":True}'/>
                    <field name="name"/>
                    <field name="ref"/>
                    <field name="statement_id" invisible="1"/>
                    <field name="partner_id"/>
                    <field name="account_id" options='{"no_open":True}' domain="[('company_id', '=', company_id)]" groups="account.group_account_user"/>
                    <field name="analytic_account_id" groups="account.group_account_user"/>
                    <field name="reconciled" invisible="1"/>
                    <field name="full_reconcile_id"/>
                    <field name="debit" sum="Total Debit"/>
                    <field name="credit" sum="Total Credit"/>
                    <field name="amount_currency" readonly="True" groups="base.group_multi_currency"/>
                    <field name="currency_id" readonly="True" invisible="1" />
                    <field name="date_maturity"/>
                    <field name="company_currency_id" invisible="1"/>
                    <field name="company_id" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="view_move_line_tax_audit_tree" model="ir.ui.view">
            <field name="name">account.move.line.tax.audit.tree</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree"/>
            <field name="arch" type="xml">
                <field name="full_reconcile_id" position="after">
                    <field name="tax_line_id"/>
                    <field name="tax_base_amount"/>
                </field>
                <field name="date_maturity" position="replace"/>
            </field>
        </record>

        <act_window
            id="action_move_line_select_tax_audit"
            name="Journal Items"
            context="{'search_default_account_id': [active_id]}"
            res_model="account.move.line"
            src_model="account.account"
            view_id="account.view_move_line_tax_audit_tree"/>

        <record id="account_move_line_graph_date" model="ir.ui.view">
            <field name="name">account.move.line.graph</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <graph string="Account Statistics" type="bar">
                    <field name="date"/>
                    <field name="balance" operator="+" type='measure'/>
                </graph>
            </field>
        </record>
        <record id="account_move_line_graph_date_cash_basis" model="ir.ui.view">
            <field name="name">account.move.line.graph</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <graph string="Account Statistics" type="bar">
                    <field name="date"/>
                    <field name="balance_cash_basis" operator="+" type='measure'/>
                </graph>
            </field>
        </record>
        <record id="view_account_move_line_filter" model="ir.ui.view">
            <field name="name">Journal Items</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <search string="Search Journal Items">
                    <field name="name" filter_domain="['|', ('name','ilike',self), ('ref','ilike',self)]" string="Move"/>
                    <field name="date"/>
                    <separator/>
                    <filter string="Unposted" domain="[('move_id.state','=','draft')]" help="Unposted Journal Items"/>
                    <filter name="posted" string="Posted" domain="[('move_id.state','=','posted')]" help="Posted Journal Items"/>
                    <separator/>
                    <filter string="Unreconciled" domain="[('full_reconcile_id', '=', False), ('balance','!=', 0), ('account_id.reconcile','=',True)]" help="Journal items where matching number isn't set" name="unreconciled"/>
                    <separator/>
                    <filter string="Payable" domain="[('account_id.internal_type', '=', 'payable')]" help="From Payable accounts" name="payable"/>
                    <filter string="Receivable" domain="[('account_id.internal_type', '=', 'receivable')]" help="From Receivable accounts" name="receivable"/>
                    <separator/>
                    <field name="account_id"/>
                    <field name="partner_id"/>
                    <field name="journal_id"/>
                    <field name="move_id" string="Number (Move)"/>
                    <field name="tax_line_id"/>
                    <field name="tax_ids" />
                    <group expand="0" string="Group By">
                        <filter string="Partner" domain="[]" context="{'group_by':'partner_id'}"/>
                        <filter string="Journal" domain="[]" context="{'group_by':'journal_id'}"/>
                        <filter string="Account"  context="{'group_by':'account_id'}"/>
                        <filter string="Date" domain="[]" context="{'group_by':'date'}"/>
                    </group>
                </search>
            </field>
        </record>
        <record id="action_account_moves_all_a" model="ir.actions.act_window">
            <field name="context">{'journal_type':'general'}</field>
            <field name="name">Journal Items</field>
            <field name="res_model">account.move.line</field>
            <field name="view_id" ref="view_move_line_tree"/>
            <field name="view_mode">tree,pivot,graph,form</field>
        </record>
        <menuitem action="action_account_moves_all_a" id="menu_action_account_moves_all" parent="account.menu_finance_entries_accounting_entries" sequence="1" groups="group_account_manager" />
        <record id="action_account_moves_all_tree" model="ir.actions.act_window">
            <field name="name">Journal Items</field>
            <field name="res_model">account.move.line</field>
            <field name="context">{'search_default_partner_id': [active_id], 'default_partner_id': active_id}</field>
            <field name="view_id" ref="view_move_line_tree"/>
        </record>

        <record id="action_move_line_graph" model="ir.actions.act_window">
            <field name="name">Journal Items</field>
            <field name="view_mode">graph,pivot</field>
            <field name="view_id" ref="account_move_line_graph_date"/>
            <field name="context">{'search_default_account_id': [active_id]}</field>
            <field name="res_model">account.move.line</field>
        </record>

        <record id="action_move_line_graph_posted" model="ir.actions.act_window">
            <field name="name">Journal Items</field>
            <field name="view_mode">graph,pivot</field>
            <field name="view_id" ref="account_move_line_graph_date"/>
            <field name="context">{'search_default_account_id': [active_id], 'search_default_posted': 1}</field>
            <field name="res_model">account.move.line</field>
        </record>

        <record id="action_move_line_graph_cash_basis" model="ir.actions.act_window">
            <field name="name">Journal Items</field>
            <field name="view_mode">graph,pivot</field>
            <field name="view_id" ref="account_move_line_graph_date_cash_basis"/>
            <field name="context">{'search_default_account_id': [active_id]}</field>
            <field name="res_model">account.move.line</field>
        </record>

        <record id="action_move_line_graph_cash_basis_posted" model="ir.actions.act_window">
            <field name="name">Journal Items</field>
            <field name="view_mode">graph,pivot</field>
            <field name="view_id" ref="account_move_line_graph_date_cash_basis"/>
            <field name="context">{'search_default_account_id': [active_id], 'search_default_posted': 1}</field>
            <field name="res_model">account.move.line</field>
        </record>

        <!-- Account.Entry Edition -->
        <record id="view_move_tree" model="ir.ui.view">
            <field name="name">account.move.tree</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <tree decoration-info="state == 'draft'" string="Journal Entries">
                    <field name="date"/>
                    <field name="name"/>
                    <field name="partner_id"/>
                    <field name="ref"/>
                    <field name="journal_id"/>
                    <field name="amount" sum="Total Amount"/>
                    <field name="state"/>
                    <field name="currency_id" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="view_account_move_kanban" model="ir.ui.view">
            <field name="name">account.move.kanban</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="journal_id"/>
                    <field name="ref"/>
                    <field name="date"/>
                    <field name="state"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_global_click">
                                <div class="row mb4">
                                    <div class="col-xs-6">
                                        <strong><span><field name="journal_id"/></span></strong>
                                    </div>
                                    <div class="col-xs-6 text-right">
                                        <strong><i class="fa fa-clock-o"/> <t t-esc="record.date.value"/></strong>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-12">
                                        <span><field name="ref"/></span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-6">
                                        <span><field name="amount" widget='monetary'/></span>
                                        <span><field name="currency_id" invisible="1"/></span>
                                    </div>
                                    <div class="col-xs-6">
                                        <span class="pull-right">
                                            <field name="state" widget="label_selection" options="{'classes': {'draft': 'default', 'posted': 'success'}}"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="view_move_form" model="ir.ui.view">
            <field name="name">account.move.form</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <form string="Account Entry" duplicate="false">
                    <header>
                        <button name="post" states="draft" string="Post" type="object" class="oe_highlight" groups="account.group_account_invoice"/>
                        <button name="%(action_view_account_move_reversal)d" states="posted" string="Reverse Entry" type="action" groups="account.group_account_invoice"/>
                        <button name="action_duplicate" string="Duplicate" type="object" groups="account.group_account_invoice"/>
                        <field name="state" widget="statusbar"/>
                    </header>
                    <sheet>
                        <field name="id" invisible="1"/>
                        <div class="oe_button_box">
                            <button name="open_reconcile_view"
                                class="oe_stat_button"
                                icon="fa-bars"
                                type="object"
                                string="Reconciled entries">
                            </button>
                        </div>
                    <h1>
                        <field name="name" readonly="True" attrs="{'invisible':[('name','=','/')]}"/>
                    </h1>
                    <group>
                        <group>
                            <field name="date"/>
                            <field name="ref" attrs="{'required': [('id', '==', False)]}"/>
                        </group>
                        <group>
                            <field name="journal_id" options="{'no_open': True, 'no_create': True}"/>
                            <field name="company_id" required="1" groups="base.group_multi_company"/>
                            <field name="amount" invisible="1"/>
                            <field name="currency_id" invisible="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Journal Items">
                            <field name="line_ids" widget="one2many_list"
                              context="{'line_ids': line_ids, 'journal_id': journal_id }">
                                <tree editable="bottom" string="Journal Items">
                                    <field name="account_id" domain="[('company_id', '=', parent.company_id), ('deprecated', '=', False)]"/>
                                    <field name="partner_id"
                                        domain="['|', ('parent_id', '=', False), ('is_company', '=', True)]"/>
                                    <field name="name"/>
                                    <field name="analytic_account_id" groups="analytic.group_analytic_accounting"/>
                                    <field name="analytic_tag_ids" widget="many2many_tags" groups="analytic.group_analytic_accounting"/>
                                    <field name="amount_currency" groups="base.group_multi_currency"/>
                                    <field name="company_currency_id" invisible="1"/>
                                    <field name="company_id" invisible="1"/>
                                    <field name="currency_id" options="{'no_create': True}" groups="base.group_multi_currency"/>
                                    <field name="debit" sum="Total Debit"/>
                                    <field name="credit" sum="Total Credit"/>
                                    <field name="date_maturity" required="0" invisible="context.get('view_no_maturity', False)"/>
                                </tree>
                            </field>
                            <field name="narration" colspan="4" placeholder="Add an internal note..." nolabel="1" height="50"/>
                        </page>
                    </notebook>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="view_account_move_filter" model="ir.ui.view">
            <field name="name">account.move.select</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <search string="Search Move">
                    <field name="name" filter_domain="['|', ('name','ilike',self), ('ref','ilike',self)]" string="Move"/>
                    <field name="date"/>
                    <filter string="Unposted" domain="[('state','=','draft')]" help="Unposted Journal Entries"/>
                    <filter string="Posted" domain="[('state','=','posted')]" help="Posted Journal Entries"/>
                    <separator/>
                    <filter string="Sales" domain="[('journal_id.type','=','sale')]" context="{'default_journal_type': 'sale'}"/>
                    <filter string="Purchases" domain="[('journal_id.type','=','purchase')]" context="{'default_journal_type': 'purchase'}"/>
                    <filter string="Bank Operations" domain="[('journal_id.type','=','bank')]" context="{'default_journal_type': 'bank'}"/>
                    <filter string="Cash Operations" domain="[('journal_id.type','=','cash')]" context="{'default_journal_type': 'cash'}"/>
                    <filter string="Miscellaneous Operations" domain="[('journal_id.type','=','general')]" name="misc_filter" context="{'default_journal_type': 'general'}"/>
                    <separator/>
                    <field name="partner_id"/>
                    <field name="journal_id"/>
                    <field name="dummy_account_id"/>
                    <group expand="0" string="Group By">
                        <filter string="Partner" domain="[]" context="{'group_by':'partner_id'}"/>
                        <filter string="Journal" domain="[]" context="{'group_by':'journal_id'}"/>
                        <filter string="Status" domain="[]" context="{'group_by':'state'}"/>
                        <filter string="Date" domain="[]" context="{'group_by':'date'}" help="Journal Entries by Month"/>
                    </group>
                </search>
            </field>
        </record>
        <record id="action_move_journal_line" model="ir.actions.act_window">
            <field name="name">Journal Entries</field>
            <field name="res_model">account.move</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_id" ref="view_move_tree"/>
            <field name="search_view_id" ref="view_account_move_filter"/>
            <field name="context">{'search_default_misc_filter':1, 'view_no_maturity': True}</field>
            <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                Click to create a journal entry.
              </p><p>
                A journal entry consists of several journal items, each of
                which is either a debit or a credit transaction.
              </p><p>
                Odoo automatically creates one journal entry per accounting
                document: invoice, refund, vendor payment, bank statements,
                etc. So, you should record journal entries manually only/mainly
                for miscellaneous operations.
              </p>
            </field>
        </record>
        <menuitem
            action="action_move_journal_line"
            id="menu_action_move_journal_line_form"
            parent="account.menu_finance_entries_accounting_entries"
            groups="account.group_account_user"
            sequence="2"/>

        <record id="action_move_line_form" model="ir.actions.act_window">
            <field name="name">Entries</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">account.move</field>
            <field name="view_type">form</field>
            <field name="view_id" ref="view_move_tree"/>
            <field name="search_view_id" ref="view_account_move_filter"/>
        </record>

        <act_window
            id="act_account_move_to_account_move_line_open"
            name="Journal Items"
            context="{'search_default_move_id': active_id, 'default_move_id': active_id}"
            res_model="account.move.line"
            src_model="account.move"
            view_mode="tree"/>

        <!-- Full Reconcile -->
        <record id="view_full_reconcile_form" model="ir.ui.view">
            <field name="name">account.full.reconcile.form</field>
            <field name="model">account.full.reconcile</field>
            <field name="arch" type="xml">
                <form string="Matching">
                    <group>
                        <div class="oe_title" colspan="4">
                            <h1><field name="name" readonly="1"/></h1>
                        </div>
                        <separator string="Matched Journal Items" colspan="4"/>
                        <field name="reconciled_line_ids" readonly="1" colspan="4" nolabel="1"/>
                    </group>
                </form>
            </field>
        </record>


        <!--Payment Terms -->
        <record id="view_payment_term_line_tree" model="ir.ui.view">
            <field name="name">account.payment.term.line.tree</field>
            <field name="model">account.payment.term.line</field>
            <field name="arch" type="xml">
                <tree string="Payment Terms">
                    <field name="sequence" widget="handle"/>
                    <field name="value" string="Due Type"/>
                    <field name="value_amount" attrs="{'readonly':[('value','=','balance')]}"/>
                    <field name="days"/>
                    <field name="option" string=""/>
                </tree>
            </field>
        </record>
        <record id="view_payment_term_line_form" model="ir.ui.view">
            <field name="name">account.payment.term.line.form</field>
            <field name="model">account.payment.term.line</field>
            <field name="arch" type="xml">
                <form string="Payment Terms">
                    <group>
                        <group string="Term Type">
                            <field name="value" widget="radio"/>
                            <label for="value_amount" attrs="{'invisible':[('value','=', 'balance')]}"/>
                            <div attrs="{'invisible':[('value','=', 'balance')]}">
                                <field name="value_amount" class="oe_inline"/>
                                <label string="%" class="oe_inline" attrs="{'invisible':[('value','!=','percent')]}"/>
                            </div>
                            <field name="sequence" invisible="1"/>
                        </group>
                        <group string="Due Date Computation">
                            <label for="days"/>
                            <div>
                                <field name="option" widget="radio"/>
                                <div class="o_row" attrs="{'invisible': [('option', 'in', ['last_day_following_month', 'last_day_current_month'])]}">
                                    <field name="days"/>
                                    days
                                </div>
                            </div>
                        </group>
                    </group>
                </form>
            </field>
        </record>
        <record id="view_payment_term_search" model="ir.ui.view">
            <field name="name">account.payment.term.search</field>
            <field name="model">account.payment.term</field>
            <field name="arch" type="xml">
                <search string="Payment Terms">
                    <field name="name" string="Payment Terms"/>
                    <field name="active"/>
                </search>
            </field>
        </record>
        <record id="view_payment_term_tree" model="ir.ui.view">
            <field name="name">account.payment.term.tree</field>
            <field name="model">account.payment.term</field>
            <field name="arch" type="xml">
                <tree string="Payment Terms">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                </tree>
            </field>
        </record>
        <record id="view_payment_term_form" model="ir.ui.view">
            <field name="name">account.payment.term.form</field>
            <field name="model">account.payment.term</field>
            <field name="arch" type="xml">
                <form string="Payment Terms">
                    <group col="4">
                        <field name="name"/>
                        <field name="active"/>
                        <field name="company_id" invisible="1"/>
                    </group>
                    <label for="note"/>
                    <field name="note" placeholder="Payment terms explanation for the customer..."/>
                    <separator string="Terms"/>
                    <p class="text-muted">
                        The last line's computation type should be "Balance" to ensure that the whole amount will be allocated.
                    </p>
                    <field name="line_ids"/>
                </form>
            </field>
        </record>
        <record id="view_account_payment_term_kanban" model="ir.ui.view">
            <field name="name">account.payment.term.kanban</field>
            <field name="model">account.payment.term</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="name"/>
                    <field name="note"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_global_click">
                                <div><strong class="o_kanban_record_title"><t t-esc="record.name.value"/></strong></div>
                                <div t-if="record.note.value"><t t-esc="record.note.value"/></div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        <record id="action_payment_term_form" model="ir.actions.act_window">
            <field name="name">Payment Terms</field>
            <field name="res_model">account.payment.term</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_payment_term_search"/>
        </record>

        <menuitem action="action_payment_term_form"
            id="menu_action_payment_term_form" parent="account_management_menu" sequence="1"/>

        <!-- Account Templates -->

        <record id="view_account_template_form" model="ir.ui.view">
            <field name="name">account.account.template.form</field>
            <field name="model">account.account.template</field>
            <field name="arch" type="xml">
                <form string="Account Template">
                    <group col="4">
                        <field name="name"/>
                        <field name="code"/>
                        <newline/>
                        <field name="user_type_id" widget="selection"/>
                        <field name="currency_id" options="{'no_create': True}" groups="base.group_multi_currency"/>
                        <field name="tag_ids" domain="[('applicability', '!=', 'taxes')]" widget="many2many_tags" context="{'default_applicability': 'accounts'}"/>
                        <field name="reconcile"/>
                        <field name="chart_template_id"/>
                    </group>
                    <separator string="Default Taxes"/>
                    <field name="tax_ids"/>
                    <separator string="Notes"/>
                    <field name="note" placeholder="Internal notes..."/>
                </form>
            </field>
        </record>
        <record id="view_account_template_tree" model="ir.ui.view">
            <field name="name">account.account.template.tree</field>
            <field name="model">account.account.template</field>
            <field name="arch" type="xml">
                <tree string="Account Template">
                    <field name="code"/>
                    <field name="name"/>
                    <field name="user_type_id" invisible="1"/>
                </tree>
            </field>
        </record>
        <record id="view_account_template_search" model="ir.ui.view">
            <field name="name">account.account.template.search</field>
            <field name="model">account.account.template</field>
            <field name="arch" type="xml">
                <search string="Search Account Templates">
                    <field name="name" filter_domain="['|', ('name','ilike',self), ('code','ilike',self)]" string="Account Template"/>
                    <field name="user_type_id"/>
                    <group expand="0" string="Group By">
                        <filter string="Account Type" domain="[]" context="{'group_by':'user_type_id'}"/>
                   </group>
                </search>
            </field>
        </record>
        <record id="action_account_template_form" model="ir.actions.act_window">
            <field name="name">Account Templates</field>
            <field name="res_model">account.account.template</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_account_template_search"/>
        </record>

        <!-- Chart of Accounts Templates -->
        <record id="view_account_chart_template_form" model="ir.ui.view">
            <field name="name">account.chart.template.form</field>
            <field name="model">account.chart.template</field>
            <field name="arch" type="xml">
                <form string="Chart of Accounts Template">
                    <group col="4">
                        <field name="name"/>
                        <field name="company_id"/>
                        <field name="parent_id" />
                        <field name="bank_account_code_prefix"/>
                        <field name="cash_account_code_prefix"/>
                        <field name="code_digits" />
                        <field name="visible" />
                        <field name="complete_tax_set" />
                        <field name="transfer_account_id"/>
                    </group>
                    <separator string="Default Taxes" colspan="4"/>
                    <field name="tax_template_ids" colspan="4"  nolabel="1"/>
                    <separator string="Properties" colspan="4"/>
                    <group col="4">
                        <field name="property_account_receivable_id"/>
                        <field name="property_account_payable_id"/>
                        <field name="property_account_expense_categ_id"/>
                        <field name="property_account_income_categ_id"/>
                        <field name="property_account_expense_id"/>
                        <field name="property_account_income_id"/>
                    </group>
                </form>
            </field>
        </record>
        <record id="view_account_chart_template_seacrh" model="ir.ui.view">
            <field name="name">account.chart.template.search</field>
            <field name="model">account.chart.template</field>
            <field name="arch" type="xml">
                <search string="Search Chart of Account Templates">
                    <field name="name" string="Account Template"/>
                    <field name="bank_account_code_prefix"/>
                    <field name="cash_account_code_prefix"/>
                    <group expand="0" string="Group By">
                        <filter string="Receivable Account" domain="[]" context="{'group_by':'property_account_receivable_id'}"/>
                        <filter string="Payable Account" domain="[]" context="{'group_by':'property_account_payable_id'}"/>
                        <filter string="Income Account" domain="[]" context="{'group_by':'property_account_income_categ_id'}"/>
                        <filter string="Expense Account" domain="[]" context="{'group_by':'property_account_expense_categ_id'}"/>
                    </group>
                </search>
            </field>
        </record>
        <record id="view_account_chart_template_tree" model="ir.ui.view">
            <field name="name">account.chart.template.tree</field>
            <field name="model">account.chart.template</field>
            <field name="arch" type="xml">
                <tree string="Chart of Accounts Template">
                    <field name="name"/>
                    <field name="company_id"/>
                    <field name="property_account_receivable_id" invisible="1"/>
                    <field name="property_account_payable_id" invisible="1"/>
                    <field name="property_account_expense_categ_id" invisible="1"/>
                    <field name="property_account_income_categ_id" invisible="1"/>
                </tree>
            </field>
        </record>
        <record id="action_account_chart_template_form" model="ir.actions.act_window">
            <field name="name">Chart of Accounts Templates</field>
            <field name="res_model">account.chart.template</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form</field>
        </record>

        <!-- Account Tax Templates -->
        <record id="view_account_tax_template_form" model="ir.ui.view">
            <field name="name">account.tax.template.form</field>
            <field name="model">account.tax.template</field>
            <field name="arch" type="xml">
                <form string="Account Tax Template">
                    <group>
                        <group>
                            <field name="name"/>
                        </group>
                        <group>
                            <field name="type_tax_use"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Definition">
                            <group>
                                <group>
                                    <field name="amount_type" />
                                    <label for="amount" attrs="{'invisible':[('amount_type','=', 'group')]}"/>
                                    <div attrs="{'invisible':[('amount_type','=', 'group')]}">
                                        <field name="amount" class="oe_inline" />
                                        <label string="%" class="oe_inline" attrs="{'invisible':[('amount_type','=','fixed')]}" />
                                    </div>
                                </group>
                                <group attrs="{'invisible':[('amount_type','=', 'group')]}">
                                    <field name="account_id"/>
                                    <field name="refund_account_id"/>
                                </group>
                            </group>
                            <field name="children_tax_ids"
                                attrs="{'invisible':['|', ('amount_type','!=','group'), ('type_tax_use','=','none')]}"
                                domain="[('type_tax_use','in',('none',type_tax_use)), ('amount_type','!=','group')]">
                                <tree string="Children Taxes">
                                    <field name="sequence" widget="handle" />
                                    <field name="name"/>
                                    <field name="amount_type" />
                                    <field name="amount" />
                                </tree>
                            </field>
                        </page>
                        <page string="Advanced Options">
                            <group>
                                <group>
                                    <field name="description" attrs="{'invisible':[('amount_type','=', 'group')]}"/>
                                    <field name="analytic" attrs="{'invisible':[('amount_type','=', 'group')]}" groups="analytic.group_analytic_accounting" />
                                </group>
                                <group>
                                    <field name="price_include" attrs="{'invisible':[('amount_type','=', 'group')]}" />
                                    <field name="include_base_amount" attrs="{'invisible':[('amount_type','=', 'group')]}" />
                                </group>
                                <group>
                                    <field name="tag_ids" domain="[('applicability', '!=', 'accounts')]" widget="many2many_tags" context="{'default_applicability': 'taxes'}"/>
                                    <field name="active" groups="base.group_no_one"/>
                                    <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                                    <field name="tax_exigibility" widget="radio" attrs="{'invisible':[('amount_type','=', 'group')]}"/>
                                    <field name="cash_basis_account" attrs="{'invisible': [('tax_exigibility', '=', 'on_invoice')], 'required': [('tax_exigibility', '=', 'on_payment')]}"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </form>
            </field>
        </record>
        <record id="view_account_tax_template_tree" model="ir.ui.view">
            <field name="name">account.tax.template.tree</field>
            <field name="model">account.tax.template</field>
            <field name="arch" type="xml">
                <tree string="Account Tax Template">
                    <field name="name"/>
                    <field name="description"/>
                </tree>
            </field>
        </record>
        <record id="view_account_tax_template_search" model="ir.ui.view">
            <field name="name">account.tax.template.search</field>
            <field name="model">account.tax.template</field>
            <field name="arch" type="xml">
                <search string="Search Tax Templates">
                    <field name="name" filter_domain="['|', ('name','ilike',self), ('description','ilike',self)]" string="Tax Template"/>
                    <filter string="Sale" domain="[('type_tax_use','=','sale')]" help="Taxes used in Sales"/>
                    <filter string="Purchase" domain="[('type_tax_use','=','purchase')]" help="Taxes used in Purchases"/>
                    <field name="chart_template_id"/>
                </search>
            </field>
        </record>
        <record id="action_account_tax_template_form" model="ir.actions.act_window">
            <field name="name">Tax Templates</field>
            <field name="res_model">account.tax.template</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_account_tax_template_search"/>
        </record>

        <!--  Wizard for Multi Charts of Accounts -->
        <record id="view_wizard_multi_chart" model="ir.ui.view">
            <field name="name">Choose Accounting Template</field>
            <field name="model">wizard.multi.charts.accounts</field>
            <field name="inherit_id" ref="base.res_config_view_base"/>
            <field name="arch" type="xml">
                <form position="attributes">
                    <attribute name="string">Accounting Application Configuration</attribute>
                </form>
                <group name="res_config_contents" position="replace">
                    <field name="only_one_chart_template" invisible="1"/>
                    <field name="complete_tax_set" invisible="1"/>
                    <group col="1">
                        <group>
                            <field name="chart_template_id" widget="selection" domain="[('visible','=', True)]"/>
                        </group>
                        <group invisible="1">
                            <field name="company_id" options="{'no_create': True}"/> <!-- we assume that this wizard will be run only by administrators and as this field may cause problem if hidden (because of the default company of the user removed from the selection because already configured), we simply choosed to remove the group "multi company" of it -->
                            <field name="currency_id" class="oe_inline"/>
                            <field name="use_anglo_saxon"/>
                            <field name="sale_tax_id" attrs="{'invisible': [('complete_tax_set', '!=', True)]}" domain="[('chart_template_id', '=', chart_template_id),('parent_id','=',False),('type_tax_use','=','sale')]"/>
                            <label for="sale_tax_rate" string="Sale Tax" attrs="{'invisible': [('complete_tax_set', '=', True)]}"/>
                            <div attrs="{'invisible': [('complete_tax_set', '=', True)]}">
                                <field name="sale_tax_rate" class="oe_inline"/> %%
                            </div>
                            <field name="purchase_tax_id" attrs="{'invisible': [('complete_tax_set', '!=', True)]}" domain="[('chart_template_id', '=', chart_template_id),('parent_id','=',False),('type_tax_use','=','purchase')]"/>
                            <label for="purchase_tax_rate" string="Purchase Tax" attrs="{'invisible': [('complete_tax_set', '=', True)]}"/>
                            <div attrs="{'invisible': [('complete_tax_set', '=', True)]}">
                                <field name="purchase_tax_rate" class="oe_inline"/> %%
                            </div>
                            <field name="transfer_account_id"/>
                        </group>
                        <group groups="account.group_account_user" invisible="1">
                            <field name="code_digits"/>
                            <field name="bank_account_code_prefix"/>
                            <field name="cash_account_code_prefix"/>
                        </group>
                    </group>
                </group>
            </field>
        </record>
        <record id="action_wizard_multi_chart" model="ir.actions.act_window">
            <field name="name">Choose Accounting Template</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">wizard.multi.charts.accounts</field>
            <field name="view_id" ref="view_wizard_multi_chart"/>
            <field name="view_type">form</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

         <!-- Fiscal Position Templates -->

        <record id="view_account_position_template_search" model="ir.ui.view">
            <field name="name">account.fiscal.position.template.search</field>
            <field name="model">account.fiscal.position.template</field>
            <field name="arch" type="xml">
                <search string="Fiscal Position">
                    <field name="name" string="Fiscal Position Template"/>
                </search>
            </field>
        </record>

        <record id="view_account_position_template_form" model="ir.ui.view">
            <field name="name">account.fiscal.position.template.form</field>
            <field name="model">account.fiscal.position.template</field>
            <field name="arch" type="xml">
                <form string="Fiscal Position Template">
                    <group col="4">
                        <field name="name"/>
                        <field name="chart_template_id"/>
                    </group>
                    <field name="tax_ids">
                        <tree string="Taxes Mapping" editable="bottom">
                            <field name="tax_src_id" domain="[('type_tax_use', '!=', None)]"/>
                            <field name="tax_dest_id" domain="[('type_tax_use', '!=', None)]"/>
                        </tree>
                        <form string="Taxes Mapping">
                            <field name="tax_src_id" domain="[('type_tax_use', '!=', None)]"/>
                            <field name="tax_dest_id" domain="[('type_tax_use', '!=', None)]"/>
                        </form>
                    </field>
                    <field name="account_ids">
                        <tree string="Accounts Mapping" editable="bottom">
                            <field name="account_src_id"/>
                            <field name="account_dest_id"/>
                        </tree>
                        <form string="Accounts Mapping">
                            <field name="account_src_id"/>
                            <field name="account_dest_id"/>
                        </form>
                    </field>
                </form>
            </field>
        </record>
        <record id="view_account_position_template_tree" model="ir.ui.view">
            <field name="name">account.fiscal.position.template.tree</field>
            <field name="model">account.fiscal.position.template</field>
            <field name="arch" type="xml">
                <tree string="Fiscal Position">
                    <field name="name"/>
                </tree>
            </field>
        </record>

        <record id="action_account_fiscal_position_template_form" model="ir.actions.act_window">
            <field name="name">Fiscal Position</field>
            <field name="res_model">account.fiscal.position.template</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_account_position_template_search"/>
        </record>

        <record id="account_bank_statement_pivot" model="ir.ui.view">
            <field name="name">account.bank.statement.pivot</field>
            <field name="model">account.bank.statement</field>
            <field name="arch" type="xml">
                <pivot string="Account Statistics" >
                    <field name="date" type="row"/>
                    <field name="balance_start" type="measure"/>
                    <field name="balance_end" type="measure"/>
                </pivot>
            </field>
         </record>
        <record id="account_bank_statement_graph" model="ir.ui.view">
            <field name="name">account.bank.statement.graph</field>
            <field name="model">account.bank.statement</field>
            <field name="arch" type="xml">
                <graph string="Account Statistics" type="bar">
                    <field name="date"/>
                    <field name="balance_start" operator="+"/>
                    <field name="balance_end" operator="+"/>
                </graph>
            </field>
         </record>

        <record id="action_view_bank_statement_tree" model="ir.actions.act_window">
            <field name="name">Cash Registers</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">account.bank.statement</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form,pivot,graph</field>
            <field name="view_id" ref="view_bank_statement_tree"/>
            <field name="search_view_id" ref="view_bank_statement_search"/>
            <field name="domain">[('journal_id.type', '=', 'cash')]</field>
            <field name="context">{'journal_type':'cash'}</field>
            <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                Click to create a new cash log.
              </p><p>
                A Cash Register allows you to manage cash entries in your cash
                journals. This feature provides an easy way to follow up cash
                payments on a daily basis. You can enter the coins that are in
                your cash box, and then post entries when money comes in or
                goes out of the cash box.
              </p>
            </field>
        </record>

        <menuitem id="menu_account_customer" name="Customers"
            parent="menu_finance_receivables_master_data"
            action="base.action_partner_customer_form" sequence="100"/>

        <record id="product_product_view_tree" model="ir.ui.view">
            <field name="name">product.product.tree</field>
            <field name="model">product.product</field>
            <field name="arch" type="xml">
                <tree string="Products">
                    <field name="default_code"/>
                    <field name="name"/>
                    <field name="attribute_value_ids" widget="many2many_tags" groups="product.group_product_variant"/>
                    <field name="lst_price"/>
                    <field name="taxes_id" widget="many2many_tags"/>
                    <field name="supplier_taxes_id" widget="many2many_tags"/>
                </tree>
            </field>
        </record>

        <record id="product_product_action" model="ir.actions.act_window">
            <field name="name">Products</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">product.product</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="view_type">form</field>
            <field name="view_id" ref="product_product_view_tree"/>
        </record>

        <record id="product_product_action_sellable" model="ir.actions.act_window">
            <field name="name">Sellable Products</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">product.product</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="view_type">form</field>
            <field name="context">{'search_default_filter_to_sell': 1}</field>
            <field name="view_id" ref="product_product_view_tree"/>
        </record>

        <record id="product_product_action_purchasable" model="ir.actions.act_window">
            <field name="name">Purchasable Products</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">product.product</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="view_type">form</field>
            <field name="context">{'search_default_filter_to_purchase': 1}</field>
            <field name="view_id" ref="product_product_view_tree"/>
        </record>

        <menuitem id="product_product_menu_sellable" name="Sellable Products" action="product_product_action_sellable" parent="account.menu_finance_receivables_master_data" sequence="100"/>

        <menuitem id="product_product_menu_purchasable" name="Purchasable Products" action="product_product_action_purchasable" parent="account.menu_finance_payables_master_data" sequence="101"/>

        <menuitem id="menu_account_supplier" name="Vendors"
            parent="menu_finance_payables_master_data"
            action="base.action_partner_supplier_form" sequence="100"/>

        <!-- Account Financial Report -->

        <record id="view_account_financial_report_form" model="ir.ui.view">
            <field name="name">account.financial.report.form</field>
            <field name="model">account.financial.report</field>
            <field name="arch" type="xml">
                <form string="Account Report">
                    <group col="4">
                        <field name="name"/>
                        <field name="parent_id"/>
                        <field name="sequence"/>
                        <field name="type"/>
                        <field name="sign"/>
                        <field name="style_overwrite"/>
                    </group>
                    <notebook attrs="{'invisible': [('type','not in',['accounts','account_type', 'account_report'])]}">
                        <page string="Report">
                            <group>
                                <field name="display_detail" attrs="{'invisible': [('type','not in',['accounts','account_type'])]}"/>
                                <field name="account_report_id" attrs="{'invisible': [('type', '!=', 'account_report')]}"/>
                            </group>
                            <field name="account_ids" attrs="{'invisible': [('type', '!=', 'accounts')]}"/>
                            <field name="account_type_ids" attrs="{'invisible': [('type', '!=', 'account_type')]}"/>
                        </page>
                    </notebook>
                </form>
            </field>
        </record>

        <record id="view_account_financial_report_tree" model="ir.ui.view">
            <field name="name">account.financial.report.tree</field>
            <field name="model">account.financial.report</field>
            <field name="arch" type="xml">
                <tree string="Account Report">
                    <field name="name"/>
                    <field name="parent_id" invisible="1"/>
                    <field name="type"/>
                    <field name="account_report_id"/>
                </tree>
            </field>
        </record>

        <record id="view_account_financial_report_search" model="ir.ui.view">
            <field name="name">account.financial.report.search</field>
            <field name="model">account.financial.report</field>
            <field name="arch" type="xml">
                <search string="Account Report">
                    <field name="name" string="Account Report"/>
                    <field name="type"/>
                    <field name="account_report_id"/>
                    <group expand="0" string="Group By">
                        <filter string="Parent Report" domain="" context="{'group_by':'parent_id'}"/>
                        <filter string="Report Type" domain="[]" context="{'group_by':'type'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="action_account_financial_report_tree" model="ir.actions.act_window">
            <field name="name">Financial Reports</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">account.financial.report</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_account_financial_report_search"/>
            <field name="view_id" ref="view_account_financial_report_tree"/>
        </record>

        <menuitem id="menu_account_financial_reports_tree" name="Account Reports" parent="menu_account_reports" action="action_account_financial_report_tree"/>


        <!-- Manual reconciliation -->
        <record id="action_manual_reconciliation" model="ir.actions.client">
            <field name="name">Manual Reconciliation</field>
            <field name="tag">manual_reconciliation_view</field>
        </record>

        <menuitem id="menu_action_manual_reconciliation" parent="menu_finance_entries_actions" action="action_manual_reconciliation" sequence="25"/>

        <record id="view_account_group_form" model="ir.ui.view">
            <field name="name">account.group.form</field>
            <field name="model">account.group</field>
            <field name="arch" type="xml">
                <form string="Account Group">
                    <group>
                        <field name="name"/>
                        <field name="code_prefix"/>
                        <field name="parent_id"/>
                    </group>
                </form>
            </field>
        </record>

        <record id="view_account_group_search" model="ir.ui.view">
            <field name="name">account.group.search</field>
            <field name="model">account.group</field>
            <field name="arch" type="xml">
                <search string="Account groups">
                    <field name="name"
                           filter_domain="['|', ('code_prefix', '=like', str(self) + '%'), ('name', 'ilike', self)]"
                           string="Account group"/>
                </search>
            </field>
        </record>

        <record id="view_account_group_tree" model="ir.ui.view">
            <field name="name">account.group.tree</field>
            <field name="model">account.group</field>
            <field name="arch" type="xml">
                <tree string="Account Group">
                    <field name="code_prefix"/>
                    <field name="name"/>
                </tree>
            </field>
        </record>

        <record id="action_account_group_tree" model="ir.actions.act_window">
            <field name="name">Account Groups</field>
            <field name="res_model">account.group</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_account_group_search"/>
        </record>

    </data>
</odoo>
