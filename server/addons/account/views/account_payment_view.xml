<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_account_payment_tree" model="ir.ui.view">
            <field name="name">account.payment.tree</field>
            <field name="model">account.payment</field>
            <field name="arch" type="xml">
                <tree decoration-info="state == 'draft'" decoration-muted="state in ['reconciled', 'cancelled']" edit="false">
                    <field name="payment_date"/>
                    <field name="name"/>
                    <field name="journal_id"/>
                    <field name="payment_method_id"/>
                    <field name="partner_id" string="Customer"/>
                    <field name="amount" sum="Amount"/>
                    <field name="state"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="partner_type" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="view_account_supplier_payment_tree" model="ir.ui.view">
            <field name="name">account.supplier.payment.tree</field>
            <field name="model">account.payment</field>
            <field name="arch" type="xml">
                <tree decoration-info="state == 'draft'" decoration-muted="state in ['reconciled', 'cancelled']" edit="false">
                    <field name="payment_date"/>
                    <field name="name"/>
                    <field name="journal_id"/>
                    <field name="payment_method_id"/>
                    <field name="partner_id" string="Vendor"/>
                    <field name="amount" sum="Amount"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <field name="state"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="partner_type" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="view_account_payment_kanban" model="ir.ui.view">
            <field name="name">account.payment.kanban</field>
            <field name="model">account.payment</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="name"/>
                    <field name="partner_id"/>
                    <field name="payment_date"/>
                    <field name="state"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_global_click">
                                <div class="row mb4">
                                    <div class="col-xs-6">
                                        <strong><span><field name="name"/></span></strong>
                                    </div>
                                    <div class="col-xs-6 text-right">
                                        <strong><i class="fa fa-clock-o"/> <t t-esc="record.payment_date.value"/></strong>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-12">
                                        <span><field name="partner_id"/></span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-6">
                                        <field name="amount" widget='monetary'/>
                                    </div>
                                    <div class="col-xs-6">
                                        <span class="pull-right label label-default">
                                            <field name="state" widget="label_selection" options="{'classes': {'draft': 'default', 'sent': 'success'}}"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="view_account_payment_search" model="ir.ui.view">
            <field name="name">account.payment.search</field>
            <field name="model">account.payment</field>
            <field name="arch" type="xml">
                <search string="Payments">
                    <field name="name" filter_domain="['|', ('name','ilike',self), ('communication','ilike',self)]" string="Description"/>
                    <separator/>
                    <field name="partner_id" />
                    <field name="journal_id" />
                    <field name="company_id" groups="base.group_multi_company" />
                    <filter string="Payable" name="payable_filter" domain="[('payment_type','=','outbound')]"/>
                    <filter string="Receivable" name="receivable_filter" domain="[('payment_type','=','inbound')]"/>
                    <filter string="Transfers" name="transfers_filter" domain="[('payment_type','=','transfer')]"/>
                    <separator/>
                    <filter string="Draft" domain="[('state','=','draft')]" name='state_draft'/>
                    <filter string="Posted" domain="[('state','=','posted')]" name='state_posted'/>
                    <filter string="Sent" domain="[('state','=','sent')]" name='state_sent'/>
                    <filter string="Reconciled" domain="[('state','=','reconciled')]"/>
                    <separator/>
                    <filter string="Partner" domain="[]" context="{'group_by': 'partner_id'}"/>
                    <filter string="Journal" domain="[]" context="{'group_by': 'journal_id'}"/>
                    <filter string="Payment Method" domain="[]" context="{'group_by': 'payment_method_id'}"/>
                    <filter string="State" domain="[]" context="{'group_by': 'state'}"/>
                    <filter string="Currency" domain="[]" context="{'group_by': 'currency_id'}" groups="base.group_multi_currency"/>
                    <filter string="Company" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </search>
            </field>
        </record>

        <record id="view_account_payment_form" model="ir.ui.view">
            <field name="name">account.payment.form</field>
            <field name="model">account.payment</field>
            <field name="arch" type="xml">
                <form string="Register Payment">
                    <header>
                        <button name="post" class="oe_highlight" states="draft" string="Confirm" type="object"/>
                        <button name="action_draft" class="oe_highlight" states="cancelled" string="Set To Draft" type="object"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,posted,reconciled,cancelled"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button class="oe_stat_button" name="button_journal_entries"
                                    string="Journal Items" type="object"
                                    groups="account.group_account_user"
                                    attrs="{'invisible':[('move_line_ids','=',[])]}" icon="fa-bars"/>
                            <field name="move_line_ids" invisible="1"/>
                            <button class="oe_stat_button" name="button_invoices"
                                    string="Invoices" type="object"
                                    attrs="{'invisible':[('has_invoices','=',False)]}" icon="fa-bars"/>
                            <button class="oe_stat_button" name="open_payment_matching_screen"
                                    string="Payment Matching" type="object"
                                    attrs="{'invisible':[('move_reconciled','=',True)]}" icon="fa-university"/>
                            <field name="has_invoices" invisible="1"/>
                            <field name="move_reconciled" invisible="1"/>
                        </div>
                        <field name="id" invisible="1"/>
                        <div class="oe_title" attrs="{'invisible': [('state', '=', 'draft')]}">
                            <h1><field name="name"/></h1>
                        </div>
                        <group>
                            <group>
                                <field name="payment_type" widget="radio" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="partner_type" widget="selection" attrs="{'required': [('state', '=', 'draft'), ('payment_type', 'in', ('inbound', 'outbound'))], 'invisible': [('payment_type', 'not in', ('inbound', 'outbound'))], 'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="partner_id" attrs="{'required': [('state', '=', 'draft'), ('payment_type', 'in', ('inbound', 'outbound'))], 'invisible': [('payment_type', 'not in', ('inbound', 'outbound'))], 'readonly': [('state', '!=', 'draft')]}" context="{'default_is_company': True, 'default_supplier': payment_type == 'outbound', 'default_customer': payment_type == 'inbound'}"/>
                                <label for="amount"/>
                                <div name="amount_div" class="o_row">
                                    <field name="amount" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="currency_id" options="{'no_create': True, 'no_open': True}" groups="base.group_multi_currency" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                </div>
                                <field name="journal_id" widget="selection" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="destination_journal_id" widget="selection" attrs="{'required': [('payment_type', '=', 'transfer')], 'invisible': [('payment_type', '!=', 'transfer')], 'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="hide_payment_method" invisible="1"/>
                                <field name="payment_method_id" string=" " widget="radio" attrs="{'invisible': [('hide_payment_method', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="payment_method_code" invisible="1"/>
                            </group>
                            <group>
                                <field name="payment_date" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="communication" attrs="{'invisible': [('state', '!=', 'draft'), ('communication', '=', False)], 'readonly': [('state', '!=', 'draft')]}"/>
                            </group>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                      <field name="message_follower_ids" widget="mail_followers" groups="base.group_user"/>
                      <field name="message_ids" widget="mail_thread"/>
                  </div>
                </form>
            </field>
        </record>

        <record id="view_account_payment_graph" model="ir.ui.view">
            <field name="name">account.payment.graph</field>
            <field name="model">account.payment</field>
            <field name="arch" type="xml">
                <graph string="Invoices" type="bar" stacked="1">
                    <field name="payment_type"/>
                    <field name="journal_id"/>
                    <field name="amount" type="measure"/>
                </graph>
            </field>
        </record>

        <record id="action_account_payments" model="ir.actions.act_window">
            <field name="name">Payments</field>
            <field name="res_model">account.payment</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,kanban,form,graph</field>
            <field name="context">{'default_payment_type': 'inbound', 'default_partner_type': 'customer', 'search_default_receivable_filter': 1}</field>
            <field name="domain">[]</field>
            <field name="view_id" ref="view_account_payment_tree"/>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                  Click to register a payment
                </p><p>
                  Payments are used to register liquidity movements (send, collect or transfer money).
                  You can then process those payments by your own means or by using installed facilities.
                </p>
            </field>
        </record>

        <record id="action_account_payments_payable" model="ir.actions.act_window">
            <field name="name">Payments</field>
            <field name="res_model">account.payment</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,kanban,form,graph</field>
            <field name="context">{'default_payment_type': 'outbound', 'default_partner_type': 'supplier', 'search_default_payable_filter': 1}</field>
            <field name="domain">[]</field>
            <field name="view_id" ref="view_account_supplier_payment_tree"/>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                  Click to register a payment
                </p><p>
                  Payments are used to register liquidity movements (send, collect or transfer money).
                  You can then process those payments by your own means or by using installed facilities.
                </p>
            </field>
        </record>

        <menuitem action="action_account_payments" id="menu_action_account_payments_receivable" parent="menu_finance_receivables_documents" sequence="20"/>
        <menuitem action="action_account_payments_payable" id="menu_action_account_payments_payable" parent="menu_finance_payables_documents" sequence="20"/>

        <record id="view_account_payment_invoice_form" model="ir.ui.view">
            <field name="name">account.payment.invoice.form</field>
            <field name="model">account.payment</field>
            <field name="arch" type="xml">
                <form string="Register Payment">
                    <sheet>
                        <group>
                            <field name="payment_type" invisible="1"/>
                            <field name="partner_type" invisible="1"/>
                            <field name="invoice_ids" invisible="1"/>
                            <field name="partner_id" invisible="1"/>
                            <field name="state" invisible="1"/>
                            <group>
                                <label for="amount"/>
                                <div name="amount_div" class="o_row">
                                    <field name="amount"/>
                                    <field name="currency_id" options="{'no_create': True, 'no_open': True}" groups="base.group_multi_currency"/>
                                </div>
                                <field name="journal_id" widget="selection" attrs="{'invisible': [('amount', '=', 0)]}"/>
                                <field name="hide_payment_method" invisible="1"/>
                                <field name="payment_method_id" widget="radio" attrs="{'invisible': ['|', ('hide_payment_method', '=', True), ('amount', '=', 0.0)]}"/>
                                <field name="payment_method_code" invisible="1"/>
                            </group>
                            <group>
                                <field name="payment_date"/>
                                <field name="communication"/>
                            </group>
                            <group attrs="{'invisible': [('payment_difference', '=', 0.0)]}">
                                <label for="payment_difference"/>
                                <div>
                                    <field name="payment_difference"/>
                                    <field name="payment_difference_handling" widget="radio" nolabel="1"/>
                                    <div attrs="{'invisible': [('payment_difference_handling','=','open')]}">
                                        <label for="writeoff_account_id" class="oe_edit_only" string="Post Difference In"/>
                                        <field name="writeoff_account_id" string="Post Difference In" attrs="{'required': [('payment_difference_handling', '=', 'reconcile')]}"/>
                                        <label string="Journal" attrs="{'invisible': [('amount', '!=', 0)]}"/>
                                        <field name="journal_id" string="Journal" widget="selection" attrs="{'invisible': [('amount', '!=', 0)]}"/>
                                        <label for="writeoff_label" class="oe_edit_only" string="Label"/>
                                        <field name="writeoff_label" attrs="{'required': [('payment_difference_handling', '=', 'reconcile')]}"/>
                                    </div>
                                </div>
                            </group>
                        </group>
                    </sheet>
                    <footer>
                        <button string='Validate' name="action_validate_invoice_payment" type="object" class="btn-primary"/>
                        <button string="Cancel" class="btn-default" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_account_invoice_payment" model="ir.actions.act_window">
            <field name="name">Register Payment</field>
            <field name="res_model">account.payment</field>
            <field name="view_type">form</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="view_account_payment_invoice_form"/>
            <field name="context">{'default_invoice_ids': [(4, active_id, None)]}</field>
            <field name="target">new</field>
        </record>


        <!-- Register payment from several invoices -->

        <record id="view_account_payment_from_invoices" model="ir.ui.view">
            <field name="name">account.register.payments.wizard</field>
            <field name="model">account.register.payments</field>
            <field name="arch" type="xml">
                <form string="Register Payment">
                    <field name="payment_type" invisible="1"/>
                    <field name="partner_type" invisible="1"/>
                    <field name="partner_id" invisible="1"/>
                    <group>
                        <group>
                            <field name="journal_id" widget="selection"/>
                            <field name="hide_payment_method" invisible="1"/>
                            <field name="payment_method_id" widget="radio" attrs="{'invisible': [('hide_payment_method', '=', True)]}"/>
                            <field name="payment_method_code" invisible="1"/>
                            <field name="currency_id" invisible="1"/>
                            <field name="multi" invisible="1"/>
                            <field name="amount" attrs="{'readonly': [('multi', '=', True)]}"/>
                        </group>
                        <group>
                            <field name="payment_date"/>
                            <field name="communication"/>
                        </group>
                    </group>
                    <footer>
                        <button string='Validate' name="create_payments" type="object" class="btn-primary"/>
                        <button string="Cancel" class="btn-default" special="cancel"/>
                    </footer>
               </form>
            </field>
        </record>

        <act_window
            id="action_account_payment_from_invoices"
            name="Register Payment"
            res_model="account.register.payments"
            src_model="account.invoice"
            view_mode="form"
            multi="True"
            target="new"
            key2="client_action_multi"
        />

        <!-- Action confirm_payments for multi -->
        <record id="action_account_confirm_payments" model="ir.actions.server">
            <field name="name">Confirm Payments</field>
            <field name="type">ir.actions.server</field>
            <field name="state">code</field>
            <field name="model_id" ref="account.model_account_payment"/>
            <field name="binding_model_id" ref="account.model_account_payment"/>
            <field name="code">
records.post()
            </field>
        </record>

    </data>
</odoo>
