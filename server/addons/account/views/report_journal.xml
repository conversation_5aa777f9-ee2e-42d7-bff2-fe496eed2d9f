<?xml version="1.0" encoding="utf-8"?>
<odoo>
<template id="report_journal">
    <t t-call="web.html_container">
        <t t-set="data_report_margin_top" t-value="12"/>
        <t t-set="data_report_header_spacing" t-value="9"/>
        <t t-set="data_report_dpi" t-value="110"/>
        <t t-foreach="docs" t-as="o">
            <t t-call="web.internal_layout">
                <div class="page">
                    <h2><t t-esc="o.name"/> Journal</h2>

                    <div class="row mt32">
                        <div class="col-xs-3">
                            <strong>Company:</strong>
                            <p t-esc="res_company.name"/>
                        </div>
                        <div class="col-xs-3">
                            <strong>Journal:</strong>
                            <p t-esc="o.name"/>
                        </div>
                        <div class="col-xs-3">
                            <strong>Entries Sorted By:</strong>
                            <p t-if="data['form'].get('sort_selection') == 'move_name'">Journal Entry Number</p>
                            <p t-if="data['form'].get('sort_selection') == 'date'">Date</p>
                        </div>
                        <div class="col-xs-3">
                            <strong>Target Moves:</strong>
                            <p t-if="data['form']['target_move'] == 'all'">All Entries</p>
                            <p t-if="data['form']['target_move'] == 'posted'">All Posted Entries</p>
                        </div>
                    </div>

                    <table class="table table-condensed">
                        <thead>
                            <tr>
                                <th>Move</th>
                                <th>Date</th>
                                <th>Account</th>
                                <th>Partner</th>
                                <th>Label</th>
                                <th>Debit</th>
                                <th>Credit</th>
                                <th t-if="data['form']['amount_currency']">Currency</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr t-foreach="lines[o.id]" t-as="aml">
                                <td><span t-esc="aml.move_id.name != '/' and aml.move_id.name or ('*'+str(aml.move_id.id))"/></td>
                                <td><span t-field="aml.date"/></td>
                                <td><span t-field="aml.account_id.code"/></td>
                                <td><span t-esc="aml.sudo().partner_id and aml.sudo().partner_id.name and aml.sudo().partner_id.name[:23] or ''"/></td>
                                <td><span t-esc="aml.name and aml.name[:35]"/></td>
                                <td><span t-esc="aml.debit" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/></td>
                                <td><span t-esc="aml.credit" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/></td>
                                <td t-if="data['form']['amount_currency'] and aml.amount_currency">
                                    <span t-esc="aml.amount_currency" t-options="{'widget': 'monetary', 'display_currency': aml.currency_id}"/>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <div class="row">
                        <div class="col-xs-4 pull-right">
                            <table class="table table-condensed">
                                <tr>
                                    <td><strong>Total</strong></td>
                                    <td><span t-esc="sum_debit(data, o)" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/></td>
                                    <td><span t-esc="sum_credit(data, o)" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-xs-4">
                            <table class="table table-condensed">
                                <thead>
                                    <tr><th colspan="3">Tax Declaration</th></tr>
                                    <tr>
                                        <th>Name</th>
                                        <th>Base Amount</th>
                                        <th>Tax Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-set="taxes" t-value="get_taxes(data, o)"/>
                                    <tr t-foreach="taxes" t-as="tax">
                                        <td><span t-esc="tax.name"/></td>
                                        <td><span t-esc="taxes[tax]['base_amount']" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/></td>
                                        <td><span t-esc="taxes[tax]['tax_amount']" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                </div>
            </t>
        </t>
    </t>
</template>
</odoo>
