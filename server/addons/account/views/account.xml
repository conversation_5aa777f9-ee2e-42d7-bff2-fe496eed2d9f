<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="assets_backend" name="account assets" inherit_id="web.assets_backend">
        <xpath expr="." position="inside">
            <link rel="stylesheet" href="/account/static/src/css/account_bank_and_cash.css"/>
            <link rel="stylesheet" href="/account/static/src/css/account.css"/>
            <link rel="stylesheet" href="/account/static/src/less/account_reconciliation.less"/>
            <link rel="stylesheet" href="/account/static/src/less/account_journal_dashboard.less"/>
            <link rel="stylesheet" href="/account/static/src/less/account_dashboard.less"/>

            <script type="text/javascript" src="/account/static/src/js/reconciliation/reconciliation_action.js"></script>
            <script type="text/javascript" src="/account/static/src/js/reconciliation/reconciliation_model.js"></script>
            <script type="text/javascript" src="/account/static/src/js/reconciliation/reconciliation_renderer.js"></script>

            <!-- <script type="text/javascript" src="/account/static/src/js/tour_bank_statement_reconciliation.js"></script> -->
            <script type="text/javascript" src="/account/static/src/js/account_payment_field.js"></script>
            <script type="text/javascript" src="/account/static/src/js/account_dashboard_setup_bar.js"></script>
        </xpath>
    </template>

    <template id="qunit_suite" name="account_reconciliation tests" inherit_id="web.qunit_suite">
        <xpath expr="//script[contains(@src, '/web/static/tests/views/kanban_tests.js')]" position="after">
            <script type="text/javascript" src="/account/static/tests/reconciliation_tests.js"></script>
            <script type="text/javascript" src="/account/static/tests/account_payment_field_tests.js"></script>
            <script type="text/javascript" src="/account/static/tests/account_dashboard_setup_bar_tests.js"></script>
        </xpath>
    </template>
</odoo>
