-
  In order to test the PDF reports defined on an invoice, we will create a Invoice Record
-
  !record {model: account.invoice, id: test_invoice_1}:
    currency_id: base.EUR
    company_id: base.main_company
    partner_id: base.res_partner_1
    state: draft
    type: out_invoice
    account_id: account.a_recv
    name: Test invoice 1
-
  In order to test the PDF reports defined on an invoice, we will print an Invoice Report
-
  !python {model: account.invoice, id: False}: |
    import os
    from odoo import tools
    data, format = ref('account.report_invoice').render(ref('account.account_invoice_customer0'))
    if tools.config['test_report_directory']:
        open(os.path.join(tools.config['test_report_directory'], 'account-invoice.'+format), 'wb+').write(data)
-
  In order to test the PDF reports defined on a partner, we will print the Overdue Report
-
  !python {model: res.partner, id: False}: |
    import os
    from odoo import tools
    data, format = ref('account.report_overdue').render([ref('base.res_partner_1'),ref('base.res_partner_2'),ref('base.res_partner_12')])
    if tools.config['test_report_directory']:
        open(os.path.join(tools.config['test_report_directory'], 'account-report_overdue.'+format), 'wb+').write(data)
-
  Print the Aged Partner Balance Report
-
  !python {model: account.account, id: False}: |
    ctx={}
    data_dict = {'chart_account_id':ref('account.chart0')}
    from odoo.tools import test_reports
    test_reports.try_report_action(self.env.cr, self.env.uid, 'action_account_aged_balance_view',wiz_data=data_dict, context=ctx, our_module='account')
-
  Print the Account Balance Sheet in Normal mode
-
  !python {model: account.account, id: False}: |
    ctx={}
    data_dict = {'chart_account_id':ref('account.chart0'), 'account_report_id': ref('account_financial_report_balancesheet0')}
    from odoo.tools import test_reports
    test_reports.try_report_action(self.env.cr, self.env.uid, 'action_account_report',wiz_data=data_dict, context=ctx, our_module='account')
-
  Print the Account Balance Report in Normal mode through the wizard - From Account Chart
-
  !python {model: account.account, id: False}: |
    ctx={}
    data_dict = {'chart_account_id':ref('account.chart0')}
    from odoo.tools import test_reports
    test_reports.try_report_action(self.env.cr, self.env.uid, 'action_account_balance_menu',wiz_data=data_dict, context=ctx, our_module='account')
-
  Print the General Ledger Report (in Landscape Mode)
-
  !python {model: account.account, id: False}: |
    ctx={}
    data_dict = {'chart_account_id':ref('account.chart0')}
    from odoo.tools import test_reports
    test_reports.try_report_action(self.env.cr, self.env.uid, 'action_account_general_ledger_menu',wiz_data=data_dict, context=ctx, our_module='account')
-
  Print the Profit-Loss Report in Normal Mode
-
  !python {model: account.account, id: False}: |
    ctx={}
    data_dict = {'chart_account_id':ref('account.chart0'), 'target_move': 'all', 'account_report_id': ref('account_financial_report_balancesheet0')}
    from odoo.tools import test_reports
    test_reports.try_report_action(self.env.cr, self.env.uid, 'action_account_report',wiz_data=data_dict, context=ctx, our_module='account')
