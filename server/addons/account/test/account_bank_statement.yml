-
  !context
    journal_type: 'bank'
-
  In order to test Bank Statement feature of account I create a bank statement line and confirm it and check it's move created
-
  I select the period and journal for the bank statement
-
  !python {model: account.bank.statement, id: False}: |
    import time
    journal = self.with_context({'lang': u'en_US', 'tz': False, 'active_model': 'ir.ui.menu',
      'journal_type': 'bank', 'date': time.strftime("%Y/%m/%d")})._default_journal()
    assert journal, 'Journal has not been selected'
-
  I create a bank statement with Opening and Closing balance 0.
-
  !record {model: account.bank.statement, id: account_bank_statement_0}:
    balance_end_real: 0.0
    balance_start: 0.0
    date: !eval time.strftime('%Y-%m-%d')
    company_id: base.main_company
-
  !python {model: account.bank.statement.line, id: False}: |
    import time
    account_id = self.env['account.account'].search([('user_type_id.type', '=', 'liquidity'), ('currency_id', '=', False)], limit=1).id
    vals = {
        'amount': 1000,
        'date': time.strftime('%Y-%m-%d'),
        'partner_id': ref('base.res_partner_4'),
        'name': 'EXT001',
        'statement_id': ref('account_bank_statement_0'),
    }
    self.create(vals)
-
  I process the bank statement line
-
  !python {model: account.bank.statement, id: False}: |
    account = self.env['account.account'].create({'name': 'toto', 'code': 'bidule', 'user_type_id': self.env.ref('account.data_account_type_fixed_assets').id})
    statement = self.browse([ref("account_bank_statement_0")])
    statement.line_ids[0].process_reconciliation(new_aml_dicts=[{
        'credit': 1000,
        'debit': 0,
        'name': 'toto',
        'account_id': account.id,
    }])
-
  I modify the bank statement and set the Closing Balance.
-
  !record {model: account.bank.statement, id: account_bank_statement_0}:
    balance_end_real: 1000.0
-
  I confirm the bank statement using Confirm button
-
  !python {model: account.bank.statement, id: False}: |
    self.browse(ref("account_bank_statement_0")).button_confirm_bank()
-
  I check that bank statement state is now "Closed"
-
  !assert {model: account.bank.statement, id: account_bank_statement_0}:
    - state == 'confirm'
