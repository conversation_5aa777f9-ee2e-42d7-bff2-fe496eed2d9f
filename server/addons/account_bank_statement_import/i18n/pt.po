# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_bank_statement_import
# 
# Translators:
# <PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-30 13:11+0000\n"
"PO-Revision-Date: 2017-09-20 10:14+0000\n"
"Last-Translator: <PERSON> <pedro<PERSON>@exo.pt>, 2019\n"
"Language-Team: Portuguese (https://www.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:241
#, python-format
msgid "%d transactions had already been imported and were ignored."
msgstr "%d transações já tinham sido importadas e foram ignoradas."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:241
#, python-format
msgid "1 transaction had already been imported and was ignored."
msgstr "1 transação já foi importada e foi ignorada."

#. module: account_bank_statement_import
#: sql_constraint:account.bank.statement.line:0
msgid "A bank account transactions can be imported only once !"
msgstr "Uma transação bancária só pode ser importada uma vez."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_bank_acc_number
msgid "Account Number"
msgstr "Número da Conta"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_type_control_ids
msgid "Account Types Allowed"
msgstr "Tipos de Conta Permitidos"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_account_control_ids
msgid "Accounts Allowed"
msgstr "Contas Permitidas"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_active
msgid "Active"
msgstr "Ativo"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_update_posted
msgid "Allow Cancelling Entries"
msgstr "Permitir Cancelamento de Entradas"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:243
#, python-format
msgid "Already imported items"
msgstr "Itens já importados"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_at_least_one_inbound
msgid "At Least One Inbound"
msgstr "Pelo Menos Uma a Receber"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_at_least_one_outbound
msgid "At Least One Outbound"
msgstr "Pelo Menos Uma a Enviar"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:78
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_bank_id
#, python-format
msgid "Bank"
msgstr "Banco"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_bank_account_id
msgid "Bank Account"
msgstr "Conta Bancária"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_bank_statements_source
msgid "Bank Feeds"
msgstr "Feeds Bancários"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid "Bank Journal Name"
msgstr "Nome do Diário de Bancos"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_data_file
msgid "Bank Statement File"
msgstr "Ficheiro de Extrato Bancário"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Linha de Extrato Bancário"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_account_setup_bank_data_done
msgid "Bank setup marked as done"
msgstr "Configuração bancária feita"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_belongs_to_company
msgid "Belong to the user's current company"
msgstr "Pertence à empresa atual do utilizador"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Cancel"
msgstr "Cancelar"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:167
#, python-format
msgid ""
"Cannot find in which journal import this statement. Please manually select a"
" journal."
msgstr ""
"Não consegue saber qual o diário que importa este extrato. Por favor, "
"selecione manualmente um diário."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" credit notes made from this journal"
msgstr ""
"Marque esta caixa se não quer usar a mesma sequência para faturas e notas de"
" crédito feitas a partir deste diário"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_update_posted
msgid ""
"Check this box if you want to allow the cancellation the entries related to "
"this journal or of the invoice related to this journal"
msgstr ""
"Marque esta caixa se quer permitir o cancelamento da fatura ou de entradas "
"relativas a este diário"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Choose a file to import..."
msgstr "Escolher um ficheiro para importar..."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_color
msgid "Color Index"
msgstr "Índice de Cores"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_company_id
msgid "Company"
msgstr "Empresa"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_company_id
msgid "Company related to this journal"
msgstr "Empresa relativa a este diário"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:108
#, python-format
msgid ""
"Could not make sense of the given file.\n"
"Did you install the module to support this type of file ?"
msgstr ""
"O ficheiro escolhido não está conforme.\n"
"Instalou o módulo que permite suportar este tipo de ficheiro?"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_create_uid
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_create_uid
msgid "Created by"
msgstr "Criada por"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_create_date
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_create_date
msgid "Created on"
msgstr "Criado em"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence_id
msgid "Credit Note Entry Sequence"
msgstr "Sequência de Entrada da Nota de Crédito"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence_number_next
msgid "Credit Notes: Next Number"
msgstr "Notas de Crédito: Próximo Número"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_currency_id
msgid "Currency"
msgstr "Moeda"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_inbound_payment_method_ids
msgid "Debit Methods"
msgstr "Métodos de Débito"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence
msgid "Dedicated Credit Note Sequence"
msgstr "Sequência de Notas de Crédito Dedicada"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_default_credit_account_id
msgid "Default Credit Account"
msgstr "Conta Crédito Predefinida"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_default_debit_account_id
msgid "Default Debit Account"
msgstr "Conta Débito Predefinida"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_display_name
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_display_name
msgid "Display Name"
msgstr "Nome a Mostrar"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid ""
"Download a bank statement from your bank and import it here. Supported "
"formats:"
msgstr ""
"Transferir um extrato bancário do seu banco e importá-lo para aqui. Formatos"
" suportados:"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence_id
msgid "Entry Sequence"
msgstr "Sequência de Entrada"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_filename
msgid "Filename"
msgstr "Nome de ficheiro"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_data_file
msgid ""
"Get you bank statements in electronic format from your bank and select them "
"here."
msgstr ""
"Obtenha os seus extratos bancários em formato eletrónico do seu banco e "
"selecione-os aqui."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_group_invoice_lines
msgid "Group Invoice Lines"
msgstr "Agrupar Linhas de Fatura"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_id
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_id
msgid "ID"
msgstr "ID"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_group_invoice_lines
msgid ""
"If this box is checked, the system will try to group the accounting lines "
"when generating them from invoices."
msgstr ""
"Se esta caixa for marcada, o sistema tentará agrupar as linhas "
"contabilísticas quando as gerar nas faturas."

#. module: account_bank_statement_import
#: model:ir.actions.act_window,name:account_bank_statement_import.action_account_bank_statement_import
msgid "Import"
msgstr "Importar"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr "Importar Extrato Bancário"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_import_journal_creation
msgid "Import Bank Statement Journal Creation Wizard"
msgstr "Assistente de Criação do Diário de Importação do Extrato Bancário"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Import Bank Statements"
msgstr "Importar extratos bancários"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_line_unique_import_id
msgid "Import ID"
msgstr "Importar identificação"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.journal_dashboard_view_inherit
msgid "Import Statement"
msgstr "Importar extrato"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_default_credit_account_id
msgid "It acts as a default account for credit amount"
msgstr "Age como uma conta de crédito por defeito"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_default_debit_account_id
msgid "It acts as a default account for debit amount"
msgstr "Age como uma conta de débito por defeito"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_journal
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_journal_id
msgid "Journal"
msgstr "Diário"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:69
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
#, python-format
msgid "Journal Creation"
msgstr "Criação de Diário"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_name
msgid "Journal Name"
msgstr "Nome do Diário"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid ""
"Just click OK to create the account/journal and finish the import. If this "
"was a mistake, hit cancel to abort the import."
msgstr ""
"Clique em \"OK\" para criar a conta/diário e finalizar a importação. Se isto"
" foi um engano, clique em \"Cancelar\" para abortar a importação."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_kanban_dashboard
msgid "Kanban Dashboard"
msgstr "Painel do Kanban"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "Gráfico do Painel do Kanban"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import___last_update
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation___last_update
msgid "Last Modified on"
msgstr "Última Modificação em"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_write_uid
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_write_uid
msgid "Last Updated by"
msgstr "Última Atualização por"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_write_date
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_write_date
msgid "Last Updated on"
msgstr "Última Atualização em"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_loss_account_id
msgid "Loss Account"
msgstr "Conta de Prejuízos"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_inbound_payment_method_ids
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo,you are suggested to reconcile the transaction with the batch deposit. Enable this option from the settings."
msgstr ""
"Manual: Receba pagamentos em numerário, cheque ou qualquer outro método fora do Odoo.\n"
"Eletrónico: Receba pagamentos automaticamente através de um gestor de transações pedindo uma transação com um cartão registado pelo cliente ao comprar ou subscrevendo online (token de pagamento).\n"
"Depósito em Lote: Junte vários cheques de clientes ao mesmo tempo gerando um depósito em lote e submetendo ao seu banco. Quando codificar o extrato bancário no Odoo, é-lhe sugerido que reconcilie a transação com o depósito em lote. Ative esta opção nas definições."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_outbound_payment_method_ids
msgid ""
"Manual:Pay bill by cash or any other method outside of Odoo.\n"
"Check:Pay bill by check and print it from Odoo.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. Enable this option from the settings."
msgstr ""
"Manual: Pague uma conta por numerário ou qualquer outro método fora do Odoo.\n"
"Cheque: Pague uma conta por cheque e imprima-o através do Odoo.\n"
"Transferência a Crédito SEPA: Pague uma conta através de um ficheiro de Transferência a Crédito SEPA submetido ao seu banco. Ative esta opção nas definições."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence_number_next
msgid "Next Number"
msgstr "Próximo Número"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:138
#, python-format
msgid "No currency found matching '%s'."
msgstr "Não foi encontrada nenhuma moeda correspondente a '%s'."

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid "OK"
msgstr "OK"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_outbound_payment_method_ids
msgid "Payment Methods"
msgstr "Métodos de Pagamento"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_profit_account_id
msgid "Profit Account"
msgstr "Conta de Ganhos"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_type
msgid ""
"Select 'Sale' for customer invoices journals.\n"
"Select 'Purchase' for vendor bills journals.\n"
"Select 'Cash' or 'Bank' for journals that are used in customer or vendor payments.\n"
"Select 'General' for miscellaneous operations journals."
msgstr ""
"Selecione 'Venda' para diários de faturas de clientes.\n"
"Selecione 'Compra' para diários de faturas de fornecedores.\n"
"\n"
"Selecione 'Numerário' ou 'Banco' para diários usados em pagamentos de clientes ou fornecedores.\n"
"Selecione 'Geral' para diários de operações diversas."

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Select a bank statement file to import"
msgstr "Selecione um ficheiro com o extrato bancário para importar"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence
msgid "Sequence"
msgstr "Sequência"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_active
msgid "Set active to false to hide the Journal without removing it."
msgstr "Marque ativo como falso para ocultar o Diário sem o remover."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_code
msgid "Short Code"
msgstr "Código Abreviado"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_show_on_dashboard
msgid "Show journal on dashboard"
msgstr "Mostrar diário no painel"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_account_setup_bank_data_done
msgid "Technical field used in the special view for the setup bar step."
msgstr ""
"Campo técnico usado na vista especial da barra dos passos de configuração."

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid ""
"The account of the statement you are importing is not yet recorded in Odoo. "
"In order to proceed with the import, you need to create a bank journal for "
"this account."
msgstr ""
"A conta correspondente ao extrato que está a importar não está ainda criada "
"no Odoo. Para prosseguir com a importação, precisa de criar um diário para "
"esta conta bancária."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:153
#, python-format
msgid ""
"The account of this statement (%s) is not the same as the journal (%s)."
msgstr "A conta deste extratro (%s) não é a mesma do diário (%s)."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:163
#, python-format
msgid ""
"The currency of the bank statement (%s) is not the same as the currency of "
"the journal (%s) !"
msgstr "A moeda do extrato bancário (%s) não é a mesma do diário (%s)!"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_currency_id
msgid "The currency used to enter statement"
msgstr "A moeda usada para a introdução da linha declarativa"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_code
msgid "The journal entries of this journal will be named using this prefix."
msgstr ""
"As entradas de diário deste diário serão identificadas utilizando este "
"prefixo."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence_number_next
msgid "The next sequence number will be used for the next credit note."
msgstr ""
"O próximo número da sequência irá ser usado na próxima nota de crédito."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence_number_next
msgid "The next sequence number will be used for the next invoice."
msgstr "O próximo número da sequência irá ser usado na próxima fatura."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence_id
msgid ""
"This field contains the information related to the numbering of the credit "
"note entries of this journal."
msgstr ""
"Este campo contém a informação relativa à numeração das entradas de notas de"
" crédito deste diário."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence_id
msgid ""
"This field contains the information related to the numbering of the journal "
"entries of this journal."
msgstr ""
"Este campo contém a informação relativa à numeração das entradas de diário "
"deste diário."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:113
#, python-format
msgid "This file doesn't contain any statement."
msgstr "O ficheiro não contém nenhum extrato."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:121
#, python-format
msgid "This file doesn't contain any transaction."
msgstr "O ficheiro não contém nenhuma transação."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_type
msgid "Type"
msgstr "Tipo"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence
msgid "Used to order Journals in the dashboard view"
msgstr "Utilizado para ordenar os Diários na visualização do painel"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_loss_account_id
msgid ""
"Used to register a loss when the ending balance of a cash register differs "
"from what the system computes"
msgstr "Usado para ordenar Diários na vista do painel"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_profit_account_id
msgid ""
"Used to register a profit when the ending balance of a cash register differs"
" from what the system computes"
msgstr ""
"Usado para registar um extravio quando o saldo de fecho de uma caixa difere "
"daquilo que o sistema calcular"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_show_on_dashboard
msgid "Whether this journal should be displayed on the dashboard or not"
msgstr "Se este diário deve ser apresentado no painel ou não"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:233
#, python-format
msgid "You have already imported that file."
msgstr "Já importou este ficheiro."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:47
#, python-format
msgid ""
"You have to set a Default Debit Account and a Default Credit Account for the"
" journal: %s"
msgstr ""
"Deve definir uma Conta de Débito predefinida e uma Conta de Crédito "
"predefinida para o Diário: %s"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "_Import"
msgstr "_Importar"
