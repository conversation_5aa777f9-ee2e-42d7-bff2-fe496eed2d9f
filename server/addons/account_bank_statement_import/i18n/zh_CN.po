# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_bank_statement_import
# 
# Translators:
# <AUTHOR> <EMAIL>, 2017
# <PERSON><PERSON> CHEN Fan <<EMAIL>>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON>, 2017
# <AUTHOR> <EMAIL>, 2017
# fausthuang, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <PERSON> <<EMAIL>>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-30 13:11+0000\n"
"PO-Revision-Date: 2017-11-30 13:11+0000\n"
"Last-Translator: e2f_cn c5 <<EMAIL>>, 2018\n"
"Language-Team: Chinese (China) (https://www.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:241
#, python-format
msgid "%d transactions had already been imported and were ignored."
msgstr "%d 已导入的交易将被忽略。"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:241
#, python-format
msgid "1 transaction had already been imported and was ignored."
msgstr "1个 已导入的交易将被忽略."

#. module: account_bank_statement_import
#: sql_constraint:account.bank.statement.line:0
msgid "A bank account transactions can be imported only once !"
msgstr "一个银行账户交易只能导入一次."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_bank_acc_number
msgid "Account Number"
msgstr "账户号码"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_type_control_ids
msgid "Account Types Allowed"
msgstr "允许的科目类型"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_account_control_ids
msgid "Accounts Allowed"
msgstr "允许的科目"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_active
msgid "Active"
msgstr "有效"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_update_posted
msgid "Allow Cancelling Entries"
msgstr "允许取消分录"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:243
#, python-format
msgid "Already imported items"
msgstr "已导入的项目"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_at_least_one_inbound
msgid "At Least One Inbound"
msgstr "至少一个转入"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_at_least_one_outbound
msgid "At Least One Outbound"
msgstr "至少一个转出"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:78
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_bank_id
#, python-format
msgid "Bank"
msgstr "银行"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_bank_account_id
msgid "Bank Account"
msgstr "银行账户"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_bank_statements_source
msgid "Bank Feeds"
msgstr "银行回单"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid "Bank Journal Name"
msgstr "银行日记账名称"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_data_file
msgid "Bank Statement File"
msgstr "银行对账单文件"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "银行对账单行"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_account_setup_bank_data_done
msgid "Bank setup marked as done"
msgstr "银行设置标记为完成"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_belongs_to_company
msgid "Belong to the user's current company"
msgstr "属于用户的当前公司"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Cancel"
msgstr "取消"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:167
#, python-format
msgid ""
"Cannot find in which journal import this statement. Please manually select a"
" journal."
msgstr "不能找到需导入此对账单的账簿。请手动选择账簿。"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" credit notes made from this journal"
msgstr "如果你不想在这帐中使用同样的序号记录发票和信用记录，请在此方格打勾"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_update_posted
msgid ""
"Check this box if you want to allow the cancellation the entries related to "
"this journal or of the invoice related to this journal"
msgstr "若果你想允许取消有关此 日记账下的分录或有关此日记帐的发票，勾选此方格。"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Choose a file to import..."
msgstr "选择需要导入的文件..."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_color
msgid "Color Index"
msgstr "颜色索引"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_company_id
msgid "Company"
msgstr "公司"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_company_id
msgid "Company related to this journal"
msgstr "此日记账相关的公司"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:108
#, python-format
msgid ""
"Could not make sense of the given file.\n"
"Did you install the module to support this type of file ?"
msgstr ""
"提供的文件不对。\n"
"你有安装支持这类文件的模块吗?"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_create_uid
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_create_uid
msgid "Created by"
msgstr "创建人"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_create_date
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_create_date
msgid "Created on"
msgstr "创建时间"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence_id
msgid "Credit Note Entry Sequence"
msgstr "信贷凭证序号"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence_number_next
msgid "Credit Notes: Next Number"
msgstr "信用证: 下一个编号"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_currency_id
msgid "Currency"
msgstr "货币"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_inbound_payment_method_ids
msgid "Debit Methods"
msgstr "借记方式"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence
msgid "Dedicated Credit Note Sequence"
msgstr "专门的信贷备注顺序"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_default_credit_account_id
msgid "Default Credit Account"
msgstr "默认贷方科目"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_default_debit_account_id
msgid "Default Debit Account"
msgstr "默认借方科目"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_display_name
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_display_name
msgid "Display Name"
msgstr "显示名称"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid ""
"Download a bank statement from your bank and import it here. Supported "
"formats:"
msgstr "从你的银行下载一张银行对账单并且在这里导入。支持的格式："

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence_id
msgid "Entry Sequence"
msgstr "分录序列"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_filename
msgid "Filename"
msgstr "文件名"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_data_file
msgid ""
"Get you bank statements in electronic format from your bank and select them "
"here."
msgstr "从银行拿到电子格式的银行对账单并且在这里选择他们。"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_group_invoice_lines
msgid "Group Invoice Lines"
msgstr "分组发票明细行"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_id
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_id
msgid "ID"
msgstr "ID"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_group_invoice_lines
msgid ""
"If this box is checked, the system will try to group the accounting lines "
"when generating them from invoices."
msgstr "如果已经勾选此框，系统将会尝试从发票生成的会计行并将之组合。"

#. module: account_bank_statement_import
#: model:ir.actions.act_window,name:account_bank_statement_import.action_account_bank_statement_import
msgid "Import"
msgstr "导入"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr "导入银行对账单"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_import_journal_creation
msgid "Import Bank Statement Journal Creation Wizard"
msgstr "导入银行对账日记账创建向导"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Import Bank Statements"
msgstr "导入银行对账单"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_line_unique_import_id
msgid "Import ID"
msgstr "导入ID"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.journal_dashboard_view_inherit
msgid "Import Statement"
msgstr "导入对账单"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_default_credit_account_id
msgid "It acts as a default account for credit amount"
msgstr "它充当贷方金额的默认科目"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_default_debit_account_id
msgid "It acts as a default account for debit amount"
msgstr "它将作为一个借记金额的默认账户"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_journal
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_journal_id
msgid "Journal"
msgstr "日记账"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:69
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
#, python-format
msgid "Journal Creation"
msgstr "日记账创建"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_name
msgid "Journal Name"
msgstr "日记账名称"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid ""
"Just click OK to create the account/journal and finish the import. If this "
"was a mistake, hit cancel to abort the import."
msgstr "只是点击 OK来创建科目／日记账并且完成导入。如果有错误，点击‘取消’放弃导入。"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_kanban_dashboard
msgid "Kanban Dashboard"
msgstr "看板仪表板"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "看板仪表板图表"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import___last_update
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation___last_update
msgid "Last Modified on"
msgstr "最后修改时间"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_write_uid
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_write_uid
msgid "Last Updated by"
msgstr "最后更新者"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_write_date
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_loss_account_id
msgid "Loss Account"
msgstr "损失科目"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_inbound_payment_method_ids
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo,you are suggested to reconcile the transaction with the batch deposit. Enable this option from the settings."
msgstr ""
"手动：通过现金、支票或除 Odoo 以外的任何其他方法获取报酬。\n"
"电子方式：在线购买或订阅时（支付令牌），通过请求客户保存的卡上的交易，通过付款受让人自动获得付款。\n"
"批量存款：通过生成批量存款立即兑现几个客户的支票，以便提交给银行。在 Odoo 中编码银行对账单时，建议您将交易与批量存款进行核对。从设置中启用此选项。 "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_outbound_payment_method_ids
msgid ""
"Manual:Pay bill by cash or any other method outside of Odoo.\n"
"Check:Pay bill by check and print it from Odoo.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. Enable this option from the settings."
msgstr ""
"手动：用现金支付账单或在Odoo之外的任何其他方法。\n"
"支票：用支票付账单，并从odoo把它打印出来。\n"
"SEPA信用转账：从你提交给银行的SEPA信用转账支付账单。从设置中启用此选项。"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence_number_next
msgid "Next Number"
msgstr "下一号码"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:138
#, python-format
msgid "No currency found matching '%s'."
msgstr "找不到与'%s'匹配的币种。"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid "OK"
msgstr "确认"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_outbound_payment_method_ids
msgid "Payment Methods"
msgstr "付款方式"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_profit_account_id
msgid "Profit Account"
msgstr "利润科目"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_type
msgid ""
"Select 'Sale' for customer invoices journals.\n"
"Select 'Purchase' for vendor bills journals.\n"
"Select 'Cash' or 'Bank' for journals that are used in customer or vendor payments.\n"
"Select 'General' for miscellaneous operations journals."
msgstr ""
"给客户发票日记账选择‘销售’。\n"
"给供应商发票选‘采购’。\n"
"在客户或者供应商付款的日记账中选择‘现金’或者‘银行’。\n"
"给其余操作的日记账选择‘通用’。"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Select a bank statement file to import"
msgstr "选择一个银行对账单文件来导入"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence
msgid "Sequence"
msgstr "序列"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_active
msgid "Set active to false to hide the Journal without removing it."
msgstr "设置活动为无效以隐藏日记帐，而不删除它。"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_code
msgid "Short Code"
msgstr "简码"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_show_on_dashboard
msgid "Show journal on dashboard"
msgstr "在仪表板显示日记账"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_account_setup_bank_data_done
msgid "Technical field used in the special view for the setup bar step."
msgstr "技术字段用于设置栏的特殊视图。"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid ""
"The account of the statement you are importing is not yet recorded in Odoo. "
"In order to proceed with the import, you need to create a bank journal for "
"this account."
msgstr "你要导入的报表中的账户还没有被记录在Odoo中。为了能够导入成功，你需要对这个账户创建一个银行日记账。"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:153
#, python-format
msgid ""
"The account of this statement (%s) is not the same as the journal (%s)."
msgstr "对账单中的科目(%s)和日记账(%s)中的不一样。"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:163
#, python-format
msgid ""
"The currency of the bank statement (%s) is not the same as the currency of "
"the journal (%s) !"
msgstr "银行对账单(%s)的币种和日记账(%s)的币种不一样！"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_currency_id
msgid "The currency used to enter statement"
msgstr "货币用来输入对账单"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_code
msgid "The journal entries of this journal will be named using this prefix."
msgstr "此日记账的日记账分录将以此前缀来命名"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence_number_next
msgid "The next sequence number will be used for the next credit note."
msgstr "下一个序列号将用于下一个信用备注。"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence_number_next
msgid "The next sequence number will be used for the next invoice."
msgstr "下一个序列号将用于下一个发票。"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence_id
msgid ""
"This field contains the information related to the numbering of the credit "
"note entries of this journal."
msgstr "该字段包含与此日记账的信用票据条目编号有关的信息。"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence_id
msgid ""
"This field contains the information related to the numbering of the journal "
"entries of this journal."
msgstr "这个字段包含和这个日记中日记账分录数量相关的信息。"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:113
#, python-format
msgid "This file doesn't contain any statement."
msgstr "这个文件不包含任何对账单。"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:121
#, python-format
msgid "This file doesn't contain any transaction."
msgstr "这个文件不包含任何交易。"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_type
msgid "Type"
msgstr "类型"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence
msgid "Used to order Journals in the dashboard view"
msgstr "用于仪表板视图中的日记账排序"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_loss_account_id
msgid ""
"Used to register a loss when the ending balance of a cash register differs "
"from what the system computes"
msgstr "现金登记的期末余额与系统计算的有差异时候用来登记损失"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_profit_account_id
msgid ""
"Used to register a profit when the ending balance of a cash register differs"
" from what the system computes"
msgstr "当现金出纳机的关账余额与系统计算的有差异时，用于登记利润"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_show_on_dashboard
msgid "Whether this journal should be displayed on the dashboard or not"
msgstr "不管这个日记账是否显示在仪表板"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:233
#, python-format
msgid "You have already imported that file."
msgstr "你已经导入此文件。"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:47
#, python-format
msgid ""
"You have to set a Default Debit Account and a Default Credit Account for the"
" journal: %s"
msgstr "您必须为日志设置默认借记帐户和默认信用帐户：%s"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "_Import"
msgstr "_Import"
