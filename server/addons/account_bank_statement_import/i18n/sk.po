# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_bank_statement_import
# 
# Translators:
# <AUTHOR> <EMAIL>, 2018
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <jaro.b<PERSON><PERSON>@ekoenergo.sk>, 2018
# <PERSON>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-30 13:11+0000\n"
"PO-Revision-Date: 2017-09-20 10:14+0000\n"
"Last-Translator: Jan Prokop, 2019\n"
"Language-Team: Slovak (https://www.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:241
#, python-format
msgid "%d transactions had already been imported and were ignored."
msgstr "%d transakcie už boli importovné a boli ignorovné."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:241
#, python-format
msgid "1 transaction had already been imported and was ignored."
msgstr "1 transakcia už bola importovaná a bola ignorovaná."

#. module: account_bank_statement_import
#: sql_constraint:account.bank.statement.line:0
msgid "A bank account transactions can be imported only once !"
msgstr "Transakcie bankového účtu môžu byť importované iba raz !"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_bank_acc_number
msgid "Account Number"
msgstr "Číslo Účtu"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_type_control_ids
msgid "Account Types Allowed"
msgstr "Povolené typy účtu"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_account_control_ids
msgid "Accounts Allowed"
msgstr "Povolené účty"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_active
msgid "Active"
msgstr "Aktívne"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_update_posted
msgid "Allow Cancelling Entries"
msgstr "Povoliť zrušenie položiek"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:243
#, python-format
msgid "Already imported items"
msgstr "Už importované položky"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_at_least_one_inbound
msgid "At Least One Inbound"
msgstr "Aspoň jeden prichádzajúci"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_at_least_one_outbound
msgid "At Least One Outbound"
msgstr "Aspoň jeden odchádzajúci"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:78
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_bank_id
#, python-format
msgid "Bank"
msgstr "Banka"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_bank_account_id
msgid "Bank Account"
msgstr "Bankový účet"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_bank_statements_source
msgid "Bank Feeds"
msgstr "Bankový prívod"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid "Bank Journal Name"
msgstr "Názov bankovej účtovnej knihy"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_data_file
msgid "Bank Statement File"
msgstr "Súbor bankového výpisu"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Riadok bankového výpisu"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_account_setup_bank_data_done
msgid "Bank setup marked as done"
msgstr ""

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_belongs_to_company
msgid "Belong to the user's current company"
msgstr "Priradené k aktuálnej spoločnosti užívateľa"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Cancel"
msgstr "Zrušiť"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:167
#, python-format
msgid ""
"Cannot find in which journal import this statement. Please manually select a"
" journal."
msgstr ""
"Nemožno nájsť v ktorej účtovnej knihe je import výpisu. Prosím zvoľte ho "
"manuálne."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" credit notes made from this journal"
msgstr ""
"Zaškrtnite toto políčko, ak nechcete zdieľať rovnakú sekvenciu pre faktúry a"
" dobropisy spravené z tejto účtovnej knihy."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_update_posted
msgid ""
"Check this box if you want to allow the cancellation the entries related to "
"this journal or of the invoice related to this journal"
msgstr ""
"Zaškrtnite toto políčko ak chcete povoliť zrušenie vstupov súvisiacich s "
"touo účtovnou knihou alebo faktúry súvisiacej s touo účtovnou knihou"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Choose a file to import..."
msgstr "Zvoľte súbor na importovanie..."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_color
msgid "Color Index"
msgstr "Farebný index"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_company_id
msgid "Company"
msgstr "Spoločnost"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_company_id
msgid "Company related to this journal"
msgstr "Spoločnosť vzťahujúca sa k tejto účtovnej knihe"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:108
#, python-format
msgid ""
"Could not make sense of the given file.\n"
"Did you install the module to support this type of file ?"
msgstr ""
"Daný súbor nedáva zmysel.\n"
"Nainštalovali ste modul pre podporu tohto typu súboru?"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_create_uid
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_create_date
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence_id
msgid "Credit Note Entry Sequence"
msgstr "Vstupná sekvencia dobropisu"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence_number_next
msgid "Credit Notes: Next Number"
msgstr "Dobropisy: nasledujúce číslo"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_currency_id
msgid "Currency"
msgstr "Mena"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_inbound_payment_method_ids
msgid "Debit Methods"
msgstr "Metódy debetu"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence
msgid "Dedicated Credit Note Sequence"
msgstr "Vyhradená sekvencia dobropisu"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_default_credit_account_id
msgid "Default Credit Account"
msgstr "Predvolený kreditný účet"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_default_debit_account_id
msgid "Default Debit Account"
msgstr "Predvolený debetný účet"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_display_name
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_display_name
msgid "Display Name"
msgstr "Zobraziť meno"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid ""
"Download a bank statement from your bank and import it here. Supported "
"formats:"
msgstr ""
"Stiahnite si výpis z účtu z vašej banky a importujte ho tu. Podporované "
"formáty:"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence_id
msgid "Entry Sequence"
msgstr "Sekvencia vstupu"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_filename
msgid "Filename"
msgstr "Názov súboru"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_data_file
msgid ""
"Get you bank statements in electronic format from your bank and select them "
"here."
msgstr ""
"Získajte svoje bankové výpisy v elektronickej podobe z vašej banky a vyberte"
" ich tu."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_group_invoice_lines
msgid "Group Invoice Lines"
msgstr "Zoskupiť riadky faktúry"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_id
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_id
msgid "ID"
msgstr "ID"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_group_invoice_lines
msgid ""
"If this box is checked, the system will try to group the accounting lines "
"when generating them from invoices."
msgstr ""
"Ak je toto políčko zaškrtnuté, systém sa pokúsi zhromaždiť účtovné riadky "
"pri ich generovaní do faktúr."

#. module: account_bank_statement_import
#: model:ir.actions.act_window,name:account_bank_statement_import.action_account_bank_statement_import
msgid "Import"
msgstr "Import"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr "Import Bank Statement"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_import_journal_creation
msgid "Import Bank Statement Journal Creation Wizard"
msgstr "Sprievodca vytvorením účtovných kníh importom bankových výpisov"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Import Bank Statements"
msgstr "Importovať bankové výpisy"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_line_unique_import_id
msgid "Import ID"
msgstr "Import ID"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.journal_dashboard_view_inherit
msgid "Import Statement"
msgstr "Importovať výpis"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_default_credit_account_id
msgid "It acts as a default account for credit amount"
msgstr "Pôsobí ako predvolený účet pre výšku kreditu"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_default_debit_account_id
msgid "It acts as a default account for debit amount"
msgstr "Pôsobí ako predvolený účet pre výšku debetu"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_journal
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_journal_id
msgid "Journal"
msgstr "Účtovná kniha"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:69
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
#, python-format
msgid "Journal Creation"
msgstr "Vytvorenie účtovnej knihy"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_name
msgid "Journal Name"
msgstr "Názov účtovnej knihy"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid ""
"Just click OK to create the account/journal and finish the import. If this "
"was a mistake, hit cancel to abort the import."
msgstr ""
"Stačí kliknúť na tlačidlo OK pre vytvorenie účtu/účtovnej knihy a dokončenie"
" importu. Ak to bola chyba, stlačte zrušiť pre prerušenie importu."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_kanban_dashboard
msgid "Kanban Dashboard"
msgstr "Kanban nástenka"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "Kanban graf nástenky"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import___last_update
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation___last_update
msgid "Last Modified on"
msgstr "Posledná modifikácia"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_write_uid
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_write_date
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_loss_account_id
msgid "Loss Account"
msgstr "Účet strát"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_inbound_payment_method_ids
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo,you are suggested to reconcile the transaction with the batch deposit. Enable this option from the settings."
msgstr ""
"Ručne: platba v hotovosti, šekom alebo inou metódou mimo Odoo.\n"
"Elektronicky: Platba automaticky prostredníctvom príjemcu platby vyžiadaním transakcie na karte predloženej zákazníkom pri nákupe alebo  online (platobný token).\n"
"Dávkové vklady: Vykryte niekoľko zákazníckych šekov naraz tým, že vytvoríte dávku na vklad, ktorú chcete predložiť banke Pri kódovaní bankového výpisu v Odoo sa odporúča zosúladiť transakciu s dávkovým vkladom. Povoľte túto možnosť v nastavení."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_outbound_payment_method_ids
msgid ""
"Manual:Pay bill by cash or any other method outside of Odoo.\n"
"Check:Pay bill by check and print it from Odoo.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. Enable this option from the settings."
msgstr ""
"Ručne: platba v hotovosti, šekom alebo inou metódou mimo Odoo..\n"
"Šek: platba šekom na základe tlače z Odoo.\n"
"SEPA bankový prevod: platba prevodným príkazom SEPA doručeným banke. Povoľte túto možnosť v nastavení."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence_number_next
msgid "Next Number"
msgstr "Nasledujúce číslo"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:138
#, python-format
msgid "No currency found matching '%s'."
msgstr "Žiadna menanenájdena zhodujúca sa s '%s'."

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid "OK"
msgstr "OK"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_outbound_payment_method_ids
msgid "Payment Methods"
msgstr "Platobné metódy"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_profit_account_id
msgid "Profit Account"
msgstr "Účet zisku"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_type
msgid ""
"Select 'Sale' for customer invoices journals.\n"
"Select 'Purchase' for vendor bills journals.\n"
"Select 'Cash' or 'Bank' for journals that are used in customer or vendor payments.\n"
"Select 'General' for miscellaneous operations journals."
msgstr ""
"Vyberte 'Predaj' pre zákaznícke faktúry v účtovných knihách.\n"
"Vyberte 'Nákup' pre účtenky dodávateľov v účtovných knihách.\n"
"Vyberte 'Hotovosť'alebo'Banka'pre účtovné knihy, ktoré sa používajú u zákazníckých alebo dodávateľských platieb.\n"
"Vyberte 'Všeobecné', pre rôzne operácie účtovných kníh."

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Select a bank statement file to import"
msgstr "Zvoľte súbor bankového výpisu n importovanie"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence
msgid "Sequence"
msgstr "Postupnosť"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_active
msgid "Set active to false to hide the Journal without removing it."
msgstr ""
"Nastav aktívne na \"false\" pre skrytie účtovnej knihy bez jej odstránenia"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_code
msgid "Short Code"
msgstr "Krátky kód"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_show_on_dashboard
msgid "Show journal on dashboard"
msgstr "Zobraziť účtovnú knihu na nástenke"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_account_setup_bank_data_done
msgid "Technical field used in the special view for the setup bar step."
msgstr ""

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid ""
"The account of the statement you are importing is not yet recorded in Odoo. "
"In order to proceed with the import, you need to create a bank journal for "
"this account."
msgstr ""
"Účet výpisu ktorý importujete ešte nie je nahratý v Odoo. Aby mohol import "
"pokračovať, potrebujete vytvoriť bankovú účtovnú knihu pre tento účet."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:153
#, python-format
msgid ""
"The account of this statement (%s) is not the same as the journal (%s)."
msgstr "Účet tohoto výpisu (%s) nie je rovnaký ako účtovná kniha (%s)."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:163
#, python-format
msgid ""
"The currency of the bank statement (%s) is not the same as the currency of "
"the journal (%s) !"
msgstr ""
"Mena bankového výpisu (%s) nie je rovnaká ako mena účtovnej knihy (%s) !"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_currency_id
msgid "The currency used to enter statement"
msgstr "Mena ktorá slúži pre zadanie výpisu"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_code
msgid "The journal entries of this journal will be named using this prefix."
msgstr "Položky tejto účtovnej knihy budú pomenované pomocou tejto predpony."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence_number_next
msgid "The next sequence number will be used for the next credit note."
msgstr "Ďalšie poradové číslo bude použité pre nasledujúci dobropis."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence_number_next
msgid "The next sequence number will be used for the next invoice."
msgstr "Ďalšie poradové číslo bude použité pre nasledujúcu faktúru."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence_id
msgid ""
"This field contains the information related to the numbering of the credit "
"note entries of this journal."
msgstr ""
"Toto pole obsahuje informácie súvisiace s číslovaním dobropisových vstupov "
"tejto účtovnej knihy."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence_id
msgid ""
"This field contains the information related to the numbering of the journal "
"entries of this journal."
msgstr ""
"Toto pole obsahuje informácie súvisiace s číslovaním vstupov tejto účtovnej "
"knihy."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:113
#, python-format
msgid "This file doesn't contain any statement."
msgstr "Tento súbor neobsahuje žiadny výpis."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:121
#, python-format
msgid "This file doesn't contain any transaction."
msgstr "Tento súbor neobsahuje žiadnu transakciu."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_type
msgid "Type"
msgstr "Typ"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence
msgid "Used to order Journals in the dashboard view"
msgstr "Používa sa na poradie účtovných kníh v zobrazení nástenky"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_loss_account_id
msgid ""
"Used to register a loss when the ending balance of a cash register differs "
"from what the system computes"
msgstr ""
"Používa sa na registráciu straty, keď sa konečný zostatok pokladne líši od "
"toho čo vypočíta systém"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_profit_account_id
msgid ""
"Used to register a profit when the ending balance of a cash register differs"
" from what the system computes"
msgstr ""
"Používa sa na registráciu zisku, keď sa konečný zostatok pokladne líši od "
"toho čo vypočíta systém"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_show_on_dashboard
msgid "Whether this journal should be displayed on the dashboard or not"
msgstr "Či má byť táto účtovná kniha zobrazená na nástenke alebo nie"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:233
#, python-format
msgid "You have already imported that file."
msgstr "Ten súbor už bol importovaný. "

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:47
#, python-format
msgid ""
"You have to set a Default Debit Account and a Default Credit Account for the"
" journal: %s"
msgstr ""
"Nastavili ste predvolený debetný účet a predvolený kreditný účet v účtovnej "
"knihe: %s"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "_Import"
msgstr "_Import"
