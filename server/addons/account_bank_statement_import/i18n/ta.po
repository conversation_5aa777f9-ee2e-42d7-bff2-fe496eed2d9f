# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_bank_statement_import
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON><PERSON><PERSON> <bag<PERSON><PERSON><PERSON>@gmail.com>, 2019
# <PERSON><PERSON> <PERSON> <mi<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-30 13:11+0000\n"
"PO-Revision-Date: 2017-09-20 10:14+0000\n"
"Last-Translator: <PERSON><PERSON> M <PERSON> <miab<PERSON><PERSON><PERSON>@gmail.com>, 2019\n"
"Language-Team: Tamil (https://www.transifex.com/odoo/teams/41243/ta/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ta\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:241
#, python-format
msgid "%d transactions had already been imported and were ignored."
msgstr "வங்கி அறிக்கைகள்"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:241
#, python-format
msgid "1 transaction had already been imported and was ignored."
msgstr "1 பரிவர்த்தனை ஏற்கனவே இறக்குமதி செய்யப்பட்டு புறக்கணிக்கப்பட்டது."

#. module: account_bank_statement_import
#: sql_constraint:account.bank.statement.line:0
msgid "A bank account transactions can be imported only once !"
msgstr ""
"ஒரு வங்கி கணக்கு பரிவர்த்தனைகளை ஒரு முறை மட்டுமே இறக்குமதி செய்ய முடியும்!"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_bank_acc_number
msgid "Account Number"
msgstr "கணக்கு எண்"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_type_control_ids
msgid "Account Types Allowed"
msgstr "கணக்கு வகைகள் அனுமதிக்கப்பட்டன"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_account_control_ids
msgid "Accounts Allowed"
msgstr "கணக்குகள் அனுமதிக்கப்பட்டன"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_active
msgid "Active"
msgstr "செயலில்"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_update_posted
msgid "Allow Cancelling Entries"
msgstr "Allow Cancelling Entries"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:243
#, python-format
msgid "Already imported items"
msgstr "ஏற்கனவே இறக்குமதி செய்யப்பட்ட பொருட்கள்"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_at_least_one_inbound
msgid "At Least One Inbound"
msgstr "குறைந்த ஒரு உள்வட்டத்தில்"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_at_least_one_outbound
msgid "At Least One Outbound"
msgstr "குறைந்த ஒரு வெளிச்சத்தில்"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:78
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_bank_id
#, python-format
msgid "Bank"
msgstr "வங்கி"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_bank_account_id
msgid "Bank Account"
msgstr "வங்கி கணக்கு"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_bank_statements_source
msgid "Bank Feeds"
msgstr "வங்கி ஊட்டங்கள்"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid "Bank Journal Name"
msgstr "வங்கி ஜர்னல் பெயர்"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_data_file
msgid "Bank Statement File"
msgstr "வங்கி அறிக்கை கோப்பு"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "வங்கி அறிக்கை வரி"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_account_setup_bank_data_done
msgid "Bank setup marked as done"
msgstr ""

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_belongs_to_company
msgid "Belong to the user's current company"
msgstr "பயனரின் தற்போதைய நிறுவனத்திற்கு சொந்தமானது"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Cancel"
msgstr "ரத்து"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:167
#, python-format
msgid ""
"Cannot find in which journal import this statement. Please manually select a"
" journal."
msgstr ""
"இந்த அறிக்கையை பத்திரிகை இறக்குமதி செய்ய முடியாது. ஒரு பத்திரிகை கைமுறையாக "
"தேர்ந்தெடுக்கவும்."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" credit notes made from this journal"
msgstr ""
"இந்த பத்திரிகையிலிருந்து உருவாக்கப்பட்ட பொருள் மற்றும் கடன் குறிப்புகளுக்கான"
" அதே காட்சியை நீங்கள் பகிர்ந்து கொள்ள விரும்பவில்லை என்றால் இந்த பெட்டியை "
"சரிபார்க்கவும்"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_update_posted
msgid ""
"Check this box if you want to allow the cancellation the entries related to "
"this journal or of the invoice related to this journal"
msgstr ""
"இந்த பத்திரிகை தொடர்பான இந்த உள்ளீடுகளை அல்லது இந்த பத்திரிகை தொடர்பான "
"விவரப்பட்டியல் ரத்துசெய்ய அனுமதிக்க விரும்பினால் இந்த பெட்டியை "
"சரிபார்க்கவும்"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Choose a file to import..."
msgstr "இறக்குவதற்கு ஒரு கோப்பைத் தேர்வு செய்க ..."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_color
msgid "Color Index"
msgstr "வண்ண குறியீட்டு"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_company_id
msgid "Company"
msgstr "நிறுவனம்"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_company_id
msgid "Company related to this journal"
msgstr "இந்த பத்திரிகை தொடர்பான நிறுவனம்"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:108
#, python-format
msgid ""
"Could not make sense of the given file.\n"
"Did you install the module to support this type of file ?"
msgstr ""
"கொடுக்கப்பட்ட கோப்பை உணர முடியவில்லை.\n"
"இந்த வகை கோப்பினை ஆதரிக்க தொகுதி நிறுவியிருக்கிறீர்களா?"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_create_uid
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_create_uid
msgid "Created by"
msgstr "உருவாக்கியவர்"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_create_date
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_create_date
msgid "Created on"
msgstr "உருவாக்கப்பட்ட தேதி"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence_id
msgid "Credit Note Entry Sequence"
msgstr "கடன் குறிப்பு நுழைவு வரிசை"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence_number_next
msgid "Credit Notes: Next Number"
msgstr "கடன் குறிப்புகள்: அடுத்த எண்"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_currency_id
msgid "Currency"
msgstr "நாணயம்"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_inbound_payment_method_ids
msgid "Debit Methods"
msgstr ""

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence
msgid "Dedicated Credit Note Sequence"
msgstr "அர்ப்பணித்து கடன் குறிப்பு வரிசை"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_default_credit_account_id
msgid "Default Credit Account"
msgstr "இயல்புநிலை கடன் கணக்கு"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_default_debit_account_id
msgid "Default Debit Account"
msgstr "இயல்புநிலை பற்று கணக்கு"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_display_name
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_display_name
msgid "Display Name"
msgstr "காட்சி பெயர்"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid ""
"Download a bank statement from your bank and import it here. Supported "
"formats:"
msgstr ""
"உங்கள் வங்கியிலிருந்து ஒரு வங்கி அறிக்கையை பதிவிறக்கம் செய்து, இங்கே "
"இறக்குமதி செய்யவும். ஆதரவு வடிவங்கள்:"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence_id
msgid "Entry Sequence"
msgstr "நுழைவு வரிசை"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_filename
msgid "Filename"
msgstr "கோப்புபெயர்"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_data_file
msgid ""
"Get you bank statements in electronic format from your bank and select them "
"here."
msgstr ""
"உங்கள் வங்கியிலிருந்து மின்னணு வடிவத்தில் வங்கிக் கூற்றுக்களைப் பெற்று "
"அவற்றை இங்கே தேர்ந்தெடுக்கவும்."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_group_invoice_lines
msgid "Group Invoice Lines"
msgstr "குழு விலைப்பட்டியல் கோடுகள்"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_id
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_id
msgid "ID"
msgstr "ID"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_group_invoice_lines
msgid ""
"If this box is checked, the system will try to group the accounting lines "
"when generating them from invoices."
msgstr ""
"இந்த பெட்டி சோதிக்கப்பட்டிருந்தால், அவை பொருள்வழங்களின்போது உருவாக்கும்போது "
"கணக்கியல் வரிகளை குழுப்படுத்த முயற்சிக்கும்."

#. module: account_bank_statement_import
#: model:ir.actions.act_window,name:account_bank_statement_import.action_account_bank_statement_import
msgid "Import"
msgstr "இறக்குமதி"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr "இறக்குமதி வங்கி அறிக்கை"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_import_journal_creation
msgid "Import Bank Statement Journal Creation Wizard"
msgstr ""

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Import Bank Statements"
msgstr "இறக்குமதி வங்கி அறிக்கைகள்"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_line_unique_import_id
msgid "Import ID"
msgstr "இறக்குமதி ஐடி"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.journal_dashboard_view_inherit
msgid "Import Statement"
msgstr "இறக்குமதி அறிக்கை"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_default_credit_account_id
msgid "It acts as a default account for credit amount"
msgstr "இது கடன் தொகைக்கான முன்னிருப்பு கணக்காக செயல்படுகிறது"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_default_debit_account_id
msgid "It acts as a default account for debit amount"
msgstr "இது டெபிட் தொகைக்கான இயல்புநிலை கணக்காக செயல்படுகிறது"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_journal
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_journal_id
msgid "Journal"
msgstr "ஜர்னல்"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:69
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
#, python-format
msgid "Journal Creation"
msgstr "ஜர்னல் உருவாக்கம்"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_name
msgid "Journal Name"
msgstr "பத்திரிகை பெயர்"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid ""
"Just click OK to create the account/journal and finish the import. If this "
"was a mistake, hit cancel to abort the import."
msgstr ""
"கணக்கு / பத்திரிகை உருவாக்க, இறக்குமதி செய்து முடிக்க சரி என்பதை கிளிக் "
"செய்யவும். இது தவறு என்றால், இறக்குமதியை நிறுத்துவதற்கு ரத்து செய்யவும்."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_kanban_dashboard
msgid "Kanban Dashboard"
msgstr "கான்பன் டாஷ்போர்டு"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "கான்பன் டாஷ்போர்டு வரைபடம்"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import___last_update
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation___last_update
msgid "Last Modified on"
msgstr "கடைசியாக திருத்திய"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_write_uid
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_write_uid
msgid "Last Updated by"
msgstr "கடைசியாக புதுப்பிக்கப்பட்டவர்"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_write_date
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_write_date
msgid "Last Updated on"
msgstr "கடைசியாக புதுப்பிக்கப்பட்டது"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_loss_account_id
msgid "Loss Account"
msgstr "இழப்பு கணக்கு"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_inbound_payment_method_ids
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo,you are suggested to reconcile the transaction with the batch deposit. Enable this option from the settings."
msgstr ""
"கையேடு: ரொக்கம், காசோலை அல்லது ஒடூக்கு வெளியே வேறு எந்த முறையிலும் பணம் செலுத்துங்கள்.\n"
"மின்னணு: ஆன்லைனில் வாங்குதல் அல்லது சந்தா செலுத்தும் போது வாடிக்கையாளர் சேமித்த ஒரு அட்டையில் ஒரு பரிவர்த்தனை கோரியதன் மூலம் பணம் செலுத்துபவர் மூலம் தானாக பணம் சம்பாதிக்கலாம்.\n"
"பேட்ச் டெபாசிட்: உங்கள் வங்கிக்கு சமர்ப்பிக்க ஒரு தொகுப்பு வைப்பு உருவாக்குவதன் மூலம் ஒரே நேரத்தில் பல வாடிக்கையாளர் காசோலைகளை வெளிப்படுத்தவும். Odoo இல் வங்கி அறிக்கையை குறியாக்கும் போது, நீங்கள் பங்கு வைப்புடன் பரிமாற்றத்தை சரிசெய்ய பரிந்துரைக்கப்படுவீர்கள். அமைப்புகளில் இருந்து இந்த விருப்பத்தை இயக்கவும்."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_outbound_payment_method_ids
msgid ""
"Manual:Pay bill by cash or any other method outside of Odoo.\n"
"Check:Pay bill by check and print it from Odoo.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. Enable this option from the settings."
msgstr ""
"கையேடு: பணம் செலுத்துதல் அல்லது ஒடுவின் வெளியே வேறு எந்த முறையிலும் பணம் செலுத்துங்கள்.\n"
"சரிபாருங்கள்: காசோலையைச் சரிபார்த்து அதை ஓடுவிலிருந்து அச்சிடவும்.\n"
"SEPA கிரெடிட் டிரான்ஸ்ஃபர்: உங்கள் வங்கியில் சமர்ப்பிக்கும் SEPA கிரெடிட் டிரான்ஸ்ஃபர் கோப்புகளிலிருந்து பணம் செலுத்துங்கள். அமைப்புகளில் இருந்து இந்த விருப்பத்தை இயக்கவும்."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence_number_next
msgid "Next Number"
msgstr "அடுத்த எண்"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:138
#, python-format
msgid "No currency found matching '%s'."
msgstr "எந்த நாணயமும் பொருந்தவில்லை"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid "OK"
msgstr "சரி"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_outbound_payment_method_ids
msgid "Payment Methods"
msgstr ""

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_profit_account_id
msgid "Profit Account"
msgstr "இலாப கணக்கு"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_type
msgid ""
"Select 'Sale' for customer invoices journals.\n"
"Select 'Purchase' for vendor bills journals.\n"
"Select 'Cash' or 'Bank' for journals that are used in customer or vendor payments.\n"
"Select 'General' for miscellaneous operations journals."
msgstr ""
"வாடிக்கையாளர் பொருள் பத்திரிகைகளுக்கான 'விற்பனை' என்பதைத் தேர்ந்தெடுக்கவும்.\n"
"விற்பனையாளர் கட்டணம் பத்திரிகைகளுக்கான 'கொள்முதல்' என்பதைத் தேர்ந்தெடுக்கவும்.\n"
"வாடிக்கையாளர் அல்லது விற்பனையாளர் கொடுப்பனவுகளில் பயன்படுத்தப்படும் பத்திரிகைகள் 'பண' அல்லது 'வங்கி' என்பதைத் தேர்ந்தெடுக்கவும்.\n"
"பல்வேறு செயல்பாட்டு பத்திரிகைகள் 'பொது' என்பதைத் தேர்ந்தெடுக்கவும்."

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Select a bank statement file to import"
msgstr "இறக்குமதி செய்ய வங்கி அறிக்கை கோப்பைத் தேர்ந்தெடுக்கவும்"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence
msgid "Sequence"
msgstr "வரிசை"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_active
msgid "Set active to false to hide the Journal without removing it."
msgstr "இதனை அகற்றாமல் பத்திரிகை மறைக்க பொய்யாக செயலில் வைக்கவும்."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_code
msgid "Short Code"
msgstr "குறுகிய குறியீடு"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_show_on_dashboard
msgid "Show journal on dashboard"
msgstr "டாஷ்போர்டில் இதழ் காட்டு"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_account_setup_bank_data_done
msgid "Technical field used in the special view for the setup bar step."
msgstr ""

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid ""
"The account of the statement you are importing is not yet recorded in Odoo. "
"In order to proceed with the import, you need to create a bank journal for "
"this account."
msgstr ""
"நீங்கள் இறக்குமதி செய்யும் அறிக்கையின் கணக்கு இதுவரை ஒடோவில் பதிவு "
"செய்யப்படவில்லை. இறக்குமதியை தொடர, இந்த கணக்கிற்கான வங்கி பத்திரிகை ஒன்றை "
"நீங்கள் உருவாக்க வேண்டும்."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:153
#, python-format
msgid ""
"The account of this statement (%s) is not the same as the journal (%s)."
msgstr "இந்த அறிக்கையின் கணக்கு () பத்திரிகையின் அதே அல்ல"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:163
#, python-format
msgid ""
"The currency of the bank statement (%s) is not the same as the currency of "
"the journal (%s) !"
msgstr ""

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_currency_id
msgid "The currency used to enter statement"
msgstr "அறிக்கை உள்ளிட பயன்படுத்தப்படும் நாணயம்"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_code
msgid "The journal entries of this journal will be named using this prefix."
msgstr ""
"இந்த பத்திரிகையின் ஜர்னல் பதிவுகள் இந்த முன்னுரிமையைப் பயன்படுத்தி "
"பெயரிடப்படும்."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence_number_next
msgid "The next sequence number will be used for the next credit note."
msgstr "அடுத்த கடன் குறிப்பு அடுத்த கடன் குறிப்புக்கு பயன்படுத்தப்படும்."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence_number_next
msgid "The next sequence number will be used for the next invoice."
msgstr "அடுத்த விலைப்பட்டியல் எண் அடுத்த வரிசை எண்."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_refund_sequence_id
msgid ""
"This field contains the information related to the numbering of the credit "
"note entries of this journal."
msgstr ""
"இந்த பத்திரிகையின் கடன் குறிப்பு உள்ளீடுகளின் எண்ணிக்கை தொடர்பான தகவலை "
"இந்தத் துறையில் கொண்டுள்ளது."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence_id
msgid ""
"This field contains the information related to the numbering of the journal "
"entries of this journal."
msgstr ""
"இந்த பத்திரிகையின் ஜர்னல் உள்ளீடுகளின் எண்ணிடத்துடன் தொடர்புடைய தகவல் இந்த "
"துறையில் உள்ளது."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:113
#, python-format
msgid "This file doesn't contain any statement."
msgstr "இந்த கோப்பில் எந்த அறிக்கையும் இல்லை."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:121
#, python-format
msgid "This file doesn't contain any transaction."
msgstr "இந்த கோப்பில் ஏதேனும் பரிவர்த்தனை இல்லை."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation_type
msgid "Type"
msgstr "வகை"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_sequence
msgid "Used to order Journals in the dashboard view"
msgstr "டாஷ்போர்டு பார்வையில் ஜர்னல்களை ஆர்டர் செய்ய பயன்படுத்தப்பட்டது"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_loss_account_id
msgid ""
"Used to register a loss when the ending balance of a cash register differs "
"from what the system computes"
msgstr ""
"ஒரு பணப்பதிவு முடிவடையும் சமநிலை கணினியை கணக்கிடுவதால் வேறுபடும் போது இழப்பை"
" பதிவு செய்ய பயன்படுத்தப்படுகிறது"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_profit_account_id
msgid ""
"Used to register a profit when the ending balance of a cash register differs"
" from what the system computes"
msgstr ""
"பணப்பதிவு முடிவடையும் சமநிலை கணினியால் கணக்கிடப்படுவதிலிருந்து மாறுபடும் "
"போது இலாபத்தை பதிவு செய்ய பயன்படுகிறது"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation_show_on_dashboard
msgid "Whether this journal should be displayed on the dashboard or not"
msgstr "இந்த பத்திரிகை டாஷ்போர்டில் காட்டப்பட வேண்டுமா அல்லது இல்லையா"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:233
#, python-format
msgid "You have already imported that file."
msgstr ""

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:47
#, python-format
msgid ""
"You have to set a Default Debit Account and a Default Credit Account for the"
" journal: %s"
msgstr ""
"நீங்கள் ஒரு Default Debit கணக்கு மற்றும் இதற்கான ஒரு Default Credit Account "
"ஐ அமைக்க வேண்டும்:"

#. module: account_bank_statement_import
#: model:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "_Import"
msgstr "இறக்குமதி"
