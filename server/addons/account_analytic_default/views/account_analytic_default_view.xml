<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="view_account_analytic_default_tree" model="ir.ui.view">
            <field name="name">account.analytic.default.tree</field>
            <field name="model">account.analytic.default</field>
            <field name="arch" type="xml">
                <tree string="Analytic Defaults">
                    <field name="sequence"/>
                    <field name="analytic_id" required="0" groups="analytic.group_analytic_accounting"/>
                    <field name="product_id"/>
                    <field name="partner_id"/>
                    <field name="user_id"/>
                    <field name="date_start"/>
                    <field name="date_stop"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <record id="view_account_analytic_default_form" model="ir.ui.view">
            <field name="name">account.analytic.default.form</field>
            <field name="model">account.analytic.default</field>
            <field name="arch" type="xml">
                <form string="Analytic Defaults">
                    <group col="4">
                        <field name="analytic_id" required="1" groups="analytic.group_analytic_accounting"/>
                        <field name="sequence"/>
                        <separator string="Conditions" colspan="4"/>
                        <field name="product_id"/>
                        <field name="partner_id"/>
                        <field name="user_id"/>
                        <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                        <field name="date_start"/>
                        <field name="date_stop"/>
                    </group>
                </form>
            </field>
        </record>

        <record id="view_account_analytic_default_kanban" model="ir.ui.view">
            <field name="name">account.analytic.default.kanban</field>
            <field name="model">account.analytic.default</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="analytic_id"/>
                    <field name="date_start"/>
                    <field name="date_stop"/>
                    <field name="product_id"/>
                    <field name="partner_id"/>
                    <field name="user_id"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                                <div>
                                    <strong><span><field name="analytic_id"/></span></strong>
                                    <img t-att-src="kanban_image('res.users', 'image_small', record.user_id.raw_value)" t-att-title="record.user_id.value" width="24" height="24" class="oe_kanban_avatar pull-right"/>
                                </div>
                                <div t-if="record.date_start.value"><i class="fa fa-calendar"></i> From <field name="date_start"/> <t t-if="record.date_stop.value">to <field name="date_stop"/></t></div>
                                <div t-if="record.product_id.value"><strong>Product</strong> <field name="product_id"/> </div>
                                <div t-if="record.partner_id.value"><strong>Customer</strong> <field name="partner_id"/> </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="view_account_analytic_default_form_search" model="ir.ui.view">
            <field name="name">account.analytic.default.search</field>
            <field name="model">account.analytic.default</field>
            <field name="arch" type="xml">
                <search string="Accounts">
                    <field name="date_stop"/>
                    <field name="analytic_id" groups="analytic.group_analytic_accounting"/>
                    <field name="product_id"/>
                    <field name="partner_id"/>
                    <field name="user_id"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <group expand="0" string="Group By">
                        <filter string="User" context="{'group_by':'user_id'}" help="User"/>
                        <filter string="Partner" context="{'group_by':'partner_id'}" help="Partner"/>
                        <filter string="Product" context="{'group_by':'product_id'}" help="Product" />
                        <filter string="Analytic Account" context="{'group_by':'analytic_id'}" help="Analytic Account" groups="analytic.group_analytic_accounting"/>
                        <filter string="Company" context="{'group_by':'company_id'}" groups="base.group_multi_company" />
                    </group>
                </search>
            </field>
        </record>

        <record id="action_analytic_default_list" model="ir.actions.act_window">
            <field name="name">Analytic Defaults</field>
            <field name="res_model">account.analytic.default</field>
            <field name="view_type">form</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_account_analytic_default_form_search"/>
            <field name="context">{"search_default_current":1}</field>
        </record>
        
        <record id="action_product_default_list" model="ir.actions.act_window">
            <field name="name">Analytic Rules</field>
            <field name="res_model">account.analytic.default</field>
            <field name="context">{'search_default_product_id': [active_id], 'default_product_id': active_id}</field>
        </record>

        <menuitem id="menu_analytic_default_list"
            action="action_analytic_default_list"
            parent="account.menu_analytic_accounting"/>


        <act_window
            name="Analytic Rules"
            id="analytic_rule_action_partner"
            res_model="account.analytic.default"
            src_model="res.partner"
            context="{'search_default_partner_id': [active_id], 'default_partner_id': active_id}"
            groups="analytic.group_analytic_accounting"/>

        <act_window
            name="Analytic Rules"
            id="analytic_rule_action_user"
            res_model="account.analytic.default"
            src_model="res.users"
            context="{'search_default_user_id': [active_id], 'default_user_id': active_id}"
            groups="analytic.group_analytic_accounting"/>
</odoo>
