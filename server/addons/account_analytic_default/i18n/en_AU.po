# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_analytic_default
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-09-07 15:43+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: English (Australia) (http://www.transifex.com/odoo/odoo-9/"
"language/en_AU/)\n"
"Language: en_AU\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_product_product_rules_count
#: model:ir.model.fields,field_description:account_analytic_default.field_product_template_rules_count
msgid "# Analytic Rules"
msgstr ""

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Accounts"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_analytic_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Analytic Account"
msgstr ""

#. module: account_analytic_default
#: model:ir.actions.act_window,name:account_analytic_default.action_analytic_default_list
#: model:ir.actions.act_window,name:account_analytic_default.action_product_default_list
#: model:ir.ui.menu,name:account_analytic_default.menu_analytic_default_list
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_tree
msgid "Analytic Defaults"
msgstr ""

#. module: account_analytic_default
#: model:ir.model,name:account_analytic_default.model_account_analytic_default
msgid "Analytic Distribution"
msgstr ""

#. module: account_analytic_default
#: model:ir.actions.act_window,name:account_analytic_default.analytic_rule_action_partner
#: model:ir.actions.act_window,name:account_analytic_default.analytic_rule_action_user
#: model:ir.ui.view,arch_db:account_analytic_default.product_form_view_default_analytic_button
#: model:ir.ui.view,arch_db:account_analytic_default.product_template_view_default_analytic_button
msgid "Analytic Rules"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_company_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Company"
msgstr ""

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form
msgid "Conditions"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_create_uid
msgid "Created by"
msgstr "Created by"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_create_date
msgid "Created on"
msgstr "Created on"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_date_stop
msgid "Default end date for this Analytic Account."
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_date_start
msgid "Default start date for this Analytic Account."
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_display_name
msgid "Display Name"
msgstr "Display Name"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_date_stop
msgid "End Date"
msgstr ""

#. module: account_analytic_default
#: model:ir.actions.act_window,name:account_analytic_default.act_account_acount_move_line_open
msgid "Entries"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_sequence
msgid ""
"Gives the sequence order when displaying a list of analytic distribution"
msgstr ""

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Group By"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_id
msgid "ID"
msgstr "ID"

#. module: account_analytic_default
#: model:ir.model,name:account_analytic_default.model_account_invoice_line
msgid "Invoice Line"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default___last_update
msgid "Last Modified on"
msgstr "Last Modified on"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_write_uid
msgid "Last Updated by"
msgstr "Last Updated by"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_write_date
msgid "Last Updated on"
msgstr "Last Updated on"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_partner_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Partner"
msgstr ""

#. module: account_analytic_default
#: model:ir.model,name:account_analytic_default.model_product_product
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_product_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Product"
msgstr ""

#. module: account_analytic_default
#: model:ir.model,name:account_analytic_default.model_product_template
msgid "Product Template"
msgstr ""

#. module: account_analytic_default
#: model:ir.model,name:account_analytic_default.model_sale_order_line
msgid "Sales Order Line"
msgstr "Sales Order Line"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_company_id
msgid ""
"Select a company which will use analytic account specified in analytic "
"default (e.g. create new customer invoice or Sales order if we select this "
"company, it will automatically take this as an analytic account)"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_partner_id
msgid ""
"Select a partner which will use analytic account specified in analytic "
"default (e.g. create new customer invoice or Sales order if we select this "
"partner, it will automatically take this as an analytic account)"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_product_id
msgid ""
"Select a product which will use analytic account specified in analytic "
"default (e.g. create new customer invoice or Sales order if we select this "
"product, it will automatically take this as an analytic account)"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_user_id
msgid ""
"Select a user which will use analytic account specified in analytic default."
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_sequence
msgid "Sequence"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_date_start
msgid "Start Date"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_user_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "User"
msgstr "User"
