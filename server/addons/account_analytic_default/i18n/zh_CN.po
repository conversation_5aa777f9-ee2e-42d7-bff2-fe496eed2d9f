# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_analytic_default
# 
# Translators:
# <PERSON>ery CHEN Fan <<EMAIL>>, 2017
# <PERSON>, 2017
# <PERSON> <PERSON> <<EMAIL>>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: waveyeung <<EMAIL>>, 2017\n"
"Language-Team: Chinese (China) (https://www.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "<i class=\"fa fa-calendar\"/> From"
msgstr "<i class=\"fa fa-calendar\"/> 从"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "<strong>Customer</strong>"
msgstr "<strong>客户</strong>"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "<strong>Product</strong>"
msgstr "<strong>产品</strong>"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Accounts"
msgstr "科目"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_analytic_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Analytic Account"
msgstr "分析账户"

#. module: account_analytic_default
#: model:ir.actions.act_window,name:account_analytic_default.action_analytic_default_list
#: model:ir.ui.menu,name:account_analytic_default.menu_analytic_default_list
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_tree
msgid "Analytic Defaults"
msgstr "分析默认"

#. module: account_analytic_default
#: model:ir.model,name:account_analytic_default.model_account_analytic_default
msgid "Analytic Distribution"
msgstr "分析分摊"

#. module: account_analytic_default
#: model:ir.actions.act_window,name:account_analytic_default.action_product_default_list
#: model:ir.actions.act_window,name:account_analytic_default.analytic_rule_action_partner
#: model:ir.actions.act_window,name:account_analytic_default.analytic_rule_action_user
msgid "Analytic Rules"
msgstr "分析规则"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_company_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Company"
msgstr "公司"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form
msgid "Conditions"
msgstr "条件"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_create_uid
msgid "Created by"
msgstr "创建人"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_create_date
msgid "Created on"
msgstr "创建时间"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.res_config_settings_view_form
msgid "Default Analytic Values"
msgstr "默认分析值"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_date_stop
msgid "Default end date for this Analytic Account."
msgstr "该分析账户的默认结束日期。"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_date_start
msgid "Default start date for this Analytic Account."
msgstr "该分析账户的默认开始日期。"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_display_name
msgid "Display Name"
msgstr "显示名称"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_date_stop
msgid "End Date"
msgstr "结束日期"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_sequence
msgid ""
"Gives the sequence order when displaying a list of analytic distribution"
msgstr "给出显示分析分布行的序列顺序"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Group By"
msgstr "分组"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_id
msgid "ID"
msgstr "ID"

#. module: account_analytic_default
#: model:ir.model,name:account_analytic_default.model_account_invoice_line
msgid "Invoice Line"
msgstr "发票行"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default___last_update
msgid "Last Modified on"
msgstr "最后修改时间"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_write_uid
msgid "Last Updated by"
msgstr "最后更新者"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_partner_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Partner"
msgstr "业务伙伴"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_product_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Product"
msgstr "产品"

#. module: account_analytic_default
#: model:ir.model,name:account_analytic_default.model_sale_order_line
msgid "Sales Order Line"
msgstr "销售订单行"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_company_id
msgid ""
"Select a company which will use analytic account specified in analytic "
"default (e.g. create new customer invoice or Sales order if we select this "
"company, it will automatically take this as an analytic account)"
msgstr "选择一个公司将用于默认分析指定的分析账户（例如：根据我们所选公司创建新客户单据或销售订单，系统将自动使用它作为分析账户）"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_partner_id
msgid ""
"Select a partner which will use analytic account specified in analytic "
"default (e.g. create new customer invoice or Sales order if we select this "
"partner, it will automatically take this as an analytic account)"
msgstr "选择一个伙伴用于默认分析指定的分析账户（例如：根据所选伙伴创建新客户单据或销售订单，系统将自动使用它作为分析账户）"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_product_id
msgid ""
"Select a product which will use analytic account specified in analytic "
"default (e.g. create new customer invoice or Sales order if we select this "
"product, it will automatically take this as an analytic account)"
msgstr "选择一个产品用于默认分析指定的分析账户（例如：若果我们选择此产品，创建新客户单据或销售订单，系统将自动使用它作为分析账户）"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_user_id
msgid ""
"Select a user which will use analytic account specified in analytic default."
msgstr "选择一个用户用在分析账户指定的默认值。"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_sequence
msgid "Sequence"
msgstr "序列"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_date_start
msgid "Start Date"
msgstr "开始日期"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_user_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "User"
msgstr "用户"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "to"
msgstr "到"
