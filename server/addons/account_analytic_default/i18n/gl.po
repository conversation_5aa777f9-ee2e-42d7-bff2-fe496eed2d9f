# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_analytic_default
# 
# Translators:
# <PERSON>, 2017
# <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Galician (https://www.transifex.com/odoo/teams/41243/gl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "<i class=\"fa fa-calendar\"/> From"
msgstr ""

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "<strong>Customer</strong>"
msgstr ""

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "<strong>Product</strong>"
msgstr ""

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Accounts"
msgstr "Contas"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_analytic_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Analytic Account"
msgstr "Conta analítica"

#. module: account_analytic_default
#: model:ir.actions.act_window,name:account_analytic_default.action_analytic_default_list
#: model:ir.ui.menu,name:account_analytic_default.menu_analytic_default_list
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_tree
msgid "Analytic Defaults"
msgstr "Análise: Valores predefinidos"

#. module: account_analytic_default
#: model:ir.model,name:account_analytic_default.model_account_analytic_default
msgid "Analytic Distribution"
msgstr "Distribución analítica"

#. module: account_analytic_default
#: model:ir.actions.act_window,name:account_analytic_default.action_product_default_list
#: model:ir.actions.act_window,name:account_analytic_default.analytic_rule_action_partner
#: model:ir.actions.act_window,name:account_analytic_default.analytic_rule_action_user
msgid "Analytic Rules"
msgstr "Regras analíticas"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_company_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Company"
msgstr "Compañía"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form
msgid "Conditions"
msgstr "Condicións"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_create_date
msgid "Created on"
msgstr "Creado o"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.res_config_settings_view_form
msgid "Default Analytic Values"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_date_stop
msgid "Default end date for this Analytic Account."
msgstr "Data final por defecto para esta conta analítica."

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_date_start
msgid "Default start date for this Analytic Account."
msgstr "Data inicial por defecto para esta conta analítica."

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_display_name
msgid "Display Name"
msgstr "Amosar nome"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_date_stop
msgid "End Date"
msgstr "Data de remate"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_sequence
msgid ""
"Gives the sequence order when displaying a list of analytic distribution"
msgstr ""
"Proporciona a orde de secuencia ó amosar unha lista de distribución "
"analítica"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Group By"
msgstr "Agrupar por"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_id
msgid "ID"
msgstr "ID"

#. module: account_analytic_default
#: model:ir.model,name:account_analytic_default.model_account_invoice_line
msgid "Invoice Line"
msgstr "Liña de factura"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_partner_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Partner"
msgstr "Empresa"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_product_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Product"
msgstr "Produto"

#. module: account_analytic_default
#: model:ir.model,name:account_analytic_default.model_sale_order_line
msgid "Sales Order Line"
msgstr "Liña de ordes de venda"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_company_id
msgid ""
"Select a company which will use analytic account specified in analytic "
"default (e.g. create new customer invoice or Sales order if we select this "
"company, it will automatically take this as an analytic account)"
msgstr ""
"Seleccione a compañía que usará a conta analítica especificada por defecto "
"(p.ex. ó crear unha nova factura de venta ou pedido de venda, se "
"seleccionamos esta compañía, tomará automáticamente esta conta analítica)"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_partner_id
msgid ""
"Select a partner which will use analytic account specified in analytic "
"default (e.g. create new customer invoice or Sales order if we select this "
"partner, it will automatically take this as an analytic account)"
msgstr ""
"Seleccione o terceiro que usará a conta analítica especificada por defecto "
"(p.ex. ó crear unha nova factura de venta ou pedido de venda, se "
"seleccionamos este terceiro, tomará automáticamente esta conta analítica)"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_product_id
msgid ""
"Select a product which will use analytic account specified in analytic "
"default (e.g. create new customer invoice or Sales order if we select this "
"product, it will automatically take this as an analytic account)"
msgstr ""
"Seleccione o producto que usará a conta analítica especificada por defecto "
"(p.ex. ó crear unha nova factura de venta ou pedido de venda, se "
"seleccionamos este producto, tomará automáticamente esta conta analítica)"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_user_id
msgid ""
"Select a user which will use analytic account specified in analytic default."
msgstr ""
"Seleccione o usuario que usará a conta analítica especificada por defecto."

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_date_start
msgid "Start Date"
msgstr "Data de comezo"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_user_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "User"
msgstr "Usuario"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "to"
msgstr "ata"
