# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_analytic_default
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 10:14+0000\n"
"Last-Translator: Gvant<PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Georgian (https://www.transifex.com/odoo/teams/41243/ka/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ka\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "<i class=\"fa fa-calendar\"/> From"
msgstr ""

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "<strong>Customer</strong>"
msgstr "<strong>მომხმარებელი</strong>"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "<strong>Product</strong>"
msgstr "<strong>პროდუქტი</strong>"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Accounts"
msgstr "ანგარიშები"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_analytic_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Analytic Account"
msgstr "ანალიტიკური ანგარიში"

#. module: account_analytic_default
#: model:ir.actions.act_window,name:account_analytic_default.action_analytic_default_list
#: model:ir.ui.menu,name:account_analytic_default.menu_analytic_default_list
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_tree
msgid "Analytic Defaults"
msgstr ""

#. module: account_analytic_default
#: model:ir.model,name:account_analytic_default.model_account_analytic_default
msgid "Analytic Distribution"
msgstr ""

#. module: account_analytic_default
#: model:ir.actions.act_window,name:account_analytic_default.action_product_default_list
#: model:ir.actions.act_window,name:account_analytic_default.analytic_rule_action_partner
#: model:ir.actions.act_window,name:account_analytic_default.analytic_rule_action_user
msgid "Analytic Rules"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_company_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Company"
msgstr "კომპანია"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form
msgid "Conditions"
msgstr "პირობები"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_create_uid
msgid "Created by"
msgstr "შემქმნელი"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_create_date
msgid "Created on"
msgstr "შექმნის თარიღი"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.res_config_settings_view_form
msgid "Default Analytic Values"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_date_stop
msgid "Default end date for this Analytic Account."
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_date_start
msgid "Default start date for this Analytic Account."
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_display_name
msgid "Display Name"
msgstr "სახელი"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_date_stop
msgid "End Date"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_sequence
msgid ""
"Gives the sequence order when displaying a list of analytic distribution"
msgstr ""

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Group By"
msgstr "დაჯგუფება"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_id
msgid "ID"
msgstr "იდენტიფიკატორი"

#. module: account_analytic_default
#: model:ir.model,name:account_analytic_default.model_account_invoice_line
msgid "Invoice Line"
msgstr "ინვოისისი სტრიქონი"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default___last_update
msgid "Last Modified on"
msgstr "ბოლოს განახლებულია"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_write_uid
msgid "Last Updated by"
msgstr "ბოლოს განაახლა"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_write_date
msgid "Last Updated on"
msgstr "ბოლოს განახლებულია"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_partner_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Partner"
msgstr "პარტნიორი"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_product_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Product"
msgstr "პროდუქტი"

#. module: account_analytic_default
#: model:ir.model,name:account_analytic_default.model_sale_order_line
msgid "Sales Order Line"
msgstr "გაყიდვის ორდერის ხაზი"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_company_id
msgid ""
"Select a company which will use analytic account specified in analytic "
"default (e.g. create new customer invoice or Sales order if we select this "
"company, it will automatically take this as an analytic account)"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_partner_id
msgid ""
"Select a partner which will use analytic account specified in analytic "
"default (e.g. create new customer invoice or Sales order if we select this "
"partner, it will automatically take this as an analytic account)"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_product_id
msgid ""
"Select a product which will use analytic account specified in analytic "
"default (e.g. create new customer invoice or Sales order if we select this "
"product, it will automatically take this as an analytic account)"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default_user_id
msgid ""
"Select a user which will use analytic account specified in analytic default."
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_sequence
msgid "Sequence"
msgstr "მიმდევრობა"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_date_start
msgid "Start Date"
msgstr "დაწყების თარიღი"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default_user_id
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "User"
msgstr "მომხმარებელი"

#. module: account_analytic_default
#: model:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "to"
msgstr ""
