<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Budgets -->
    <data noupdate="1">
        <record id="crossovered_budget_budgetoptimistic0" model="crossovered.budget">
            <field eval="'Budget '+str(time.localtime(time.time())[0]+1)+': Optimistic'" name="name"/>
            <field eval="str(time.localtime(time.time())[0]+1)+'-01-01'" name="date_from"/>
            <field eval="&quot;&quot;&quot;draft&quot;&quot;&quot;" name="state"/>
            <field eval="str(time.localtime(time.time())[0]+1)+'-12-31'" name="date_to"/>
            <field name="creating_user_id" ref="base.user_root"/>
        </record>
    </data>
    <data noupdate="1">
        <record id="crossovered_budget_budgetpessimistic0" model="crossovered.budget">
            <field eval="'Budget '+str(time.localtime(time.time())[0]+1)+': Pessimistic'" name="name"/>
            <field eval="str(time.localtime(time.time())[0]+1)+'-01-01'" name="date_from"/>
            <field eval="&quot;&quot;&quot;draft&quot;&quot;&quot;" name="state"/>
            <field eval="str(time.localtime(time.time())[0]+1)+'-12-31'" name="date_to"/>
            <field name="creating_user_id" ref="base.user_root"/>
        </record>
    </data>
</odoo>
