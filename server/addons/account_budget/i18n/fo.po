# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_budget
# 
# Translators:
# <PERSON>, 2017
# <PERSON><PERSON><PERSON><PERSON> Nattest<PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-10 11:34+0000\n"
"PO-Revision-Date: 2017-10-10 11:34+0000\n"
"Last-Translator: <PERSON>ar<PERSON><PERSON> Nattestad <<EMAIL>>, 2018\n"
"Language-Team: Faroese (https://www.transifex.com/odoo/teams/41243/fo/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fo\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_budget
#: model:ir.actions.act_window,help:account_budget.act_crossovered_budget_view
msgid ""
"A budget is a forecast of your company's income and/or expenses\n"
"                expected for a period in the future. A budget is defined on some\n"
"                financial accounts and/or analytic accounts (that may represent\n"
"                projects, departments, categories of products, etc.)"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_account_ids
#: model:ir.ui.view,arch_db:account_budget.view_budget_post_form
msgid "Accounts"
msgstr "Bókhald"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_percentage
msgid "Achievement"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_analytic_account
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_analytic_account_id
msgid "Analytic Account"
msgstr "Analytiskt bókhald"

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Approve"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_crossovered_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_crossovered_budget_id
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_tree
#: model:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Budget"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_account_analytic_account_cb_lines
#: model:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Budget Items"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_crossovered_budget_lines
msgid "Budget Line"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_analytic_account_crossovered_budget_line
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_crossovered_budget_line
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_crossovered_budget_line
#: model:ir.model.fields,field_description:account_budget.field_project_project_crossovered_budget_line
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_form
#: model:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
#: model:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_tree
msgid "Budget Lines"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_name
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Budget Name"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_budget_post
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_general_budget_id
#: model:ir.ui.view,arch_db:account_budget.view_budget_post_form
#: model:ir.ui.view,arch_db:account_budget.view_budget_post_search
#: model:ir.ui.view,arch_db:account_budget.view_budget_post_tree
msgid "Budgetary Position"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.open_budget_post_form
#: model:ir.ui.menu,name:account_budget.menu_budget_post_form
#: model:ir.ui.view,arch_db:account_budget.res_config_settings_view_form
msgid "Budgetary Positions"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_crossovered_budget_lines_view
#: model:ir.actions.act_window,name:account_budget.act_crossovered_budget_view
#: model:ir.ui.menu,name:account_budget.menu_act_crossovered_budget_lines_view
#: model:ir.ui.menu,name:account_budget.menu_act_crossovered_budget_view
msgid "Budgets"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,help:account_budget.act_crossovered_budget_view
msgid ""
"By keeping track of where your money goes, you may be less\n"
"                likely to overspend, and more likely to meet your financial\n"
"                goals. Forecast a budget by detailing the expected revenue per\n"
"                analytic account and monitor its evolution based on the actuals\n"
"                realised during that period."
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Cancel Budget"
msgstr ""

#. module: account_budget
#: selection:crossovered.budget,state:0
msgid "Cancelled"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,help:account_budget.act_crossovered_budget_view
msgid "Click to create a new budget."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_company_id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_company_id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_company_id
msgid "Company"
msgstr "Fyritøka"

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Confirm"
msgstr ""

#. module: account_budget
#: selection:crossovered.budget,state:0
msgid "Confirmed"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_create_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_create_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_create_uid
msgid "Created by"
msgstr "Byrjað av"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_create_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_create_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_create_date
msgid "Created on"
msgstr "Byrjað tann"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_display_name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_display_name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_display_name
msgid "Display Name"
msgstr "Vís navn"

#. module: account_budget
#: selection:crossovered.budget,state:0
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Done"
msgstr "Liðugt"

#. module: account_budget
#: selection:crossovered.budget,state:0
#: model:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Draft"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Draft Budgets"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_date_to
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_date_to
msgid "End Date"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_id
msgid "ID"
msgstr "ID"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post___last_update
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget___last_update
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines___last_update
msgid "Last Modified on"
msgstr "Seinast rættað tann"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_write_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_write_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_write_uid
msgid "Last Updated by"
msgstr "Seinast dagført av"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_write_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_write_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_write_date
msgid "Last Updated on"
msgstr "Seinast dagført tann"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_name
msgid "Name"
msgstr "Navn"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_paid_date
msgid "Paid Date"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Period"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_planned_amount
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Planned Amount"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_practical_amount
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Practical Amount"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Reset to Draft"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_creating_user_id
msgid "Responsible"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_date_from
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_date_from
msgid "Start Date"
msgstr "Byrjanardato"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_state
msgid "Status"
msgstr ""

#. module: account_budget
#: code:addons/account_budget/models/account_budget.py:31
#, python-format
msgid "The budget must have at least one account."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_theoritical_amount
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Theoretical Amount"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Theoritical Amount"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "To Approve"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "To Approve Budgets"
msgstr ""

#. module: account_budget
#: selection:crossovered.budget,state:0
msgid "Validated"
msgstr ""
