# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_budget
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-09-19 08:17+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: English (Australia) (http://www.transifex.com/odoo/odoo-9/"
"language/en_AU/)\n"
"Language: en_AU\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.report_budget
#: model:ir.ui.view,arch_db:account_budget.report_crossoveredbudget
msgid "<strong>Analysis from:</strong>"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.report_analyticacco<PERSON>budget
msgid "<strong>Analysis from</strong>"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.report_analyticaccountbudget
msgid "<strong>Analytic Account</strong>"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.report_budget
#: model:ir.ui.view,arch_db:account_budget.report_crossoveredbudget
msgid "<strong>Budget:</strong>"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.report_budget
#: model:ir.ui.view,arch_db:account_budget.report_crossoveredbudget
msgid "<strong>Currency:</strong>"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.report_analyticaccountbudget
msgid "<strong>Currency</strong>"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.report_budget
msgid "<strong>Printed at:</strong>"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.report_analyticaccountbudget
#: model:ir.ui.view,arch_db:account_budget.report_budget
#: model:ir.ui.view,arch_db:account_budget.report_crossoveredbudget
msgid "<strong>Total:</strong>"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,help:account_budget.act_crossovered_budget_view
msgid ""
"A budget is a forecast of your company's income and/or expenses\n"
"                expected for a period in the future. A budget is defined on "
"some\n"
"                financial accounts and/or analytic accounts (that may "
"represent\n"
"                projects, departments, categories of products, etc.)"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_budget_crossvered_report
msgid "Account Budget crossovered report"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_budget_crossvered_summary_report
msgid "Account Budget crossovered summary report"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_budget_analytic
#: model:ir.model,name:account_budget.model_account_budget_report
msgid "Account Budget report for analytic account"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_account_ids
#: model:ir.ui.view,arch_db:account_budget.view_budget_post_form
msgid "Accounts"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_percentage
msgid "Achievement"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_analytic_account
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_analytic_account_id
msgid "Analytic Account"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.report_analyticaccountbudget
msgid "Analytic Budget"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Approve"
msgstr ""

#. module: account_budget
#: model:ir.actions.report.xml,name:account_budget.action_report_budget
#: model:ir.model,name:account_budget.model_crossovered_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_crossovered_budget_id
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_tree
#: model:ir.ui.view,arch_db:account_budget.report_budget
#: model:ir.ui.view,arch_db:account_budget.report_crossoveredbudget
#: model:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Budget"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_account_analytic_account_cb_lines
#: model:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Budget Items"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_crossovered_budget_lines
msgid "Budget Line"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_analytic_account_crossovered_budget_line
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_crossovered_budget_line
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_crossovered_budget_line
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_form
#: model:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
#: model:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_tree
msgid "Budget Lines"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_name
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Budget Name"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_budget_post
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_general_budget_id
#: model:ir.ui.view,arch_db:account_budget.view_budget_post_form
#: model:ir.ui.view,arch_db:account_budget.view_budget_post_search
#: model:ir.ui.view,arch_db:account_budget.view_budget_post_tree
msgid "Budgetary Position"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.open_budget_post_form
#: model:ir.ui.menu,name:account_budget.menu_budget_post_form
msgid "Budgetary Positions"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_crossovered_budget_lines_view
#: model:ir.actions.act_window,name:account_budget.act_crossovered_budget_view
#: model:ir.actions.act_window,name:account_budget.action_account_budget_report
#: model:ir.actions.report.xml,name:account_budget.action_report_analytic_account_budget
#: model:ir.actions.report.xml,name:account_budget.action_report_crossovered_budget
#: model:ir.ui.menu,name:account_budget.menu_act_crossovered_budget_lines_view
#: model:ir.ui.menu,name:account_budget.menu_act_crossovered_budget_view
msgid "Budgets"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,help:account_budget.act_crossovered_budget_view
msgid ""
"By keeping track of where your money goes, you may be less\n"
"                likely to overspend, and more likely to meet your financial\n"
"                goals. Forecast a budget by detailing the expected revenue "
"per\n"
"                analytic account and monitor its evolution based on the "
"actuals\n"
"                realised during that period."
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.account_budget_analytic_view
#: model:ir.ui.view,arch_db:account_budget.account_budget_crossvered_report_view
#: model:ir.ui.view,arch_db:account_budget.account_budget_crossvered_summary_report_view
#: model:ir.ui.view,arch_db:account_budget.account_budget_report_view
msgid "Cancel"
msgstr "Cancel"

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Cancel Budget"
msgstr ""

#. module: account_budget
#: selection:crossovered.budget,state:0
msgid "Cancelled"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,help:account_budget.act_crossovered_budget_view
msgid "Click to create a new budget."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_company_id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_company_id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_company_id
msgid "Company"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Confirm"
msgstr "Confirm"

#. module: account_budget
#: selection:crossovered.budget,state:0
msgid "Confirmed"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_analytic_create_uid
#: model:ir.model.fields,field_description:account_budget.field_account_budget_crossvered_report_create_uid
#: model:ir.model.fields,field_description:account_budget.field_account_budget_crossvered_summary_report_create_uid
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_create_uid
#: model:ir.model.fields,field_description:account_budget.field_account_budget_report_create_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_create_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_create_uid
msgid "Created by"
msgstr "Created by"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_analytic_create_date
#: model:ir.model.fields,field_description:account_budget.field_account_budget_crossvered_report_create_date
#: model:ir.model.fields,field_description:account_budget.field_account_budget_crossvered_summary_report_create_date
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_create_date
#: model:ir.model.fields,field_description:account_budget.field_account_budget_report_create_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_create_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_create_date
msgid "Created on"
msgstr "Created on"

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.report_analyticaccountbudget
#: model:ir.ui.view,arch_db:account_budget.report_budget
#: model:ir.ui.view,arch_db:account_budget.report_crossoveredbudget
msgid "Description"
msgstr "Description"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_analytic_display_name
#: model:ir.model.fields,field_description:account_budget.field_account_budget_crossvered_report_display_name
#: model:ir.model.fields,field_description:account_budget.field_account_budget_crossvered_summary_report_display_name
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_display_name
#: model:ir.model.fields,field_description:account_budget.field_account_budget_report_display_name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_display_name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_display_name
#: model:ir.model.fields,field_description:account_budget.field_report_account_budget_report_analyticaccountbudget_display_name
#: model:ir.model.fields,field_description:account_budget.field_report_account_budget_report_budget_display_name
#: model:ir.model.fields,field_description:account_budget.field_report_account_budget_report_crossoveredbudget_display_name
msgid "Display Name"
msgstr "Display Name"

#. module: account_budget
#: selection:crossovered.budget,state:0
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Done"
msgstr ""

#. module: account_budget
#: selection:crossovered.budget,state:0
#: model:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Draft"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Draft Budgets"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_date_to
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_date_to
msgid "End Date"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_analytic_date_to
#: model:ir.model.fields,field_description:account_budget.field_account_budget_crossvered_report_date_to
#: model:ir.model.fields,field_description:account_budget.field_account_budget_crossvered_summary_report_date_to
#: model:ir.model.fields,field_description:account_budget.field_account_budget_report_date_to
msgid "End of period"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_analytic_id
#: model:ir.model.fields,field_description:account_budget.field_account_budget_crossvered_report_id
#: model:ir.model.fields,field_description:account_budget.field_account_budget_crossvered_summary_report_id
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_id
#: model:ir.model.fields,field_description:account_budget.field_account_budget_report_id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_id
#: model:ir.model.fields,field_description:account_budget.field_report_account_budget_report_analyticaccountbudget_id
#: model:ir.model.fields,field_description:account_budget.field_report_account_budget_report_budget_id
#: model:ir.model.fields,field_description:account_budget.field_report_account_budget_report_crossoveredbudget_id
msgid "ID"
msgstr "ID"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_analytic___last_update
#: model:ir.model.fields,field_description:account_budget.field_account_budget_crossvered_report___last_update
#: model:ir.model.fields,field_description:account_budget.field_account_budget_crossvered_summary_report___last_update
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post___last_update
#: model:ir.model.fields,field_description:account_budget.field_account_budget_report___last_update
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget___last_update
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines___last_update
#: model:ir.model.fields,field_description:account_budget.field_report_account_budget_report_analyticaccountbudget___last_update
#: model:ir.model.fields,field_description:account_budget.field_report_account_budget_report_budget___last_update
#: model:ir.model.fields,field_description:account_budget.field_report_account_budget_report_crossoveredbudget___last_update
msgid "Last Modified on"
msgstr "Last Modified on"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_analytic_write_uid
#: model:ir.model.fields,field_description:account_budget.field_account_budget_crossvered_report_write_uid
#: model:ir.model.fields,field_description:account_budget.field_account_budget_crossvered_summary_report_write_uid
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_write_uid
#: model:ir.model.fields,field_description:account_budget.field_account_budget_report_write_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_write_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_write_uid
msgid "Last Updated by"
msgstr "Last Updated by"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_analytic_write_date
#: model:ir.model.fields,field_description:account_budget.field_account_budget_crossvered_report_write_date
#: model:ir.model.fields,field_description:account_budget.field_account_budget_crossvered_summary_report_write_date
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_write_date
#: model:ir.model.fields,field_description:account_budget.field_account_budget_report_write_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_write_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_write_date
msgid "Last Updated on"
msgstr "Last Updated on"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post_name
msgid "Name"
msgstr "Name"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_paid_date
msgid "Paid Date"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.report_analyticaccountbudget
#: model:ir.ui.view,arch_db:account_budget.report_budget
#: model:ir.ui.view,arch_db:account_budget.report_crossoveredbudget
msgid "Perc(%)"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Period"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_planned_amount
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model:ir.ui.view,arch_db:account_budget.report_budget
msgid "Planned Amount"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.report_analyticaccountbudget
#: model:ir.ui.view,arch_db:account_budget.report_crossoveredbudget
msgid "Planned Amt"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_practical_amount
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model:ir.ui.view,arch_db:account_budget.report_budget
#: model:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Practical Amount"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.report_analyticaccountbudget
#: model:ir.ui.view,arch_db:account_budget.report_crossoveredbudget
msgid "Practical Amt"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.account_budget_analytic_view
#: model:ir.ui.view,arch_db:account_budget.account_budget_crossvered_report_view
#: model:ir.ui.view,arch_db:account_budget.account_budget_crossvered_summary_report_view
#: model:ir.ui.view,arch_db:account_budget.account_budget_report_view
msgid "Print"
msgstr "Print"

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.action_account_budget_analytic
#: model:ir.actions.act_window,name:account_budget.action_account_budget_crossvered_report
#: model:ir.ui.view,arch_db:account_budget.account_budget_report_view
msgid "Print Budgets"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.action_account_budget_crossvered_summary_report
msgid "Print Summary"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Reset to Draft"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_creating_user_id
msgid "Responsible"
msgstr "Responsible"

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.account_budget_analytic_view
#: model:ir.ui.view,arch_db:account_budget.account_budget_crossvered_report_view
#: model:ir.ui.view,arch_db:account_budget.account_budget_crossvered_summary_report_view
#: model:ir.ui.view,arch_db:account_budget.account_budget_report_view
msgid "Select Dates Period"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_date_from
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_date_from
msgid "Start Date"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_analytic_date_from
#: model:ir.model.fields,field_description:account_budget.field_account_budget_crossvered_report_date_from
#: model:ir.model.fields,field_description:account_budget.field_account_budget_crossvered_summary_report_date_from
#: model:ir.model.fields,field_description:account_budget.field_account_budget_report_date_from
msgid "Start of period"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_state
msgid "Status"
msgstr ""

#. module: account_budget
#: code:addons/account_budget/account_budget.py:104
#, python-format
msgid "The Budget '%s' has no accounts!"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines_theoritical_amount
#: model:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model:ir.ui.view,arch_db:account_budget.report_budget
msgid "Theoretical Amount"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.report_analyticaccountbudget
#: model:ir.ui.view,arch_db:account_budget.report_crossoveredbudget
msgid "Theoretical Amt"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Theoritical Amount"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.account_budget_analytic_view
#: model:ir.ui.view,arch_db:account_budget.account_budget_crossvered_report_view
msgid "This wizard is used to print budget"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.account_budget_crossvered_summary_report_view
msgid "This wizard is used to print summary of budgets"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "To Approve"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "To Approve Budgets"
msgstr ""

#. module: account_budget
#: selection:crossovered.budget,state:0
msgid "Validated"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.report_budget
msgid "at"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_report_account_budget_report_analyticaccountbudget
msgid "report.account_budget.report_analyticaccountbudget"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_report_account_budget_report_budget
msgid "report.account_budget.report_budget"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_report_account_budget_report_crossoveredbudget
msgid "report.account_budget.report_crossoveredbudget"
msgstr ""

#. module: account_budget
#: model:ir.ui.view,arch_db:account_budget.report_analyticaccountbudget
#: model:ir.ui.view,arch_db:account_budget.report_budget
#: model:ir.ui.view,arch_db:account_budget.report_crossoveredbudget
msgid "to"
msgstr ""
