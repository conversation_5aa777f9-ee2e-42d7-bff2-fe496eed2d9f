# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_cash_basis_base_account
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-07-27 11:55+0000\n"
"PO-Revision-Date: 2018-07-27 11:55+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2018\n"
"Language-Team: German (https://www.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_cash_basis_base_account
#: model:ir.model.fields,help:account_cash_basis_base_account.field_account_tax_cash_basis_base_account_id
msgid ""
"Account that will be set on lines created in cash basis journal entry and "
"used to keep track of the tax base amount."
msgstr ""
"Konto, welches bei Positionen des Journals Einnahmen-Überschussrechnung "
"eingesetzt wird und zum Fixieren des Steuerbasisbetrags verwendet wird."

#. module: account_cash_basis_base_account
#: model:ir.model.fields,field_description:account_cash_basis_base_account.field_account_tax_cash_basis_base_account_id
msgid "Base Tax Received Account"
msgstr "Konto für erhaltenen Steuerbetrag"

#. module: account_cash_basis_base_account
#: model:ir.model,name:account_cash_basis_base_account.model_account_partial_reconcile
msgid "Partial Reconcile"
msgstr "Teilausgleich"

#. module: account_cash_basis_base_account
#: model:ir.model,name:account_cash_basis_base_account.model_account_tax
msgid "Tax"
msgstr "Steuer"
