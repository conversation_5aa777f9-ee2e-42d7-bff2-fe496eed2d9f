# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_cash_basis_base_account
# 
# Translators:
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-07-27 11:55+0000\n"
"PO-Revision-Date: 2018-07-27 11:55+0000\n"
"Last-Translator: 敬雲 林 <<EMAIL>>, 2018\n"
"Language-Team: Chinese (Taiwan) (https://www.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_cash_basis_base_account
#: model:ir.model.fields,help:account_cash_basis_base_account.field_account_tax_cash_basis_base_account_id
msgid ""
"Account that will be set on lines created in cash basis journal entry and "
"used to keep track of the tax base amount."
msgstr "帳戶將設定在以現金為基礎的日記帳項中創建的行，並用於追蹤稅基金額."

#. module: account_cash_basis_base_account
#: model:ir.model.fields,field_description:account_cash_basis_base_account.field_account_tax_cash_basis_base_account_id
msgid "Base Tax Received Account"
msgstr "基本稅應收科目"

#. module: account_cash_basis_base_account
#: model:ir.model,name:account_cash_basis_base_account.model_account_partial_reconcile
msgid "Partial Reconcile"
msgstr "部分調節"

#. module: account_cash_basis_base_account
#: model:ir.model,name:account_cash_basis_base_account.model_account_tax
msgid "Tax"
msgstr "稅"
