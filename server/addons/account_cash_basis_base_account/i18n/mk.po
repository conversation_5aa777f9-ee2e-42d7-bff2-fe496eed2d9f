# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_cash_basis_base_account
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-07-27 11:55+0000\n"
"PO-Revision-Date: 2018-07-27 11:55+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Macedonian (https://www.transifex.com/odoo/teams/41243/mk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mk\n"
"Plural-Forms: nplurals=2; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : 1;\n"

#. module: account_cash_basis_base_account
#: model:ir.model.fields,help:account_cash_basis_base_account.field_account_tax_cash_basis_base_account_id
msgid ""
"Account that will be set on lines created in cash basis journal entry and "
"used to keep track of the tax base amount."
msgstr ""

#. module: account_cash_basis_base_account
#: model:ir.model.fields,field_description:account_cash_basis_base_account.field_account_tax_cash_basis_base_account_id
msgid "Base Tax Received Account"
msgstr ""

#. module: account_cash_basis_base_account
#: model:ir.model,name:account_cash_basis_base_account.model_account_partial_reconcile
msgid "Partial Reconcile"
msgstr "Делумно порамнување"

#. module: account_cash_basis_base_account
#: model:ir.model,name:account_cash_basis_base_account.model_account_tax
msgid "Tax"
msgstr "Данок"
