# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_asset
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON><PERSON><PERSON><PERSON> <bag<PERSON><PERSON><PERSON>@gmail.com>, 2019
# <PERSON><PERSON> <miab<PERSON><PERSON><PERSON>@gmail.com>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-30 13:11+0000\n"
"PO-Revision-Date: 2017-09-20 10:14+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Tamil (https://www.transifex.com/odoo/teams/41243/ta/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ta\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:404
#, python-format
msgid " (copy)"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:540
#, python-format
msgid " (grouped)"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_entry_count
msgid "# Asset Entries"
msgstr "சொத்து பதிவுகள்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_depreciation_nbr
msgid "# of Depreciation Lines"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_installment_nbr
msgid "# of Installment Lines"
msgstr ""

#. module: account_asset
#: model:ir.actions.server,name:account_asset.account_asset_cron_ir_actions_server
#: model:ir.cron,cron_name:account_asset.account_asset_cron
#: model:ir.cron,name:account_asset.account_asset_cron
msgid "Account Asset: Generate asset entries"
msgstr "கணக்கு சொத்து: சொத்து உள்ளீடுகளை உருவாக்கவும்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_date
msgid "Account Date"
msgstr "கணக்கு தேதி"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move
msgid "Account Entry"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr ""
"சொத்து மதிப்பு குறைக்க, தேய்மான உள்ளீடுகளில் பயன்படுத்தப்படும் கணக்கு."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr ""
"குறிப்பிட்ட காலப்பகுதியில் பயன்படுத்தப்படும் கணக்கு, சொத்துக்களின் ஒரு "
"பகுதியை செலவில் பதிவு செய்ய."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr ""
"அசல் விலையில் சொத்து வாங்குவதை பதிவு செய்யப் பயன்படுத்தப்படும் கணக்கு."

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset.js:52
#, python-format
msgid "Accounting entries waiting for manual verification"
msgstr "கையேடு சரிபார்ப்புக்காக காத்திருக்கும் பைனான்ஸ் உள்ளீடுகள்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_active
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_active
msgid "Active"
msgstr "செயலில்"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Additional Options"
msgstr "கூடுதல் விருப்பங்கள்"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:597
#, python-format
msgid "Amount"
msgstr "தொகை"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_depreciation_value
msgid "Amount of Depreciation Lines"
msgstr "தேய்மான வரிகளின் தொகை"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_installment_value
msgid "Amount of Installment Lines"
msgstr "தவணை வரி தொகை"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_analytic_id
msgid "Analytic Account"
msgstr "பகுப்பாய்வு கணக்கு"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_asset_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_asset_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Asset"
msgstr "சொத்து"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_asset_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset Account"
msgstr "சொத்து கணக்கு"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
#: model:ir.ui.view,arch_db:account_asset.view_invoice_asset_category
msgid "Asset Category"
msgstr "சொத்து வகை"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Asset Durations to Modify"
msgstr "Asset Durations to Modify"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_end_date
msgid "Asset End Date"
msgstr "சொத்து முடிவு தேதி"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_asset_method_time
msgid "Asset Method Time"
msgstr "சொத்து முறை நேரம்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_name
msgid "Asset Name"
msgstr "சொத்து பெயர்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_start_date
msgid "Asset Start Date"
msgstr "சொத்து தொடக்க தேதி"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_name
#: model:ir.model.fields,field_description:account_asset.field_product_product_asset_category_id
#: model:ir.model.fields,field_description:account_asset.field_product_template_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Asset Type"
msgstr "சொத்து வகை"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_asset_list_normal_purchase
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_asset_list_normal_purchase
#: model:ir.ui.view,arch_db:account_asset.res_config_settings_view_form
msgid "Asset Types"
msgstr "சொத்து வகைகள்"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_category
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_tree
msgid "Asset category"
msgstr "சொத்து வகை"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:286
#, python-format
msgid "Asset created"
msgstr "சொத்து உருவாக்கப்பட்டது"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_depreciation_line
msgid "Asset depreciation line"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:318
#, python-format
msgid "Asset sold or disposed. Accounting entry awaiting for validation."
msgstr ""
"சொத்து விற்பனை செய்யப்பட்டது அல்லது அகற்றப்பட்டது. சரிபார்ப்புக்காக "
"கணக்கியல் நுழைவு காத்திருக்கிறது."

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_asset
msgid "Asset/Revenue Recognition"
msgstr "சொத்து / வருவாய் அங்கீகாரம்"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_asset_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_asset_form
#: model:ir.ui.menu,name:account_asset.menu_action_asset_asset_report
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_purchase_tree
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Assets"
msgstr "சொத்துக்கள்"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_asset_asset_report
#: model:ir.model,name:account_asset.model_asset_asset_report
#: model:ir.ui.view,arch_db:account_asset.action_account_asset_report_graph
#: model:ir.ui.view,arch_db:account_asset.action_account_asset_report_pivot
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Assets Analysis"
msgstr "சொத்துகளின் பகுப்பாய்வு"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_move_asset_depreciation_ids
msgid "Assets Depreciation Lines"
msgstr "சொத்துக்கள் தேய்மான வரிகளை"

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr "சொத்துக்கள் மற்றும் வருவாய்கள்"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr "மூடிய நிலையில் சொத்துகள்"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "வரைவு மற்றும் திறந்த மாநிலங்களில் சொத்துகள்"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Assets in draft state"
msgstr "வரைவு மாநிலத்தில் சொத்துகள்"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Assets in running state"
msgstr "மாநில இயங்கும் சொத்துகள்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_open_asset
msgid "Auto-confirm Assets"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid "Cancel"
msgstr "ரத்து"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Category"
msgstr "வகை"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Category of asset"
msgstr "சொத்து வகை"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_open_asset
msgid ""
"Check this if you want to automatically confirm the assets of this category "
"when created by invoices."
msgstr ""
"பொருள் மூலம் உருவாக்கப்பட்ட போது, இந்த வகையின் சொத்துக்களை தானாகவே "
"உறுதிப்படுத்த விரும்பினால், இதைச் சரிபார்க்கவும்."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_group_entries
msgid "Check this if you want to group the generated entries by categories."
msgstr ""
"நீங்கள் உருவாக்கிய உள்ளீடுகளை பிரிவுகளால் குழுப்படுத்த விரும்பினால் இதைச் "
"சரிபார்க்கவும்."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Linear: Calculated on basis of: Gross Value / Number of Depreciations\n"
"  * Degressive: Calculated on basis of: Residual Value * Degressive Factor"
msgstr ""
"தேய்மான வரிகளின் அளவை கணக்கிடுவதற்குப் பயன்படுத்தும் முறையைத் தேர்வு செய்க.\n"
"   * நேரியல்: அடிப்படையில் கணக்கிடப்படுகிறது: மொத்த மதிப்பு / குறைபாடுகளின் எண்\n"
"   * செயலிழப்பு: அடிப்படையில் கணக்கிடப்படுகிறது: எஞ்சிய மதிப்பு * முடக்கம் காரணி"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method_time
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method_time
msgid ""
"Choose the method to use to compute the dates and number of entries.\n"
"  * Number of Entries: Fix the number of entries and the time between 2 depreciations.\n"
"  * Ending Date: Choose the time between 2 depreciations and the date the depreciations won't go beyond."
msgstr ""
"தேதிகள் மற்றும் எண்ணங்களின் எண்ணிக்கை கணக்கிட முறைகளை தேர்வு செய்யவும்.\n"
"   * பதிவுகள் எண்ணிக்கை: உள்ளீடுகளின் எண்ணிக்கை மற்றும் நேரம் 2 depreciations இடையே நேரம் சரி.\n"
"   * முடிவுத்திறன் தேதி: 2 முறைகளுக்கு இடையே உள்ள நேரத்தைத் தேர்ந்தெடுங்கள் மற்றும் தேதியற்ற நிலைகள் அப்பால் போகாது."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_depreciation_confirmation_wizard_date
msgid ""
"Choose the period for which you want to automatically post the depreciation "
"lines of running assets"
msgstr ""
"சொத்துக்களை இயங்கும் தேய்மானம் வரிகளை தானாகவே வெளியிட விரும்பும் காலத்தைத் "
"தேர்வுசெய்யவும்"

#. module: account_asset
#: selection:account.asset.asset,state:0 selection:asset.asset.report,state:0
msgid "Close"
msgstr "நெருக்கமான"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Closed"
msgstr "மூடப்பட்ட"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_company_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_company_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_company_id
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Company"
msgstr "நிறுவனம்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method
msgid "Computation Method"
msgstr "கணக்கீட்டு முறை"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid "Compute Asset"
msgstr "சொத்து கணக்கிடு"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Compute Depreciation"
msgstr "Compute Depreciation"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Confirm"
msgstr "உறுதிப்படுத்தவும்"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_depreciation_confirmation_wizard.py:20
#, python-format
msgid "Created Asset Moves"
msgstr "சொத்து நகர்வை உருவாக்கியது"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_depreciation_confirmation_wizard.py:20
#, python-format
msgid "Created Revenue Moves"
msgstr "வருவாய் நகர்வுகள் உருவாக்கப்பட்டது"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_create_uid
msgid "Created by"
msgstr "உருவாக்கியவர்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_create_date
msgid "Created on"
msgstr "உருவாக்கப்பட்ட தேதி"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_depreciated_value
msgid "Cumulative Depreciation"
msgstr "குவிந்த தேய்மானம்"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:597
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_currency_id
#, python-format
msgid "Currency"
msgstr "நாணயம்"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Current"
msgstr "தற்போதைய"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_amount
msgid "Current Depreciation"
msgstr "தற்போதைய தேய்மானம்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_date
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_date
msgid "Date"
msgstr "தேதி"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Date of asset"
msgstr "சொத்து தேதி"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Date of asset purchase"
msgstr "சொத்து கொள்முதல் தேதி"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Date of depreciation"
msgstr "தேய்மானம் தேதி"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Deferred Revenue Account"
msgstr "ஒத்திவைக்கப்பட்ட வருவாய் கணக்கு"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_product_product_deferred_revenue_category_id
#: model:ir.model.fields,field_description:account_asset.field_product_template_deferred_revenue_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Deferred Revenue Type"
msgstr "Deferred Revenue Type"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Deferred Revenues"
msgstr "ஒத்திவைக்கப்பட்ட வருவாய் வகை"

#. module: account_asset
#: selection:account.asset.asset,method:0
#: selection:account.asset.category,method:0
msgid "Degressive"
msgstr "சரிவு வீத"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_progress_factor
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_progress_factor
msgid "Degressive Factor"
msgstr "முரட்டு காரணி"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation"
msgstr "தேய்மானம்"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation Board"
msgstr "தேய்மானம் வாரியம்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_depreciation_date
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_depreciation_date
msgid "Depreciation Date"
msgstr "தேய்மானம் தேதி"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_depreciation_id
msgid "Depreciation Entries: Asset Account"
msgstr "தேய்மான நுழைவுகள்: சொத்து கணக்கு"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_depreciation_expense_id
msgid "Depreciation Entries: Expense Account"
msgstr "தேய்மானம் பதிவுகள்: செலவு கணக்கு"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_move_id
msgid "Depreciation Entry"
msgstr "தேய்மான நுழைவு"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation Information"
msgstr "தேய்மான தகவல்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_depreciation_line_ids
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation Lines"
msgstr "தேய்மான வரிகளை"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Depreciation Method"
msgstr "தேய்மான முறை"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Depreciation Month"
msgstr "தேய்மானம் மாதம்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_name
msgid "Depreciation Name"
msgstr "தேய்மானி பெயர்"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:85
#, python-format
msgid "Depreciation board modified"
msgstr "மாற்றியமைக்கப்பட்ட குழு மாற்றப்பட்டது"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:600
#, python-format
msgid "Depreciation line posted."
msgstr "தேய்மான வரி வெளியிடப்பட்டது."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_display_name
msgid "Display Name"
msgstr "காட்சி பெயர்"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:327
#, python-format
msgid "Disposal Move"
msgstr "நீக்குதல் நகர்த்து"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:330
#, python-format
msgid "Disposal Moves"
msgstr "அகற்றல் நகர்வுகள்"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:579
#, python-format
msgid "Document closed."
msgstr "ஆவணம் மூடப்பட்டது."

#. module: account_asset
#: selection:account.asset.asset,state:0 selection:asset.asset.report,state:0
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Draft"
msgstr "வரைவு"

#. module: account_asset
#: selection:account.asset.asset,method_time:0
#: selection:account.asset.category,method_time:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_end
msgid "Ending Date"
msgstr "Ending Date"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_end
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_method_end
msgid "Ending date"
msgstr "முடிவு தேதி"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Extended Filters..."
msgstr "நீட்டிக்கப்பட்ட வடிப்பான்கள் ..."

#. module: account_asset
#: model:ir.actions.act_window,help:account_asset.action_asset_asset_report
msgid ""
"From this report, you can have an overview on all depreciations. The\n"
"            search bar can also be used to personalize your assets depreciation reporting."
msgstr ""
"இந்த அறிக்கையில் இருந்து, நீங்கள் அனைத்து குறைபாடுகளையும் ஒரு கண்ணோட்டத்தை காணலாம். தி\n"
"             உங்கள் சொத்துக்களைத் தேய்மான அறிக்கையினைத் தனிப்பயனாக்க, தேடல் பட்டி பயன்படுத்தப்படலாம்."

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_asset_depreciation_confirmation_wizard
msgid "Generate Assets Entries"
msgstr "சொத்து பதிவுகள் உருவாக்கவும்"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid "Generate Entries"
msgstr "பதிவுகள் உருவாக்குக"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_gross_value
msgid "Gross Amount"
msgstr "மொத்த தொகை"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_value
msgid "Gross Value"
msgstr "மொத்த மதிப்பு"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Gross value of asset"
msgstr "சொத்து மொத்த மதிப்பு"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Group By"
msgstr "மூலம் குழு"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Group By..."
msgstr "மூலம் குழு ..."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_group_entries
msgid "Group Journal Entries"
msgstr "குழு ஜர்னல் பதிவுகள்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_id
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_id
msgid "ID"
msgstr "ID"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first January / Start date of fiscal "
"year"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first of January"
msgstr ""
"ஜனவரி முதல் அதற்குப் பதிலாக, இந்த சொத்துக்கான முதல் மதிப்பு இழப்பு நுழைவு "
"வாங்குதலில் இருந்து செய்யப்பட வேண்டும் என்பதைக் குறிக்கிறது"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_invoice
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_invoice_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Invoice"
msgstr "விலைப்பட்டியல்"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_invoice_line
msgid "Invoice Line"
msgstr "விலைப்பட்டியல் வரி"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_salvage_value
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr "இது நீங்கள் துரதிர்ஷ்டவசமாக முடியாது என்று திட்டமிடுகிற அளவு."

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Items"
msgstr "பொருட்களை"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_journal_id
msgid "Journal"
msgstr "ஜர்னல்"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:438
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
#, python-format
msgid "Journal Entries"
msgstr "ஜர்னல் பதிவுகள்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset___last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category___last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line___last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report___last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard___last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_modify___last_update
msgid "Last Modified on"
msgstr "கடைசியாக திருத்திய"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_write_uid
msgid "Last Updated by"
msgstr "கடைசியாக புதுப்பிக்கப்பட்டவர்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_write_date
msgid "Last Updated on"
msgstr "கடைசியாக புதுப்பிக்கப்பட்டது"

#. module: account_asset
#: selection:account.asset.asset,method:0
#: selection:account.asset.category,method:0
msgid "Linear"
msgstr "நேரியல்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_move_check
msgid "Linked"
msgstr "இணைக்கப்பட்ட"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify"
msgstr "மாற்று"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_asset_modify
#: model:ir.model,name:account_asset.model_asset_modify
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify Asset"
msgstr "சொத்து மாற்றியமை"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Modify Depreciation"
msgstr "தேய்மானத்தை மாற்றவும்"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Month"
msgstr "மாதம்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_mrr
msgid "Monthly Recurring Revenue"
msgstr "மாதாந்திர வருவாய் வருவாய்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_remaining_value
msgid "Next Period Depreciation"
msgstr "அடுத்த காலம் தேய்மானம்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_note
msgid "Note"
msgstr "குறிப்பு"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_number
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_number
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_method_number
msgid "Number of Depreciations"
msgstr "குறைபாடுகளின் எண்ணிக்கை"

#. module: account_asset
#: selection:account.asset.asset,method_time:0
#: selection:account.asset.category,method_time:0
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Number of Entries"
msgstr "பதிவுகள் எண்ணிக்கை"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_period
msgid "Number of Months in a Period"
msgstr "ஒரு காலத்தில் மாதங்களின் எண்ணிக்கை"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "One Entry Every"
msgstr "ஒரு நுழைவு ஒவ்வொரு"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:599
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_partner_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_partner_id
#, python-format
msgid "Partner"
msgstr "கூட்டாளி"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_period
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_method_period
msgid "Period Length"
msgstr "காலம் நீளம்"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Periodicity"
msgstr "காலமுறைமை"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_asset_depreciation_confirmation_wizard
msgid "Post Depreciation Lines"
msgstr "இடுகையின் மறுபார்வை கோடுகள்"

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset.js:49
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_move_posted_check
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_move_check
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
#, python-format
msgid "Posted"
msgstr "வெளியிட்டது"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_posted_value
msgid "Posted Amount"
msgstr "அஞ்சல் தொகை"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Posted depreciation lines"
msgstr "தேய்மான வரிகளை வெளியிட்டது"

#. module: account_asset
#: model:ir.model,name:account_asset.model_product_template
msgid "Product Template"
msgstr "தயாரிப்பு டெம்ப்ளேட்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_prorata
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_prorata
msgid "Prorata Temporis"
msgstr "ப்ரொராடா டெம்போரிஸ்"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:370
#, python-format
msgid ""
"Prorata temporis can be applied only for time method \"number of "
"depreciations\"."
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Purchase"
msgstr "கொள்முதல்"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Purchase Month"
msgstr "மாதம் வாங்கவும்"

#. module: account_asset
#: selection:account.asset.category,type:0
msgid "Purchase: Asset"
msgstr "கொள்முதல்: சொத்து"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_name
msgid "Reason"
msgstr "காரணம்"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Recognition Account"
msgstr "அங்கீகாரம் கணக்கு"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Recognition Income Account"
msgstr "அங்கீகார வருமானக் கணக்கு"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_code
msgid "Reference"
msgstr "குறிப்பு"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Residual"
msgstr "எச்ச"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_value_residual
msgid "Residual Value"
msgstr "எஞ்சிய மதிப்பு"

#. module: account_asset
#: selection:account.asset.asset,state:0 selection:asset.asset.report,state:0
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Running"
msgstr "இயங்கும்"

#. module: account_asset
#: selection:account.asset.category,type:0
msgid "Sale: Revenue Recognition"
msgstr "விற்பனை: வருவாய் அங்கீகாரம்"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Sales"
msgstr "விற்பனை"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_salvage_value
msgid "Salvage Value"
msgstr "காப்பு மதிப்பு"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Search Asset Category"
msgstr "சொத்து வகை தேடு"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Sell or Dispose"
msgstr "விற்க அல்லது விலக்கு"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_sequence
msgid "Sequence"
msgstr "வரிசை"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Set to Draft"
msgstr "வரைவை அமைக்கவும்"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method_period
msgid "State here the time between 2 depreciations, in months"
msgstr "2 மாதங்களுக்குள், மாதங்களில், மாநிலத்திற்கு இடையேயான நேரம்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_parent_state
msgid "State of Asset"
msgstr "சொத்து மாநிலம்"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_state
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_state
msgid "Status"
msgstr "நிலைமை"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method_period
msgid "The amount of time between two depreciations, in months"
msgstr "மாதங்களில் இரண்டு குறைபாடுகள் இடையே நேரம் அளவு"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method_number
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr "உங்கள் சொத்துக்களைக் குறைப்பதற்கு தேவையான இழப்புக்களின் எண்ணிக்கை"

#. module: account_asset
#: code:addons/account_asset/models/account_invoice.py:62
#, python-format
msgid ""
"The number of depreciations or the period length of your asset category "
"cannot be null."
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:482
#, python-format
msgid ""
"This depreciation is already linked to a journal entry! Please post or "
"delete it."
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid ""
"This wizard will post installment/depreciation lines for the selected month.<br/>\n"
"                        This will generate journal entries for all related installment lines on this period of asset/revenue recognition as well."
msgstr ""
"இந்த வழிகாட்டி தேர்ந்தெடுத்த மாதத்திற்கு தவணை / தேய்மான வரிகளை இடுகையிடும்.\n"
"                         இது சொத்து / வருவாய் அங்கீகாரம் ஆகியவற்றின் இந்த காலப்பகுதியில் அனைத்து தொடர்புடைய தவணை வரிகளுக்காகவும் ஜர்னல் உள்ளீடுகளை உருவாக்கும்."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_time
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_time
msgid "Time Method"
msgstr "நேரம் முறை"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Time Method Based On"
msgstr "அடிப்படையில் முறைமுறை"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_type
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Type"
msgstr "வகை"

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset.js:55
#, python-format
msgid "Unposted"
msgstr "இடுகையிடப்படாத"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_unposted_value
msgid "Unposted Amount"
msgstr "ஆதரிக்கப்படாத தொகை"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_purchase_tree
msgid "Vendor"
msgstr "விற்பனையாளர்"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_state
#: model:ir.model.fields,help:account_asset.field_account_asset_depreciation_line_parent_state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"You can manually close an asset when the depreciation is over. If the last line of depreciation is posted, the asset automatically goes in that status."
msgstr ""
"ஒரு சொத்து உருவாக்கப்படும்போது, அந்த நிலை 'வரைவு' ஆகும்.\n"
"சொத்து உறுதிப்படுத்தப்பட்டால், நிலை 'ரைனிங்' இல் செல்கிறது மற்றும் தேய்மான வரிகளை கணக்கில் பதிவு செய்யலாம்.\n"
"தேய்மானம் முடிந்தவுடன் நீங்கள் கைமுறையாக சொத்துக்களை மூடலாம். தேய்மானம் கடைசி வரி வெளியிடப்பட்டது என்றால், சொத்து தானாக அந்த நிலையில் செல்கிறது."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_name
msgid "Year"
msgstr "ஆண்டு"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:111
#, python-format
msgid "You cannot delete a document is in %s state."
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:114
#, python-format
msgid "You cannot delete a document that contains posted entries."
msgstr " உள்ளீடுகள் கொண்ட ஒரு ஆவணத்தை நீக்க முடியாது."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:608
#, python-format
msgid "You cannot delete posted depreciation lines."
msgstr "இடுகையிடப்பட்ட தேய்மான வரிகளை நீக்க முடியாது."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:610
#, python-format
msgid "You cannot delete posted installment lines."
msgstr "இடுகையிடப்பட்ட தவணை வரிகளை நீக்க முடியாது."

#. module: account_asset
#: model:ir.model,name:account_asset.model_asset_depreciation_confirmation_wizard
msgid "asset.depreciation.confirmation.wizard"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "e.g. Computers"
msgstr "எ.கா. கணனிகள்"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "e.g. Laptop iBook"
msgstr "எ.கா. லேப்டாப் ஐ புக்"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "months"
msgstr "மாதங்கள்"
