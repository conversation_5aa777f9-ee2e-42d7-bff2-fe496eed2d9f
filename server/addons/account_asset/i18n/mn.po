# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_asset
# 
# Translators:
# Bayark<PERSON>u Bataa, 2018
# <PERSON>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# Khishig<PERSON> Ganbold <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-30 13:11+0000\n"
"PO-Revision-Date: 2017-09-20 10:14+0000\n"
"Last-Translator: Baskhu<PERSON> <baskhuu<PERSON><EMAIL>>, 2019\n"
"Language-Team: Mongolian (https://www.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:404
#, python-format
msgid " (copy)"
msgstr " (Хуулах)"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:540
#, python-format
msgid " (grouped)"
msgstr " (бүлэглэсэн)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_entry_count
msgid "# Asset Entries"
msgstr "# Хөрөнгийн Бичилтүүд"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_depreciation_nbr
msgid "# of Depreciation Lines"
msgstr "Элэгдлийн мөрүүдийнм #"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_installment_nbr
msgid "# of Installment Lines"
msgstr "Суурьлуулалтын мөрүүдийн тоо"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.account_asset_cron_ir_actions_server
#: model:ir.cron,cron_name:account_asset.account_asset_cron
#: model:ir.cron,name:account_asset.account_asset_cron
msgid "Account Asset: Generate asset entries"
msgstr "Үндсэн хөрөнгө: Хөрөнгийн гүйлгээ бүртгэх"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_date
msgid "Account Date"
msgstr "Санхүүгийн огноо"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move
msgid "Account Entry"
msgstr "Санхүүгийн бичилт"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr "Элэгдлийн бичилтүүдэд хэрэглэгдэх данс, хөрөнгийн үнэлгээг бууруулна."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr ""
"Мөчлөгийн тогтмол хугацаат бичилтэд хэрэглэгддэг данс, хөрөнгийн хэсгийг "
"зардал болгож хөтлөнө."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr "Хөрөнгийг анх худалдан авсан үнээр нь хөтлөхөд хэрэглэдэг данс."

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset.js:52
#, python-format
msgid "Accounting entries waiting for manual verification"
msgstr "Санхүүгийн бичилтүүд "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_active
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_active
msgid "Active"
msgstr "Идэвхитэй"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Additional Options"
msgstr "Нэмэлт сонголтууд"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:597
#, python-format
msgid "Amount"
msgstr "Дүн"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_depreciation_value
msgid "Amount of Depreciation Lines"
msgstr "Элэгдлийн мөрүүдийн дүн"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_installment_value
msgid "Amount of Installment Lines"
msgstr "Суурьлуулалтын мөрүүдийн дүн"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_analytic_id
msgid "Analytic Account"
msgstr "Шинжилгээний данс"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_asset_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_asset_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Asset"
msgstr "Хөрөнгө"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_asset_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset Account"
msgstr "Хөрөнгийн данс"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
#: model:ir.ui.view,arch_db:account_asset.view_invoice_asset_category
msgid "Asset Category"
msgstr "Хөрөнгийн Ангилал"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Asset Durations to Modify"
msgstr "Засварлах хөрөнгийн үргэлжлэх хугацаа"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_end_date
msgid "Asset End Date"
msgstr "Хөрөнгийн Төгсгөлийн Огноо"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_asset_method_time
msgid "Asset Method Time"
msgstr "Хөрөнгийн аргын хугацаа"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_name
msgid "Asset Name"
msgstr "Хөрөнгийн нэр"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_start_date
msgid "Asset Start Date"
msgstr "Хөрөнгийн Эхлэл Огноо"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_name
#: model:ir.model.fields,field_description:account_asset.field_product_product_asset_category_id
#: model:ir.model.fields,field_description:account_asset.field_product_template_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Asset Type"
msgstr "Хөрөнгийн ангилал"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_asset_list_normal_purchase
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_asset_list_normal_purchase
#: model:ir.ui.view,arch_db:account_asset.res_config_settings_view_form
msgid "Asset Types"
msgstr "Хөрөнгийн ангилал"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_category
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_tree
msgid "Asset category"
msgstr "Хөрөнгийн ангилал"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:286
#, python-format
msgid "Asset created"
msgstr "Хөрөнгө үүсгэгдсэн"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_depreciation_line
msgid "Asset depreciation line"
msgstr "Хөрөнгийн элэгдлийн мөр"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:318
#, python-format
msgid "Asset sold or disposed. Accounting entry awaiting for validation."
msgstr ""
"Хөрөнгө зарагдсан эсвэл устгагдсан. Санхүүгийн бичилт нь батлахыг хүлээж "
"буй."

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_asset
msgid "Asset/Revenue Recognition"
msgstr "Хөрөнгө/Орлого Таних"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_asset_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_asset_form
#: model:ir.ui.menu,name:account_asset.menu_action_asset_asset_report
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_purchase_tree
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Assets"
msgstr "Хөрөнгүүд"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_asset_asset_report
#: model:ir.model,name:account_asset.model_asset_asset_report
#: model:ir.ui.view,arch_db:account_asset.action_account_asset_report_graph
#: model:ir.ui.view,arch_db:account_asset.action_account_asset_report_pivot
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Assets Analysis"
msgstr "Хөрөнгийн судалгаа шинжилгээ"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_move_asset_depreciation_ids
msgid "Assets Depreciation Lines"
msgstr "Хөрөнгийн Элэгдлийн Мөрүүд"

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr "Хөрөнгө ба Орлого"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr "Хөрөнгийн хаалттай төлөв"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "Хөрөнгийн ноорог болон нээлттэй төлөв"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Assets in draft state"
msgstr "Хөрөнгийн ноорог төлөв"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Assets in running state"
msgstr "Хэрэглэгдэж байгаа төлөвтэй хөрөнгүүд"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_open_asset
msgid "Auto-confirm Assets"
msgstr "Хөрөнгийг Автомат Батлах"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid "Cancel"
msgstr "Цуцлах"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Category"
msgstr "Ангилал"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Category of asset"
msgstr "Хөрөнгийн ангилал"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_open_asset
msgid ""
"Check this if you want to automatically confirm the assets of this category "
"when created by invoices."
msgstr ""
"Энэ ангилалын хөрөнгүүд нь нэхэмжлэлээр үүсгэгдэхдээ автоматаар батлагдахаар"
" бол үүнийг тэмдэглэ."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_group_entries
msgid "Check this if you want to group the generated entries by categories."
msgstr ""
"Хэрэв үүссэн бичилтүүдийг хөрөнгийн ангилалаар бүлэглэхийг хүсвэл үүнийг "
"сонгоно."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Linear: Calculated on basis of: Gross Value / Number of Depreciations\n"
"  * Degressive: Calculated on basis of: Residual Value * Degressive Factor"
msgstr ""
"Элэгдүүлэлтийн мөрийг тооцоолох аргыг сонгоно уу.\n"
"  * Шугаман: Тооцоолох арга нь: Нийт үнэ / Элэгдүүлэх тоо\n"
"  * Бууруулах: Тооцоолох арга нь: Үлдэгдэл үнэ * Бууруулах үзүүлэлт"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method_time
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method_time
msgid ""
"Choose the method to use to compute the dates and number of entries.\n"
"  * Number of Entries: Fix the number of entries and the time between 2 depreciations.\n"
"  * Ending Date: Choose the time between 2 depreciations and the date the depreciations won't go beyond."
msgstr ""
"Хөрөнгийг ашиглах хугацааг тооцоолох аргыг сонгоно уу.\n"
"  * Элэгдэх тоо: Нийт хэдэн удаа элэгдэл байгуулах болон хэдий хугацаа тутамд нэг удаа элэгдэхийг тодорхойлно.\n"
"  * Дуусах огноо: Хэдий хугацаа тутамд нэг удаа элэгдэхийг тодорхойлох ба дуусах огноо өнгөртөл тогтмол элэгдэл байгуулна."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_depreciation_confirmation_wizard_date
msgid ""
"Choose the period for which you want to automatically post the depreciation "
"lines of running assets"
msgstr ""
"Хэрэглэгдэж байгаа хөрөнгүүдийн элэгдлийн мөрүүдийг автоматаар илгээх "
"мөчлөгийг сонго"

#. module: account_asset
#: selection:account.asset.asset,state:0 selection:asset.asset.report,state:0
msgid "Close"
msgstr "Хаах"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Closed"
msgstr "Хаагдсан"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_company_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_company_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_company_id
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Company"
msgstr "Компани"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method
msgid "Computation Method"
msgstr "Тооцоолох арга"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid "Compute Asset"
msgstr "Хөрөнгө тооцоолох"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Compute Depreciation"
msgstr "Элэгдэл тооцоолох"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Confirm"
msgstr "Батлах"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_depreciation_confirmation_wizard.py:20
#, python-format
msgid "Created Asset Moves"
msgstr "Үүсгэгдсэн Хөрөнгийн Хөдөлгөөнүүд"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_depreciation_confirmation_wizard.py:20
#, python-format
msgid "Created Revenue Moves"
msgstr "Орлогын Хөдөлгөөнүүд Үүсгэгдсэн."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_create_uid
msgid "Created by"
msgstr "Үүсгэгч"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_create_date
msgid "Created on"
msgstr "Үүсгэсэн"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_depreciated_value
msgid "Cumulative Depreciation"
msgstr "Хуримтлагдсан Элэгдэл"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:597
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_currency_id
#, python-format
msgid "Currency"
msgstr "Валют"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Current"
msgstr "Одоогийн"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_amount
msgid "Current Depreciation"
msgstr "Одоогийн элэгдэл"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_date
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_date
msgid "Date"
msgstr "Огноо"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Date of asset"
msgstr "Хөрөнгийн огноо"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Date of asset purchase"
msgstr "Хөрөнгө худалдан авсан огноо"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Date of depreciation"
msgstr "Элэгдлийн огноо"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Deferred Revenue Account"
msgstr "Хойшлогдсон Орлогын Данс"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_product_product_deferred_revenue_category_id
#: model:ir.model.fields,field_description:account_asset.field_product_template_deferred_revenue_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Deferred Revenue Type"
msgstr "Хойшлогдсон Орлогын Төрөл"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Deferred Revenues"
msgstr "Хойшлогдсон Орлогууд"

#. module: account_asset
#: selection:account.asset.asset,method:0
#: selection:account.asset.category,method:0
msgid "Degressive"
msgstr "Бууруулах"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_progress_factor
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_progress_factor
msgid "Degressive Factor"
msgstr "Буурах Үзүүлэлт"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation"
msgstr "Элэгдэл"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation Board"
msgstr "Элэгдлийн Самбар"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_depreciation_date
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_depreciation_date
msgid "Depreciation Date"
msgstr "Элэгдлийн огноо"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_depreciation_id
msgid "Depreciation Entries: Asset Account"
msgstr "Элэгдэлийн Бичилтүүд: Хөрөнгийн Данс"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_depreciation_expense_id
msgid "Depreciation Entries: Expense Account"
msgstr "Элэгдэлийн Бичилтүүд: Зардлын Данс"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_move_id
msgid "Depreciation Entry"
msgstr "Элэгдлийн Бичилт"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation Information"
msgstr "Элэгдлийн Мэдээлэл"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_depreciation_line_ids
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation Lines"
msgstr "Элэгдлийн мөрүүд"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Depreciation Method"
msgstr "Элэгдлийн арга"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Depreciation Month"
msgstr "Элэгдүүлэлтийн сар"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_name
msgid "Depreciation Name"
msgstr "Элэгдлийн Нэр"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:85
#, python-format
msgid "Depreciation board modified"
msgstr "Элэгдэлийн Самбар Засагдсан"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:600
#, python-format
msgid "Depreciation line posted."
msgstr "Элэгдэлийн мөр илгээгдсэн."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_display_name
msgid "Display Name"
msgstr "Дэлгэцийн Нэр"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:327
#, python-format
msgid "Disposal Move"
msgstr "Устгал Хөдөлгөөн"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:330
#, python-format
msgid "Disposal Moves"
msgstr "Устгал Хөдөлгөөн"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:579
#, python-format
msgid "Document closed."
msgstr "Баримт хаагдсан."

#. module: account_asset
#: selection:account.asset.asset,state:0 selection:asset.asset.report,state:0
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Draft"
msgstr "Ноорог"

#. module: account_asset
#: selection:account.asset.asset,method_time:0
#: selection:account.asset.category,method_time:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_end
msgid "Ending Date"
msgstr "Дуусах огноо"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_end
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_method_end
msgid "Ending date"
msgstr "Дуусах хугацаа"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Extended Filters..."
msgstr "Өргөтгөсөн Шүүлтүүр..."

#. module: account_asset
#: model:ir.actions.act_window,help:account_asset.action_asset_asset_report
msgid ""
"From this report, you can have an overview on all depreciations. The\n"
"            search bar can also be used to personalize your assets depreciation reporting."
msgstr ""
"Энэ тайлангаас, бүх элэгдлийн тоймыг ойлгох боломжтой. Хайлтын боломжийг "
"ашиглан тайлагналаа өөрийнхөөрөө өөрчилж болно."

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_asset_depreciation_confirmation_wizard
msgid "Generate Assets Entries"
msgstr "Хөрөнгийн бичилтүүдийг үүсгэх"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid "Generate Entries"
msgstr "Бичилтүүдийг үүсгэх"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_gross_value
msgid "Gross Amount"
msgstr "Нийт Дүн"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_value
msgid "Gross Value"
msgstr "Нийт Үнэ"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Gross value of asset"
msgstr "Хөрөнгийн нийт үнэ"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Group By"
msgstr "Бүлэглэх"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Group By..."
msgstr "Бүлэглэх..."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_group_entries
msgid "Group Journal Entries"
msgstr "Журналийн Бичилтүүдийн Бүлэг"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_id
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_id
msgid "ID"
msgstr "ID"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first January / Start date of fiscal "
"year"
msgstr ""
"Энэ нь хөрөнгийг худалдан авсан өдрөөс эхлэн элэгдүүлэхийг илэрхийлнэ. "
"Санхүүгийн жилийн эхлэлээс биш байна."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first of January"
msgstr ""
"Энэ нь хөрөнгийг худалдан авсан өдрөөс эхлэн элэгдүүлэхийг илэрхийлнэ. "
"Санхүүгийн жилийн эхлэлээс биш байна."

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_invoice
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_invoice_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Invoice"
msgstr "Нэхэмжлэл"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_invoice_line
msgid "Invoice Line"
msgstr "Нэхэмжлэлийн мөр"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_salvage_value
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr "Энэ нь таны төлөвлөж байгаа дүн буюу таны элэгдүүлж чадахгүй дүн"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Items"
msgstr "Зүйлс"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_journal_id
msgid "Journal"
msgstr "Журнал"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:438
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
#, python-format
msgid "Journal Entries"
msgstr "Журналийн бичилт"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset___last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category___last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line___last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report___last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard___last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_modify___last_update
msgid "Last Modified on"
msgstr "Сүүлийн зассан хийсэн огноо"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_write_uid
msgid "Last Updated by"
msgstr "Сүүлийн засвар хийсэн"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_write_date
msgid "Last Updated on"
msgstr "Сүүлийн засвар хийсэн огноо"

#. module: account_asset
#: selection:account.asset.asset,method:0
#: selection:account.asset.category,method:0
msgid "Linear"
msgstr "Шугаман"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_move_check
msgid "Linked"
msgstr "Холбогдсон"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify"
msgstr "Өөрчлөх"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_asset_modify
#: model:ir.model,name:account_asset.model_asset_modify
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify Asset"
msgstr "Хөрөнгийг өөрчлөх"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Modify Depreciation"
msgstr "Элэгдэл өөрчлөх"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Month"
msgstr "Сар"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_mrr
msgid "Monthly Recurring Revenue"
msgstr "Сар тутам давтагдах орлого"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_remaining_value
msgid "Next Period Depreciation"
msgstr "Элэгдүүлэлтийн дараагийн мөчлөг"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_note
msgid "Note"
msgstr "Тэмдэглэл"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_number
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_number
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_method_number
msgid "Number of Depreciations"
msgstr "Элэгдэх тоо"

#. module: account_asset
#: selection:account.asset.asset,method_time:0
#: selection:account.asset.category,method_time:0
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Number of Entries"
msgstr "Ашиглах хугацаа"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_period
msgid "Number of Months in a Period"
msgstr "Мөчлөг дахь сарын тоо"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "One Entry Every"
msgstr "Элэгдэл бодох давтамж"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:599
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_partner_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_partner_id
#, python-format
msgid "Partner"
msgstr "Харилцагч"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_period
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_method_period
msgid "Period Length"
msgstr "Мөчлөгийн урт"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Periodicity"
msgstr "Тогтмол хугацаат"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_asset_depreciation_confirmation_wizard
msgid "Post Depreciation Lines"
msgstr "Элэгдлийн мөрүүдийг илгээх"

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset.js:49
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_move_posted_check
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_move_check
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
#, python-format
msgid "Posted"
msgstr "Батлагдсан"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_posted_value
msgid "Posted Amount"
msgstr "Бичигдсэн дүн"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Posted depreciation lines"
msgstr "Элэгдлийн илгээгдсэн мөрүүд"

#. module: account_asset
#: model:ir.model,name:account_asset.model_product_template
msgid "Product Template"
msgstr "Бараа"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_prorata
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_prorata
msgid "Prorata Temporis"
msgstr "Prorata Temporis"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:370
#, python-format
msgid ""
"Prorata temporis can be applied only for time method \"number of "
"depreciations\"."
msgstr ""
"Prorata temporis нь зөвхөн \"элэгдүүлэлтийн тоо\" хугацааны арга сонгогдсон "
"үед хэрэглэгдэх боломжтой."

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Purchase"
msgstr "Худалдан авалт"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Purchase Month"
msgstr "Худалдан авалтын сар"

#. module: account_asset
#: selection:account.asset.category,type:0
msgid "Purchase: Asset"
msgstr "Худалдан авалт: Хөрөнгө"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_name
msgid "Reason"
msgstr "Шалтгаан"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Recognition Account"
msgstr "Таних Данс"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Recognition Income Account"
msgstr "Орлогон Таних Данс"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_code
msgid "Reference"
msgstr "Дугаар"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Residual"
msgstr "Үлдэгдэл"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_value_residual
msgid "Residual Value"
msgstr "Үлдэгдэл дүн"

#. module: account_asset
#: selection:account.asset.asset,state:0 selection:asset.asset.report,state:0
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Running"
msgstr "Ажиллаж буй"

#. module: account_asset
#: selection:account.asset.category,type:0
msgid "Sale: Revenue Recognition"
msgstr "Борлуулалт: Орлого Таних"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Sales"
msgstr "Борлуулалт"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_salvage_value
msgid "Salvage Value"
msgstr "Үлдэх өртөг"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Search Asset Category"
msgstr "Хөрөнгийн ангилалаар хайх"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Sell or Dispose"
msgstr "Зарах эсвэл Устгах"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_sequence
msgid "Sequence"
msgstr "Дараалал"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Set to Draft"
msgstr "Ноорог Болгох"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method_period
msgid "State here the time between 2 depreciations, in months"
msgstr "Энд 2 элэгдлийн хоорондын хугацааг сараар тодорхойлно"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_parent_state
msgid "State of Asset"
msgstr "Хөрөнгийн төлөв"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_state
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_state
msgid "Status"
msgstr "Төлөв"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method_period
msgid "The amount of time between two depreciations, in months"
msgstr "Хоёр элэгдлийн хоорондын хугацаа сараар"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method_number
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr "Хөрөнгийг элэгдүүлэхэд шаардлагатай элэгдүүлэлтийн тоо"

#. module: account_asset
#: code:addons/account_asset/models/account_invoice.py:62
#, python-format
msgid ""
"The number of depreciations or the period length of your asset category "
"cannot be null."
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:482
#, python-format
msgid ""
"This depreciation is already linked to a journal entry! Please post or "
"delete it."
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid ""
"This wizard will post installment/depreciation lines for the selected month.<br/>\n"
"                        This will generate journal entries for all related installment lines on this period of asset/revenue recognition as well."
msgstr ""
"Энэ харилцах цонх нь сонгосон сарын суурилуулалт/элэгдэлийн мөрүүдийг илгээнэ.<br/>\n"
"                        Энэ нь энэ мөчлөгийн хөрөнгө/орлого танилтын бүх суурилуулалтын журналын бичилтүүдийг үүсгэнэ."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_time
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_time
msgid "Time Method"
msgstr "Хугацааны арга"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Time Method Based On"
msgstr "Хугацааны Аргын Суурь"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_type
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Type"
msgstr "Төрөл"

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset.js:55
#, python-format
msgid "Unposted"
msgstr "Батлагдаагүй"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_unposted_value
msgid "Unposted Amount"
msgstr "Илгээгдээгүй дүн"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_purchase_tree
msgid "Vendor"
msgstr "Нийлүүлэгч"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_state
#: model:ir.model.fields,help:account_asset.field_account_asset_depreciation_line_parent_state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"You can manually close an asset when the depreciation is over. If the last line of depreciation is posted, the asset automatically goes in that status."
msgstr ""
"Хөрөнгийг үүсгэсэн даруйд \"Ноорог\" төлөвтэй байна.\n"
"Баталсан дараа \"Хэрэглэгдэж буй\" төлөвтэй болж санхүүд элэгдлийн бичилтүүд илгээгдэнэ.\n"
"Элэгдэж дууссан дараа хөрөнгийг гараараа хаах боломжтой. Хамгийн сүүлийн элэгдүүлэлт хийгдмэгц хөрөнгө автоматаар хаагдсан төлөвт орно."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_name
msgid "Year"
msgstr "Жил"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:111
#, python-format
msgid "You cannot delete a document is in %s state."
msgstr "%s төлөвт байгаа баримтыг устгах боломжгүй."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:114
#, python-format
msgid "You cannot delete a document that contains posted entries."
msgstr "Илгээгдсэн бичилттэй баримтыг устгах боломжгүй."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:608
#, python-format
msgid "You cannot delete posted depreciation lines."
msgstr "Илгээгдсэн элэгдлийн мөрүүдийг устгах боломжгүй."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:610
#, python-format
msgid "You cannot delete posted installment lines."
msgstr "Илгээгдсэн суурилуулалтын мөрүүдийг устгах боломжгүй."

#. module: account_asset
#: model:ir.model,name:account_asset.model_asset_depreciation_confirmation_wizard
msgid "asset.depreciation.confirmation.wizard"
msgstr "asset.depreciation.confirmation.wizard"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "e.g. Computers"
msgstr "ж. Компьютер"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "e.g. Laptop iBook"
msgstr "ж. Лаптоп iBook"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "months"
msgstr "сарууд"
