# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_asset
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
# <PERSON>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-30 13:11+0000\n"
"PO-Revision-Date: 2017-09-20 10:14+0000\n"
"Last-Translator: JH CHOI <<EMAIL>>, 2019\n"
"Language-Team: Korean (https://www.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:404
#, python-format
msgid " (copy)"
msgstr " (복사)"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:540
#, python-format
msgid " (grouped)"
msgstr " (그룹화됨)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_entry_count
msgid "# Asset Entries"
msgstr "자산 항목 수"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_depreciation_nbr
msgid "# of Depreciation Lines"
msgstr "감가상각 라인 수"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_installment_nbr
msgid "# of Installment Lines"
msgstr "할부 라인 수"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.account_asset_cron_ir_actions_server
#: model:ir.cron,cron_name:account_asset.account_asset_cron
#: model:ir.cron,name:account_asset.account_asset_cron
msgid "Account Asset: Generate asset entries"
msgstr "계정 자산: 자산 항목 생성"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_date
msgid "Account Date"
msgstr "계정 날짜"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move
msgid "Account Entry"
msgstr "계정 항목"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr "자산 가치를 낮추기 위해 감가상각 기입에 사용하는 계정입니다."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr "자산의 일부를 경비로 기록하기 위해 정기 항목에 사용하는 계정입니다."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr "자산의 정가 구매를 기록하는 데 사용하는 계정입니다."

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset.js:52
#, python-format
msgid "Accounting entries waiting for manual verification"
msgstr "수동 검증을 기다리는 회계 항목"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_active
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_active
msgid "Active"
msgstr "활성"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Additional Options"
msgstr "추가 옵션"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:597
#, python-format
msgid "Amount"
msgstr "금액"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_depreciation_value
msgid "Amount of Depreciation Lines"
msgstr "감가상각 라인 금액"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_installment_value
msgid "Amount of Installment Lines"
msgstr "할부 라인 금액"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_analytic_id
msgid "Analytic Account"
msgstr "분석 계정"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_asset_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_asset_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Asset"
msgstr "자산"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_asset_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset Account"
msgstr "자산 계정"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
#: model:ir.ui.view,arch_db:account_asset.view_invoice_asset_category
msgid "Asset Category"
msgstr "자산 분류"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Asset Durations to Modify"
msgstr "수정할 자산 듀레이션"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_end_date
msgid "Asset End Date"
msgstr "자산 마감일"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_asset_method_time
msgid "Asset Method Time"
msgstr "자산법 기간"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_name
msgid "Asset Name"
msgstr "자산 이름"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_start_date
msgid "Asset Start Date"
msgstr "자산 시작일"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_name
#: model:ir.model.fields,field_description:account_asset.field_product_product_asset_category_id
#: model:ir.model.fields,field_description:account_asset.field_product_template_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Asset Type"
msgstr "자산 유형"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_asset_list_normal_purchase
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_asset_list_normal_purchase
#: model:ir.ui.view,arch_db:account_asset.res_config_settings_view_form
msgid "Asset Types"
msgstr "자산 유형"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_category
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_tree
msgid "Asset category"
msgstr "자산 분류"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:286
#, python-format
msgid "Asset created"
msgstr "생성된 자산"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_depreciation_line
msgid "Asset depreciation line"
msgstr "자산 감가상각 라인"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:318
#, python-format
msgid "Asset sold or disposed. Accounting entry awaiting for validation."
msgstr "매각/처분된 자산. 검증을 기다리는 회계 항목입니다."

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_asset
msgid "Asset/Revenue Recognition"
msgstr "자산/수익 인식"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_asset_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_asset_form
#: model:ir.ui.menu,name:account_asset.menu_action_asset_asset_report
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_purchase_tree
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Assets"
msgstr "자산"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_asset_asset_report
#: model:ir.model,name:account_asset.model_asset_asset_report
#: model:ir.ui.view,arch_db:account_asset.action_account_asset_report_graph
#: model:ir.ui.view,arch_db:account_asset.action_account_asset_report_pivot
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Assets Analysis"
msgstr "자산 분석"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_move_asset_depreciation_ids
msgid "Assets Depreciation Lines"
msgstr "자산 감가상각 라인"

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr "자산 및 수익"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr "마감된 상태의 자산"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "초안 및 개시된 상태의 자산"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Assets in draft state"
msgstr "초안 상태의 자산"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Assets in running state"
msgstr "운영 상태의 자산"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_open_asset
msgid "Auto-confirm Assets"
msgstr "자산 자동 확인"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid "Cancel"
msgstr "취소"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Category"
msgstr "분류"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Category of asset"
msgstr "자산 분류"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_open_asset
msgid ""
"Check this if you want to automatically confirm the assets of this category "
"when created by invoices."
msgstr "청구서를 생성할 때 이 카테고리의 자산을 자동 확인하려면 여기에 표시합니다."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_group_entries
msgid "Check this if you want to group the generated entries by categories."
msgstr "생성된 항목을 카테고리별로 그룹화하려면 여기에 표시합니다."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Linear: Calculated on basis of: Gross Value / Number of Depreciations\n"
"  * Degressive: Calculated on basis of: Residual Value * Degressive Factor"
msgstr ""
"감가상각 라인 금액을 계산할 때 사용할 방법을 선택합니다.\n"
"  * 선형: 계산 기준: 총 가치 / 감가상각 횟수\n"
"  * 체감: 계산 기준: 잔존 가치 * 체감 계수"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method_time
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method_time
msgid ""
"Choose the method to use to compute the dates and number of entries.\n"
"  * Number of Entries: Fix the number of entries and the time between 2 depreciations.\n"
"  * Ending Date: Choose the time between 2 depreciations and the date the depreciations won't go beyond."
msgstr ""
"항목의 날짜와 개수를 계산하는 데 사용할 방법을 선택합니다.\n"
"  * 항목 개수: 두 감가상각 사이의 항목 개수와 기간을 고정합니다.\n"
"  * 마감일: 두 감가상각 사이의 기간과 감가상각이 종료되는 날짜를 선택합니다."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_depreciation_confirmation_wizard_date
msgid ""
"Choose the period for which you want to automatically post the depreciation "
"lines of running assets"
msgstr "운영 자산의 감가상각 라인을 자동 전기하고자 하는 기간을 선택합니다."

#. module: account_asset
#: selection:account.asset.asset,state:0 selection:asset.asset.report,state:0
msgid "Close"
msgstr "마감"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Closed"
msgstr "마감됨"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_company_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_company_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_company_id
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Company"
msgstr "회사"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method
msgid "Computation Method"
msgstr "계산법"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid "Compute Asset"
msgstr "자산 계산"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Compute Depreciation"
msgstr "감가상각 계산"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Confirm"
msgstr "확인"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_depreciation_confirmation_wizard.py:20
#, python-format
msgid "Created Asset Moves"
msgstr "생성된 자산 이동"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_depreciation_confirmation_wizard.py:20
#, python-format
msgid "Created Revenue Moves"
msgstr "생성된 수익 이동"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_create_uid
msgid "Created by"
msgstr "작성자"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_create_date
msgid "Created on"
msgstr "작성일"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_depreciated_value
msgid "Cumulative Depreciation"
msgstr "누적 감가상각"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:597
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_currency_id
#, python-format
msgid "Currency"
msgstr "통화"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Current"
msgstr "당기"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_amount
msgid "Current Depreciation"
msgstr "당기 감가상각"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_date
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_date
msgid "Date"
msgstr "날짜"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Date of asset"
msgstr "자산 날짜"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Date of asset purchase"
msgstr "자산 매입일"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Date of depreciation"
msgstr "감가상각일"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Deferred Revenue Account"
msgstr "이연 수익 계정"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_product_product_deferred_revenue_category_id
#: model:ir.model.fields,field_description:account_asset.field_product_template_deferred_revenue_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Deferred Revenue Type"
msgstr "이연 수익 유형"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Deferred Revenues"
msgstr "이연 수익"

#. module: account_asset
#: selection:account.asset.asset,method:0
#: selection:account.asset.category,method:0
msgid "Degressive"
msgstr "체감"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_progress_factor
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_progress_factor
msgid "Degressive Factor"
msgstr "체감 계수"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation"
msgstr "감가상각"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation Board"
msgstr "감가상각 기록"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_depreciation_date
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_depreciation_date
msgid "Depreciation Date"
msgstr "감가상각일"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_depreciation_id
msgid "Depreciation Entries: Asset Account"
msgstr "감가상각 항목: 자산 계정"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_depreciation_expense_id
msgid "Depreciation Entries: Expense Account"
msgstr "감가상각 항목: 경비 계정"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_move_id
msgid "Depreciation Entry"
msgstr "감가상각 항목"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation Information"
msgstr "감가상각 정보"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_depreciation_line_ids
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation Lines"
msgstr "감가상각 라인"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Depreciation Method"
msgstr "감가상각법"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Depreciation Month"
msgstr "감가상각 월"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_name
msgid "Depreciation Name"
msgstr "감가상각 이름"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:85
#, python-format
msgid "Depreciation board modified"
msgstr "수정된 감가상각 기록"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:600
#, python-format
msgid "Depreciation line posted."
msgstr "전기된 감가상각 라인입니다."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_display_name
msgid "Display Name"
msgstr "표시 이름"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:327
#, python-format
msgid "Disposal Move"
msgstr "처분 이동"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:330
#, python-format
msgid "Disposal Moves"
msgstr "처분 이동"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:579
#, python-format
msgid "Document closed."
msgstr "문서가 마감되었습니다."

#. module: account_asset
#: selection:account.asset.asset,state:0 selection:asset.asset.report,state:0
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Draft"
msgstr "초안"

#. module: account_asset
#: selection:account.asset.asset,method_time:0
#: selection:account.asset.category,method_time:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_end
msgid "Ending Date"
msgstr "마감일"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_end
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_method_end
msgid "Ending date"
msgstr "마감일"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Extended Filters..."
msgstr "확장 필터..."

#. module: account_asset
#: model:ir.actions.act_window,help:account_asset.action_asset_asset_report
msgid ""
"From this report, you can have an overview on all depreciations. The\n"
"            search bar can also be used to personalize your assets depreciation reporting."
msgstr ""
"이 보고서에서 모든 감가상각의 개요를 확인할 수 있습니다.\n"
"            검색 막대를 사용하여 자산 감가상각 보고를 개인화할 수 있습니다."

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_asset_depreciation_confirmation_wizard
msgid "Generate Assets Entries"
msgstr "자산 항목 생성"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid "Generate Entries"
msgstr "항목 생성"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_gross_value
msgid "Gross Amount"
msgstr "총액"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_value
msgid "Gross Value"
msgstr "총 가치"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Gross value of asset"
msgstr "총 자산 가치"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Group By"
msgstr "그룹화"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Group By..."
msgstr "그룹화..."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_group_entries
msgid "Group Journal Entries"
msgstr "분개 그룹화"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_id
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_id
msgid "ID"
msgstr "ID"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first January / Start date of fiscal "
"year"
msgstr "이 자산의 첫 번째 감가상각이 회계연도 시작/1월 1일이 아니라 매입일에 분개되었음을 나타냅니다."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first of January"
msgstr "이 자산의 첫 번째 감가상각이 1월 1일이 아니라 매입일에 분개되었음을 나타냅니다."

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_invoice
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_invoice_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Invoice"
msgstr "청구서"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_invoice_line
msgid "Invoice Line"
msgstr "청구서 라인"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_salvage_value
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr "감가상각할 수 없는 금액입니다."

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Items"
msgstr "항목"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_journal_id
msgid "Journal"
msgstr "분개"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:438
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
#, python-format
msgid "Journal Entries"
msgstr "분개 기재 항목"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset___last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category___last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line___last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report___last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard___last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_modify___last_update
msgid "Last Modified on"
msgstr "마지막 수정일"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_write_uid
msgid "Last Updated by"
msgstr "마지막으로 업데이트한 사람"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_write_date
msgid "Last Updated on"
msgstr "마지막 업데이트 날짜"

#. module: account_asset
#: selection:account.asset.asset,method:0
#: selection:account.asset.category,method:0
msgid "Linear"
msgstr "선형"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_move_check
msgid "Linked"
msgstr "연결됨"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify"
msgstr "수정"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_asset_modify
#: model:ir.model,name:account_asset.model_asset_modify
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify Asset"
msgstr "자산 수정"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Modify Depreciation"
msgstr "감가상각 수정"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Month"
msgstr "월"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_mrr
msgid "Monthly Recurring Revenue"
msgstr "월별 반복 수입"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_remaining_value
msgid "Next Period Depreciation"
msgstr "차기 감가상각"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_note
msgid "Note"
msgstr "메모"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_number
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_number
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_method_number
msgid "Number of Depreciations"
msgstr "감가상각 횟수"

#. module: account_asset
#: selection:account.asset.asset,method_time:0
#: selection:account.asset.category,method_time:0
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Number of Entries"
msgstr "항목 개수"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_period
msgid "Number of Months in a Period"
msgstr "기간 내 개월 수"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "One Entry Every"
msgstr "항목 당"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:599
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_partner_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_partner_id
#, python-format
msgid "Partner"
msgstr "협력사"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_period
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_method_period
msgid "Period Length"
msgstr "기간"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Periodicity"
msgstr "주기"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_asset_depreciation_confirmation_wizard
msgid "Post Depreciation Lines"
msgstr "감가상각 라인 전기"

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset.js:49
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_move_posted_check
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_move_check
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
#, python-format
msgid "Posted"
msgstr "전기됨"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_posted_value
msgid "Posted Amount"
msgstr "전기된 금액"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Posted depreciation lines"
msgstr "전기된 감가상각 라인"

#. module: account_asset
#: model:ir.model,name:account_asset.model_product_template
msgid "Product Template"
msgstr "제품 템플릿"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_prorata
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_prorata
msgid "Prorata Temporis"
msgstr "비례 보상 원칙"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:370
#, python-format
msgid ""
"Prorata temporis can be applied only for time method \"number of "
"depreciations\"."
msgstr "비례 보상 원칙은 \"감가상각 횟수\\” 시간법에만 적용됩니다."

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Purchase"
msgstr "매입"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Purchase Month"
msgstr "매입월"

#. module: account_asset
#: selection:account.asset.category,type:0
msgid "Purchase: Asset"
msgstr "매입: 자산"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_name
msgid "Reason"
msgstr "사유"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Recognition Account"
msgstr "인식 계정"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Recognition Income Account"
msgstr "인식 수입 계정"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_code
msgid "Reference"
msgstr "참조"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Residual"
msgstr "잔존"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_value_residual
msgid "Residual Value"
msgstr "잔존 가치"

#. module: account_asset
#: selection:account.asset.asset,state:0 selection:asset.asset.report,state:0
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Running"
msgstr "운영"

#. module: account_asset
#: selection:account.asset.category,type:0
msgid "Sale: Revenue Recognition"
msgstr "매출: 수익 인식"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Sales"
msgstr "매출"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_salvage_value
msgid "Salvage Value"
msgstr "잔존 가치"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Search Asset Category"
msgstr "자산 분류 검색"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Sell or Dispose"
msgstr "매각 또는 처분"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_sequence
msgid "Sequence"
msgstr "순서"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Set to Draft"
msgstr "초안으로 설정"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method_period
msgid "State here the time between 2 depreciations, in months"
msgstr "여기에 두 감가상각 사이의 기간(월)을 표시합니다."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_parent_state
msgid "State of Asset"
msgstr "자산 상태"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_state
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_state
msgid "Status"
msgstr "상태"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method_period
msgid "The amount of time between two depreciations, in months"
msgstr "두 감가상각 사이의 기간(월)"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method_number
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr "자산을 감각상각해야 하는 횟수"

#. module: account_asset
#: code:addons/account_asset/models/account_invoice.py:62
#, python-format
msgid ""
"The number of depreciations or the period length of your asset category "
"cannot be null."
msgstr "자산 분류가 null이 될 수 없는 감가상각 횟수 또는 기간입니다."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:482
#, python-format
msgid ""
"This depreciation is already linked to a journal entry! Please post or "
"delete it."
msgstr "이 감가상각은 이미 분개에 연결되어 있습니다! 전기하거나 삭제해주십시오."

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid ""
"This wizard will post installment/depreciation lines for the selected month.<br/>\n"
"                        This will generate journal entries for all related installment lines on this period of asset/revenue recognition as well."
msgstr ""
"이 마법사는 선택한 월의 할부/감가상각을 전기합니다.<br/>\n"
"                        자산/수익 인식 기간의 모든 관련 할부 라인에 대한 분개도 생성됩니다."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_time
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_time
msgid "Time Method"
msgstr "시간법"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Time Method Based On"
msgstr "시간법 기준"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_type
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Type"
msgstr "유형"

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset.js:55
#, python-format
msgid "Unposted"
msgstr "전기되지 않음"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_unposted_value
msgid "Unposted Amount"
msgstr "전기되지 않은 금액"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_purchase_tree
msgid "Vendor"
msgstr "공급업체"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_state
#: model:ir.model.fields,help:account_asset.field_account_asset_depreciation_line_parent_state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"You can manually close an asset when the depreciation is over. If the last line of depreciation is posted, the asset automatically goes in that status."
msgstr ""
"자산을 생성할 때의 상태는 ‘초안’입니다.\n"
"자산이 확인되면 상태가 ‘운영’으로 변경되고 감가상각 라인이 회계 장부에 전기됩니다.\n"
"감가상각이 끝나면 자산을 직접 마감할 수 있습니다. 감가상각의 마지막 라인이 전기되면, 자산이 해당 상태로 자동 변경됩니다."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_name
msgid "Year"
msgstr "년"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:111
#, python-format
msgid "You cannot delete a document is in %s state."
msgstr "%s 상태의 문서는 삭제할 수 없습니다."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:114
#, python-format
msgid "You cannot delete a document that contains posted entries."
msgstr "전기된 항목이 포함된 문서는 삭제할 수 없습니다."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:608
#, python-format
msgid "You cannot delete posted depreciation lines."
msgstr "전기된 감가상각 라인을 삭제할 수 없습니다."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:610
#, python-format
msgid "You cannot delete posted installment lines."
msgstr "전기된 할부 라인을 삭제할 수 없습니다."

#. module: account_asset
#: model:ir.model,name:account_asset.model_asset_depreciation_confirmation_wizard
msgid "asset.depreciation.confirmation.wizard"
msgstr "asset.depreciation.confirmation.wizard"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "e.g. Computers"
msgstr "예) 컴퓨터"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "e.g. Laptop iBook"
msgstr "예) 노트북, 아이북"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "months"
msgstr "월"
