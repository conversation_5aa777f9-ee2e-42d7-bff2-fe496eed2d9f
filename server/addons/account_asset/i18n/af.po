# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_asset
# 
# Translators:
# <PERSON>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-30 13:11+0000\n"
"PO-Revision-Date: 2017-11-30 13:11+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Afrikaans (https://www.transifex.com/odoo/teams/41243/af/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: af\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:404
#, python-format
msgid " (copy)"
msgstr "(kopie)"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:540
#, python-format
msgid " (grouped)"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_entry_count
msgid "# Asset Entries"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_depreciation_nbr
msgid "# of Depreciation Lines"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_installment_nbr
msgid "# of Installment Lines"
msgstr ""

#. module: account_asset
#: model:ir.actions.server,name:account_asset.account_asset_cron_ir_actions_server
#: model:ir.cron,cron_name:account_asset.account_asset_cron
#: model:ir.cron,name:account_asset.account_asset_cron
msgid "Account Asset: Generate asset entries"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_date
msgid "Account Date"
msgstr ""

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move
msgid "Account Entry"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr ""

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset.js:52
#, python-format
msgid "Accounting entries waiting for manual verification"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_active
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_active
msgid "Active"
msgstr "Aktief"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Additional Options"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:597
#, python-format
msgid "Amount"
msgstr "Bedrag"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_depreciation_value
msgid "Amount of Depreciation Lines"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_installment_value
msgid "Amount of Installment Lines"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_analytic_id
msgid "Analytic Account"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_asset_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_asset_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Asset"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_asset_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset Account"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
#: model:ir.ui.view,arch_db:account_asset.view_invoice_asset_category
msgid "Asset Category"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Asset Durations to Modify"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_end_date
msgid "Asset End Date"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_asset_method_time
msgid "Asset Method Time"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_name
msgid "Asset Name"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_start_date
msgid "Asset Start Date"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_name
#: model:ir.model.fields,field_description:account_asset.field_product_product_asset_category_id
#: model:ir.model.fields,field_description:account_asset.field_product_template_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Asset Type"
msgstr ""

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_asset_list_normal_purchase
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_asset_list_normal_purchase
#: model:ir.ui.view,arch_db:account_asset.res_config_settings_view_form
msgid "Asset Types"
msgstr ""

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_category
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_tree
msgid "Asset category"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:286
#, python-format
msgid "Asset created"
msgstr ""

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_depreciation_line
msgid "Asset depreciation line"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:318
#, python-format
msgid "Asset sold or disposed. Accounting entry awaiting for validation."
msgstr ""

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_asset
msgid "Asset/Revenue Recognition"
msgstr ""

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_asset_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_asset_form
#: model:ir.ui.menu,name:account_asset.menu_action_asset_asset_report
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_purchase_tree
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Assets"
msgstr ""

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_asset_asset_report
#: model:ir.model,name:account_asset.model_asset_asset_report
#: model:ir.ui.view,arch_db:account_asset.action_account_asset_report_graph
#: model:ir.ui.view,arch_db:account_asset.action_account_asset_report_pivot
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Assets Analysis"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_move_asset_depreciation_ids
msgid "Assets Depreciation Lines"
msgstr ""

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Assets in draft state"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Assets in running state"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_open_asset
msgid "Auto-confirm Assets"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid "Cancel"
msgstr "Kanselleer"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Category"
msgstr "Kategorie"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Category of asset"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_open_asset
msgid ""
"Check this if you want to automatically confirm the assets of this category "
"when created by invoices."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_group_entries
msgid "Check this if you want to group the generated entries by categories."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Linear: Calculated on basis of: Gross Value / Number of Depreciations\n"
"  * Degressive: Calculated on basis of: Residual Value * Degressive Factor"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method_time
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method_time
msgid ""
"Choose the method to use to compute the dates and number of entries.\n"
"  * Number of Entries: Fix the number of entries and the time between 2 depreciations.\n"
"  * Ending Date: Choose the time between 2 depreciations and the date the depreciations won't go beyond."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_depreciation_confirmation_wizard_date
msgid ""
"Choose the period for which you want to automatically post the depreciation "
"lines of running assets"
msgstr ""

#. module: account_asset
#: selection:account.asset.asset,state:0 selection:asset.asset.report,state:0
msgid "Close"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Closed"
msgstr "Gesluit"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_company_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_company_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_company_id
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Company"
msgstr "Maatskappy"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method
msgid "Computation Method"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid "Compute Asset"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Compute Depreciation"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Confirm"
msgstr "Bevestig"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_depreciation_confirmation_wizard.py:20
#, python-format
msgid "Created Asset Moves"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/wizard/asset_depreciation_confirmation_wizard.py:20
#, python-format
msgid "Created Revenue Moves"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_create_uid
msgid "Created by"
msgstr "Geskep deur"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_create_date
msgid "Created on"
msgstr "Geskep op"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_depreciated_value
msgid "Cumulative Depreciation"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:597
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_currency_id
#, python-format
msgid "Currency"
msgstr "Geldeenheid"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Current"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_amount
msgid "Current Depreciation"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_date
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_date
msgid "Date"
msgstr "Datum"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Date of asset"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Date of asset purchase"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Date of depreciation"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Deferred Revenue Account"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_product_product_deferred_revenue_category_id
#: model:ir.model.fields,field_description:account_asset.field_product_template_deferred_revenue_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Deferred Revenue Type"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Deferred Revenues"
msgstr ""

#. module: account_asset
#: selection:account.asset.asset,method:0
#: selection:account.asset.category,method:0
msgid "Degressive"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_progress_factor
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_progress_factor
msgid "Degressive Factor"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation Board"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_depreciation_date
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_depreciation_date
msgid "Depreciation Date"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_depreciation_id
msgid "Depreciation Entries: Asset Account"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_depreciation_expense_id
msgid "Depreciation Entries: Expense Account"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_move_id
msgid "Depreciation Entry"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation Information"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_depreciation_line_ids
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation Lines"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Depreciation Method"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Depreciation Month"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_name
msgid "Depreciation Name"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:85
#, python-format
msgid "Depreciation board modified"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:600
#, python-format
msgid "Depreciation line posted."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_display_name
msgid "Display Name"
msgstr "Vertoningsnaam"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:327
#, python-format
msgid "Disposal Move"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:330
#, python-format
msgid "Disposal Moves"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:579
#, python-format
msgid "Document closed."
msgstr ""

#. module: account_asset
#: selection:account.asset.asset,state:0 selection:asset.asset.report,state:0
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Draft"
msgstr "Konsep"

#. module: account_asset
#: selection:account.asset.asset,method_time:0
#: selection:account.asset.category,method_time:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_end
msgid "Ending Date"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_end
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_method_end
msgid "Ending date"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Extended Filters..."
msgstr "Uitgebreide filters..."

#. module: account_asset
#: model:ir.actions.act_window,help:account_asset.action_asset_asset_report
msgid ""
"From this report, you can have an overview on all depreciations. The\n"
"            search bar can also be used to personalize your assets depreciation reporting."
msgstr ""

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_asset_depreciation_confirmation_wizard
msgid "Generate Assets Entries"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid "Generate Entries"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_gross_value
msgid "Gross Amount"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_value
msgid "Gross Value"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Gross value of asset"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Group By"
msgstr "Groepeer deur"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Group By..."
msgstr "Groepeer op ..."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_group_entries
msgid "Group Journal Entries"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_id
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_id
msgid "ID"
msgstr "ID"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first January / Start date of fiscal "
"year"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first of January"
msgstr ""

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_invoice
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_invoice_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Invoice"
msgstr "Faktuur"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_invoice_line
msgid "Invoice Line"
msgstr "Faktuur Lyn"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_salvage_value
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Items"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_journal_id
msgid "Journal"
msgstr "Joernaal"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:438
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
#, python-format
msgid "Journal Entries"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset___last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category___last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line___last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report___last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard___last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_modify___last_update
msgid "Last Modified on"
msgstr "Laas Gewysig op"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_write_uid
msgid "Last Updated by"
msgstr "Laas Opgedateer deur"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_write_date
msgid "Last Updated on"
msgstr "Laas Opgedateer op"

#. module: account_asset
#: selection:account.asset.asset,method:0
#: selection:account.asset.category,method:0
msgid "Linear"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_move_check
msgid "Linked"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify"
msgstr "Wysig"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_asset_modify
#: model:ir.model,name:account_asset.model_asset_modify
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify Asset"
msgstr "Wysig Bate"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Modify Depreciation"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Month"
msgstr "Maand"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_mrr
msgid "Monthly Recurring Revenue"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_remaining_value
msgid "Next Period Depreciation"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_note
msgid "Note"
msgstr "Nota"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_number
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_number
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_method_number
msgid "Number of Depreciations"
msgstr ""

#. module: account_asset
#: selection:account.asset.asset,method_time:0
#: selection:account.asset.category,method_time:0
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Number of Entries"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_period
msgid "Number of Months in a Period"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "One Entry Every"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:599
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_partner_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_partner_id
#, python-format
msgid "Partner"
msgstr "Vennoot"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_period
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_method_period
msgid "Period Length"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Periodicity"
msgstr ""

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_asset_depreciation_confirmation_wizard
msgid "Post Depreciation Lines"
msgstr ""

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset.js:49
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_move_posted_check
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_move_check
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
#, python-format
msgid "Posted"
msgstr "Gepos"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_posted_value
msgid "Posted Amount"
msgstr "Geposte Bedrag"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Posted depreciation lines"
msgstr ""

#. module: account_asset
#: model:ir.model,name:account_asset.model_product_template
msgid "Product Template"
msgstr "Produk Profielvorm"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_prorata
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_prorata
msgid "Prorata Temporis"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:370
#, python-format
msgid ""
"Prorata temporis can be applied only for time method \"number of "
"depreciations\"."
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Purchase"
msgstr "Aankoop"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Purchase Month"
msgstr ""

#. module: account_asset
#: selection:account.asset.category,type:0
msgid "Purchase: Asset"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_name
msgid "Reason"
msgstr "Rede"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Recognition Account"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Recognition Income Account"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_code
msgid "Reference"
msgstr "Verwysing"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Residual"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_value_residual
msgid "Residual Value"
msgstr ""

#. module: account_asset
#: selection:account.asset.asset,state:0 selection:asset.asset.report,state:0
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Running"
msgstr ""

#. module: account_asset
#: selection:account.asset.category,type:0
msgid "Sale: Revenue Recognition"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Sales"
msgstr "Verkope"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_salvage_value
msgid "Salvage Value"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Search Asset Category"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Sell or Dispose"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_sequence
msgid "Sequence"
msgstr "Volgorde"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Set to Draft"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method_period
msgid "State here the time between 2 depreciations, in months"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_parent_state
msgid "State of Asset"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_state
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_state
msgid "Status"
msgstr "Stand"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method_period
msgid "The amount of time between two depreciations, in months"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method_number
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_invoice.py:62
#, python-format
msgid ""
"The number of depreciations or the period length of your asset category "
"cannot be null."
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:482
#, python-format
msgid ""
"This depreciation is already linked to a journal entry! Please post or "
"delete it."
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid ""
"This wizard will post installment/depreciation lines for the selected month.<br/>\n"
"                        This will generate journal entries for all related installment lines on this period of asset/revenue recognition as well."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_time
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_time
msgid "Time Method"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Time Method Based On"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_type
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Type"
msgstr "Soort"

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset.js:55
#, python-format
msgid "Unposted"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_unposted_value
msgid "Unposted Amount"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_purchase_tree
msgid "Vendor"
msgstr "Verkoper"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_state
#: model:ir.model.fields,help:account_asset.field_account_asset_depreciation_line_parent_state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"You can manually close an asset when the depreciation is over. If the last line of depreciation is posted, the asset automatically goes in that status."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_name
msgid "Year"
msgstr "Jaar"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:111
#, python-format
msgid "You cannot delete a document is in %s state."
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:114
#, python-format
msgid "You cannot delete a document that contains posted entries."
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:608
#, python-format
msgid "You cannot delete posted depreciation lines."
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:610
#, python-format
msgid "You cannot delete posted installment lines."
msgstr ""

#. module: account_asset
#: model:ir.model,name:account_asset.model_asset_depreciation_confirmation_wizard
msgid "asset.depreciation.confirmation.wizard"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "e.g. Computers"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "e.g. Laptop iBook"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "months"
msgstr ""
