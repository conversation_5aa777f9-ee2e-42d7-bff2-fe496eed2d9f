-
    Creating demo data that require looking for accounts
-
    !python {model: ir.model.data, id: False} : |
        company = self.env.user.company_id
        journal_id = self.env['account.journal'].search([('type', '=', 'general'), ('id', '!=', company.currency_exchange_journal_id.id)], limit=1).id
        expense_account_id = self.env['account.account'].search([('user_type_id', '=', ref('account.data_account_type_expenses'))], limit=1).id
        xfa_account_id = self.env['account.account'].search([('user_type_id', '=', ref('account.data_account_type_fixed_assets'))], limit=1).id
        if not xfa_account_id:
            xfa_account_id = self.env['account.account'].search([('user_type_id', '=', ref('account.data_account_type_current_assets'))], limit=1).id
        from datetime import datetime, timedelta
        import time
        if xfa_account_id and expense_account_id and journal_id:
            vals = {
                'journal_id': journal_id,
                'name': 'Hardware - 3 Years',
                'method_number': 3,
                'account_asset_id': xfa_account_id,
                'account_depreciation_id': xfa_account_id,
                'account_depreciation_expense_id': expense_account_id,
            }
            self._update('account.asset.category', 'account_asset', vals, 'account_asset_category_fixedassets0')
            vals = {
                'journal_id': journal_id,
                'name': 'Cars - 5 Years',
                'method_number': 5,
                'account_asset_id': xfa_account_id,
                'account_depreciation_id': xfa_account_id,
                'account_depreciation_expense_id': expense_account_id,
            }
            self._update('account.asset.category', 'account_asset', vals, 'account_asset_category_fixedassets1')
            vals = {
                'salvage_value': 2000.0,
                'state': 'open',
                'method_period': 12,
                'method_number': 5,
                'name': "CEO's car",
                'value': 12000.0,
                'category_id': ref('account_asset_category_fixedassets0'),
            }
            self._update('account.asset.asset', 'account_asset', vals, 'account_asset_asset_vehicles0')
            vals = {
                'method_end': (datetime.now().replace(month=8, day=11) + timedelta(days=3*365)).strftime('%Y-%m-%d'),
                'salvage_value': 0.0,
                'method_time': 'end',
                'name': 'V6 Engine and 10 inches tires',
                'value': 2800.0,
                'category_id': ref('account_asset_category_fixedassets0'),
            }
            self._update('account.asset.asset', 'account_asset', vals, 'account_asset_asset_cab0')
            vals = {
                'prorata': 1,
                'salvage_value': 100000.0,
                'state': 'open',
                'method_period': 12,
                'method_number': 20,
                'date': time.strftime('%Y-01-01'),
                'name': 'Office',
                'value': 500000.0,
                'category_id': ref('account_asset_category_fixedassets0'),
            }
            self._update('account.asset.asset', 'account_asset', vals, 'account_asset_asset_office0')
