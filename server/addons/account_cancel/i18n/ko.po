# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_cancel
# 
# Translators:
# <PERSON>, 2017
# <AUTHOR> <EMAIL>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: Goh <PERSON>tai <<EMAIL>>, 2018\n"
"Language-Team: Korean (https://www.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_cancel
#: model:ir.model,name:account_cancel.model_account_bank_statement
msgid "Bank Statement"
msgstr "예금거래 명세서"

#. module: account_cancel
#: model:ir.ui.view,arch_db:account_cancel.payment_cancel_form_inherit
msgid "Cancel"
msgstr "취소"

#. module: account_cancel
#: model:ir.ui.view,arch_db:account_cancel.view_move_form_inherit_account_cancel
msgid "Cancel Entry"
msgstr "분개 취소"

#. module: account_cancel
#: model:ir.ui.view,arch_db:account_cancel.invoice_form_cancel_inherit
#: model:ir.ui.view,arch_db:account_cancel.invoice_supplier_cancel_form_inherit
msgid "Cancel Invoice"
msgstr "청구서 취소"

#. module: account_cancel
#: model:ir.ui.view,arch_db:account_cancel.bank_statement_draft_form_inherit
msgid "Reset to New"
msgstr "신규로 재설정"

#. module: account_cancel
#: model:ir.ui.view,arch_db:account_cancel.bank_statement_cancel_form_inherit
msgid "Revert reconciliation"
msgstr "조정 되돌리기"
