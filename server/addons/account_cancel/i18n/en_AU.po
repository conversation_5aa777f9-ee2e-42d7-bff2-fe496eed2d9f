# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_cancel
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-09-07 15:52+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: English (Australia) (http://www.transifex.com/odoo/odoo-9/"
"language/en_AU/)\n"
"Language: en_AU\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_cancel
#: model:ir.model,name:account_cancel.model_account_bank_statement
msgid "Bank Statement"
msgstr ""

#. module: account_cancel
#: model:ir.model,name:account_cancel.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr ""

#. module: account_cancel
#: model:ir.ui.view,arch_db:account_cancel.bank_statement_cancel_form_inherit
#: model:ir.ui.view,arch_db:account_cancel.payment_cancel_form_inherit
msgid "Cancel"
msgstr "Cancel"

#. module: account_cancel
#: model:ir.ui.view,arch_db:account_cancel.invoice_form_cancel_inherit
#: model:ir.ui.view,arch_db:account_cancel.invoice_supplier_cancel_form_inherit
msgid "Cancel Invoice"
msgstr ""

#. module: account_cancel
#: code:addons/account_cancel/models/account_bank_statement.py:28
#, python-format
msgid "Please set the bank statement to New before canceling."
msgstr ""

#. module: account_cancel
#: model:ir.ui.view,arch_db:account_cancel.bank_statement_draft_form_inherit
msgid "Reset to New"
msgstr ""
