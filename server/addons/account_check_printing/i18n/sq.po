# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_check_printing
# 
# Translators:
# <PERSON>, 2017
# <PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Albanian (https://www.transifex.com/odoo/teams/41243/sq/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sq\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:58
#, python-format
msgid " : Check Number Sequence"
msgstr ""

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:69
#, python-format
msgid "A check memo cannot exceed 60 characters."
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment_check_amount_in_words
#: model:ir.model.fields,field_description:account_check_printing.field_account_register_payments_check_amount_in_words
msgid "Amount in Words"
msgstr ""

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Cancel"
msgstr "Anullo"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment_check_number
#: model:ir.model.fields,field_description:account_check_printing.field_account_register_payments_check_number
msgid "Check Number"
msgstr ""

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.view_account_journal_form_inherited
msgid "Check Printing"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal_check_printing_payment_method_selected
msgid "Check Printing Payment Method Selected"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal_check_sequence_id
msgid "Check Sequence"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal_check_manual_sequencing
#: model:ir.model.fields,help:account_check_printing.field_account_payment_check_manual_sequencing
#: model:ir.model.fields,help:account_check_printing.field_account_register_payments_check_manual_sequencing
msgid "Check this option if your pre-printed checks are not numbered."
msgstr ""

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Check to print"
msgstr ""

#. module: account_check_printing
#: model:account.payment.method,name:account_check_printing.account_payment_method_check
msgid "Checks"
msgstr ""

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.view_payment_check_printing_search
msgid "Checks To Print"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal_check_sequence_id
msgid "Checks numbering sequence."
msgstr ""

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:97
#, python-format
msgid "Checks to Print"
msgstr ""

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Checks to print"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_create_uid
msgid "Created by"
msgstr "Krijuar nga"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_create_date
msgid "Created on"
msgstr "Krijuar me"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_display_name
msgid "Display Name"
msgstr "Emri i paraqitur"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_id
msgid "ID"
msgstr "ID"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:89
#, python-format
msgid ""
"In order to print multiple checks at once, they must belong to the same bank"
" journal."
msgstr ""

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_journal
msgid "Journal"
msgstr "Ditar"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks___last_update
msgid "Last Modified on"
msgstr "Modifikimi i fundit në"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_write_uid
msgid "Last Updated by"
msgstr "Modifikuar per here te fundit nga"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_write_date
msgid "Last Updated on"
msgstr "Modifikuar per here te fundit me"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal_check_manual_sequencing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment_check_manual_sequencing
#: model:ir.model.fields,field_description:account_check_printing.field_account_register_payments_check_manual_sequencing
msgid "Manual Numbering"
msgstr ""

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.view_account_bank_journal_form_inherited_check_printing
msgid "Manual Numbering of check"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal_check_next_number
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_next_check_number
msgid "Next Check Number"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_register_payments_check_number
msgid ""
"Number of the check corresponding to this payment. If your pre-printed check"
" are not already numbered, you can manage the numbering in the journal "
"configuration page."
msgstr ""

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment
msgid "Payments"
msgstr ""

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:86
#, python-format
msgid ""
"Payments to print as a checks must have 'Check' selected as payment method "
"and not have already been reconciled"
msgstr ""

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"Please enter the number of the first pre-printed check that you are about to"
" print on."
msgstr ""

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Print"
msgstr "Print"

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Print Check"
msgstr ""

#. module: account_check_printing
#: model:ir.actions.server,name:account_check_printing.action_account_print_checks
msgid "Print Checks"
msgstr ""

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:99
#: model:ir.model,name:account_check_printing.model_print_prenumbered_checks
#: model:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
#, python-format
msgid "Print Pre-numbered Checks"
msgstr ""

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_register_payments
msgid "Register payments on multiple invoices"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal_check_next_number
msgid "Sequence number of the next printed check."
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal_check_printing_payment_method_selected
msgid ""
"Technical feature used to know whether check printing was enabled as payment"
" method."
msgstr ""

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:26
#, python-format
msgid ""
"The last check number was %s. In order to avoid a check being rejected by "
"the bank, you can only use a greater number."
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment_check_number
msgid ""
"The selected journal is configured to print check numbers. If your pre-"
"printed check paper already has numbers or if the current numbering is "
"wrong, you can change it in the journal configuration page."
msgstr ""

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:121
#, python-format
msgid ""
"There is no check layout configured.\n"
"Make sure the proper check printing module is installed and its configuration (in company settings > 'Configuration' tab) is correct."
msgstr ""

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"This will allow to save on payments the number of the corresponding check."
msgstr ""

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Unmark Sent"
msgstr ""

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_wizard_multi_charts_accounts
msgid "wizard.multi.charts.accounts"
msgstr ""
