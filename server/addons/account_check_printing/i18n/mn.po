# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_check_printing
# 
# Translators:
# <AUTHOR> <EMAIL>, 2018
# Bayarkhuu Bataa, 2018
# Martin Trigaux, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# Baskhuu <PERSON>doikhuu <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 10:14+0000\n"
"Last-Translator: Baskhuu <PERSON>hu<PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Mongolian (https://www.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:58
#, python-format
msgid " : Check Number Sequence"
msgstr " : Чекийн Дугаарын Дараалал"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:69
#, python-format
msgid "A check memo cannot exceed 60 characters."
msgstr "Чекийн тайлбар 60 тэмдэгтээс хэтрэхгүй."

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment_check_amount_in_words
#: model:ir.model.fields,field_description:account_check_printing.field_account_register_payments_check_amount_in_words
msgid "Amount in Words"
msgstr "Дүн үгээр"

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Cancel"
msgstr "Цуцлах"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment_check_number
#: model:ir.model.fields,field_description:account_check_printing.field_account_register_payments_check_number
msgid "Check Number"
msgstr "Чекийн Дугаар"

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.view_account_journal_form_inherited
msgid "Check Printing"
msgstr "Чек хэвлэлт"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal_check_printing_payment_method_selected
msgid "Check Printing Payment Method Selected"
msgstr "Төлбөрийн журналыг чек хэвлэх байдлаар ашиглах эсэх"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal_check_sequence_id
msgid "Check Sequence"
msgstr "Чекийн Дараалал"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal_check_manual_sequencing
#: model:ir.model.fields,help:account_check_printing.field_account_payment_check_manual_sequencing
#: model:ir.model.fields,help:account_check_printing.field_account_register_payments_check_manual_sequencing
msgid "Check this option if your pre-printed checks are not numbered."
msgstr ""
"Хэрэв дугаарлагдаагүй чекийн урьдчилан хэвлэх бол энэ сонголтыг сонгоно."

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Check to print"
msgstr "Хэвлэх Чек"

#. module: account_check_printing
#: model:account.payment.method,name:account_check_printing.account_payment_method_check
msgid "Checks"
msgstr "Шалгалтууд"

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.view_payment_check_printing_search
msgid "Checks To Print"
msgstr "Хэвлэх Чек"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal_check_sequence_id
msgid "Checks numbering sequence."
msgstr "Чекийг дугаарлах дараалал."

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:97
#, python-format
msgid "Checks to Print"
msgstr "Хэвлэх Чек"

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Checks to print"
msgstr "Хэвлэх Чек"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_create_uid
msgid "Created by"
msgstr "Үүсгэгч"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_create_date
msgid "Created on"
msgstr "Үүсгэсэн"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_display_name
msgid "Display Name"
msgstr "Дэлгэцийн Нэр"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_id
msgid "ID"
msgstr "ID"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:89
#, python-format
msgid ""
"In order to print multiple checks at once, they must belong to the same bank"
" journal."
msgstr ""
"Олон чекийг нэг мөр хэвлэхийн тулд тэдгээр нь нэг банкны журнальд "
"харъяалагдсан байх ёстой."

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_journal
msgid "Journal"
msgstr "Журнал"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks___last_update
msgid "Last Modified on"
msgstr "Сүүлийн зассан хийсэн огноо"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_write_uid
msgid "Last Updated by"
msgstr "Сүүлийн засвар хийсэн"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_write_date
msgid "Last Updated on"
msgstr "Сүүлийн засвар хийсэн огноо"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal_check_manual_sequencing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment_check_manual_sequencing
#: model:ir.model.fields,field_description:account_check_printing.field_account_register_payments_check_manual_sequencing
msgid "Manual Numbering"
msgstr "Гараар дугаарлах"

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.view_account_bank_journal_form_inherited_check_printing
msgid "Manual Numbering of check"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal_check_next_number
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_next_check_number
msgid "Next Check Number"
msgstr "Дараагийн Чек дугаар"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_register_payments_check_number
msgid ""
"Number of the check corresponding to this payment. If your pre-printed check"
" are not already numbered, you can manage the numbering in the journal "
"configuration page."
msgstr ""
"Энэ төлбөрт харгалзах чекийн тоо. Хэрэв таны урьдчилан хэвлэсэн чек нь "
"дугаарлагдаагүй бол журналийн тохиргооны хуусанд дугаарлалтыг удирдах "
"боломжтой."

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment
msgid "Payments"
msgstr "Төлбөрүүд"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:86
#, python-format
msgid ""
"Payments to print as a checks must have 'Check' selected as payment method "
"and not have already been reconciled"
msgstr ""
"Чек болгож хэвлэх төлбөр нь 'Чек' төлбөрийн арга сонгогдсон байх ёстой "
"бөгөөд тулгагдаагүй байх ёстой"

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"Please enter the number of the first pre-printed check that you are about to"
" print on."
msgstr ""
"Таны хэвлэх гэж байгаа урьдчилан хэвлэсэн чекийн тоог эхлээд оруулна уу."

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Print"
msgstr "Хэвлэх"

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Print Check"
msgstr "Чек хэвлэх"

#. module: account_check_printing
#: model:ir.actions.server,name:account_check_printing.action_account_print_checks
msgid "Print Checks"
msgstr "Чек хэвлэх"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:99
#: model:ir.model,name:account_check_printing.model_print_prenumbered_checks
#: model:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
#, python-format
msgid "Print Pre-numbered Checks"
msgstr "Урьдчилан дугаарласан чекийг хэвлэх"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_register_payments
msgid "Register payments on multiple invoices"
msgstr "Олон нэхэмжлэл дээр төлбөр бүртгэх"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal_check_next_number
msgid "Sequence number of the next printed check."
msgstr "Дараагийн хэвлэгдсэн чекийн дарааллын дугаар."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal_check_printing_payment_method_selected
msgid ""
"Technical feature used to know whether check printing was enabled as payment"
" method."
msgstr ""
"Төлбөрийн аргад чек хэвлэхийг зөвшөөрсөн эсэхийг мэдэхэд хэрэглэдэг технкийн"
" боломж."

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:26
#, python-format
msgid ""
"The last check number was %s. In order to avoid a check being rejected by "
"the bank, you can only use a greater number."
msgstr ""
"Сүүлийн чекийн дугаар нь %s байсан. Банк буцааж болох тул заавал үүнээс их "
"тоог хэрэглэх хэрэгтэй."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment_check_number
msgid ""
"The selected journal is configured to print check numbers. If your pre-"
"printed check paper already has numbers or if the current numbering is "
"wrong, you can change it in the journal configuration page."
msgstr ""
"Сонгосон журналь нь чекийн дугаар хэвлэхээр тохируулагдсан. Хэрэв таны "
"урьдчилан хэвлэсэн чекийн цаас нь дугаартай эсвэл одоогийн дугаарлалт нь "
"буруу байсан бол журналийн тохиргооны хуудсанд солих боломжтой."

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:121
#, python-format
msgid ""
"There is no check layout configured.\n"
"Make sure the proper check printing module is installed and its configuration (in company settings > 'Configuration' tab) is correct."
msgstr ""
"Чекийн зохиомж тохируулагдаагүй байна.\n"
"Зөв чек хэвлэх модуль суулгагдсан бөгөөд тохируулсан эсэхийг шалгана уу (Компанийн тохиргоо дотор > 'Тохиргоо' хавтас)."

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"This will allow to save on payments the number of the corresponding check."
msgstr "Энэ нь харгалзах чекүүдийн төлбөрийг хадгалахад хэрэглэгддэг."

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Unmark Sent"
msgstr "Илгээгдсэн тэмдэглэгээг арилгах"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_wizard_multi_charts_accounts
msgid "wizard.multi.charts.accounts"
msgstr "wizard.multi.charts.accounts"
