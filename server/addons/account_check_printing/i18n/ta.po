# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_check_printing
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON><PERSON><PERSON> <bag<PERSON><PERSON><PERSON>@gmail.com>, 2019
# <PERSON><PERSON> <PERSON> <mi<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 10:14+0000\n"
"Last-Translator: <PERSON><PERSON> M <PERSON> <miab<PERSON><PERSON><PERSON>@gmail.com>, 2019\n"
"Language-Team: Tamil (https://www.transifex.com/odoo/teams/41243/ta/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ta\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:58
#, python-format
msgid " : Check Number Sequence"
msgstr ": எண் வரிசை வரிசைமுறை"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:69
#, python-format
msgid "A check memo cannot exceed 60 characters."
msgstr "ஒரு காசோலை மெமோ 60 எழுத்துகளை தாண்டிவிட முடியாது."

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment_check_amount_in_words
#: model:ir.model.fields,field_description:account_check_printing.field_account_register_payments_check_amount_in_words
msgid "Amount in Words"
msgstr "சொற்கள் தொகை"

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Cancel"
msgstr "ரத்து"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment_check_number
#: model:ir.model.fields,field_description:account_check_printing.field_account_register_payments_check_number
msgid "Check Number"
msgstr "சரிபார்ப்பு எண்"

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.view_account_journal_form_inherited
msgid "Check Printing"
msgstr "அச்சிடு சரிபார்க்கவும்"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal_check_printing_payment_method_selected
msgid "Check Printing Payment Method Selected"
msgstr "அச்சிடுதல் செலுத்துதல் முறையை தேர்வுசெய்க"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal_check_sequence_id
msgid "Check Sequence"
msgstr "வரிசை காசோலை"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal_check_manual_sequencing
#: model:ir.model.fields,help:account_check_printing.field_account_payment_check_manual_sequencing
#: model:ir.model.fields,help:account_check_printing.field_account_register_payments_check_manual_sequencing
msgid "Check this option if your pre-printed checks are not numbered."
msgstr ""
"உங்கள் முன் அச்சிடப்பட்ட காசோலைகள் எண்ணிடப்பட்டால் இந்த விருப்பத்தை தேர்வு "
"செய்யவும்."

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Check to print"
msgstr "அச்சிடுவதற்குச் சரிபார்க்கவும்"

#. module: account_check_printing
#: model:account.payment.method,name:account_check_printing.account_payment_method_check
msgid "Checks"
msgstr "காசோலைகள்"

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.view_payment_check_printing_search
msgid "Checks To Print"
msgstr "அச்சிடுவதற்கான காசோலைகள்"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal_check_sequence_id
msgid "Checks numbering sequence."
msgstr "வரிசை எண் வரிசைப்படுத்துகிறது."

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:97
#, python-format
msgid "Checks to Print"
msgstr "அச்சிடுவதற்கான காசோலைகள்"

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Checks to print"
msgstr "அச்சிடுவதற்கான காசோலைகள்"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_create_uid
msgid "Created by"
msgstr "உருவாக்கியவர்"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_create_date
msgid "Created on"
msgstr "உருவாக்கப்பட்ட தேதி"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_display_name
msgid "Display Name"
msgstr "காட்சி பெயர்"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_id
msgid "ID"
msgstr "ID"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:89
#, python-format
msgid ""
"In order to print multiple checks at once, they must belong to the same bank"
" journal."
msgstr ""
"ஒரே நேரத்தில் பல காசோலைகளை அச்சிடுவதற்கு, அவை ஒரே வங்கி இதழின் சொந்தக்காரர்."

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_journal
msgid "Journal"
msgstr "ஜர்னல்"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks___last_update
msgid "Last Modified on"
msgstr "கடைசியாக திருத்திய"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_write_uid
msgid "Last Updated by"
msgstr "கடைசியாக புதுப்பிக்கப்பட்டவர்"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_write_date
msgid "Last Updated on"
msgstr "கடைசியாக புதுப்பிக்கப்பட்டது"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal_check_manual_sequencing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment_check_manual_sequencing
#: model:ir.model.fields,field_description:account_check_printing.field_account_register_payments_check_manual_sequencing
msgid "Manual Numbering"
msgstr "கையேடு எண்ணிடல்"

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.view_account_bank_journal_form_inherited_check_printing
msgid "Manual Numbering of check"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal_check_next_number
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks_next_check_number
msgid "Next Check Number"
msgstr "அடுத்த சரிபார்ப்பு எண்"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_register_payments_check_number
msgid ""
"Number of the check corresponding to this payment. If your pre-printed check"
" are not already numbered, you can manage the numbering in the journal "
"configuration page."
msgstr ""
"இந்த கட்டணத்துடன் தொடர்புடைய காசோலை எண். முன்பே அச்சிடப்பட்ட காசோலை ஏற்கனவே எண்ணிவிட்டால், பத்திரிகை கட்டமைப்பு பக்கத்தில் நீங்கள் எண்ணை நிர்வகிக்கலாம்.\n"
"அபிவிருத்தி இல்லை"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment
msgid "Payments"
msgstr "கொடுப்பனவு"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:86
#, python-format
msgid ""
"Payments to print as a checks must have 'Check' selected as payment method "
"and not have already been reconciled"
msgstr ""
"காசோலைகள் என அச்சிட செலுத்த வேண்டிய பணம், 'சரிபார்க்க' கட்டணம் செலுத்தும் "
"முறையாக தேர்வு செய்யப்பட வேண்டும், ஏற்கெனவே சமரசம் செய்யப்படவில்லை"

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"Please enter the number of the first pre-printed check that you are about to"
" print on."
msgstr "நீங்கள் அச்சிட உள்ள முதல் முன்பே அச்சிடப்பட்ட எண்ணை உள்ளிடவும்."

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Print"
msgstr "அச்சு"

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Print Check"
msgstr "அச்சு சோதனை"

#. module: account_check_printing
#: model:ir.actions.server,name:account_check_printing.action_account_print_checks
msgid "Print Checks"
msgstr "அச்சு சரிவுகள்"

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:99
#: model:ir.model,name:account_check_printing.model_print_prenumbered_checks
#: model:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
#, python-format
msgid "Print Pre-numbered Checks"
msgstr "முன்பே குறியிடப்பட்ட காசோலைகளை அச்சிடுக"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_register_payments
msgid "Register payments on multiple invoices"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal_check_next_number
msgid "Sequence number of the next printed check."
msgstr "அடுத்த அச்சிடப்பட்ட காசோலை வரிசை எண்."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal_check_printing_payment_method_selected
msgid ""
"Technical feature used to know whether check printing was enabled as payment"
" method."
msgstr ""
"காசோலை அச்சிடுதல் பணம் செலுத்தும் முறையாக செயல்படுகிறதா என்பதை அறிந்து கொள்ள"
" தொழில்நுட்ப அம்சம்."

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_journal.py:26
#, python-format
msgid ""
"The last check number was %s. In order to avoid a check being rejected by "
"the bank, you can only use a greater number."
msgstr ""
"கடைசி காசோலை எண். வங்கியால் நிராகரிக்கப்படும் ஒரு காசோலை தவிர்க்க, நீங்கள் "
"அதிக எண்ணிக்கையிலான எண்ணிக்கையை மட்டுமே பயன்படுத்த முடியும்."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment_check_number
msgid ""
"The selected journal is configured to print check numbers. If your pre-"
"printed check paper already has numbers or if the current numbering is "
"wrong, you can change it in the journal configuration page."
msgstr ""
"தேர்ந்தெடுத்த பத்திரிகை காசோலை எண்களை அச்சிட கட்டமைக்கப்பட்டுள்ளது. முன்பே "
"அச்சிடப்பட்ட காசோலை ஏற்கனவே எண்களைக் கொண்டிருக்கிறது அல்லது நடப்பு எண் "
"தவறானது எனில், இதனை நீங்கள் இதனை உள்ளமைக்க முடியும்."

#. module: account_check_printing
#: code:addons/account_check_printing/models/account_payment.py:121
#, python-format
msgid ""
"There is no check layout configured.\n"
"Make sure the proper check printing module is installed and its configuration (in company settings > 'Configuration' tab) is correct."
msgstr ""

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"This will allow to save on payments the number of the corresponding check."
msgstr ""
"இது பணம் செலுத்துவதில் தொடர்புடைய காசோலைகளின் எண்ணிக்கையை சேமிக்க "
"அனுமதிக்கும்."

#. module: account_check_printing
#: model:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Unmark Sent"
msgstr "குறிநீக்கவும்  அனுப்பப்பட்டது"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_wizard_multi_charts_accounts
msgid "wizard.multi.charts.accounts"
msgstr ""
