version: '3.8'

services:
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: odoo
      POSTGRES_USER: odoo
      POSTGRES_PASSWORD: odoo
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - odoo-db-data:/var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    restart: unless-stopped

  odoo:
    build: .
    depends_on:
      - db
    ports:
      - "8069:8069"
    volumes:
      - odoo-web-data:/var/lib/odoo
      - ./server:/opt/odoo/server
      - ./odoo-server.conf:/etc/odoo/odoo.conf
      - ./addons:/opt/odoo/addons
    environment:
      - HOST=db
      - USER=odoo
      - PASSWORD=odoo
    restart: unless-stopped
    command: ["/opt/odoo/server/odoo-bin", "-c", "/etc/odoo/odoo.conf"]

volumes:
  odoo-web-data:
  odoo-db-data:
