#!/bin/bash

echo "🚀 Starting ODEX Server locally (without Docker)..."
echo "📋 This will start the Odoo server on port 8069"
echo ""

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3 first."
    exit 1
fi

# Check if PostgreSQL is running
if ! pg_isready -h localhost -p 5432 &> /dev/null; then
    echo "⚠️  PostgreSQL is not running on localhost:5432"
    echo "   You can either:"
    echo "   1. Install and start PostgreSQL locally"
    echo "   2. Use Docker for PostgreSQL only: docker run -d --name postgres -e POSTGRES_DB=postgres -e POSTGRES_USER=odoo -e POSTGRES_PASSWORD=odoo -p 5432:5432 postgres:13"
    echo ""
    read -p "Do you want to start PostgreSQL with Docker? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🐘 Starting PostgreSQL with Docker..."
        docker run -d --name odoo-postgres -e POSTGRES_DB=postgres -e POSTGRES_USER=odoo -e POSTGRES_PASSWORD=odoo -p 5432:5432 postgres:13
        if [ $? -eq 0 ]; then
            echo "✅ PostgreSQL started successfully"
            echo "⏳ Waiting for PostgreSQL to be ready..."
            sleep 5
        else
            echo "❌ Failed to start PostgreSQL with Docker"
            exit 1
        fi
    else
        echo "Please start PostgreSQL manually and try again."
        exit 1
    fi
fi

# Check if requirements are installed
echo "📦 Checking Python dependencies..."
if ! python3 -c "import odoo" 2>/dev/null; then
    echo "📥 Installing Python dependencies..."
    pip3 install -r server/requirements.txt
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies. You might need to run: sudo pip3 install -r server/requirements.txt"
        exit 1
    fi
fi

# Update configuration for local setup
echo "🔧 Updating configuration..."
cat > odoo-local.conf << EOF
[options]
admin_passwd = admin
addons_path = server/odoo/addons,server/addons
xmlrpc_port = 8069
db_host = localhost
db_port = 5432
db_user = odoo
db_password = odoo
logfile = odoo-server.log
data_dir = ./odoo-data
EOF

# Create data directory
mkdir -p odoo-data

echo "🚀 Starting ODEX server..."
echo "📍 Server will be available at: http://localhost:8069"
echo "🔑 Admin password: admin"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start the server
python3 server/odoo-bin -c odoo-local.conf
