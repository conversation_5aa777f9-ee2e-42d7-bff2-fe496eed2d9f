FROM python:3.8-slim-buster

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libxml2-dev \
    libxslt1-dev \
    libevent-dev \
    libsasl2-dev \
    libldap2-dev \
    libpq-dev \
    libjpeg-dev \
    libpng-dev \
    libfreetype6-dev \
    liblcms2-dev \
    libwebp-dev \
    libharfbuzz-dev \
    libfribidi-dev \
    libxcb1-dev \
    pkg-config \
    wget \
    curl \
    node-less \
    npm \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install wkhtmltopdf
RUN wget -O wkhtmltox.deb https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox_0.12.6-1.buster_amd64.deb \
    && apt-get update \
    && apt-get install -y ./wkhtmltox.deb \
    && rm wkhtmltox.deb \
    && rm -rf /var/lib/apt/lists/*

# Create odoo user
RUN useradd --create-home --home-dir /opt/odoo --no-log-init odoo

# Set working directory
WORKDIR /opt/odoo

# Copy requirements and install Python dependencies
COPY server/requirements.txt /opt/odoo/
RUN pip install --no-cache-dir -r requirements.txt

# Copy server files
COPY server /opt/odoo/server

# Create necessary directories
RUN mkdir -p /var/lib/odoo \
    && mkdir -p /etc/odoo \
    && chown -R odoo:odoo /opt/odoo \
    && chown -R odoo:odoo /var/lib/odoo

# Switch to odoo user
USER odoo

# Expose port
EXPOSE 8069

# Set default command
CMD ["/opt/odoo/server/odoo-bin", "-c", "/etc/odoo/odoo.conf"]
